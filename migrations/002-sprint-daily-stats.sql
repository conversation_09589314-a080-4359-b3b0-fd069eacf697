-- 002-sprint-daily-stats.sql
-- Sprint daily stats functions and triggers

-- Function to update sprint daily stats with streak calculation
CREATE OR REPLACE FUNCTION update_sprint_daily_stats(
    user_id_param VARCHAR(36),
    sprint_date DATE,
    elo_type_param VARCHAR(100),
    sprint_won BOOLEAN,
    duration_seconds INTEGER,
    puzzles_solved_param INTEGER,
    puzzles_attempted_param INTEGER
) RETURNS VOID AS $$
DECLARE
    current_success INTEGER := 0;
    current_total INTEGER := 0;
    current_duration INTEGER := 0;
    current_puzzles_solved INTEGER := 0;
    current_puzzles_attempted INTEGER := 0;
    new_success INTEGER;
    new_total INTEGER;
    new_duration INTEGER;
    new_puzzles_solved INTEGER;
    new_puzzles_attempted INTEGER;
    yesterday_date DATE := sprint_date - INTERVAL '1 day';
    yesterday_had_activity BOOLEAN := FALSE;
    current_streak INTEGER := 0;
    new_streak INTEGER;
BEGIN
    -- Get current stats for this date and elo type
    SELECT sprint_success, sprint_total, sprint_total_duration, puzzles_solved, puzzles_attempted
    INTO current_success, current_total, current_duration, current_puzzles_solved, current_puzzles_attempted
    FROM user_sprint_daily_stats
    WHERE user_id = user_id_param AND date = sprint_date AND elo_type = elo_type_param;

    -- If no record exists, initialize with zeros
    IF NOT FOUND THEN
        current_success := 0;
        current_total := 0;
        current_duration := 0;
        current_puzzles_solved := 0;
        current_puzzles_attempted := 0;
    END IF;

    -- Calculate new values
    new_success := current_success + (CASE WHEN sprint_won THEN 1 ELSE 0 END);
    new_total := current_total + 1;
    new_duration := current_duration + duration_seconds;
    new_puzzles_solved := current_puzzles_solved + puzzles_solved_param;
    new_puzzles_attempted := current_puzzles_attempted + puzzles_attempted_param;

    -- Check if yesterday had activity for streak calculation (any elo type)
    SELECT COUNT(*) > 0 INTO yesterday_had_activity
    FROM user_sprint_daily_stats
    WHERE user_id = user_id_param AND date = yesterday_date AND sprint_total > 0;

    -- Get current streak (from yesterday if it exists, otherwise 0)
    -- Use the same elo type for streak calculation
    IF yesterday_had_activity THEN
        SELECT COALESCE(MAX(streak), 0) INTO current_streak
        FROM user_sprint_daily_stats
        WHERE user_id = user_id_param AND date = yesterday_date AND elo_type = elo_type_param;
    ELSE
        current_streak := 0;
    END IF;

    -- Calculate new streak (increment if yesterday had activity, otherwise start at 1)
    new_streak := current_streak + 1;

    -- Insert or update sprint daily stats
    INSERT INTO user_sprint_daily_stats (
        id, user_id, date, elo_type, sprint_success, sprint_total, sprint_total_duration,
        puzzles_solved, puzzles_attempted, streak, created_at, updated_at
    ) VALUES (
        gen_random_uuid()::VARCHAR(36), user_id_param, sprint_date, elo_type_param,
        new_success, new_total, new_duration, new_puzzles_solved, new_puzzles_attempted,
        new_streak, NOW(), NOW()
    )
    ON CONFLICT (user_id, date, elo_type) DO UPDATE SET
        sprint_success = new_success,
        sprint_total = new_total,
        sprint_total_duration = new_duration,
        puzzles_solved = new_puzzles_solved,
        puzzles_attempted = new_puzzles_attempted,
        streak = new_streak,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- Trigger function for sprint daily stats updates
CREATE OR REPLACE FUNCTION trigger_update_sprint_daily_stats()
RETURNS TRIGGER AS $$
BEGIN
    -- Handle puzzle sprint events only
    IF NEW.event_type = 'puzzle_sprint' AND NEW.event_sub_type = 'end' THEN
        DECLARE
            sprint_date DATE := DATE(NEW.event_time);
            sprint_status VARCHAR(50) := NEW.event_data->>'status';
            duration_val INTEGER := COALESCE((NEW.event_data->>'duration')::integer, 0);
            succeeded_puzzles_val INTEGER := COALESCE((NEW.event_data->>'succeeded_puzzles')::integer, 0);
            failed_puzzles_val INTEGER := COALESCE((NEW.event_data->>'failed_puzzles')::integer, 0);
            elo_type_val VARCHAR(100) := COALESCE(NEW.event_data->>'elo_type', 'blitz');
            sprint_won BOOLEAN := FALSE;
            puzzles_attempted INTEGER;
        BEGIN
            -- Determine if sprint was won based on status
            IF sprint_status = 'completed_success' THEN
                sprint_won := TRUE;
            END IF;

            -- Calculate total puzzles attempted
            puzzles_attempted := succeeded_puzzles_val + failed_puzzles_val;

            -- Update sprint daily stats
            PERFORM update_sprint_daily_stats(
                NEW.user_id,
                sprint_date,
                elo_type_val,
                sprint_won,
                duration_val,
                succeeded_puzzles_val,
                puzzles_attempted
            );
        END;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
CREATE OR REPLACE TRIGGER update_sprint_daily_stats_trigger
    AFTER INSERT ON events
    FOR EACH ROW
    EXECUTE FUNCTION trigger_update_sprint_daily_stats();
