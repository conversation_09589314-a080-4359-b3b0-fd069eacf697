-- 003-arrow-duel-stats.sql
-- Arrow-duel stats functions and enhanced triggers

-- Function to update per-puzzle arrow-duel stats for user-generated puzzles
CREATE OR REPLACE FUNCTION update_user_puzzle_arrow_duel_stats(user_id_param VARCHAR(36), puzzle_id_param VARCHAR(255), solved BOOLEAN, time_spent INTEGER, attempt_time TIMESTAMPTZ, is_disliked_param BOOLEAN DEFAULT NULL)
RETURNS VOID AS $$
DECLARE
    current_attempts INTEGER := 0;
    current_success_count INTEGER := 0;
    current_total_time INTEGER := 0;
    new_success_count INTEGER;
    new_total_time INTEGER;
    new_average_time FLOAT;
    disliked_time TIMESTAMPTZ := NULL;
BEGIN
    -- Get current stats for this user-puzzle combination
    SELECT attempts, success_count, total_time
    INTO current_attempts, current_success_count, current_total_time
    FROM user_puzzle_arrow_duel_stats
    WHERE user_id = user_id_param AND puzzle_id = puzzle_id_param;

    -- If no existing record, initialize to 0
    IF current_attempts IS NULL THEN
        current_attempts := 0;
        current_success_count := 0;
        current_total_time := 0;
    END IF;

    -- Increment attempts
    current_attempts := current_attempts + 1;

    -- Update success count if solved
    new_success_count := current_success_count;
    IF solved THEN
        new_success_count := current_success_count + 1;
    END IF;

    -- Update total time
    new_total_time := current_total_time + time_spent;

    -- Calculate new average time
    new_average_time := new_total_time::FLOAT / current_attempts;

    -- Set disliked timestamp if marking as disliked
    IF is_disliked_param = TRUE THEN
        disliked_time := attempt_time;
    END IF;

    -- Insert or update arrow-duel puzzle stats
    INSERT INTO user_puzzle_arrow_duel_stats (
        id, user_id, puzzle_id, attempts, success_count, total_time, average_time,
        last_attempt_time, last_attempt_success, is_disliked, disliked_at, created_at, updated_at
    ) VALUES (
        gen_random_uuid()::VARCHAR(36), user_id_param, puzzle_id_param,
        current_attempts, new_success_count, new_total_time, new_average_time,
        attempt_time, solved, COALESCE(is_disliked_param, FALSE), disliked_time, NOW(), NOW()
    )
    ON CONFLICT (user_id, puzzle_id) DO UPDATE SET
        attempts = current_attempts,
        success_count = new_success_count,
        total_time = new_total_time,
        average_time = new_average_time,
        last_attempt_time = attempt_time,
        last_attempt_success = solved,
        is_disliked = CASE WHEN is_disliked_param IS NOT NULL THEN is_disliked_param ELSE user_puzzle_arrow_duel_stats.is_disliked END,
        disliked_at = CASE WHEN is_disliked_param = TRUE THEN disliked_time WHEN is_disliked_param = FALSE THEN NULL ELSE user_puzzle_arrow_duel_stats.disliked_at END,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- Function to update per-lichess-puzzle arrow-duel stats
CREATE OR REPLACE FUNCTION update_user_lichess_puzzle_arrow_duel_stats(user_id_param VARCHAR(36), lichess_puzzle_id_param VARCHAR(10), solved BOOLEAN, time_spent INTEGER, attempt_time TIMESTAMPTZ, is_disliked_param BOOLEAN DEFAULT NULL)
RETURNS VOID AS $$
DECLARE
    current_attempts INTEGER := 0;
    current_success_count INTEGER := 0;
    current_total_time INTEGER := 0;
    new_success_count INTEGER;
    new_total_time INTEGER;
    new_average_time FLOAT;
    disliked_time TIMESTAMPTZ := NULL;
BEGIN
    -- Get current stats for this user-lichess-puzzle combination
    SELECT attempts, success_count, total_time
    INTO current_attempts, current_success_count, current_total_time
    FROM user_lichess_puzzle_arrow_duel_stats
    WHERE user_id = user_id_param AND lichess_puzzle_id = lichess_puzzle_id_param;

    -- If no existing record, initialize to 0
    IF current_attempts IS NULL THEN
        current_attempts := 0;
        current_success_count := 0;
        current_total_time := 0;
    END IF;

    -- Increment attempts
    current_attempts := current_attempts + 1;

    -- Update success count if solved
    new_success_count := current_success_count;
    IF solved THEN
        new_success_count := current_success_count + 1;
    END IF;

    -- Update total time
    new_total_time := current_total_time + time_spent;

    -- Calculate new average time
    new_average_time := new_total_time::FLOAT / current_attempts;

    -- Set disliked timestamp if marking as disliked
    IF is_disliked_param = TRUE THEN
        disliked_time := attempt_time;
    END IF;

    -- Insert or update lichess arrow-duel puzzle stats
    INSERT INTO user_lichess_puzzle_arrow_duel_stats (
        id, user_id, lichess_puzzle_id, attempts, success_count, total_time, average_time,
        last_attempt_time, last_attempt_success, is_disliked, disliked_at, created_at, updated_at
    ) VALUES (
        gen_random_uuid()::VARCHAR(36), user_id_param, lichess_puzzle_id_param,
        current_attempts, new_success_count, new_total_time, new_average_time,
        attempt_time, solved, COALESCE(is_disliked_param, FALSE), disliked_time, NOW(), NOW()
    )
    ON CONFLICT (user_id, lichess_puzzle_id) DO UPDATE SET
        attempts = current_attempts,
        success_count = new_success_count,
        total_time = new_total_time,
        average_time = new_average_time,
        last_attempt_time = attempt_time,
        last_attempt_success = solved,
        is_disliked = CASE WHEN is_disliked_param IS NOT NULL THEN is_disliked_param ELSE user_lichess_puzzle_arrow_duel_stats.is_disliked END,
        disliked_at = CASE WHEN is_disliked_param = TRUE THEN disliked_time WHEN is_disliked_param = FALSE THEN NULL ELSE user_lichess_puzzle_arrow_duel_stats.disliked_at END,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- Enhanced trigger function for daily stats updates with Arrow-Duel support
CREATE OR REPLACE FUNCTION trigger_update_daily_stats()
RETURNS TRIGGER AS $$
BEGIN
    -- Handle puzzle events only
    IF NEW.event_type = 'puzzle' THEN
        DECLARE
            solved BOOLEAN := (NEW.event_data->>'solved')::boolean;
            puzzle_date DATE := DATE(NEW.event_time);
            puzzle_id_val VARCHAR(255) := NEW.event_data->>'puzzle_id';
            puzzle_type_val VARCHAR(20) := COALESCE(NEW.event_data->>'puzzle_type', 'user');
            time_spent INTEGER := COALESCE((NEW.event_data->>'time_spent')::integer, 0);
            is_disliked_val BOOLEAN := NULL;
            attempt_type_val VARCHAR(20) := COALESCE(NEW.event_data->>'attempt_type', 'regular');
        BEGIN
            -- Parse is_disliked field if present (only set when not NULL)
            IF NEW.event_data ? 'is_disliked' AND NEW.event_data->>'is_disliked' IS NOT NULL THEN
                is_disliked_val := (NEW.event_data->>'is_disliked')::boolean;
            END IF;

            -- Update daily stats with streak calculation (for all attempt types)
            PERFORM update_daily_puzzle_stats(NEW.user_id, puzzle_date, solved, time_spent);

            -- Update per-puzzle stats only if puzzle_id is not null
            IF puzzle_id_val IS NOT NULL THEN
                -- Route to appropriate stats update based on attempt_type
                IF attempt_type_val = 'arrow_duel' THEN
                    -- Update arrow-duel specific stats
                    IF puzzle_type_val = 'lichess' THEN
                        PERFORM update_user_lichess_puzzle_arrow_duel_stats(
                            NEW.user_id,
                            puzzle_id_val,
                            solved,
                            time_spent,
                            NEW.event_time,
                            is_disliked_val
                        );
                    ELSE
                        PERFORM update_user_puzzle_arrow_duel_stats(
                            NEW.user_id,
                            puzzle_id_val,
                            solved,
                            time_spent,
                            NEW.event_time,
                            is_disliked_val
                        );
                    END IF;
                ELSE
                    -- Existing regular stats update logic
                    IF puzzle_type_val = 'lichess' THEN
                        -- Update lichess puzzle stats
                        PERFORM update_user_lichess_puzzle_stats(
                            NEW.user_id,
                            puzzle_id_val,
                            solved,
                            time_spent,
                            NEW.event_time,
                            is_disliked_val
                        );
                    ELSE
                        -- Update user puzzle stats
                        PERFORM update_user_puzzle_stats(
                            NEW.user_id,
                            puzzle_id_val,
                            solved,
                            time_spent,
                            NEW.event_time,
                            is_disliked_val
                        );
                    END IF;
                END IF;
            END IF;
        END;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
