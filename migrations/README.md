# Database Migrations

This directory contains SQL migration files that are automatically executed during the migration process.

## How it works

1. **Auto Migration**: First, GORM auto-migration runs to create/update tables based on Go models
2. **SQL Migrations**: Then, SQL files in this directory are executed in alphabetical order
3. **Migration Tracking**: A `schema_migrations` table tracks which migrations have been executed to avoid re-running them

## Naming Convention

Use the following naming pattern for migration files:
```
XXX-description.sql
```

Where:
- `XXX` is a 3-digit number (001, 002, 003, etc.)
- `description` is a brief description of what the migration does
- Use hyphens to separate words

Examples:
- `001-user-stats-functions.sql`
- `002-add-indexes.sql`
- `003-update-triggers.sql`

## Creating New Migrations

1. Create a new SQL file in this directory with the next sequential number
2. Write idempotent SQL (use `CREATE OR REPLACE`, `IF NOT EXISTS`, etc.)
3. Test your migration locally
4. Commit the file to version control

## Example Migration File

```sql
-- 002-add-performance-indexes.sql
-- Add indexes for better query performance

-- Create index if it doesn't exist
CREATE INDEX IF NOT EXISTS idx_events_user_time 
ON events(user_id, event_time DESC);

-- Create index for puzzle stats
CREATE INDEX IF NOT EXISTS idx_user_puzzle_stats_user_puzzle 
ON user_puzzle_stats(user_id, puzzle_id);

-- Add a comment
COMMENT ON INDEX idx_events_user_time IS 'Improves performance for user event queries';
```

## Best Practices

1. **Idempotent**: Always write migrations that can be run multiple times safely
2. **Backwards Compatible**: Avoid breaking changes when possible
3. **Test Locally**: Test migrations on your local database before committing
4. **Small Changes**: Keep migrations focused on a single logical change
5. **Comments**: Add comments explaining complex migrations

## Running Migrations

Migrations are automatically run when you execute:
```bash
make migrate
# or
./build/chessticize migrate
```

The system will:
1. Run GORM auto-migration for Go models
2. Execute any new SQL migration files
3. Track executed migrations to avoid duplicates

## Migration Status

You can check which migrations have been executed by querying the `schema_migrations` table:
```sql
SELECT * FROM schema_migrations ORDER BY executed_at;
```
