-- 004-daily-quest-requirements.sql
-- This migration adds support for daily quest requirements and updates user daily stats
-- to include sprint and arrow duel tracking with quest completion functionality.

-- Insert initial quest requirements
-- This will run after GORM auto-migration creates the daily_quest_requirements table
INSERT INTO daily_quest_requirements (id, type, name, description, target, is_active, created_at, updated_at)
VALUES 
    ('quest-sprint-daily', 'sprint', 'Daily Sprint', 'Complete at least one puzzle sprint', 1, true, NOW(), NOW()),
    ('quest-arrow-duel-daily', 'arrow_duel', 'Daily Arrow Duel', 'Participate in at least one arrow duel', 1, true, NOW(), NOW())
ON CONFLICT (type) DO NOTHING;

-- Update the existing trigger function to handle sprint and arrow duel stats
-- FIXED: Use event_time instead of created_at for proper date calculation
CREATE OR REPLACE FUNCTION trigger_update_daily_stats()
RETURNS TRIGGER AS $$
BEGIN
    -- Handle puzzle events (existing logic)
    IF NEW.event_type = 'puzzle' AND NEW.event_sub_type = 'end' THEN
        -- Check if this is an arrow duel
        IF NEW.event_data::jsonb ? 'attemptType' AND
           NEW.event_data::jsonb->>'attemptType' = 'arrow_duel' THEN
            CALL update_arrow_duel_daily_stats(NEW.user_id, NEW.event_time::date, NEW.event_data::jsonb);
        ELSE
            -- Regular puzzle logic (existing)
            CALL update_puzzle_daily_stats(NEW.user_id, NEW.event_time::date, NEW.event_data::jsonb);
        END IF;
    END IF;

    -- Handle sprint events (NEW)
    IF NEW.event_type = 'puzzle_sprint' AND NEW.event_sub_type = 'end' THEN
        CALL update_sprint_daily_stats(NEW.user_id, NEW.event_time::date, NEW.event_data::jsonb);
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create procedure to update sprint daily stats
CREATE OR REPLACE PROCEDURE update_sprint_daily_stats(
    p_user_id VARCHAR(36),
    p_date DATE,
    p_event_data JSONB
)
LANGUAGE plpgsql AS $$
DECLARE
    v_status TEXT;
    v_duration INT;
    v_success_increment INT := 0;
BEGIN
    -- Extract sprint data
    v_status := p_event_data->>'status';
    v_duration := COALESCE((p_event_data->>'duration')::INT, 0);
    
    -- Determine if sprint was successful
    IF v_status = 'completed_success' THEN
        v_success_increment := 1;
    END IF;
    
    -- Insert or update daily stats
    INSERT INTO user_daily_stats (
        id, user_id, date, sprint_success, sprint_total, sprint_total_duration,
        created_at, updated_at
    ) VALUES (
        gen_random_uuid()::text, p_user_id, p_date, v_success_increment, 1, v_duration,
        NOW(), NOW()
    )
    ON CONFLICT (user_id, date) DO UPDATE SET
        sprint_success = user_daily_stats.sprint_success + v_success_increment,
        sprint_total = user_daily_stats.sprint_total + 1,
        sprint_total_duration = user_daily_stats.sprint_total_duration + v_duration,
        updated_at = NOW();
    
    -- Update quest completion status
    CALL update_quest_completion(p_user_id, p_date);
END;
$$;

-- Create procedure to update arrow duel daily stats
CREATE OR REPLACE PROCEDURE update_arrow_duel_daily_stats(
    p_user_id VARCHAR(36),
    p_date DATE,
    p_event_data JSONB
)
LANGUAGE plpgsql AS $$
DECLARE
    v_was_correct BOOLEAN;
    v_time_taken INT;
    v_success_increment INT := 0;
BEGIN
    -- Extract arrow duel data
    v_was_correct := COALESCE((p_event_data->>'was_correct')::BOOLEAN, false);
    v_time_taken := COALESCE((p_event_data->>'time_taken_ms')::INT, 0);
    
    -- Determine if arrow duel was successful
    IF v_was_correct THEN
        v_success_increment := 1;
    END IF;
    
    -- Insert or update daily stats
    INSERT INTO user_daily_stats (
        id, user_id, date, arrow_duel_success, arrow_duel_total, arrow_duel_total_duration,
        created_at, updated_at
    ) VALUES (
        gen_random_uuid()::text, p_user_id, p_date, v_success_increment, 1, v_time_taken,
        NOW(), NOW()
    )
    ON CONFLICT (user_id, date) DO UPDATE SET
        arrow_duel_success = user_daily_stats.arrow_duel_success + v_success_increment,
        arrow_duel_total = user_daily_stats.arrow_duel_total + 1,
        arrow_duel_total_duration = user_daily_stats.arrow_duel_total_duration + v_time_taken,
        updated_at = NOW();
    
    -- Update quest completion status
    CALL update_quest_completion(p_user_id, p_date);
END;
$$;

-- Create procedure to check and update quest completion
CREATE OR REPLACE PROCEDURE update_quest_completion(
    p_user_id VARCHAR(36),
    p_date DATE
)
LANGUAGE plpgsql AS $$
DECLARE
    v_stats_record RECORD;
    v_quest_completed BOOLEAN := true;
    v_requirement RECORD;
    v_previous_streak INT := 0;
    v_new_streak INT;
BEGIN
    -- Get current daily stats
    SELECT * INTO v_stats_record
    FROM user_daily_stats
    WHERE user_id = p_user_id AND date = p_date;
    
    IF v_stats_record IS NULL THEN
        RETURN; -- No stats record exists yet
    END IF;
    
    -- Check each active quest requirement
    FOR v_requirement IN 
        SELECT type, target FROM daily_quest_requirements WHERE is_active = true
    LOOP
        CASE v_requirement.type
            WHEN 'sprint' THEN
                IF COALESCE(v_stats_record.sprint_total, 0) < v_requirement.target THEN
                    v_quest_completed := false;
                    EXIT; -- No need to check further
                END IF;
            WHEN 'arrow_duel' THEN
                IF COALESCE(v_stats_record.arrow_duel_total, 0) < v_requirement.target THEN
                    v_quest_completed := false;
                    EXIT;
                END IF;
        END CASE;
    END LOOP;
    
    -- Calculate quest streak
    IF v_quest_completed THEN
        -- Get previous day's quest streak
        SELECT COALESCE(quest_streak, 0) INTO v_previous_streak
        FROM user_daily_stats
        WHERE user_id = p_user_id 
          AND date = p_date - INTERVAL '1 day'
          AND quest_completed = true;
        
        v_new_streak := COALESCE(v_previous_streak, 0) + 1;
    ELSE
        v_new_streak := 0;
    END IF;
    
    -- Update quest completion status
    UPDATE user_daily_stats 
    SET 
        quest_completed = v_quest_completed,
        quest_streak = v_new_streak,
        updated_at = NOW()
    WHERE user_id = p_user_id AND date = p_date;
END;
$$;

-- Update the existing update_puzzle_daily_stats procedure to also update quest completion
-- FIXED: Handle both old and new field names, store duration in seconds, fix streak calculation
CREATE OR REPLACE PROCEDURE update_puzzle_daily_stats(
    p_user_id VARCHAR(36),
    p_date DATE,
    p_event_data JSONB
)
LANGUAGE plpgsql AS $$
DECLARE
    v_was_correct BOOLEAN;
    v_time_taken_seconds INT;
    v_success_increment INT := 0;
    v_previous_streak INT := 0;
    v_new_streak INT;
BEGIN
    -- Extract puzzle data - handle both old and new field names
    -- Try new field names first, then fall back to old ones
    IF p_event_data ? 'was_correct' THEN
        v_was_correct := COALESCE((p_event_data->>'was_correct')::BOOLEAN, false);
    ELSE
        v_was_correct := COALESCE((p_event_data->>'solved')::BOOLEAN, false);
    END IF;

    IF p_event_data ? 'time_taken_ms' THEN
        -- Convert milliseconds to seconds
        v_time_taken_seconds := COALESCE((p_event_data->>'time_taken_ms')::INT, 0) / 1000;
    ELSE
        -- Already in seconds
        v_time_taken_seconds := COALESCE((p_event_data->>'time_spent')::INT, 0);
    END IF;
    
    -- Determine if puzzle was successful
    IF v_was_correct THEN
        v_success_increment := 1;
    END IF;

    -- Calculate new streak based on yesterday's activity (FIXED)
    IF v_was_correct THEN
        -- Check if user had successful activity yesterday
        SELECT COALESCE(streak, 0) INTO v_previous_streak
        FROM user_daily_stats
        WHERE user_id = p_user_id
          AND date = p_date - INTERVAL '1 day'
          AND puzzle_success > 0; -- Only count days with successful puzzles

        -- If we found yesterday's streak, increment it; otherwise start at 1
        v_new_streak := COALESCE(v_previous_streak, 0) + 1;
    ELSE
        -- Failed puzzle resets streak to 0
        v_new_streak := 0;
    END IF;
    
    -- Insert or update daily stats (store duration in seconds)
    INSERT INTO user_daily_stats (
        id, user_id, date, puzzle_success, puzzle_total, puzzle_total_duration, streak,
        created_at, updated_at
    ) VALUES (
        gen_random_uuid()::text, p_user_id, p_date, v_success_increment, 1, v_time_taken_seconds, v_new_streak,
        NOW(), NOW()
    )
    ON CONFLICT (user_id, date) DO UPDATE SET
        puzzle_success = user_daily_stats.puzzle_success + v_success_increment,
        puzzle_total = user_daily_stats.puzzle_total + 1,
        puzzle_total_duration = user_daily_stats.puzzle_total_duration + v_time_taken_seconds,
        -- Recalculate streak based on whether this day now has successful puzzles
        streak = CASE
            WHEN (user_daily_stats.puzzle_success + v_success_increment) > 0 THEN
                -- This day has successful puzzles, calculate streak
                COALESCE((
                    SELECT streak + 1
                    FROM user_daily_stats prev
                    WHERE prev.user_id = p_user_id
                      AND prev.date = p_date - INTERVAL '1 day'
                      AND prev.puzzle_success > 0
                ), 1)
            ELSE
                -- This day has no successful puzzles, streak is 0
                0
        END,
        updated_at = NOW();

    -- Also create/update individual puzzle stats (NEW FEATURE)
    -- Extract puzzle ID and other fields
    DECLARE
        v_puzzle_id VARCHAR(255);
        v_puzzle_type VARCHAR(20);
        v_is_disliked BOOLEAN := NULL;
    BEGIN
        v_puzzle_id := p_event_data->>'puzzle_id';
        v_puzzle_type := COALESCE(p_event_data->>'puzzle_type', 'user');

        -- Parse is_disliked field if present
        IF p_event_data ? 'is_disliked' AND p_event_data->>'is_disliked' IS NOT NULL THEN
            v_is_disliked := (p_event_data->>'is_disliked')::boolean;
        END IF;

        -- Only create puzzle stats for user-generated puzzles (not lichess puzzles)
        IF v_puzzle_type = 'user' AND v_puzzle_id IS NOT NULL THEN
            -- Insert or update puzzle stats (store time in seconds)
            INSERT INTO user_puzzle_stats (
                id, user_id, puzzle_id, attempts, success_count, total_time, average_time,
                last_attempt_time, last_attempt_success, is_disliked, disliked_at,
                created_at, updated_at
            ) VALUES (
                gen_random_uuid()::text, p_user_id, v_puzzle_id, 1,
                CASE WHEN v_was_correct THEN 1 ELSE 0 END,
                v_time_taken_seconds, -- Store in seconds
                v_time_taken_seconds::float,
                NOW(), v_was_correct,
                COALESCE(v_is_disliked, false),
                CASE WHEN v_is_disliked = true THEN NOW() ELSE NULL END,
                NOW(), NOW()
            )
            ON CONFLICT (user_id, puzzle_id) DO UPDATE SET
                attempts = user_puzzle_stats.attempts + 1,
                success_count = user_puzzle_stats.success_count + CASE WHEN v_was_correct THEN 1 ELSE 0 END,
                total_time = user_puzzle_stats.total_time + v_time_taken_seconds,
                average_time = (user_puzzle_stats.total_time + v_time_taken_seconds)::float / (user_puzzle_stats.attempts + 1),
                last_attempt_time = NOW(),
                last_attempt_success = v_was_correct,
                is_disliked = COALESCE(v_is_disliked, user_puzzle_stats.is_disliked),
                disliked_at = CASE
                    WHEN v_is_disliked = true AND user_puzzle_stats.is_disliked = false THEN NOW()
                    WHEN v_is_disliked = false THEN NULL
                    ELSE user_puzzle_stats.disliked_at
                END,
                updated_at = NOW();
        END IF;
    END;
    
    -- Update quest completion status
    CALL update_quest_completion(p_user_id, p_date);
END;
$$;