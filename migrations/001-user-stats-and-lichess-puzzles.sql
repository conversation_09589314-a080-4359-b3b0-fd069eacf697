-- 001-user-stats-and-lichess-puzzles.sql
-- User statistics functions, triggers, and Lichess puzzles support

-- Function to update daily puzzle stats with streak calculation
CREATE OR REPLACE FUNCTION update_daily_puzzle_stats(user_id_param VARCHAR(36), puzzle_date DATE, solved BOOLEAN, time_spent INTEGER)
RETURNS VOID AS $$
DECLARE
    current_success INTEGER := 0;
    current_total INTEGER := 0;
    current_duration INTEGER := 0;
    new_success INTEGER;
    new_total INTEGER;
    new_duration INTEGER;
    yesterday_date DATE := puzzle_date - INTERVAL '1 day';
    yesterday_had_activity BOOLEAN := FALSE;
    current_streak INTEGER := 0;
    new_streak INTEGER;
BEGIN
    -- Get current stats for this date
    SELECT puzzle_success, puzzle_total, puzzle_total_duration
    INTO current_success, current_total, current_duration
    FROM user_daily_stats
    WHERE user_id = user_id_param AND date = puzzle_date;

    -- If no existing record found, initialize to 0
    IF current_success IS NULL THEN
        current_success := 0;
        current_total := 0;
        current_duration := 0;
    END IF;

    -- Calculate new values
    new_success := current_success + (CASE WHEN solved THEN 1 ELSE 0 END);
    new_total := current_total + 1;
    new_duration := current_duration + time_spent;

    -- Check if yesterday had activity for streak calculation
    SELECT COUNT(*) > 0 INTO yesterday_had_activity
    FROM user_daily_stats
    WHERE user_id = user_id_param AND date = yesterday_date AND puzzle_total > 0;

    -- Get current streak (from yesterday if it exists, otherwise 0)
    IF yesterday_had_activity THEN
        SELECT streak INTO current_streak
        FROM user_daily_stats
        WHERE user_id = user_id_param AND date = yesterday_date;
        
        IF current_streak IS NULL THEN
            current_streak := 0;
        END IF;
    ELSE
        current_streak := 0;
    END IF;

    -- Calculate new streak (increment if yesterday had activity, otherwise start at 1)
    new_streak := current_streak + 1;

    -- Insert or update daily stats
    INSERT INTO user_daily_stats (
        id, user_id, date, puzzle_success, puzzle_total, streak, puzzle_total_duration, created_at, updated_at
    ) VALUES (
        gen_random_uuid()::VARCHAR(36), user_id_param, puzzle_date,
        new_success, new_total, new_streak, new_duration, NOW(), NOW()
    )
    ON CONFLICT (user_id, date) DO UPDATE SET
        puzzle_success = new_success,
        puzzle_total = new_total,
        streak = new_streak,
        puzzle_total_duration = new_duration,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- Function to update per-puzzle stats for user-generated puzzles with IsDisliked support
CREATE OR REPLACE FUNCTION update_user_puzzle_stats(user_id_param VARCHAR(36), puzzle_id_param VARCHAR(255), solved BOOLEAN, time_spent INTEGER, attempt_time TIMESTAMPTZ, is_disliked_param BOOLEAN DEFAULT NULL)
RETURNS VOID AS $$
DECLARE
    current_attempts INTEGER := 0;
    current_success_count INTEGER := 0;
    current_total_time INTEGER := 0;
    new_success_count INTEGER;
    new_total_time INTEGER;
    new_average_time FLOAT;
    disliked_time TIMESTAMPTZ := NULL;
BEGIN
    -- Get current stats for this user-puzzle combination
    SELECT attempts, success_count, total_time
    INTO current_attempts, current_success_count, current_total_time
    FROM user_puzzle_stats
    WHERE user_id = user_id_param AND puzzle_id = puzzle_id_param;

    -- If no existing record found, initialize to 0
    IF current_attempts IS NULL THEN
        current_attempts := 0;
        current_success_count := 0;
        current_total_time := 0;
    END IF;

    -- Calculate new values
    current_attempts := current_attempts + 1;
    new_success_count := current_success_count + (CASE WHEN solved THEN 1 ELSE 0 END);
    new_total_time := current_total_time + time_spent;
    new_average_time := new_total_time::FLOAT / current_attempts::FLOAT;

    -- Set disliked timestamp if is_disliked is true
    IF is_disliked_param = TRUE THEN
        disliked_time := attempt_time;
    END IF;

    -- Insert or update puzzle stats
    INSERT INTO user_puzzle_stats (
        id, user_id, puzzle_id, attempts, success_count, total_time, average_time,
        last_attempt_time, last_attempt_success, is_disliked, disliked_at, created_at, updated_at
    ) VALUES (
        gen_random_uuid()::VARCHAR(36), user_id_param, puzzle_id_param,
        current_attempts, new_success_count, new_total_time, new_average_time,
        attempt_time, solved, COALESCE(is_disliked_param, FALSE), disliked_time, NOW(), NOW()
    )
    ON CONFLICT (user_id, puzzle_id) DO UPDATE SET
        attempts = current_attempts,
        success_count = new_success_count,
        total_time = new_total_time,
        average_time = new_average_time,
        last_attempt_time = attempt_time,
        last_attempt_success = solved,
        is_disliked = CASE WHEN is_disliked_param IS NOT NULL THEN is_disliked_param ELSE user_puzzle_stats.is_disliked END,
        disliked_at = CASE WHEN is_disliked_param = TRUE THEN disliked_time WHEN is_disliked_param = FALSE THEN NULL ELSE user_puzzle_stats.disliked_at END,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- Function to update per-lichess-puzzle stats with IsDisliked support
CREATE OR REPLACE FUNCTION update_user_lichess_puzzle_stats(user_id_param VARCHAR(36), lichess_puzzle_id_param VARCHAR(10), solved BOOLEAN, time_spent INTEGER, attempt_time TIMESTAMPTZ, is_disliked_param BOOLEAN DEFAULT NULL)
RETURNS VOID AS $$
DECLARE
    current_attempts INTEGER := 0;
    current_success_count INTEGER := 0;
    current_total_time INTEGER := 0;
    new_success_count INTEGER;
    new_total_time INTEGER;
    new_average_time FLOAT;
    disliked_time TIMESTAMPTZ := NULL;
BEGIN
    -- Get current stats for this user-lichess-puzzle combination
    SELECT attempts, success_count, total_time
    INTO current_attempts, current_success_count, current_total_time
    FROM user_lichess_puzzle_stats
    WHERE user_id = user_id_param AND lichess_puzzle_id = lichess_puzzle_id_param;

    -- If no existing record found, initialize to 0
    IF current_attempts IS NULL THEN
        current_attempts := 0;
        current_success_count := 0;
        current_total_time := 0;
    END IF;

    -- Calculate new values
    current_attempts := current_attempts + 1;
    new_success_count := current_success_count + (CASE WHEN solved THEN 1 ELSE 0 END);
    new_total_time := current_total_time + time_spent;
    new_average_time := new_total_time::FLOAT / current_attempts::FLOAT;

    -- Set disliked timestamp if is_disliked is true
    IF is_disliked_param = TRUE THEN
        disliked_time := attempt_time;
    END IF;

    -- Insert or update lichess puzzle stats
    INSERT INTO user_lichess_puzzle_stats (
        id, user_id, lichess_puzzle_id, attempts, success_count, total_time, average_time,
        last_attempt_time, last_attempt_success, is_disliked, disliked_at, created_at, updated_at
    ) VALUES (
        gen_random_uuid()::VARCHAR(36), user_id_param, lichess_puzzle_id_param,
        current_attempts, new_success_count, new_total_time, new_average_time,
        attempt_time, solved, COALESCE(is_disliked_param, FALSE), disliked_time, NOW(), NOW()
    )
    ON CONFLICT (user_id, lichess_puzzle_id) DO UPDATE SET
        attempts = current_attempts,
        success_count = new_success_count,
        total_time = new_total_time,
        average_time = new_average_time,
        last_attempt_time = attempt_time,
        last_attempt_success = solved,
        is_disliked = CASE WHEN is_disliked_param IS NOT NULL THEN is_disliked_param ELSE user_lichess_puzzle_stats.is_disliked END,
        disliked_at = CASE WHEN is_disliked_param = TRUE THEN disliked_time WHEN is_disliked_param = FALSE THEN NULL ELSE user_lichess_puzzle_stats.disliked_at END,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- Trigger function for daily stats updates with IsDisliked support
CREATE OR REPLACE FUNCTION trigger_update_daily_stats()
RETURNS TRIGGER AS $$
BEGIN
    -- Handle puzzle events only
    IF NEW.event_type = 'puzzle' THEN
        DECLARE
            solved BOOLEAN := (NEW.event_data->>'solved')::boolean;
            puzzle_date DATE := DATE(NEW.event_time);
            puzzle_id_val VARCHAR(255) := NEW.event_data->>'puzzle_id';
            puzzle_type_val VARCHAR(20) := COALESCE(NEW.event_data->>'puzzle_type', 'user');
            time_spent INTEGER := COALESCE((NEW.event_data->>'time_spent')::integer, 0);
            is_disliked_val BOOLEAN := NULL;
        BEGIN
            -- Parse is_disliked field if present (only set when not NULL)
            IF NEW.event_data ? 'is_disliked' AND NEW.event_data->>'is_disliked' IS NOT NULL THEN
                is_disliked_val := (NEW.event_data->>'is_disliked')::boolean;
            END IF;

            -- Update daily stats with streak calculation
            PERFORM update_daily_puzzle_stats(NEW.user_id, puzzle_date, solved, time_spent);

            -- Update per-puzzle stats only if puzzle_id is not null
            IF puzzle_id_val IS NOT NULL THEN
                IF puzzle_type_val = 'lichess' THEN
                    -- Update lichess puzzle stats
                    PERFORM update_user_lichess_puzzle_stats(
                        NEW.user_id,
                        puzzle_id_val,
                        solved,
                        time_spent,
                        NEW.event_time,
                        is_disliked_val
                    );
                ELSE
                    -- Update user puzzle stats
                    PERFORM update_user_puzzle_stats(
                        NEW.user_id,
                        puzzle_id_val,
                        solved,
                        time_spent,
                        NEW.event_time,
                        is_disliked_val
                    );
                END IF;
            END IF;
        END;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
CREATE OR REPLACE TRIGGER update_daily_stats_trigger
    AFTER INSERT ON events
    FOR EACH ROW
    EXECUTE FUNCTION trigger_update_daily_stats();
