package repository

import (
	"context"

	"github.com/chessticize/chessticize-server/internal/models"
	"gorm.io/gorm"
)

type UserPuzzleArrowDuelStatsRepository struct {
	db *gorm.DB
}

func NewUserPuzzleArrowDuelStatsRepository(db *gorm.DB) IUserPuzzleArrowDuelStatsRepository {
	return &UserPuzzleArrowDuelStatsRepository{db: db}
}

// GetByUserID retrieves all arrow-duel puzzle stats for a specific user
func (r *UserPuzzleArrowDuelStatsRepository) GetByUserID(ctx context.Context, userID string) ([]models.UserPuzzleArrowDuelStats, error) {
	var stats []models.UserPuzzleArrowDuelStats
	err := r.db.WithContext(ctx).Where("user_id = ?", userID).Order("last_attempt_time DESC").Find(&stats).Error
	return stats, err
}

// GetByUserIDAndPuzzleID retrieves arrow-duel stats for a specific user and puzzle
func (r *UserPuzzleArrowDuelStatsRepository) GetByUserIDAndPuzzleID(ctx context.Context, userID, puzzleID string) (*models.UserPuzzleArrowDuelStats, error) {
	var stats models.UserPuzzleArrowDuelStats
	err := r.db.WithContext(ctx).Where("user_id = ? AND puzzle_id = ?", userID, puzzleID).First(&stats).Error
	if err != nil {
		return nil, err
	}
	return &stats, nil
}

// GetDislikedPuzzleIDs retrieves all disliked puzzle IDs for a specific user (arrow-duel mode)
func (r *UserPuzzleArrowDuelStatsRepository) GetDislikedPuzzleIDs(ctx context.Context, userID string) ([]string, error) {
	var puzzleIDs []string
	err := r.db.WithContext(ctx).
		Model(&models.UserPuzzleArrowDuelStats{}).
		Where("user_id = ? AND is_disliked = ?", userID, true).
		Pluck("puzzle_id", &puzzleIDs).Error
	return puzzleIDs, err
}
