# Common Repository Utilities

## Package Overview
This package provides shared utilities and common code for both the real and fake repository implementations. It helps reduce code duplication and ensures consistent behavior across different repository implementations.

## Contents

### Time Grouping
- `TimeGrouping` struct and related constants for grouping data by time periods (day, week, month)
- Time period calculation utilities for consistent date handling

### Filter Handling
- Common filter types and utilities for applying filters to database queries
- Support for both PostgreSQL and SQLite database dialects

### Statistics Calculation
- Shared functions for calculating statistics on puzzles and games
- Consistent implementation of statistics aggregation logic

## Usage

### Time Grouping
```go
// Create a time grouping for daily periods
grouping := &common.TimeGrouping{
    Unit:   common.TimeGroupingDay,
    Length: 1,
}

// Generate time groups for a given range
startTime := time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)
endTime := time.Date(2023, 1, 7, 23, 59, 59, 999999999, time.UTC)
timeGroups, err := common.GenerateTimeGroups(startTime, endTime, grouping)
```

### Filter Handling
```go
// Apply filters to a database query
db := gorm.DB{}
filter := common.PuzzleFilter{
    // Set filter criteria
}
db = common.ApplyPuzzleFilters(db, filter, true) // true for PostgreSQL
```

### Statistics Calculation
```go
// Calculate statistics for a set of puzzles
puzzles := []models.Puzzle{...}
stats := &models.PuzzleStats{}
common.CalculatePuzzleStats(puzzles, stats)
```

## Benefits
- **Consistency**: Ensures the same behavior across real and fake repositories
- **Maintainability**: Single source of truth for shared functionality
- **Testability**: Common code can be tested once and relied upon by all implementations
- **Reduced Duplication**: Eliminates duplicate code between repository implementations

## Design Principles
- Keep utility functions focused on specific tasks
- Maintain clear separation between database-specific and general logic
- Ensure compatibility with both PostgreSQL and SQLite where needed
- Provide comprehensive documentation for all shared functionality
