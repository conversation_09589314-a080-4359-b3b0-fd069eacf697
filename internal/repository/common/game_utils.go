package common

import (
	"encoding/json"
	"fmt"
	"sort"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"gorm.io/gorm"
)

// ApplyGameFilters applies the given filters to the game query
func ApplyGameFilters(db *gorm.DB, filter GameFilter) *gorm.DB {
	// Apply filters
	if filter.Platform != nil {
		db = db.Where("platform = ?", *filter.Platform)
	}
	if filter.ChessUsername != nil {
		db = db.Where("chess_username = ?", *filter.ChessUsername)
	}
	if filter.StartTime != nil {
		db = db.Where("game_time >= ?", *filter.StartTime)
	}
	if filter.EndTime != nil {
		db = db.Where("game_time <= ?", *filter.EndTime)
	}
	if filter.UserColor != nil {
		db = db.Where("user_color = ?", *filter.UserColor)
	}
	if filter.TimeControl != nil {
		db = db.Where("time_control = ?", *filter.TimeControl)
	}
	if filter.Rated != nil {
		db = db.Where("rated = ?", *filter.Rated)
	}
	if filter.Result != nil {
		db = db.Where("result = ?", *filter.Result)
	}

	return db
}

// CalculateGameStats calculates statistics for a set of games and populates the stats object
func CalculateGameStats(games []models.Game, stats *models.GameStats) {
	// Maps to track counts
	platformCounts := make(map[models.ChessPlatform]int)
	userColorCounts := make(map[models.Color]int)
	resultCounts := make(map[models.GameResult]int)
	timeControlCounts := make(map[string]int)
	ratedCounts := make(map[bool]int)

	// Variables for average opponent rating
	var totalRating int
	var ratedOpponentCount int

	// Process each game
	for _, game := range games {
		// Count by platform
		platformCounts[game.Platform]++

		// Count by user color
		userColorCounts[game.UserColor]++

		// Count by result
		resultCounts[game.Result]++

		// Count by time control
		timeControlCounts[game.TimeControl]++

		// Count by rated status
		ratedCounts[game.Rated]++

		// Calculate opponent rating
		var whitePlayer, blackPlayer models.PlayerInfo
		var opponentPlayer *models.PlayerInfo

		// Parse player info
		err := json.Unmarshal([]byte(game.WhitePlayer), &whitePlayer)
		if err != nil {
			continue
		}
		err = json.Unmarshal([]byte(game.BlackPlayer), &blackPlayer)
		if err != nil {
			continue
		}

		// Determine opponent player
		if game.UserColor == models.White {
			opponentPlayer = &blackPlayer
		} else {
			opponentPlayer = &whitePlayer
		}

		// Skip AI opponents
		if opponentPlayer.IsAI {
			continue
		}

		// Skip opponents without rating
		if opponentPlayer.Rating == nil {
			continue
		}

		totalRating += *opponentPlayer.Rating
		ratedOpponentCount++
	}

	// Convert maps to slices for the stats struct
	stats.PlatformCounts = make([]models.PlatformCount, 0, len(platformCounts))
	for platform, count := range platformCounts {
		stats.PlatformCounts = append(stats.PlatformCounts, models.PlatformCount{
			Platform: platform,
			Count:    count,
		})
	}

	stats.UserColorCounts = make([]models.ColorCount, 0, len(userColorCounts))
	for color, count := range userColorCounts {
		stats.UserColorCounts = append(stats.UserColorCounts, models.ColorCount{
			Color: color,
			Count: count,
		})
	}

	stats.ResultCounts = make([]models.ResultCount, 0, len(resultCounts))
	for result, count := range resultCounts {
		stats.ResultCounts = append(stats.ResultCounts, models.ResultCount{
			Result: result,
			Count:  count,
		})
	}

	stats.TimeControlCounts = make([]models.TimeControlCount, 0, len(timeControlCounts))
	for timeControl, count := range timeControlCounts {
		stats.TimeControlCounts = append(stats.TimeControlCounts, models.TimeControlCount{
			TimeControl: timeControl,
			Count:       count,
		})
	}

	stats.RatedCounts = make([]models.RatedCount, 0, len(ratedCounts))
	for rated, count := range ratedCounts {
		stats.RatedCounts = append(stats.RatedCounts, models.RatedCount{
			Rated: rated,
			Count: count,
		})
	}

	// Calculate average rating
	if ratedOpponentCount > 0 {
		stats.AverageOpponentRating = float64(totalRating) / float64(ratedOpponentCount)
	}
}

func CalculateGroupedGameStats(games []models.Game, grouping *TimeGrouping) ([]*models.GameStats, error) {
	// Validate grouping parameters
	if !grouping.IsValid() {
		return nil, fmt.Errorf("invalid grouping parameters")
	}

	// Filter out games with invalid game times
	validGames := make([]models.Game, 0, len(games))
	for _, game := range games {
		// Check if GameTime is not zero and is reasonable
		if !game.GameTime.IsZero() {
			validGames = append(validGames, game)
		}
	}
	games = validGames

	// If there are no games, return empty results
	if len(games) == 0 {
		return []*models.GameStats{}, nil
	}

	// Sort games by game time
	sort.Slice(games, func(i, j int) bool {
		return games[i].GameTime.Before(games[j].GameTime)
	})

	// Get min/max times from first/last game (since they're sorted)
	minTime := games[0].GameTime
	maxTime := games[len(games)-1].GameTime

	// Do NOT round start time, since we are counting from endTime backwards
	startTime := minTime

	// Round end time up to the beginning of the next day (00:00:00)
	endTime := time.Date(maxTime.Year(), maxTime.Month(), maxTime.Day()+1, 0, 0, 0, 0, maxTime.Location())

	// Generate time groups using the new function
	timeGroups, err := GenerateTimeGroups(startTime, endTime, grouping)
	if err != nil {
		return nil, fmt.Errorf("failed to generate time groups: %w", err)
	}

	// Initialize result structures
	groupedStats := make([]*models.GameStats, len(timeGroups))

	// Calculate stats for each group
	gameIndex := 0
	for i, group := range timeGroups {
		// Filter games for this group
		groupGames := make([]models.Game, 0)
		for ; gameIndex < len(games) && !games[gameIndex].GameTime.After(group.EndTime); gameIndex++ {
			groupGames = append(groupGames, games[gameIndex])
		}

		// Calculate stats for this group
		stats := &models.GameStats{
			TotalCount:  int64(len(groupGames)),
			PeriodStart: &group.StartTime,
			PeriodEnd:   &group.EndTime,
		}
		CalculateGameStats(groupGames, stats)
		groupedStats[i] = stats
	}

	return groupedStats, nil
}
