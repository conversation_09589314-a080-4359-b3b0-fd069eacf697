package common

import (
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"gorm.io/gorm"
)

// ApplyPuzzleFilters applies the given filters to the puzzle query
// The isPostgres parameter determines whether to use PostgreSQL-specific syntax
func ApplyPuzzleFilters(db *gorm.DB, filter PuzzleFilter, isPostgres bool) *gorm.DB {
	// Apply tag filters
	if len(filter.Tags) > 0 {
		if isPostgres {
			// PostgreSQL supports array operators
			var tagConditions []string
			var tagValues []interface{}
			for _, tag := range filter.Tags {
				tagConditions = append(tagConditions, "? = ANY(puzzles.tags)")
				tagValues = append(tagValues, tag)
			}
			db = db.Where(strings.Join(tagConditions, " OR "), tagValues...)
		}
		// SQLite needs a different approach (handled by the caller)
	}

	// Apply game time filters
	if filter.GameStartTime != nil {
		db = db.Where("games.game_time >= ?", *filter.GameStartTime)
	}
	if filter.GameEndTime != nil {
		db = db.Where("games.game_time <= ?", *filter.GameEndTime)
	}

	// Apply theme filters (OR relation)
	if len(filter.Themes) > 0 {
		if isPostgres {
			// PostgreSQL supports IN operator with arrays
			db = db.Where("puzzles.theme IN ?", filter.Themes)
		} else {
			// SQLite fallback
			var themeConditions []string
			var themeValues []interface{}
			for _, theme := range filter.Themes {
				themeConditions = append(themeConditions, "puzzles.theme = ?")
				themeValues = append(themeValues, theme)
			}
			db = db.Where(strings.Join(themeConditions, " OR "), themeValues...)
		}
	}

	// Apply color filters
	if filter.UserColor != nil {
		db = db.Where("puzzles.user_color = ?", *filter.UserColor)
	}
	if filter.PuzzleColor != nil {
		db = db.Where("puzzles.puzzle_color = ?", *filter.PuzzleColor)
	}

	// Apply game move range filters
	if filter.GameMoveMin != nil {
		db = db.Where("puzzles.game_move >= ?", *filter.GameMoveMin)
	}
	if filter.GameMoveMax != nil {
		db = db.Where("puzzles.game_move <= ?", *filter.GameMoveMax)
	}

	// Apply CP range filters
	if filter.PrevCpMin != nil {
		db = db.Where("puzzles.prev_cp >= ?", *filter.PrevCpMin)
	}
	if filter.PrevCpMax != nil {
		db = db.Where("puzzles.prev_cp <= ?", *filter.PrevCpMax)
	}

	// Apply CP change range filters
	if filter.CpChangeMin != nil {
		db = db.Where("(puzzles.cp - puzzles.prev_cp) >= ?", *filter.CpChangeMin)
	}
	if filter.CpChangeMax != nil {
		db = db.Where("(puzzles.cp - puzzles.prev_cp) <= ?", *filter.CpChangeMax)
	}

	// Apply game-related filters
	if filter.TimeControl != nil {
		db = db.Where("games.time_control = ?", *filter.TimeControl)
	}
	if filter.Rated != nil {
		db = db.Where("games.rated = ?", *filter.Rated)
	}

	return db
}

// CalculatePuzzleStats calculates statistics for a set of puzzles and populates the stats object
func CalculatePuzzleStats(puzzles []models.Puzzle, stats *models.PuzzleStats) {
	// Maps to count occurrences
	tagCounts := make(map[string]int)
	themeCounts := make(map[models.PuzzleTheme]int)
	userColorCounts := make(map[models.Color]int)
	uniqueGameIDs := make(map[string]bool)
	gameMoveBuckets := map[string]*models.GameMoveBucket{
		"Early opening": {
			Name:    "Early opening",
			MinMove: 1,
			MaxMove: 5,
			Count:   0,
		},
		"Late opening": {
			Name:    "Late opening",
			MinMove: 6,
			MaxMove: 12,
			Count:   0,
		},
		"Early middlegame": {
			Name:    "Early middlegame",
			MinMove: 13,
			MaxMove: 20,
			Count:   0,
		},
		"Late middlegame": {
			Name:    "Late middlegame",
			MinMove: 21,
			MaxMove: 30,
			Count:   0,
		},
		"Endgame": {
			Name:    "Endgame",
			MinMove: 31,
			MaxMove: 999, // Large number to include all moves beyond 31
			Count:   0,
		},
	}
	moveLengthCounts := make(map[int]int)
	totalMoveLength := 0

	// Process each puzzle
	for _, puzzle := range puzzles {
		// Count unique games
		uniqueGameIDs[puzzle.GameID] = true

		// Count tags
		for _, tag := range puzzle.Tags {
			tagCounts[tag]++
		}

		// Count themes
		themeCounts[puzzle.Theme]++

		// Count user colors
		userColorCounts[puzzle.UserColor]++

		// Count game move buckets
		gameMove := puzzle.GameMove
		if gameMove >= 1 && gameMove <= 5 {
			gameMoveBuckets["Early opening"].Count++
		} else if gameMove >= 6 && gameMove <= 12 {
			gameMoveBuckets["Late opening"].Count++
		} else if gameMove >= 13 && gameMove <= 20 {
			gameMoveBuckets["Early middlegame"].Count++
		} else if gameMove >= 21 && gameMove <= 30 {
			gameMoveBuckets["Late middlegame"].Count++
		} else if gameMove >= 31 {
			gameMoveBuckets["Endgame"].Count++
		}

		// Count move lengths and accumulate total for average calculation
		moveLength := len(puzzle.Moves)
		moveLengthCounts[moveLength]++
		totalMoveLength += moveLength
	}

	// Set unique game count
	stats.UniqueGameCount = int64(len(uniqueGameIDs))

	// Calculate average move length
	if len(puzzles) > 0 {
		stats.AverageMoveLength = float64(totalMoveLength) / float64(len(puzzles))
	} else {
		stats.AverageMoveLength = 0.0
	}

	// Convert tag counts to slice and sort
	stats.TagCounts = make([]models.TagCount, 0, len(tagCounts))
	for tag, count := range tagCounts {
		stats.TagCounts = append(stats.TagCounts, models.TagCount{
			Tag:   tag,
			Count: count,
		})
	}
	sort.Slice(stats.TagCounts, func(i, j int) bool {
		return stats.TagCounts[i].Count > stats.TagCounts[j].Count
	})

	// Convert theme counts to slice and sort
	stats.ThemeCounts = make([]models.ThemeCount, 0, len(themeCounts))
	for theme, count := range themeCounts {
		stats.ThemeCounts = append(stats.ThemeCounts, models.ThemeCount{
			Theme: theme,
			Count: count,
		})
	}
	sort.Slice(stats.ThemeCounts, func(i, j int) bool {
		return stats.ThemeCounts[i].Count > stats.ThemeCounts[j].Count
	})

	// Convert user color counts to slice
	stats.UserColorCounts = make([]models.ColorCount, 0, len(userColorCounts))
	for color, count := range userColorCounts {
		stats.UserColorCounts = append(stats.UserColorCounts, models.ColorCount{
			Color: color,
			Count: count,
		})
	}

	// Convert game move buckets to slice
	stats.GameMoveBuckets = make([]models.GameMoveBucket, 0, len(gameMoveBuckets))
	for _, bucket := range gameMoveBuckets {
		stats.GameMoveBuckets = append(stats.GameMoveBuckets, *bucket)
	}
	// Sort by min_move to ensure consistent order
	sort.Slice(stats.GameMoveBuckets, func(i, j int) bool {
		return stats.GameMoveBuckets[i].MinMove < stats.GameMoveBuckets[j].MinMove
	})

	// Convert move length counts to slice and sort
	stats.MoveLengthCounts = make([]models.MoveLengthBucket, 0, len(moveLengthCounts))
	for length, count := range moveLengthCounts {
		stats.MoveLengthCounts = append(stats.MoveLengthCounts, models.MoveLengthBucket{
			Length: length,
			Count:  count,
		})
	}
	sort.Slice(stats.MoveLengthCounts, func(i, j int) bool {
		return stats.MoveLengthCounts[i].Length < stats.MoveLengthCounts[j].Length
	})
}

// CalculateGroupedPuzzleStats calculates grouped puzzle statistics using a simpler single-pass approach
func CalculateGroupedPuzzleStats(puzzles []models.Puzzle, grouping *TimeGrouping) ([]*models.PuzzleStats, error) {
	// Validate grouping parameters
	if !grouping.IsValid() {
		return nil, fmt.Errorf("invalid grouping parameters")
	}

	// If there are no puzzles, return empty results
	if len(puzzles) == 0 {
		return []*models.PuzzleStats{}, nil
	}

	// Filter out puzzles with no game time
	validPuzzles := make([]models.Puzzle, 0, len(puzzles))
	for _, puzzle := range puzzles {
		if puzzle.Game.ID != "" {
			validPuzzles = append(validPuzzles, puzzle)
		}
	}

	// If no valid puzzles, return empty results
	if len(validPuzzles) == 0 {
		return []*models.PuzzleStats{}, nil
	}

	// Sort puzzles by game time
	sort.Slice(validPuzzles, func(i, j int) bool {
		return validPuzzles[i].Game.GameTime.Before(validPuzzles[j].Game.GameTime)
	})

	// Get min/max times from first/last puzzle (since they're sorted)
	minTime := validPuzzles[0].Game.GameTime
	maxTime := validPuzzles[len(validPuzzles)-1].Game.GameTime

	// Do NOT round start time, since we are counting from endTime backwards
	startTime := minTime

	// Round end time up to the beginning of the next day (00:00:00)
	endTime := time.Date(maxTime.Year(), maxTime.Month(), maxTime.Day()+1, 0, 0, 0, 0, maxTime.Location())

	// Generate time groups using the new function
	timeGroups, err := GenerateTimeGroups(startTime, endTime, grouping)
	if err != nil {
		return nil, fmt.Errorf("failed to generate time groups: %w", err)
	}

	// Initialize result structures
	groupedStats := make([]*models.PuzzleStats, len(timeGroups))

	// Calculate stats for each group
	puzzleIndex := 0
	for i, group := range timeGroups {
		// Filter puzzles for this group
		groupPuzzles := make([]models.Puzzle, 0)
		for ; puzzleIndex < len(validPuzzles) && !validPuzzles[puzzleIndex].Game.GameTime.After(group.EndTime); puzzleIndex++ {
			groupPuzzles = append(groupPuzzles, validPuzzles[puzzleIndex])
		}

		// Calculate stats for this group
		stats := &models.PuzzleStats{
			TotalCount:  int64(len(groupPuzzles)),
			PeriodStart: &group.StartTime,
			PeriodEnd:   &group.EndTime,
		}
		CalculatePuzzleStats(groupPuzzles, stats)
		groupedStats[i] = stats
	}

	return groupedStats, nil
}
