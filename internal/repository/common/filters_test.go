package common

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestTimeGrouping_IsValid(t *testing.T) {
	tests := []struct {
		name     string
		grouping *TimeGrouping
		want     bool
	}{
		{
			name:     "nil grouping",
			grouping: nil,
			want:     false,
		},
		{
			name: "valid day grouping",
			grouping: &TimeGrouping{
				Unit:   TimeGroupingDay,
				Length: 1,
			},
			want: true,
		},
		{
			name: "valid week grouping",
			grouping: &TimeGrouping{
				Unit:   TimeGroupingWeek,
				Length: 2,
			},
			want: true,
		},
		{
			name: "valid month grouping",
			grouping: &TimeGrouping{
				Unit:   TimeGroupingMonth,
				Length: 3,
			},
			want: true,
		},
		{
			name: "invalid unit",
			grouping: &TimeGrouping{
				Unit:   "INVALID",
				Length: 1,
			},
			want: false,
		},
		{
			name: "invalid length (zero)",
			grouping: &TimeGrouping{
				Unit:   TimeGroupingDay,
				Length: 0,
			},
			want: false,
		},
		{
			name: "invalid length (negative)",
			grouping: &TimeGrouping{
				Unit:   TimeGroupingDay,
				Length: -1,
			},
			want: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.grouping.IsValid()
			assert.Equal(t, tt.want, got)
		})
	}
}

func TestAddMonthsWithDayAdjustment(t *testing.T) {
	tests := []struct {
		name        string
		originalDay int
		start       time.Time
		months      int
		expected    time.Time
	}{
		{
			name:        "normal month addition",
			originalDay: 15,
			start:       time.Date(2025, 1, 15, 0, 0, 0, 0, time.UTC),
			months:      1,
			expected:    time.Date(2025, 2, 15, 0, 0, 0, 0, time.UTC),
		},
		{
			name:        "Jan 29 to Feb (should become Feb 28)",
			originalDay: 29,
			start:       time.Date(2025, 1, 29, 0, 0, 0, 0, time.UTC),
			months:      1,
			expected:    time.Date(2025, 2, 28, 0, 0, 0, 0, time.UTC),
		},
		{
			name:        "Jan 31 to Feb (should become Feb 28)",
			originalDay: 31,
			start:       time.Date(2025, 1, 31, 0, 0, 0, 0, time.UTC),
			months:      1,
			expected:    time.Date(2025, 2, 28, 0, 0, 0, 0, time.UTC),
		},
		{
			name:        "Feb 28 to Mar 30 (original day 30)",
			originalDay: 30,
			start:       time.Date(2025, 2, 28, 0, 0, 0, 0, time.UTC),
			months:      1,
			expected:    time.Date(2025, 3, 30, 0, 0, 0, 0, time.UTC),
		},
		{
			name:        "Feb 28 to Mar 31 (original day 30)",
			originalDay: 31,
			start:       time.Date(2025, 2, 28, 0, 0, 0, 0, time.UTC),
			months:      1,
			expected:    time.Date(2025, 3, 31, 0, 0, 0, 0, time.UTC),
		},
		{
			name:        "backwards: Feb 28 to Jan (original day 28)",
			originalDay: 28,
			start:       time.Date(2025, 2, 28, 0, 0, 0, 0, time.UTC),
			months:      -1,
			expected:    time.Date(2025, 1, 28, 0, 0, 0, 0, time.UTC),
		},
		{
			name:        "backwards: Feb 28 to Jan (original day 31)",
			originalDay: 31,
			start:       time.Date(2025, 2, 28, 0, 0, 0, 0, time.UTC),
			months:      -1,
			expected:    time.Date(2025, 1, 31, 0, 0, 0, 0, time.UTC),
		},
		{
			name:        "leap year: Jan 29 to Feb (should become Feb 29)",
			originalDay: 29,
			start:       time.Date(2024, 1, 29, 0, 0, 0, 0, time.UTC),
			months:      1,
			expected:    time.Date(2024, 2, 29, 0, 0, 0, 0, time.UTC),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := addMonthsWithDayAdjustment(tt.originalDay, tt.start, tt.months)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestGenerateTimeGroups(t *testing.T) {
	tests := []struct {
		name      string
		startTime time.Time
		endTime   time.Time
		grouping  *TimeGrouping
		expected  []TimeGroup
		wantErr   bool
	}{
		{
			name:      "daily grouping - 3 days",
			startTime: time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC),
			endTime:   time.Date(2025, 1, 4, 0, 0, 0, 0, time.UTC),
			grouping: &TimeGrouping{
				Unit:   TimeGroupingDay,
				Length: 1,
			},
			expected: []TimeGroup{
				{
					StartTime: time.Date(2024, 12, 31, 0, 0, 0, 0, time.UTC),
					EndTime:   time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC),
				},
				{
					StartTime: time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC),
					EndTime:   time.Date(2025, 1, 2, 0, 0, 0, 0, time.UTC),
				},
				{
					StartTime: time.Date(2025, 1, 2, 0, 0, 0, 0, time.UTC),
					EndTime:   time.Date(2025, 1, 3, 0, 0, 0, 0, time.UTC),
				},
				{
					StartTime: time.Date(2025, 1, 3, 0, 0, 0, 0, time.UTC),
					EndTime:   time.Date(2025, 1, 4, 0, 0, 0, 0, time.UTC),
				},
			},
			wantErr: false,
		},
		{
			name:      "weekly grouping - 2 weeks",
			startTime: time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC),
			endTime:   time.Date(2025, 1, 15, 0, 0, 0, 0, time.UTC),
			grouping: &TimeGrouping{
				Unit:   TimeGroupingWeek,
				Length: 1,
			},
			expected: []TimeGroup{
				{
					StartTime: time.Date(2024, 12, 25, 0, 0, 0, 0, time.UTC),
					EndTime:   time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC),
				},
				{
					StartTime: time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC),
					EndTime:   time.Date(2025, 1, 8, 0, 0, 0, 0, time.UTC),
				},
				{
					StartTime: time.Date(2025, 1, 8, 0, 0, 0, 0, time.UTC),
					EndTime:   time.Date(2025, 1, 15, 0, 0, 0, 0, time.UTC),
				},
			},
			wantErr: false,
		},
		{
			name:      "monthly grouping - 3 months",
			startTime: time.Date(2025, 1, 16, 0, 0, 0, 0, time.UTC),
			endTime:   time.Date(2025, 4, 15, 0, 0, 0, 0, time.UTC),
			grouping: &TimeGrouping{
				Unit:   TimeGroupingMonth,
				Length: 1,
			},
			expected: []TimeGroup{
				{
					StartTime: time.Date(2025, 1, 15, 0, 0, 0, 0, time.UTC),
					EndTime:   time.Date(2025, 2, 15, 0, 0, 0, 0, time.UTC),
				},
				{
					StartTime: time.Date(2025, 2, 15, 0, 0, 0, 0, time.UTC),
					EndTime:   time.Date(2025, 3, 15, 0, 0, 0, 0, time.UTC),
				},
				{
					StartTime: time.Date(2025, 3, 15, 0, 0, 0, 0, time.UTC),
					EndTime:   time.Date(2025, 4, 15, 0, 0, 0, 0, time.UTC),
				},
			},
			wantErr: false,
		},
		{
			name:      "monthly grouping with day adjustment - Feb 1 to Mar 31",
			startTime: time.Date(2025, 2, 1, 0, 0, 0, 0, time.UTC),
			endTime:   time.Date(2025, 3, 31, 0, 0, 0, 0, time.UTC),
			grouping: &TimeGrouping{
				Unit:   TimeGroupingMonth,
				Length: 1,
			},
			expected: []TimeGroup{
				{
					StartTime: time.Date(2025, 1, 31, 0, 0, 0, 0, time.UTC),
					EndTime:   time.Date(2025, 2, 28, 0, 0, 0, 0, time.UTC), // Feb doesn't have 31 days
				},
				{
					StartTime: time.Date(2025, 2, 28, 0, 0, 0, 0, time.UTC),
					EndTime:   time.Date(2025, 3, 31, 0, 0, 0, 0, time.UTC), // Returns to 31 when possible
				},
			},
			wantErr: false,
		},
		{
			name:      "startTime equal to endTime",
			startTime: time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC),
			endTime:   time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC),
			grouping: &TimeGrouping{
				Unit:   TimeGroupingDay,
				Length: 1,
			},
			expected: []TimeGroup{
				{
					StartTime: time.Date(2024, 12, 31, 0, 0, 0, 0, time.UTC),
					EndTime:   time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC),
				},
			},
			wantErr: false,
		},
		{
			name:      "invalid grouping",
			startTime: time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC),
			endTime:   time.Date(2025, 1, 2, 0, 0, 0, 0, time.UTC),
			grouping: &TimeGrouping{
				Unit:   "INVALID",
				Length: 1,
			},
			expected: nil,
			wantErr:  true,
		},
		{
			name:      "startTime after endTime",
			startTime: time.Date(2025, 1, 2, 0, 0, 0, 0, time.UTC),
			endTime:   time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC),
			grouping: &TimeGrouping{
				Unit:   TimeGroupingDay,
				Length: 1,
			},
			expected: nil,
			wantErr:  true,
		},
		{
			name:      "multi-day grouping",
			startTime: time.Date(2025, 1, 2, 0, 0, 0, 0, time.UTC),
			endTime:   time.Date(2025, 1, 7, 0, 0, 0, 0, time.UTC),
			grouping: &TimeGrouping{
				Unit:   TimeGroupingDay,
				Length: 3,
			},
			expected: []TimeGroup{
				{
					StartTime: time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC),
					EndTime:   time.Date(2025, 1, 4, 0, 0, 0, 0, time.UTC),
				},
				{
					StartTime: time.Date(2025, 1, 4, 0, 0, 0, 0, time.UTC),
					EndTime:   time.Date(2025, 1, 7, 0, 0, 0, 0, time.UTC),
				},
			},
			wantErr: false,
		},
		{
			name:      "leap year monthly grouping",
			startTime: time.Date(2024, 1, 29, 1, 0, 0, 0, time.UTC),
			endTime:   time.Date(2024, 3, 29, 0, 0, 0, 0, time.UTC),
			grouping: &TimeGrouping{
				Unit:   TimeGroupingMonth,
				Length: 1,
			},
			expected: []TimeGroup{
				{
					StartTime: time.Date(2024, 1, 29, 0, 0, 0, 0, time.UTC),
					EndTime:   time.Date(2024, 2, 29, 0, 0, 0, 0, time.UTC), // Leap year has Feb 29
				},
				{
					StartTime: time.Date(2024, 2, 29, 0, 0, 0, 0, time.UTC),
					EndTime:   time.Date(2024, 3, 29, 0, 0, 0, 0, time.UTC),
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := GenerateTimeGroups(tt.startTime, tt.endTime, tt.grouping)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
				return
			}

			assert.NoError(t, err)
			assert.Equal(t, len(tt.expected), len(result))

			for i, expected := range tt.expected {
				assert.Equal(t, expected.StartTime, result[i].StartTime, "TimeGroup %d StartTime mismatch", i)
				assert.Equal(t, expected.EndTime, result[i].EndTime, "TimeGroup %d EndTime mismatch", i)
			}

			// Verify that groups are consecutive (no gaps or overlaps)
			for i := 1; i < len(result); i++ {
				assert.Equal(t, result[i-1].EndTime, result[i].StartTime, "Gap or overlap between TimeGroup %d and %d", i-1, i)
			}

			// Verify that the first group starts at startTime and last group ends at endTime
			if len(result) > 0 {
				assert.Less(t, result[0].StartTime, tt.startTime, "First group should start before startTime")
				assert.Equal(t, tt.endTime, result[len(result)-1].EndTime, "Last group should end at endTime")
			}
		})
	}
}
