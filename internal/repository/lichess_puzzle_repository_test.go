package repository

import (
	"context"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/glebarez/sqlite"
	"github.com/lib/pq"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"
)

func setupTestDB(t *testing.T) *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	require.NoError(t, err)

	// Auto-migrate the LichessPuzzle model
	err = db.AutoMigrate(&models.LichessPuzzle{})
	require.NoError(t, err)

	return db
}

func TestLichessPuzzleRepository_SaveBatch(t *testing.T) {
	db := setupTestDB(t)
	repo := NewLichessPuzzleRepository(db)
	ctx := context.Background()

	// Test data
	puzzles := []models.LichessPuzzle{
		{
			ID:              "00001",
			FEN:             "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
			Moves:           pq.StringArray{"e2e4", "e7e5"},
			Rating:          1500,
			RatingDeviation: 75,
			Popularity:      90,
			NbPlays:         1000,
			Themes:          pq.StringArray{"opening"},
			GameURL:         "https://lichess.org/test1",
			OpeningTags:     pq.StringArray{},
		},
		{
			ID:              "00002",
			FEN:             "rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2",
			Moves:           pq.StringArray{"g1f3", "b8c6"},
			Rating:          1600,
			RatingDeviation: 80,
			Popularity:      85,
			NbPlays:         2000,
			Themes:          pq.StringArray{"middlegame"},
			GameURL:         "https://lichess.org/test2",
			OpeningTags:     pq.StringArray{},
		},
		{
			ID:              "00003",
			FEN:             "rnbqkbnr/pppp1ppp/8/4p3/4P3/5N2/PPPP1PPP/RNBQKB1R b KQkq - 1 2",
			Moves:           pq.StringArray{"f8c5", "d2d3"},
			Rating:          1700,
			RatingDeviation: 85,
			Popularity:      80,
			NbPlays:         3000,
			Themes:          pq.StringArray{"endgame"},
			GameURL:         "https://lichess.org/test3",
			OpeningTags:     pq.StringArray{},
		},
	}

	t.Run("SaveBatch with new puzzles", func(t *testing.T) {
		result, err := repo.SaveBatch(ctx, puzzles)
		require.NoError(t, err)
		assert.Equal(t, 3, result.Imported)
		assert.Equal(t, 0, result.Skipped)
		assert.Equal(t, "00003", result.LastID)
		assert.Empty(t, result.Errors)

		// Verify puzzles were saved
		for _, puzzle := range puzzles {
			saved, err := repo.GetByID(ctx, puzzle.ID)
			require.NoError(t, err)
			assert.Equal(t, puzzle.ID, saved.ID)
			assert.Equal(t, puzzle.FEN, saved.FEN)
			assert.Equal(t, puzzle.Rating, saved.Rating)
		}
	})

	t.Run("SaveBatch with existing puzzles (idempotency)", func(t *testing.T) {
		// Modify one puzzle and try to save again
		puzzles[1].Rating = 1650 // Change rating

		result, err := repo.SaveBatch(ctx, puzzles)
		require.NoError(t, err)
		assert.Equal(t, 3, result.Imported)
		assert.Equal(t, 0, result.Skipped)
		assert.Equal(t, "00003", result.LastID)

		// Verify the updated puzzle
		saved, err := repo.GetByID(ctx, "00002")
		require.NoError(t, err)
		assert.Equal(t, 1650, saved.Rating)
	})

	t.Run("SaveBatch with empty slice", func(t *testing.T) {
		result, err := repo.SaveBatch(ctx, []models.LichessPuzzle{})
		require.NoError(t, err)
		assert.Equal(t, 0, result.Imported)
		assert.Equal(t, 0, result.Skipped)
		assert.Equal(t, "", result.LastID)
		assert.Empty(t, result.Errors)
	})
}

func TestLichessPuzzleRepository_Save(t *testing.T) {
	db := setupTestDB(t)
	repo := NewLichessPuzzleRepository(db)
	ctx := context.Background()

	puzzle := &models.LichessPuzzle{
		ID:              "test001",
		FEN:             "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
		Moves:           pq.StringArray{"e2e4", "e7e5"},
		Rating:          1500,
		RatingDeviation: 75,
		Popularity:      90,
		NbPlays:         1000,
		Themes:          pq.StringArray{"opening"},
		GameURL:         "https://lichess.org/test",
		OpeningTags:     pq.StringArray{},
	}

	t.Run("Save new puzzle", func(t *testing.T) {
		err := repo.Save(ctx, puzzle)
		require.NoError(t, err)

		// Verify timestamps were set
		assert.False(t, puzzle.CreatedAt.IsZero())
		assert.False(t, puzzle.UpdatedAt.IsZero())

		// Verify puzzle was saved
		saved, err := repo.GetByID(ctx, puzzle.ID)
		require.NoError(t, err)
		assert.Equal(t, puzzle.ID, saved.ID)
		assert.Equal(t, puzzle.FEN, saved.FEN)
	})

	t.Run("Save existing puzzle (update)", func(t *testing.T) {
		originalUpdatedAt := puzzle.UpdatedAt
		time.Sleep(time.Millisecond) // Ensure different timestamp

		puzzle.Rating = 1600 // Change rating
		err := repo.Save(ctx, puzzle)
		require.NoError(t, err)

		// Verify UpdatedAt was changed
		assert.True(t, puzzle.UpdatedAt.After(originalUpdatedAt))

		// Verify puzzle was updated
		saved, err := repo.GetByID(ctx, puzzle.ID)
		require.NoError(t, err)
		assert.Equal(t, 1600, saved.Rating)
	})
}
