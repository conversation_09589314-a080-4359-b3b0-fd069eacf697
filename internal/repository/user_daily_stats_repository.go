package repository

import (
	"context"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"gorm.io/gorm"
)

type UserDailyStatsRepository struct {
	db *gorm.DB
}

func NewUserDailyStatsRepository(db *gorm.DB) IUserDailyStatsRepository {
	return &UserDailyStatsRepository{db: db}
}

// ListByUserID retrieves a paginated list of daily stats for a specific user,
// optionally filtered by date range.
// Returns the list of daily stats and the total count matching the criteria.
func (r *UserDailyStatsRepository) ListByUserID(ctx context.Context, userID string, startDate *time.Time, endDate *time.Time, offset int, limit int) ([]models.UserDailyStats, int64, error) {
	var stats []models.UserDailyStats
	var totalCount int64

	db := r.db.WithContext(ctx).Model(&models.UserDailyStats{}).Where("user_id = ?", userID)

	// Apply date range filters
	if startDate != nil {
		db = db.Where("date >= ?", *startDate)
	}
	if endDate != nil {
		db = db.Where("date <= ?", *endDate)
	}

	// Get total count before applying pagination
	err := db.Count(&totalCount).Error
	if err != nil {
		return nil, 0, err
	}

	// Apply pagination and fetch stats
	err = db.Offset(offset).Limit(limit).Order("date DESC").Find(&stats).Error
	if err != nil {
		return nil, 0, err
	}

	return stats, totalCount, nil
}
