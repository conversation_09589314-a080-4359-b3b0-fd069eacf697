package repository

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var (
	ErrTaskUpdateConflict = errors.New("task update conflict: optimistic lock failed")
)

// TaskRepository implements ITaskRepository for database operations.
type TaskRepository struct {
	db *gorm.DB
}

// NewTaskRepository creates a new instance of TaskRepository.
func NewTaskRepository(db *gorm.DB) *TaskRepository {
	return &TaskRepository{db: db}
}

// Create inserts a new task into the database.
func (r *TaskRepository) Create(ctx context.Context, task *models.Task) error {
	task.ID = uuid.New().String()
	if task.CreatedAt.IsZero() {
		task.CreatedAt = time.Now()
	}
	task.UpdatedAt = task.CreatedAt
	if task.ScheduledAt.IsZero() {
		task.ScheduledAt = task.CreatedAt // Default ScheduledAt to CreatedAt
	}
	return r.db.WithContext(ctx).Create(task).Error
}

// GetByID retrieves a task by its ID.
func (r *TaskRepository) GetByID(ctx context.Context, taskID string) (*models.Task, error) {
	var task models.Task
	err := r.db.WithContext(ctx).First(&task, "id = ?", taskID).Error
	if err != nil {
		return nil, err
	}
	return &task, nil
}

// ListByUserID retrieves tasks for a specific user, optionally filtered by status.
func (r *TaskRepository) ListByUserID(ctx context.Context, userID string, statuses []models.TaskStatus) ([]models.Task, error) {
	var tasks []models.Task
	query := r.db.WithContext(ctx).Where("user_id = ?", userID)
	if len(statuses) > 0 {
		query = query.Where("status IN ?", statuses)
	}
	err := query.Order("created_at desc").Find(&tasks).Error
	return tasks, err
}

// ClaimNextPending finds a pending task, updates its status to in_progress,
// assigns the workerID, sets the pickedUpAt timestamp, and returns the claimed task.
// The selection logic:
// 1. Selects a random user who has pending tasks
// 2. From that user's tasks, prioritizes GenerateChessPuzzles tasks if available
// 3. Otherwise selects the oldest scheduled task from that user
// Uses FOR UPDATE SKIP LOCKED for concurrency control.
func (r *TaskRepository) ClaimNextPending(ctx context.Context, workerID string) (*models.Task, error) {
	var task models.Task
	n := time.Now()
	err := r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// First, get a random user who has pending tasks
		// We use a CTE to find all users with pending tasks, then select one randomly
		query := `
			WITH users_with_pending_tasks AS (
				SELECT DISTINCT user_id
				FROM tasks
				WHERE status = ? AND scheduled_at <= ?
			)
			SELECT user_id FROM users_with_pending_tasks
			ORDER BY RANDOM()
			LIMIT 1
		`
		var selectedUserID string
		err := tx.Raw(query, models.TaskStatusPending, n).Scan(&selectedUserID).Error
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return gorm.ErrRecordNotFound
			}
			return fmt.Errorf("failed to find user with pending tasks: %w", err)
		}

		// If no user was found, return record not found
		if selectedUserID == "" {
			return gorm.ErrRecordNotFound
		}

		// Now, try to find a GenerateChessPuzzles task for this user first
		err = tx.Clauses(clause.Locking{Strength: "UPDATE", Options: "SKIP LOCKED"}).
			Where("status = ? AND scheduled_at <= ? AND user_id = ? AND task_type = ?",
				models.TaskStatusPending, n, selectedUserID, models.GenerateChessPuzzlesTask).
			Order("scheduled_at asc").
			First(&task).Error

		// If no GenerateChessPuzzles task found, get the oldest pending task for this user
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = tx.Clauses(clause.Locking{Strength: "UPDATE", Options: "SKIP LOCKED"}).
				Where("status = ? AND scheduled_at <= ? AND user_id = ?",
					models.TaskStatusPending, n, selectedUserID).
				Order("scheduled_at asc").
				First(&task).Error
		}

		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				// No pending task found for this user, not an error
				return gorm.ErrRecordNotFound // Special handling outside transaction
			}
			return fmt.Errorf("failed to find pending task: %w", err)
		}

		// Claim the task
		now := time.Now()
		updateData := map[string]interface{}{
			"status":       models.TaskStatusInProgress,
			"worker_id":    sql.NullString{String: workerID, Valid: true},
			"picked_up_at": sql.NullTime{Time: now, Valid: true},
			"updated_at":   now, // Explicitly set updated_at
		}

		// We only increment attempts when resetting a failed/hanging task, not when claiming.

		res := tx.Model(&task).Updates(updateData)
		if res.Error != nil {
			return fmt.Errorf("failed to claim task: %w", res.Error)
		}
		if res.RowsAffected == 0 {
			// Should not happen with FOR UPDATE if the record was found, but check anyway
			return errors.New("failed to acquire lock or update task during claim")
		}

		// Refresh the task struct with updated values (like updated_at)
		// This is important because the original 'task' variable holds pre-update values
		return tx.First(&task, "id = ?", task.ID).Error
	})

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// No task available to claim
			return nil, nil
		}
		return nil, err // Other transaction or update errors
	}

	return &task, nil
}

// Update updates an existing task using optimistic locking based on expectedUpdatedAt.
func (r *TaskRepository) Update(ctx context.Context, task *models.Task, expectedUpdatedAt time.Time) error {
	// Check if task pointer is nil
	if task == nil {
		return errors.New("task cannot be nil")
	}

	// Ensure UpdatedAt is managed by GORM or manually set before update
	task.UpdatedAt = time.Now() // GORM usually handles this, but explicit for clarity

	res := r.db.WithContext(ctx).
		Model(&models.Task{}). // Update against the model type
		Where("id = ? AND updated_at = ?", task.ID, expectedUpdatedAt).
		Updates(task) // Pass the task struct itself

	if res.Error != nil {
		return res.Error
	}
	if res.RowsAffected == 0 {
		// Check if the task still exists but with a different updated_at
		var existingTask models.Task
		err := r.db.WithContext(ctx).Select("id", "updated_at").First(&existingTask, "id = ?", task.ID).Error
		if err == nil {
			// Task exists but updated_at doesn't match -> conflict
			return ErrTaskUpdateConflict
		}
		return err
	}
	return nil
}

// ResetHanging resets tasks stuck in 'in_progress' status for longer than the timeout.
func (r *TaskRepository) ResetHanging(ctx context.Context, timeout time.Duration) (int64, error) {
	hangTime := time.Now().Add(-timeout)
	updateData := map[string]interface{}{
		"status":       models.TaskStatusPending,
		"worker_id":    sql.NullString{Valid: false},
		"picked_up_at": sql.NullTime{Valid: false},
		"updated_at":   time.Now(),
		"attempts":     gorm.Expr("attempts + 1"),
	}

	res := r.db.WithContext(ctx).
		Model(&models.Task{}).
		Where("status = ? AND picked_up_at < ?", models.TaskStatusInProgress, hangTime).
		Updates(updateData)

	return res.RowsAffected, res.Error
}

// ListAll retrieves a paginated list of all tasks, optionally filtered by status and user ID.
func (r *TaskRepository) ListAll(ctx context.Context, userID *string, statuses []models.TaskStatus, offset int, limit int) ([]models.Task, int64, error) {
	var tasks []models.Task
	var totalCount int64

	query := r.db.WithContext(ctx).Model(&models.Task{})

	// Apply optional user ID filter
	if userID != nil && *userID != "" {
		query = query.Where("user_id = ?", *userID)
	}

	// Apply optional status filter
	if len(statuses) > 0 {
		query = query.Where("status IN ?", statuses)
	}

	// Get total count before applying limit/offset
	err := query.Count(&totalCount).Error
	if err != nil {
		return nil, 0, err
	}

	// Apply pagination and retrieve tasks
	if limit <= 0 {
		limit = 10 // Default limit
	}
	if offset < 0 {
		offset = 0
	}

	err = query.Order("created_at desc").Offset(offset).Limit(limit).Find(&tasks).Error
	if err != nil {
		return nil, 0, err
	}

	return tasks, totalCount, nil
}

// DeleteOldTasks removes tasks older than the cutoff time with specified statuses, up to a limit.
func (r *TaskRepository) DeleteOldTasks(ctx context.Context, cutoff time.Time, statuses []models.TaskStatus, limit int) (int64, error) {
	// Safety check: If only pending/in-progress tasks are requested for deletion,
	// return 0 indicating no deletions (protecting against accidental deletions)
	if len(statuses) > 0 {
		onlyUnsafeStatuses := true
		for _, status := range statuses {
			if status != models.TaskStatusPending && status != models.TaskStatusInProgress {
				onlyUnsafeStatuses = false
				break
			}
		}
		if onlyUnsafeStatuses {
			return 0, nil
		}
	}

	query := r.db.WithContext(ctx).Model(&models.Task{})

	// Apply status filter
	if len(statuses) > 0 {
		query = query.Where("status IN ?", statuses)
	} else {
		// Default to completed or failed if no statuses provided
		query = query.Where("status IN ?", []models.TaskStatus{models.TaskStatusCompleted, models.TaskStatusFailed})
	}

	// Apply cutoff time filter
	query = query.Where("created_at < ?", cutoff)

	// Get IDs to delete with limit
	var idsToDelete []string
	subQuery := query.Select("id").Order("created_at asc")

	if limit > 0 {
		subQuery = subQuery.Limit(limit)
	}

	err := subQuery.Find(&idsToDelete).Error
	if err != nil {
		return 0, err
	}

	// If no IDs to delete, return early
	if len(idsToDelete) == 0 {
		return 0, nil
	}

	// Delete the tasks by ID
	result := r.db.WithContext(ctx).
		Where("id IN ?", idsToDelete).
		Delete(&models.Task{})

	return result.RowsAffected, result.Error
}
