package repository

import (
	"context"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

const (
	DefaultIdempotencyRecordTTL = 7 * 24 * time.Hour
)

// IdempotencyRepository implements IIdempotencyRepository for database operations.
type IdempotencyRepository struct {
	db *gorm.DB
}

// NewIdempotencyRepository creates a new instance of IdempotencyRepository.
func NewIdempotencyRepository(db *gorm.DB) *IdempotencyRepository {
	return &IdempotencyRepository{db: db}
}

// Get retrieves an idempotency record by its key, user ID, request method, and request path.
func (r *IdempotencyRepository) Get(ctx context.Context, idempotencyKey string, userID string, requestMethod string, requestPath string) (*models.IdempotencyRecord, error) {
	var record models.IdempotencyRecord
	err := r.db.WithContext(ctx).
		Where("idempotency_key = ? AND user_id = ? AND request_method = ? AND request_path = ?",
			idempotencyKey, userID, requestMethod, requestPath).
		First(&record).Error

	if err != nil {
		return nil, err
	}
	return &record, nil
}

// Create stores a new idempotency record.
// Assumes a unique constraint exists on (user_id, idempotency_key) in the database.
func (r *IdempotencyRepository) Create(ctx context.Context, record *models.IdempotencyRecord) error {
	if record.ID == "" {
		record.ID = uuid.New().String()
	}
	if record.CreatedAt.IsZero() {
		now := time.Now()
		record.CreatedAt = now
	}
	// Set ExpiresAt if not provided
	if record.ExpiresAt.IsZero() {
		record.ExpiresAt = record.CreatedAt.Add(DefaultIdempotencyRecordTTL)
	}

	return r.db.WithContext(ctx).Create(record).Error
}

// DeleteExpired removes idempotency records whose ExpiresAt time has passed.
func (r *IdempotencyRepository) DeleteExpired(ctx context.Context) (int64, error) {
	now := time.Now()
	res := r.db.WithContext(ctx).
		Where("expires_at < ?", now).
		Delete(&models.IdempotencyRecord{})

	return res.RowsAffected, res.Error
}
