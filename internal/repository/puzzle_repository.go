package repository

import (
	"context"
	"fmt"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository/common"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type PuzzleRepository struct {
	db *gorm.DB
}

func NewPuzzleRepository(db *gorm.DB) *PuzzleRepository {
	return &PuzzleRepository{
		db: db,
	}
}

// Create creates a new puzzle
func (r *PuzzleRepository) Create(ctx context.Context, puzzle *models.Puzzle) error {
	puzzle.ID = uuid.New().String()
	now := time.Now()
	puzzle.CreatedAt = now
	puzzle.UpdatedAt = now
	return r.db.WithContext(ctx).Create(puzzle).Error
}

// GetByID retrieves a puzzle by ID
func (r *PuzzleRepository) GetByID(ctx context.Context, id string) (*models.Puzzle, error) {
	var puzzle models.Puzzle
	err := r.db.WithContext(ctx).First(&puzzle, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &puzzle, nil
}

// GetByGameID retrieves all puzzles for a game
func (r *PuzzleRepository) GetByGameID(ctx context.Context, gameID string) ([]models.Puzzle, error) {
	var puzzles []models.Puzzle
	err := r.db.WithContext(ctx).Where("game_id = ?", gameID).Find(&puzzles).Error
	if err != nil {
		return nil, err
	}
	return puzzles, nil
}

// Update updates a puzzle
func (r *PuzzleRepository) Update(ctx context.Context, puzzle *models.Puzzle) error {
	now := time.Now()
	puzzle.UpdatedAt = now
	// Use Select("*") to force updating all fields, including zero values
	// This is necessary because GORM's Save() method skips zero values by default
	return r.db.WithContext(ctx).Select("*").Save(puzzle).Error
}

// Delete deletes a puzzle by ID
func (r *PuzzleRepository) Delete(ctx context.Context, id string) error {
	return r.db.WithContext(ctx).Delete(&models.Puzzle{}, "id = ?", id).Error
}

// ListByUserID retrieves a paginated list of puzzles for a specific user,
// optionally filtered by tags and the associated game's time range.
// Returns the list of puzzles and the total count matching the criteria.
func (r *PuzzleRepository) ListByUserID(ctx context.Context, userID string, filter PuzzleFilter, offset int, limit int) ([]models.Puzzle, int64, error) {
	var puzzles []models.Puzzle
	var totalCount int64

	// Base query joining puzzles and games
	db := r.db.WithContext(ctx).Model(&models.Puzzle{}).Preload("Game", func(db *gorm.DB) *gorm.DB {
		return db.Omit("CompressedPGN") // Omit PGN to reduce memory usage
	}).Joins("JOIN games ON games.id = puzzles.game_id")
	db = db.Where("puzzles.user_id = ?", userID)

	// Apply filters using common utility
	db = common.ApplyPuzzleFilters(db, common.PuzzleFilter(filter), true) // true for PostgreSQL

	// Get total count before applying pagination
	// Need to be careful with Count() after Joins
	// Create a separate count query based on the filtered db
	countDB := db
	err := countDB.Count(&totalCount).Error
	if err != nil {
		return nil, 0, fmt.Errorf("error counting puzzles: %w", err)
	}

	// Apply pagination and fetch puzzles
	// Remove Select() to allow preloaded Game data to be loaded
	err = db.Offset(offset).Limit(limit).Order("puzzles.created_at DESC").Find(&puzzles).Error
	if err != nil {
		return nil, 0, fmt.Errorf("error fetching puzzles: %w", err)
	}

	return puzzles, totalCount, nil
}

// GetPuzzleStats retrieves statistics about puzzles for a specific user,
// optionally filtered by the same criteria as ListByUserID.
// If offset and limit are provided, stats will be calculated only for the specified puzzles.
// If grouping is provided, stats will be grouped by time periods.
// Returns an array of PuzzleStats structs, one for each time period (or a single-element array if no grouping is requested).
func (r *PuzzleRepository) GetPuzzleStats(ctx context.Context, userID string, filter PuzzleFilter, offset int, limit int, grouping *common.TimeGrouping) ([]*models.PuzzleStats, error) {
	// Get puzzles matching the filter with pagination
	puzzles, totalCount, err := r.ListByUserID(ctx, userID, filter, offset, limit)
	if err != nil {
		return nil, fmt.Errorf("error fetching puzzles for stats: %w", err)
	}

	// If no grouping is requested, calculate stats for all puzzles
	if grouping == nil || !grouping.IsValid() {
		stats := &models.PuzzleStats{
			TotalCount: totalCount,
		}

		// Calculate stats for all puzzles using common utility
		common.CalculatePuzzleStats(puzzles, stats)

		return []*models.PuzzleStats{stats}, nil
	}

	// Calculate grouped stats using the simpler single-pass approach
	return common.CalculateGroupedPuzzleStats(puzzles, grouping)
}

// GetRandomPuzzles retrieves random user puzzles based on the provided filter
// Since user puzzles don't have explicit ratings, this uses puzzle length (number of moves) as a difficulty metric
func (r *PuzzleRepository) GetRandomPuzzles(ctx context.Context, userID string, filter common.UserPuzzleFilter, limit int) ([]models.Puzzle, error) {
	query := r.db.WithContext(ctx).Where("user_id = ?", userID)

	// Use cardinality(moves) for PostgreSQL to get array length
	query = query.Where("cardinality(moves) >= ? AND cardinality(moves) <= ?", filter.MinMoveLength, filter.MaxMoveLength)

	// Filter by tags if provided (using tags field for user puzzles)
	if len(filter.Tags) > 0 {
		query = query.Where("tags && ?", filter.Tags)
	}

	// Filter by themes if provided (using theme field for user puzzles)
	if len(filter.Themes) > 0 {
		query = query.Where("theme IN ?", filter.Themes)
	}

	// Filter by game time range if provided
	if filter.GameTimeStart != nil || filter.GameTimeEnd != nil {
		// Join with games table to filter by game_time
		query = query.Joins("JOIN games ON puzzles.game_id = games.id")
		if filter.GameTimeStart != nil {
			query = query.Where("games.game_time >= ?", filter.GameTimeStart)
		}
		if filter.GameTimeEnd != nil {
			query = query.Where("games.game_time <= ?", filter.GameTimeEnd)
		}
	}

	// Exclude disliked puzzles
	if len(filter.DislikedIDs) > 0 {
		query = query.Where("puzzles.id NOT IN ?", filter.DislikedIDs)
	}

	var puzzles []models.Puzzle
	err := query.Order("RANDOM()").Limit(limit).Find(&puzzles).Error
	if err != nil {
		return nil, err
	}

	return puzzles, nil
}
