package repository

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"gorm.io/gorm"
)

// PuzzleQueueRepository implements IPuzzleQueueRepository for database operations.
type PuzzleQueueRepository struct {
	db *gorm.DB
}

// NewPuzzleQueueRepository creates a new instance of PuzzleQueueRepository.
func NewPuzzleQueueRepository(db *gorm.DB) IPuzzleQueueRepository {
	return &PuzzleQueueRepository{db: db}
}

// AddPuzzlesToQueue adds puzzles to the user's queue
func (r *PuzzleQueueRepository) AddPuzzlesToQueue(ctx context.Context, userID string, puzzles []models.PuzzleQueueEntry) (int, error) {
	if len(puzzles) == 0 {
		return 0, nil
	}

	// Set common fields for all puzzles
	now := time.Now()
	for i := range puzzles {
		puzzles[i].UserID = userID
		// Set DueAt to now if not already set
		if puzzles[i].DueAt.IsZero() {
			puzzles[i].DueAt = now // Due today
		}
		puzzles[i].CreatedAt = now
		puzzles[i].UpdatedAt = now
	}

	// Use CreateInBatches to handle potential unique constraint violations gracefully
	result := r.db.WithContext(ctx).CreateInBatches(puzzles, 100)
	if result.Error != nil {
		return 0, fmt.Errorf("failed to add puzzles to queue: %w", result.Error)
	}

	return int(result.RowsAffected), nil
}

// GetDuePuzzles retrieves due puzzles for a user, optionally filtered by mistake type
func (r *PuzzleQueueRepository) GetDuePuzzles(ctx context.Context, userID string, mistakeBy *string, limit int) ([]models.PuzzleQueueItem, error) {
	var queueEntries []models.PuzzleQueueEntry

	query := r.db.WithContext(ctx).
		Where("user_id = ? AND due_at <= ?", userID, time.Now()).
		Preload("Puzzle")

	// Apply mistake_by filter if specified
	if mistakeBy != nil && *mistakeBy != "" {
		query = query.Where("mistake_by = ?", *mistakeBy)
	}

	// Apply limit
	if limit <= 0 {
		limit = 10 // Default limit
	}

	err := query.Order("due_at ASC").Limit(limit).Find(&queueEntries).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get due puzzles: %w", err)
	}

	// Convert to PuzzleQueueItem
	items := make([]models.PuzzleQueueItem, len(queueEntries))
	for i, entry := range queueEntries {
		items[i] = models.PuzzleQueueItem{
			QueueID:            entry.ID,
			PuzzleID:           entry.PuzzleID,
			PuzzleTheme:        string(entry.PuzzleTheme),
			MistakeBy:          entry.MistakeBy,
			DueAt:              entry.DueAt,
			AttemptsSinceAdded: entry.AttemptsSinceAdded,
			ConsecutiveCorrect: entry.ConsecutiveCorrect,
			PuzzleData:         &entry.Puzzle,
		}
	}

	return items, nil
}

// UpdateAfterAttempt updates a puzzle queue entry after an attempt
func (r *PuzzleQueueRepository) UpdateAfterAttempt(ctx context.Context, userID, puzzleID string, wasCorrect bool) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var entry models.PuzzleQueueEntry
		err := tx.Where("user_id = ? AND puzzle_id = ?", userID, puzzleID).First(&entry).Error
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				// Puzzle not in queue, nothing to update
				return nil
			}
			return fmt.Errorf("failed to find queue entry: %w", err)
		}

		// Update attempt counters
		entry.AttemptsSinceAdded++

		// Apply spaced repetition logic
		if wasCorrect {
			entry.ConsecutiveCorrect++

			// If mastered (5 consecutive correct), remove from queue
			if entry.ConsecutiveCorrect >= 5 {
				return tx.Delete(&entry).Error
			}

			// Schedule for future review based on consecutive correct count
			switch entry.ConsecutiveCorrect {
			case 1:
				entry.DueAt = time.Now().Add(2 * 24 * time.Hour) // 2 days
			case 2:
				entry.DueAt = time.Now().Add(4 * 24 * time.Hour) // 4 days
			case 3:
				entry.DueAt = time.Now().Add(7 * 24 * time.Hour) // 7 days
			case 4:
				entry.DueAt = time.Now().Add(15 * 24 * time.Hour) // 15 days
			}
		} else {
			// Reset consecutive correct and schedule for tomorrow
			entry.ConsecutiveCorrect = 0
			entry.DueAt = time.Now().Add(24 * time.Hour) // 24 hours
		}

		// Save the updated entry
		return tx.Save(&entry).Error
	})
}

// GetRecentPuzzlesNotInQueue retrieves recent puzzles not already in the queue
func (r *PuzzleQueueRepository) GetRecentPuzzlesNotInQueue(ctx context.Context, userID string, mistakeBy *string, limit int) ([]string, error) {
	// Build the theme filter based on mistake_by
	var themes []models.PuzzleTheme
	if mistakeBy == nil {
		// Include all queueable themes
		themes = []models.PuzzleTheme{
			models.OpponentMistakeMissed, models.OpponentBlunderMissed,
			models.OwnMistakePunished, models.OwnMistakeEscaped,
			models.OwnBlunderPunished, models.OwnBlunderEscaped,
		}
	} else if *mistakeBy == "opponent" {
		themes = []models.PuzzleTheme{models.OpponentMistakeMissed, models.OpponentBlunderMissed}
	} else if *mistakeBy == "own" {
		themes = []models.PuzzleTheme{
			models.OwnMistakePunished, models.OwnMistakeEscaped,
			models.OwnBlunderPunished, models.OwnBlunderEscaped,
		}
	} else {
		return nil, fmt.Errorf("invalid mistake_by value: %s", *mistakeBy)
	}

	var puzzleIDs []string

	// Query puzzles that are not in the queue and match the theme criteria
	subQuery := r.db.WithContext(ctx).
		Model(&models.PuzzleQueueEntry{}).
		Select("puzzle_id").
		Where("user_id = ?", userID)

	err := r.db.WithContext(ctx).
		Model(&models.Puzzle{}).
		Select("id").
		Where("user_id = ? AND theme IN ?", userID, themes).
		Where("id NOT IN (?)", subQuery).
		Order("created_at DESC").
		Limit(limit).
		Pluck("id", &puzzleIDs).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get recent puzzles not in queue: %w", err)
	}

	return puzzleIDs, nil
}

// RemoveFromQueue removes a puzzle from the queue (when mastered)
func (r *PuzzleQueueRepository) RemoveFromQueue(ctx context.Context, userID, puzzleID string) error {
	result := r.db.WithContext(ctx).
		Where("user_id = ? AND puzzle_id = ?", userID, puzzleID).
		Delete(&models.PuzzleQueueEntry{})

	if result.Error != nil {
		return fmt.Errorf("failed to remove puzzle from queue: %w", result.Error)
	}

	return nil
}

// GetQueueStats retrieves queue statistics for a user
func (r *PuzzleQueueRepository) GetQueueStats(ctx context.Context, userID string) (*models.PuzzleQueueStats, error) {
	var stats models.PuzzleQueueStats

	// Get total queued
	err := r.db.WithContext(ctx).
		Model(&models.PuzzleQueueEntry{}).
		Where("user_id = ?", userID).
		Count(&stats.TotalQueued).Error
	if err != nil {
		return nil, fmt.Errorf("failed to count total queued: %w", err)
	}

	// Get due today
	today := time.Now().Truncate(24 * time.Hour).Add(24*time.Hour - time.Nanosecond)
	err = r.db.WithContext(ctx).
		Model(&models.PuzzleQueueEntry{}).
		Where("user_id = ? AND due_at <= ?", userID, today).
		Count(&stats.DueToday).Error
	if err != nil {
		return nil, fmt.Errorf("failed to count due today: %w", err)
	}

	// Get opponent mistakes count
	err = r.db.WithContext(ctx).
		Model(&models.PuzzleQueueEntry{}).
		Where("user_id = ? AND mistake_by = ?", userID, "opponent").
		Count(&stats.OpponentMistakes).Error
	if err != nil {
		return nil, fmt.Errorf("failed to count opponent mistakes: %w", err)
	}

	// Get own mistakes count
	err = r.db.WithContext(ctx).
		Model(&models.PuzzleQueueEntry{}).
		Where("user_id = ? AND mistake_by = ?", userID, "own").
		Count(&stats.OwnMistakes).Error
	if err != nil {
		return nil, fmt.Errorf("failed to count own mistakes: %w", err)
	}

	return &stats, nil
}

// GetByUserIDAndPuzzleID retrieves a specific queue entry
func (r *PuzzleQueueRepository) GetByUserIDAndPuzzleID(ctx context.Context, userID, puzzleID string) (*models.PuzzleQueueEntry, error) {
	var entry models.PuzzleQueueEntry
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND puzzle_id = ?", userID, puzzleID).
		First(&entry).Error
	if err != nil {
		return nil, err
	}
	return &entry, nil
}
