package repository

import (
	"context"
	"fmt"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/google/uuid"
	"github.com/lib/pq"
	"gorm.io/gorm"
)

type SprintPuzzleRepository struct {
	db *gorm.DB
}

func NewSprintPuzzleRepository(db *gorm.DB) ISprintPuzzleRepository {
	return &SprintPuzzleRepository{db: db}
}

// <PERSON><PERSON> creates a new sprint puzzle
func (r *SprintPuzzleRepository) Create(ctx context.Context, sprintPuzzle *models.SprintPuzzle) error {
	if sprintPuzzle.ID == "" {
		sprintPuzzle.ID = uuid.New().String()
	}
	now := time.Now()
	sprintPuzzle.CreatedAt = now
	sprintPuzzle.UpdatedAt = now
	return r.db.WithContext(ctx).Create(sprintPuzzle).Error
}

// CreateBatch creates multiple sprint puzzles in a batch
func (r *SprintPuzzleRepository) CreateBatch(ctx context.Context, sprintPuzzles []models.SprintPuzzle) error {
	if len(sprintPuzzles) == 0 {
		return nil
	}

	now := time.Now()
	for i := range sprintPuzzles {
		if sprintPuzzles[i].ID == "" {
			sprintPuzzles[i].ID = uuid.New().String()
		}
		sprintPuzzles[i].CreatedAt = now
		sprintPuzzles[i].UpdatedAt = now
	}

	return r.db.WithContext(ctx).CreateInBatches(sprintPuzzles, len(sprintPuzzles)).Error
}

// GetBySprintID retrieves all sprint puzzles for a specific sprint
func (r *SprintPuzzleRepository) GetBySprintID(ctx context.Context, sprintID string) ([]models.SprintPuzzle, error) {
	var sprintPuzzles []models.SprintPuzzle
	err := r.db.WithContext(ctx).
		Where("sprint_id = ?", sprintID).
		Order("sequence_in_sprint ASC").
		Find(&sprintPuzzles).Error
	if err != nil {
		return nil, err
	}
	return sprintPuzzles, nil
}

// GetByID retrieves a sprint puzzle by ID
func (r *SprintPuzzleRepository) GetByID(ctx context.Context, id string) (*models.SprintPuzzle, error) {
	var sprintPuzzle models.SprintPuzzle
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&sprintPuzzle).Error
	if err != nil {
		return nil, err
	}
	return &sprintPuzzle, nil
}

// CountBySprintID returns the count of sprint puzzles for a specific sprint
func (r *SprintPuzzleRepository) CountBySprintID(ctx context.Context, sprintID string) (int, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.SprintPuzzle{}).
		Where("sprint_id = ?", sprintID).
		Count(&count).Error
	if err != nil {
		return 0, err
	}
	return int(count), nil
}

// GetSprintPuzzlesWithAttempts retrieves sprint puzzles with attempt information
func (r *SprintPuzzleRepository) GetSprintPuzzlesWithAttempts(ctx context.Context, sprintID string, userID string, filter SprintPuzzleFilter, offset int, limit int) ([]SprintPuzzleWithAttempt, int64, error) {
	// Build the base query with joins to the new sprint_puzzle_attempts table
	query := r.db.WithContext(ctx).
		Table("sprint_puzzles sp").
		Select(`
			sp.id as sprint_puzzle_id,
			sp.sprint_id,
			sp.lichess_puzzle_id,
			sp.sequence_in_sprint,
			sp.created_at as sprint_puzzle_created_at,
			sp.updated_at as sprint_puzzle_updated_at,
			lp.fen,
			lp.moves,
			lp.rating,
			lp.themes,
			lp.popularity,
			lp.nb_plays,
			lp.created_at as lichess_puzzle_created_at,
			lp.updated_at as lichess_puzzle_updated_at,
			spa.user_moves,
			spa.was_correct,
			spa.time_taken_ms,
			spa.attempted_at,
			spa.attempt_type,
			spa.candidate_moves,
			spa.chosen_move
		`).
		Joins("JOIN lichess_puzzles lp ON sp.lichess_puzzle_id = lp.id").
		Joins(`LEFT JOIN sprint_puzzle_attempts spa ON spa.sprint_id = sp.sprint_id AND
			spa.user_id = ? AND spa.lichess_puzzle_id = sp.lichess_puzzle_id`, userID).
		Where("sp.sprint_id = ?", sprintID)

	// Apply filters
	if filter.SequenceMin != nil {
		query = query.Where("sp.sequence_in_sprint >= ?", *filter.SequenceMin)
	}
	if filter.SequenceMax != nil {
		query = query.Where("sp.sequence_in_sprint <= ?", *filter.SequenceMax)
	}

	// Apply status filter if specified
	if filter.Status != nil {
		switch *filter.Status {
		case SprintPuzzleStatusUnattempted:
			query = query.Where("spa.id IS NULL")
		case SprintPuzzleStatusSolved:
			query = query.Where("spa.was_correct = true")
		case SprintPuzzleStatusFailed:
			query = query.Where("spa.was_correct = false")
		case SprintPuzzleStatusAttempted:
			query = query.Where("spa.id IS NOT NULL")
		}
	}

	// Apply attempt type filter if specified
	if filter.AttemptType != nil {
		query = query.Where("spa.attempt_type = ?", *filter.AttemptType)
	}

	// Get total count before pagination - use a simpler count query
	var totalCount int64
	countQuery := r.db.WithContext(ctx).
		Table("sprint_puzzles sp").
		Joins("JOIN lichess_puzzles lp ON sp.lichess_puzzle_id = lp.id").
		Joins(`LEFT JOIN sprint_puzzle_attempts spa ON spa.sprint_id = sp.sprint_id AND
			spa.user_id = ? AND spa.lichess_puzzle_id = sp.lichess_puzzle_id`, userID).
		Where("sp.sprint_id = ?", sprintID)

	// Apply the same filters to count query
	if filter.SequenceMin != nil {
		countQuery = countQuery.Where("sp.sequence_in_sprint >= ?", *filter.SequenceMin)
	}
	if filter.SequenceMax != nil {
		countQuery = countQuery.Where("sp.sequence_in_sprint <= ?", *filter.SequenceMax)
	}

	if filter.Status != nil {
		switch *filter.Status {
		case SprintPuzzleStatusUnattempted:
			countQuery = countQuery.Where("spa.id IS NULL")
		case SprintPuzzleStatusSolved:
			countQuery = countQuery.Where("spa.was_correct = true")
		case SprintPuzzleStatusFailed:
			countQuery = countQuery.Where("spa.was_correct = false")
		case SprintPuzzleStatusAttempted:
			countQuery = countQuery.Where("spa.id IS NOT NULL")
		}
	}

	if filter.AttemptType != nil {
		countQuery = countQuery.Where("spa.attempt_type = ?", *filter.AttemptType)
	}

	err := countQuery.Count(&totalCount).Error
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count sprint puzzles: %w", err)
	}

	// Apply pagination and ordering
	query = query.Order("sp.sequence_in_sprint ASC").Offset(offset).Limit(limit)

	// Execute query
	type queryResult struct {
		SprintPuzzleID         string         `gorm:"column:sprint_puzzle_id"`
		SprintID               string         `gorm:"column:sprint_id"`
		LichessPuzzleID        string         `gorm:"column:lichess_puzzle_id"`
		SequenceInSprint       int            `gorm:"column:sequence_in_sprint"`
		SprintPuzzleCreatedAt  time.Time      `gorm:"column:sprint_puzzle_created_at"`
		SprintPuzzleUpdatedAt  time.Time      `gorm:"column:sprint_puzzle_updated_at"`
		FEN                    string         `gorm:"column:fen"`
		Moves                  pq.StringArray `gorm:"column:moves"`
		Rating                 int            `gorm:"column:rating"`
		Themes                 pq.StringArray `gorm:"column:themes"`
		Popularity             int            `gorm:"column:popularity"`
		NbPlays                int            `gorm:"column:nb_plays"`
		LichessPuzzleCreatedAt time.Time      `gorm:"column:lichess_puzzle_created_at"`
		LichessPuzzleUpdatedAt time.Time      `gorm:"column:lichess_puzzle_updated_at"`
		UserMoves              pq.StringArray `gorm:"column:user_moves"`
		WasCorrect             *bool          `gorm:"column:was_correct"`
		TimeTakenMs            *int           `gorm:"column:time_taken_ms"`
		AttemptedAt            *time.Time     `gorm:"column:attempted_at"`
		AttemptType            *string        `gorm:"column:attempt_type"`
		CandidateMoves         pq.StringArray `gorm:"column:candidate_moves"`
		ChosenMove             *string        `gorm:"column:chosen_move"`
	}

	var results []queryResult
	err = query.Find(&results).Error
	if err != nil {
		return nil, 0, fmt.Errorf("failed to query sprint puzzles: %w", err)
	}

	// Convert results to SprintPuzzleWithAttempt
	sprintPuzzles := make([]SprintPuzzleWithAttempt, len(results))
	for i, result := range results {
		// Convert pq.StringArray to []string
		moves := []string(result.Moves)
		themes := []string(result.Themes)

		sprintPuzzle := models.SprintPuzzle{
			ID:               result.SprintPuzzleID,
			SprintID:         result.SprintID,
			LichessPuzzleID:  result.LichessPuzzleID,
			SequenceInSprint: result.SequenceInSprint,
			CreatedAt:        result.SprintPuzzleCreatedAt,
			UpdatedAt:        result.SprintPuzzleUpdatedAt,
		}

		lichessPuzzle := models.LichessPuzzle{
			ID:         result.LichessPuzzleID,
			FEN:        result.FEN,
			Moves:      moves,
			Rating:     result.Rating,
			Themes:     themes,
			Popularity: result.Popularity,
			NbPlays:    result.NbPlays,
			CreatedAt:  result.LichessPuzzleCreatedAt,
			UpdatedAt:  result.LichessPuzzleUpdatedAt,
		}

		// Determine attempt status and extract attempt data from sprint_puzzle_attempts
		attemptStatus := SprintPuzzleStatusUnattempted
		var userMoves []string
		var wasCorrect *bool
		var timeTakenMs *int
		var attemptedAt *time.Time

		if result.WasCorrect != nil {
			// Attempt exists
			if *result.WasCorrect {
				attemptStatus = SprintPuzzleStatusSolved
			} else {
				attemptStatus = SprintPuzzleStatusFailed
			}

			userMoves = []string(result.UserMoves)
			wasCorrect = result.WasCorrect
			timeTakenMs = result.TimeTakenMs
			attemptedAt = result.AttemptedAt
		}

		sprintPuzzles[i] = SprintPuzzleWithAttempt{
			SprintPuzzle:   sprintPuzzle,
			LichessPuzzle:  lichessPuzzle,
			AttemptStatus:  attemptStatus,
			UserMoves:      userMoves,
			WasCorrect:     wasCorrect,
			TimeTakenMs:    timeTakenMs,
			AttemptedAt:    attemptedAt,
			AttemptType:    result.AttemptType,
			CandidateMoves: []string(result.CandidateMoves),
			ChosenMove:     result.ChosenMove,
		}
	}

	return sprintPuzzles, totalCount, nil
}
