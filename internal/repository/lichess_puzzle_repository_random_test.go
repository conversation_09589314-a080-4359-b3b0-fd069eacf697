package repository

import (
	"context"
	"fmt"
	"testing"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository/common"
	"github.com/glebarez/sqlite"
	"github.com/lib/pq"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"
)

func TestLichessPuzzleRepository_GetRandomByRatingRange_Comprehensive(t *testing.T) {
	// Create in-memory SQLite database for testing
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	require.NoError(t, err)

	// Auto-migrate the schema
	err = db.AutoMigrate(&models.LichessPuzzle{})
	require.NoError(t, err)

	repo := NewLichessPuzzleRepository(db)
	ctx := context.Background()

	// Create test puzzles with different ratings
	testPuzzles := []models.LichessPuzzle{
		{
			ID:              "test001",
			FEN:             "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
			Moves:           pq.StringArray{"e2e4", "e7e5"},
			Rating:          1400,
			RatingDeviation: 75,
			Popularity:      90,
			NbPlays:         1000,
			Themes:          pq.StringArray{"opening", "tactics"},
			GameURL:         "https://lichess.org/test001",
		},
		{
			ID:              "test002",
			FEN:             "rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2",
			Moves:           pq.StringArray{"g1f3", "b8c6"},
			Rating:          1450,
			RatingDeviation: 80,
			Popularity:      85,
			NbPlays:         2000,
			Themes:          pq.StringArray{"middlegame", "tactics"},
			GameURL:         "https://lichess.org/test002",
		},
		{
			ID:              "test003",
			FEN:             "rnbqkbnr/pppp1ppp/8/4p3/4P3/5N2/PPPP1PPP/RNBQKB1R b KQkq - 1 2",
			Moves:           pq.StringArray{"f8c5", "d2d3"},
			Rating:          1500,
			RatingDeviation: 85,
			Popularity:      80,
			NbPlays:         3000,
			Themes:          pq.StringArray{"endgame", "tactics"},
			GameURL:         "https://lichess.org/test003",
		},
		{
			ID:              "test004",
			FEN:             "r1bqkbnr/pppp1ppp/2n5/4p3/4P3/5N2/PPPP1PPP/RNBQKB1R w KQkq - 2 3",
			Moves:           pq.StringArray{"f1c4", "f7f5"},
			Rating:          1550,
			RatingDeviation: 90,
			Popularity:      75,
			NbPlays:         4000,
			Themes:          pq.StringArray{"opening", "attack"},
			GameURL:         "https://lichess.org/test004",
		},
		{
			ID:              "test005",
			FEN:             "r1bqkb1r/pppp1ppp/2n2n2/4p3/2B1P3/5N2/PPPP1PPP/RNBQK2R b KQkq - 4 4",
			Moves:           pq.StringArray{"f6e4", "d2d3"},
			Rating:          1600,
			RatingDeviation: 95,
			Popularity:      70,
			NbPlays:         5000,
			Themes:          pq.StringArray{"middlegame", "attack"},
			GameURL:         "https://lichess.org/test005",
		},
	}

	// Save all test puzzles
	for _, puzzle := range testPuzzles {
		err := repo.Save(ctx, &puzzle)
		require.NoError(t, err)
	}

	t.Run("returns puzzles within rating range", func(t *testing.T) {
		filter := common.LichessPuzzleFilter{
			MinRating:  1400,
			MaxRating:  1600,
			Themes:     nil,
			ExcludeIDs: nil,
		}
		puzzles, err := repo.GetRandomPuzzles(ctx, filter, 3)
		require.NoError(t, err)
		assert.Len(t, puzzles, 3)

		// Verify all returned puzzles are within the rating range
		for _, puzzle := range puzzles {
			assert.GreaterOrEqual(t, puzzle.Rating, 1400)
			assert.LessOrEqual(t, puzzle.Rating, 1600)
		}
	})

	t.Run("filters by themes correctly", func(t *testing.T) {
		// Skip theme filtering test for SQLite as it doesn't support PostgreSQL array operators
		if db.Name() == "sqlite" {
			t.Skip("Skipping theme filtering test for SQLite - PostgreSQL array operators not supported")
		}

		filter := common.LichessPuzzleFilter{
			MinRating:  1400,
			MaxRating:  1600,
			Themes:     []string{"tactics"},
			ExcludeIDs: nil,
		}
		puzzles, err := repo.GetRandomPuzzles(ctx, filter, 5)
		require.NoError(t, err)
		assert.LessOrEqual(t, len(puzzles), 3) // Only 3 puzzles have "tactics" theme

		// Verify all returned puzzles have the required theme
		for _, puzzle := range puzzles {
			assert.Contains(t, puzzle.Themes, "tactics")
		}
	})

	t.Run("excludes specified IDs", func(t *testing.T) {
		excludeIDs := []string{"test001", "test002"}
		filter := common.LichessPuzzleFilter{
			MinRating:  1400,
			MaxRating:  1600,
			Themes:     nil,
			ExcludeIDs: excludeIDs,
		}
		puzzles, err := repo.GetRandomPuzzles(ctx, filter, 5)
		require.NoError(t, err)
		assert.LessOrEqual(t, len(puzzles), 3) // 5 total - 2 excluded = 3 max

		// Verify excluded puzzles are not returned
		for _, puzzle := range puzzles {
			assert.NotContains(t, excludeIDs, puzzle.ID)
		}
	})

	t.Run("returns empty when no matches", func(t *testing.T) {
		filter := common.LichessPuzzleFilter{
			MinRating:  2000,
			MaxRating:  2500,
			Themes:     nil,
			ExcludeIDs: nil,
		}
		puzzles, err := repo.GetRandomPuzzles(ctx, filter, 5)
		require.NoError(t, err)
		assert.Empty(t, puzzles)
	})

	t.Run("returns all when limit exceeds available", func(t *testing.T) {
		filter := common.LichessPuzzleFilter{
			MinRating:  1400,
			MaxRating:  1600,
			Themes:     nil,
			ExcludeIDs: nil,
		}
		puzzles, err := repo.GetRandomPuzzles(ctx, filter, 100)
		require.NoError(t, err)
		assert.Len(t, puzzles, 5) // All 5 test puzzles should be returned

		// Verify all returned puzzles are within the rating range
		for _, puzzle := range puzzles {
			assert.GreaterOrEqual(t, puzzle.Rating, 1400)
			assert.LessOrEqual(t, puzzle.Rating, 1600)
		}

		// Verify we got all expected puzzle IDs
		expectedIDs := []string{"test001", "test002", "test003", "test004", "test005"}
		actualIDs := make([]string, len(puzzles))
		for i, puzzle := range puzzles {
			actualIDs[i] = puzzle.ID
		}
		assert.ElementsMatch(t, expectedIDs, actualIDs)
	})

	t.Run("handles edge cases gracefully", func(t *testing.T) {
		// Test with limit 0
		filter := common.LichessPuzzleFilter{
			MinRating:  1400,
			MaxRating:  1600,
			Themes:     nil,
			ExcludeIDs: nil,
		}
		puzzles, err := repo.GetRandomPuzzles(ctx, filter, 0)
		require.NoError(t, err)
		assert.Empty(t, puzzles)

		// Test with very narrow rating range
		narrowFilter := common.LichessPuzzleFilter{
			MinRating:  1450,
			MaxRating:  1450,
			Themes:     nil,
			ExcludeIDs: nil,
		}
		puzzles, err = repo.GetRandomPuzzles(ctx, narrowFilter, 5)
		require.NoError(t, err)
		assert.LessOrEqual(t, len(puzzles), 1) // Only test002 has rating 1450
		if len(puzzles) > 0 {
			assert.Equal(t, 1450, puzzles[0].Rating)
		}
	})

	t.Run("provides good randomization", func(t *testing.T) {
		// Test that multiple calls return different orderings (probabilistic test)
		var results [][]string

		filter := common.LichessPuzzleFilter{
			MinRating:  1400,
			MaxRating:  1600,
			Themes:     nil,
			ExcludeIDs: nil,
		}

		for i := 0; i < 10; i++ {
			puzzles, err := repo.GetRandomPuzzles(ctx, filter, 3)
			require.NoError(t, err)
			require.Len(t, puzzles, 3)

			var ids []string
			for _, p := range puzzles {
				ids = append(ids, p.ID)
			}
			results = append(results, ids)
		}

		// Check that we got some variation in the results
		// Count unique first elements to verify randomization
		firstElements := make(map[string]int)
		for _, result := range results {
			if len(result) > 0 {
				firstElements[result[0]]++
			}
		}

		// We should have at least 2 different first elements in 10 tries
		assert.GreaterOrEqual(t, len(firstElements), 2, "Expected some variation in random results")
	})

	t.Run("performance with large exclude list", func(t *testing.T) {
		// Test performance with a large exclusion list
		largeExcludeList := make([]string, 1000)
		for i := range largeExcludeList {
			largeExcludeList[i] = fmt.Sprintf("exclude_%d", i)
		}

		filter := common.LichessPuzzleFilter{
			MinRating:  1400,
			MaxRating:  1600,
			Themes:     nil,
			ExcludeIDs: largeExcludeList,
		}
		puzzles, err := repo.GetRandomPuzzles(ctx, filter, 3)
		require.NoError(t, err)
		assert.Len(t, puzzles, 3) // Should still return our test puzzles
	})
}
