package repository

import (
	"context"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// InvitationCodeRepository implements IInvitationCodeRepository for database operations
type InvitationCodeRepository struct {
	db *gorm.DB
}

// NewInvitationCodeRepository creates a new instance of InvitationCodeRepository
func NewInvitationCodeRepository(db *gorm.DB) *InvitationCodeRepository {
	return &InvitationCodeRepository{db: db}
}

// Create creates a new invitation code
func (r *InvitationCodeRepository) Create(ctx context.Context, code *models.InvitationCode) error {
	if code.ID == "" {
		code.ID = uuid.New().String()
	}
	if code.CreatedAt.IsZero() {
		code.CreatedAt = time.Now()
	}
	return r.db.WithContext(ctx).Create(code).Error
}

// GetByID retrieves an invitation code by ID
func (r *InvitationCodeRepository) GetByID(ctx context.Context, id string) (*models.InvitationCode, error) {
	var code models.InvitationCode
	err := r.db.WithContext(ctx).First(&code, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &code, nil
}

// GetByCode retrieves an invitation code by its code string
func (r *InvitationCodeRepository) GetByCode(ctx context.Context, codeStr string) (*models.InvitationCode, error) {
	var code models.InvitationCode
	err := r.db.WithContext(ctx).First(&code, "code = ?", codeStr).Error
	if err != nil {
		return nil, err
	}
	return &code, nil
}

// Update updates an invitation code (e.g., to mark it as used)
func (r *InvitationCodeRepository) Update(ctx context.Context, code *models.InvitationCode) error {
	return r.db.WithContext(ctx).Save(code).Error
}

// ListValid retrieves a paginated list of valid (unused and not expired) invitation codes
func (r *InvitationCodeRepository) ListValid(ctx context.Context, offset int, limit int) ([]models.InvitationCode, int64, error) {
	var codes []models.InvitationCode
	var count int64

	// Base query for valid codes (not used and not expired)
	query := r.db.WithContext(ctx).Model(&models.InvitationCode{}).
		Where("used_at IS NULL").
		Where("expires_at IS NULL OR expires_at > ?", time.Now())

	// Get total count
	if err := query.Count(&count).Error; err != nil {
		return nil, 0, err
	}

	// Get paginated results
	if err := query.Offset(offset).Limit(limit).Find(&codes).Error; err != nil {
		return nil, 0, err
	}

	return codes, count, nil
}

// ListAll retrieves a paginated list of all invitation codes
func (r *InvitationCodeRepository) ListAll(ctx context.Context, offset int, limit int) ([]models.InvitationCode, int64, error) {
	var codes []models.InvitationCode
	var count int64

	// Get total count
	if err := r.db.WithContext(ctx).Model(&models.InvitationCode{}).Count(&count).Error; err != nil {
		return nil, 0, err
	}

	// Get paginated results
	if err := r.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&codes).Error; err != nil {
		return nil, 0, err
	}

	return codes, count, nil
}
