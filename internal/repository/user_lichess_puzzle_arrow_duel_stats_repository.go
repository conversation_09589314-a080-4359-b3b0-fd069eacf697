package repository

import (
	"context"

	"github.com/chessticize/chessticize-server/internal/models"
	"gorm.io/gorm"
)

type UserLichessPuzzleArrowDuelStatsRepository struct {
	db *gorm.DB
}

func NewUserLichessPuzzleArrowDuelStatsRepository(db *gorm.DB) IUserLichessPuzzleArrowDuelStatsRepository {
	return &UserLichessPuzzleArrowDuelStatsRepository{db: db}
}

// GetByUserIDAndPuzzleID retrieves arrow-duel stats for a specific user and lichess puzzle
func (r *UserLichessPuzzleArrowDuelStatsRepository) GetByUserIDAndPuzzleID(ctx context.Context, userID, puzzleID string) (*models.UserLichessPuzzleArrowDuelStats, error) {
	var stats models.UserLichessPuzzleArrowDuelStats
	err := r.db.WithContext(ctx).Where("user_id = ? AND lichess_puzzle_id = ?", userID, puzzleID).First(&stats).Error
	if err != nil {
		return nil, err
	}
	return &stats, nil
}

// GetDislikedPuzzleIDs retrieves all disliked lichess puzzle IDs for a specific user (arrow-duel mode)
func (r *UserLichessPuzzleArrowDuelStatsRepository) GetDislikedPuzzleIDs(ctx context.Context, userID string) ([]string, error) {
	var puzzleIDs []string
	err := r.db.WithContext(ctx).
		Model(&models.UserLichessPuzzleArrowDuelStats{}).
		Where("user_id = ? AND is_disliked = ?", userID, true).
		Pluck("lichess_puzzle_id", &puzzleIDs).Error
	return puzzleIDs, err
}
