package repository

import (
	"context"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type SessionTokenRepository struct {
	db *gorm.DB
}

// NewSessionTokenRepository creates a new session token repository
func NewSessionTokenRepository(db *gorm.DB) ISessionTokenRepository {
	return &SessionTokenRepository{db: db}
}

// <PERSON><PERSON> creates a new session token
func (r *SessionTokenRepository) Create(ctx context.Context, token *models.SessionToken) error {
	token.ID = uuid.New().String()
	now := time.Now()
	token.CreatedAt = now
	token.UpdatedAt = now
	return r.db.WithContext(ctx).Create(token).Error
}

// GetByToken retrieves a session token by its token string
func (r *SessionTokenRepository) GetByToken(ctx context.Context, token string) (*models.SessionToken, error) {
	var sessionToken models.SessionToken
	err := r.db.WithContext(ctx).Preload("User").First(&sessionToken, "token = ?", token).Error
	if err != nil {
		return nil, err
	}
	return &sessionToken, nil
}

// GetByID retrieves a session token by its ID
func (r *SessionTokenRepository) GetByID(ctx context.Context, id string) (*models.SessionToken, error) {
	var sessionToken models.SessionToken
	err := r.db.WithContext(ctx).Preload("User").First(&sessionToken, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &sessionToken, nil
}

// ListByUserID retrieves all session tokens for a specific user
func (r *SessionTokenRepository) ListByUserID(ctx context.Context, userID string) ([]models.SessionToken, error) {
	var tokens []models.SessionToken
	err := r.db.WithContext(ctx).Where("user_id = ?", userID).Order("created_at DESC").Find(&tokens).Error
	return tokens, err
}

// Update updates a session token
func (r *SessionTokenRepository) Update(ctx context.Context, token *models.SessionToken) error {
	token.UpdatedAt = time.Now()
	return r.db.WithContext(ctx).Save(token).Error
}

// Delete deletes a session token by ID
func (r *SessionTokenRepository) Delete(ctx context.Context, id string) error {
	return r.db.WithContext(ctx).Delete(&models.SessionToken{}, "id = ?", id).Error
}

// DeleteByUserID deletes all session tokens for a specific user
func (r *SessionTokenRepository) DeleteByUserID(ctx context.Context, userID string) error {
	return r.db.WithContext(ctx).Delete(&models.SessionToken{}, "user_id = ?", userID).Error
}

// DeleteExpired removes session tokens that have expired
func (r *SessionTokenRepository) DeleteExpired(ctx context.Context) (int64, error) {
	result := r.db.WithContext(ctx).Delete(&models.SessionToken{}, "expires_at < ?", time.Now())
	return result.RowsAffected, result.Error
}
