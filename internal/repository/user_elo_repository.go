package repository

import (
	"context"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"gorm.io/gorm"
)

type UserEloRepository struct {
	db *gorm.DB
}

func NewUserEloRepository(db *gorm.DB) IUserEloRepository {
	return &UserEloRepository{db: db}
}

// GetByUserIDAndEloType retrieves ELO rating for a specific user and ELO type
func (r *UserEloRepository) GetByUserIDAndEloType(ctx context.Context, userID string, eloType string) (*models.UserElo, error) {
	var userElo models.UserElo
	err := r.db.WithContext(ctx).Where("user_id = ? AND elo_type = ?", userID, eloType).First(&userElo).Error
	if err != nil {
		return nil, err
	}
	return &userElo, nil
}

// GetByUserID retrieves all ELO ratings for a specific user
func (r *UserEloRepository) GetByUserID(ctx context.Context, userID string) ([]models.UserElo, error) {
	var userElos []models.UserElo
	err := r.db.WithContext(ctx).Where("user_id = ?", userID).Order("elo_type ASC").Find(&userElos).Error
	if err != nil {
		return nil, err
	}
	return userElos, nil
}

// Save creates or updates a user's ELO rating for a specific type
func (r *UserEloRepository) Save(ctx context.Context, userElo *models.UserElo) error {
	now := time.Now()

	// Check if record already exists
	var existing models.UserElo
	err := r.db.WithContext(ctx).Where("user_id = ? AND elo_type = ?", userElo.UserID, userElo.EloType).First(&existing).Error

	if err == gorm.ErrRecordNotFound {
		// Record doesn't exist, create new one
		if userElo.CreatedAt.IsZero() {
			userElo.CreatedAt = now
		}
		userElo.UpdatedAt = now
		userElo.LastActiveAt = now
		return r.db.WithContext(ctx).Create(userElo).Error
	} else if err != nil {
		// Some other error occurred
		return err
	} else {
		// Record exists, update it
		userElo.ID = existing.ID
		userElo.CreatedAt = existing.CreatedAt // Preserve original creation time
		userElo.UpdatedAt = now
		userElo.LastActiveAt = now
		return r.db.WithContext(ctx).Save(userElo).Error
	}
}
