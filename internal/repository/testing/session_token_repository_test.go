package testing

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"
)

// hashToken hashes a token string like the auth service does
// This should match the hashSessionToken function in the auth service
func hashToken(token string) string {
	hasher := sha256.New()
	hasher.Write([]byte(token))
	return hex.EncodeToString(hasher.Sum(nil))
}

// TestSessionTokenRepository runs all tests for both real and fake session token repositories
func TestSessionTokenRepository(t *testing.T) {
	// Test with SQLite by default for faster tests
	provider := GetTestDBProvider(FakeRepository)
	defer provider.Cleanup(t)

	RunSessionTokenRepositoryTests(t, provider)
}

// TestRealSessionTokenRepository runs tests against the real PostgreSQL repository implementation
// This will be skipped if the test database is not available
func TestRealSessionTokenRepository(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping real database tests")
	}
	provider := GetTestDBProvider(RealRepository)
	defer provider.Cleanup(t)

	RunSessionTokenRepositoryTests(t, provider)
}

// RunSessionTokenRepositoryTests runs all tests for a session token repository
func RunSessionTokenRepositoryTests(t *testing.T, provider TestDBProvider) {
	userRepo := provider.GetUserRepository(t)
	sessionTokenRepo := provider.GetSessionTokenRepository(t)

	t.Run("Create", func(t *testing.T) {
		// Create a test user first
		user := CreateTestUser(t, userRepo)

		// Create a session token with hashed token
		rawToken := "test-token-" + uuid.New().String()
		token := &models.SessionToken{
			UserID:    user.ID,
			Token:     hashToken(rawToken), // Store the hash
			UserAgent: "Mozilla/5.0 Test Browser",
			ExpiresAt: time.Now().Add(30 * 24 * time.Hour), // 30 days
		}

		err := sessionTokenRepo.Create(context.Background(), token)
		require.NoError(t, err)
		assert.NotEmpty(t, token.ID)
		assert.NotZero(t, token.CreatedAt)
		assert.NotZero(t, token.UpdatedAt)
	})

	t.Run("GetByToken", func(t *testing.T) {
		// Create a test user first
		user := CreateTestUser(t, userRepo)

		// Create a session token
		rawToken := "test-token-" + uuid.New().String()
		tokenHash := hashToken(rawToken)
		token := &models.SessionToken{
			UserID:    user.ID,
			Token:     tokenHash, // Store the hash
			UserAgent: "Mozilla/5.0 Test Browser",
			ExpiresAt: time.Now().Add(30 * 24 * time.Hour),
		}

		err := sessionTokenRepo.Create(context.Background(), token)
		require.NoError(t, err)

		// Retrieve by token hash
		retrieved, err := sessionTokenRepo.GetByToken(context.Background(), tokenHash)
		require.NoError(t, err)
		assert.Equal(t, token.ID, retrieved.ID)
		assert.Equal(t, token.UserID, retrieved.UserID)
		assert.Equal(t, token.Token, retrieved.Token)
		assert.Equal(t, token.UserAgent, retrieved.UserAgent)
		assert.Equal(t, user.Email, retrieved.User.Email)
	})

	t.Run("GetByToken_NotFound", func(t *testing.T) {
		_, err := sessionTokenRepo.GetByToken(context.Background(), "non-existent-token")
		assert.Error(t, err)
		assert.ErrorIs(t, err, gorm.ErrRecordNotFound)
	})

	t.Run("GetByID", func(t *testing.T) {
		// Create a test user first
		user := CreateTestUser(t, userRepo)

		// Create a session token
		rawToken := "test-token-" + uuid.New().String()
		token := &models.SessionToken{
			UserID:    user.ID,
			Token:     hashToken(rawToken), // Store the hash
			UserAgent: "Mozilla/5.0 Test Browser",
			ExpiresAt: time.Now().Add(30 * 24 * time.Hour),
		}

		err := sessionTokenRepo.Create(context.Background(), token)
		require.NoError(t, err)

		// Retrieve by ID
		retrieved, err := sessionTokenRepo.GetByID(context.Background(), token.ID)
		require.NoError(t, err)
		assert.Equal(t, token.ID, retrieved.ID)
		assert.Equal(t, token.UserID, retrieved.UserID)
		assert.Equal(t, token.Token, retrieved.Token)
		assert.Equal(t, user.Email, retrieved.User.Email)
	})

	t.Run("ListByUserID", func(t *testing.T) {
		// Create a test user first
		user := CreateTestUser(t, userRepo)

		// Create multiple session tokens for the user
		token1 := &models.SessionToken{
			UserID:    user.ID,
			Token:     hashToken("test-token-1-" + uuid.New().String()),
			UserAgent: "Mozilla/5.0 Test Browser 1",
			ExpiresAt: time.Now().Add(30 * 24 * time.Hour),
		}
		token2 := &models.SessionToken{
			UserID:    user.ID,
			Token:     hashToken("test-token-2-" + uuid.New().String()),
			UserAgent: "Mozilla/5.0 Test Browser 2",
			ExpiresAt: time.Now().Add(30 * 24 * time.Hour),
		}

		err := sessionTokenRepo.Create(context.Background(), token1)
		require.NoError(t, err)
		err = sessionTokenRepo.Create(context.Background(), token2)
		require.NoError(t, err)

		// List tokens for the user
		tokens, err := sessionTokenRepo.ListByUserID(context.Background(), user.ID)
		require.NoError(t, err)
		assert.Len(t, tokens, 2)

		// Verify tokens are ordered by created_at DESC
		assert.True(t, tokens[0].CreatedAt.After(tokens[1].CreatedAt) || tokens[0].CreatedAt.Equal(tokens[1].CreatedAt))
	})

	t.Run("Update", func(t *testing.T) {
		// Create a test user first
		user := CreateTestUser(t, userRepo)

		// Create a session token
		token := &models.SessionToken{
			UserID:    user.ID,
			Token:     hashToken("test-token-" + uuid.New().String()),
			UserAgent: "Mozilla/5.0 Test Browser",
			ExpiresAt: time.Now().Add(30 * 24 * time.Hour),
		}

		err := sessionTokenRepo.Create(context.Background(), token)
		require.NoError(t, err)

		// Update the token
		newExpiresAt := time.Now().Add(60 * 24 * time.Hour) // 60 days
		token.ExpiresAt = newExpiresAt
		token.UserAgent = "Updated User Agent"

		err = sessionTokenRepo.Update(context.Background(), token)
		require.NoError(t, err)

		// Retrieve and verify the update
		retrieved, err := sessionTokenRepo.GetByID(context.Background(), token.ID)
		require.NoError(t, err)
		assert.Equal(t, "Updated User Agent", retrieved.UserAgent)
		assert.WithinDuration(t, newExpiresAt, retrieved.ExpiresAt, time.Second)
	})

	t.Run("Delete", func(t *testing.T) {
		// Create a test user first
		user := CreateTestUser(t, userRepo)

		// Create a session token
		token := &models.SessionToken{
			UserID:    user.ID,
			Token:     hashToken("test-token-" + uuid.New().String()),
			UserAgent: "Mozilla/5.0 Test Browser",
			ExpiresAt: time.Now().Add(30 * 24 * time.Hour),
		}

		err := sessionTokenRepo.Create(context.Background(), token)
		require.NoError(t, err)

		// Delete the token
		err = sessionTokenRepo.Delete(context.Background(), token.ID)
		require.NoError(t, err)

		// Verify it's deleted
		_, err = sessionTokenRepo.GetByID(context.Background(), token.ID)
		assert.Error(t, err)
		assert.ErrorIs(t, err, gorm.ErrRecordNotFound)
	})

	t.Run("DeleteByUserID", func(t *testing.T) {
		// Create a test user first
		user := CreateTestUser(t, userRepo)

		// Create multiple session tokens for the user
		token1 := &models.SessionToken{
			UserID:    user.ID,
			Token:     hashToken("test-token-1-" + uuid.New().String()),
			UserAgent: "Mozilla/5.0 Test Browser 1",
			ExpiresAt: time.Now().Add(30 * 24 * time.Hour),
		}
		token2 := &models.SessionToken{
			UserID:    user.ID,
			Token:     hashToken("test-token-2-" + uuid.New().String()),
			UserAgent: "Mozilla/5.0 Test Browser 2",
			ExpiresAt: time.Now().Add(30 * 24 * time.Hour),
		}

		err := sessionTokenRepo.Create(context.Background(), token1)
		require.NoError(t, err)
		err = sessionTokenRepo.Create(context.Background(), token2)
		require.NoError(t, err)

		// Delete all tokens for the user
		err = sessionTokenRepo.DeleteByUserID(context.Background(), user.ID)
		require.NoError(t, err)

		// Verify all tokens are deleted
		tokens, err := sessionTokenRepo.ListByUserID(context.Background(), user.ID)
		require.NoError(t, err)
		assert.Len(t, tokens, 0)
	})

	t.Run("DeleteExpired", func(t *testing.T) {
		// Create a test user first
		user := CreateTestUser(t, userRepo)

		// Create an expired token
		expiredToken := &models.SessionToken{
			UserID:    user.ID,
			Token:     hashToken("expired-token-" + uuid.New().String()),
			UserAgent: "Mozilla/5.0 Test Browser",
			ExpiresAt: time.Now().Add(-1 * time.Hour), // Expired 1 hour ago
		}

		// Create a valid token
		validToken := &models.SessionToken{
			UserID:    user.ID,
			Token:     hashToken("valid-token-" + uuid.New().String()),
			UserAgent: "Mozilla/5.0 Test Browser",
			ExpiresAt: time.Now().Add(30 * 24 * time.Hour), // Valid for 30 days
		}

		err := sessionTokenRepo.Create(context.Background(), expiredToken)
		require.NoError(t, err)
		err = sessionTokenRepo.Create(context.Background(), validToken)
		require.NoError(t, err)

		// Delete expired tokens
		deletedCount, err := sessionTokenRepo.DeleteExpired(context.Background())
		require.NoError(t, err)
		assert.Equal(t, int64(1), deletedCount)

		// Verify only the valid token remains
		tokens, err := sessionTokenRepo.ListByUserID(context.Background(), user.ID)
		require.NoError(t, err)
		assert.Len(t, tokens, 1)
		assert.Equal(t, validToken.ID, tokens[0].ID)
	})

	t.Run("IsExpired", func(t *testing.T) {
		// Test expired token
		expiredToken := &models.SessionToken{
			ExpiresAt: time.Now().Add(-1 * time.Hour),
		}
		assert.True(t, expiredToken.IsExpired())

		// Test valid token
		validToken := &models.SessionToken{
			ExpiresAt: time.Now().Add(1 * time.Hour),
		}
		assert.False(t, validToken.IsExpired())
	})
}
