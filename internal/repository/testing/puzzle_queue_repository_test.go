package testing

import (
	"context"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"
)

// TestPuzzleQueueRepository runs all tests for both real and fake puzzle queue repositories
func TestPuzzleQueueRepository(t *testing.T) {
	// Test with SQLite by default for faster tests
	provider := GetTestDBProvider(FakeRepository)
	defer provider.Cleanup(t)

	RunPuzzleQueueRepositoryTests(t, provider)
}

// TestRealPuzzleQueueRepository runs tests against the real PostgreSQL repository implementation
func TestRealPuzzleQueueRepository(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping real database tests")
	}
	provider := GetTestDBProvider(RealRepository)
	defer provider.Cleanup(t)

	RunPuzzleQueueRepositoryTests(t, provider)
}

// RunPuzzleQueueRepositoryTests runs all tests for a puzzle queue repository
func RunPuzzleQueueRepositoryTests(t *testing.T, provider TestDBProvider) {
	var db *gorm.DB
	isRealTest := false

	if p, ok := provider.(*PostgresTestDBProvider); ok {
		isRealTest = true
		db = p.GetDB(t)

		// Clean the puzzle_queue table for real tests
		err := db.Exec("DELETE FROM puzzle_queue").Error
		require.NoError(t, err, "Failed to clean puzzle_queue table before tests")
	}

	ctx := context.Background()

	// Create test puzzles that will be referenced by queue entries
	setupTestPuzzles := func(t *testing.T) {
		if isRealTest {
			// Create test puzzles in the database for foreign key constraints
			testPuzzles := []models.Puzzle{
				{ID: "puzzle-1", UserID: "user-1", GameID: "game-1", Theme: models.OpponentMistakeMissed, FEN: "test", Moves: []string{"e2e4"}, UserColor: models.White, PuzzleColor: models.Black},
				{ID: "puzzle-2", UserID: "user-1", GameID: "game-1", Theme: models.OwnMistakePunished, FEN: "test", Moves: []string{"e2e4"}, UserColor: models.White, PuzzleColor: models.Black},
				{ID: "puzzle-duplicate", UserID: "user-2", GameID: "game-2", Theme: models.OpponentMistakeMissed, FEN: "test", Moves: []string{"e2e4"}, UserColor: models.White, PuzzleColor: models.Black},
				{ID: "puzzle-due-opponent", UserID: "user-3", GameID: "game-3", Theme: models.OpponentMistakeMissed, FEN: "test", Moves: []string{"e2e4"}, UserColor: models.White, PuzzleColor: models.Black},
				{ID: "puzzle-due-own", UserID: "user-3", GameID: "game-3", Theme: models.OwnMistakePunished, FEN: "test", Moves: []string{"e2e4"}, UserColor: models.White, PuzzleColor: models.Black},
				{ID: "puzzle-future", UserID: "user-3", GameID: "game-3", Theme: models.OpponentBlunderMissed, FEN: "test", Moves: []string{"e2e4"}, UserColor: models.White, PuzzleColor: models.Black},
				{ID: "puzzle-update", UserID: "user-4", GameID: "game-4", Theme: models.OpponentMistakeMissed, FEN: "test", Moves: []string{"e2e4"}, UserColor: models.White, PuzzleColor: models.Black},
				{ID: "puzzle-mastery", UserID: "user-5", GameID: "game-5", Theme: models.OwnMistakePunished, FEN: "test", Moves: []string{"e2e4"}, UserColor: models.White, PuzzleColor: models.Black},
				{ID: "puzzle-remove", UserID: "user-6", GameID: "game-6", Theme: models.OpponentBlunderMissed, FEN: "test", Moves: []string{"e2e4"}, UserColor: models.White, PuzzleColor: models.Black},
				{ID: "puzzle-stats-1", UserID: "user-7", GameID: "game-7", Theme: models.OpponentMistakeMissed, FEN: "test", Moves: []string{"e2e4"}, UserColor: models.White, PuzzleColor: models.Black},
				{ID: "puzzle-stats-2", UserID: "user-7", GameID: "game-7", Theme: models.OwnMistakePunished, FEN: "test", Moves: []string{"e2e4"}, UserColor: models.White, PuzzleColor: models.Black},
				{ID: "puzzle-stats-3", UserID: "user-7", GameID: "game-7", Theme: models.OpponentBlunderMissed, FEN: "test", Moves: []string{"e2e4"}, UserColor: models.White, PuzzleColor: models.Black},
			}

			// Create test users first
			testUsers := []models.User{
				{ID: "user-1", Email: "<EMAIL>"},
				{ID: "user-2", Email: "<EMAIL>"},
				{ID: "user-3", Email: "<EMAIL>"},
				{ID: "user-4", Email: "<EMAIL>"},
				{ID: "user-5", Email: "<EMAIL>"},
				{ID: "user-6", Email: "<EMAIL>"},
				{ID: "user-7", Email: "<EMAIL>"},
			}

			// Create test games first
			testGames := []models.Game{
				{ID: "game-1", UserID: "user-1", Platform: models.ChessDotCom, ChessUsername: "user1", UserColor: models.White, CompressedPGN: []byte("test"), TimeControl: "5+0"},
				{ID: "game-2", UserID: "user-2", Platform: models.ChessDotCom, ChessUsername: "user2", UserColor: models.White, CompressedPGN: []byte("test"), TimeControl: "5+0"},
				{ID: "game-3", UserID: "user-3", Platform: models.ChessDotCom, ChessUsername: "user3", UserColor: models.White, CompressedPGN: []byte("test"), TimeControl: "5+0"},
				{ID: "game-4", UserID: "user-4", Platform: models.ChessDotCom, ChessUsername: "user4", UserColor: models.White, CompressedPGN: []byte("test"), TimeControl: "5+0"},
				{ID: "game-5", UserID: "user-5", Platform: models.ChessDotCom, ChessUsername: "user5", UserColor: models.White, CompressedPGN: []byte("test"), TimeControl: "5+0"},
				{ID: "game-6", UserID: "user-6", Platform: models.ChessDotCom, ChessUsername: "user6", UserColor: models.White, CompressedPGN: []byte("test"), TimeControl: "5+0"},
				{ID: "game-7", UserID: "user-7", Platform: models.ChessDotCom, ChessUsername: "user7", UserColor: models.White, CompressedPGN: []byte("test"), TimeControl: "5+0"},
			}

			// Insert test data
			for _, user := range testUsers {
				db.Create(&user)
			}
			for _, game := range testGames {
				db.Create(&game)
			}
			for _, puzzle := range testPuzzles {
				db.Create(&puzzle)
			}
		}
	}

	// Function to run a test case, potentially within a transaction
	runTestCase := func(name string, testFunc func(t *testing.T, repo repository.IPuzzleQueueRepository)) {
		t.Run(name, func(t *testing.T) {
			var currentRepo repository.IPuzzleQueueRepository
			var tx *gorm.DB

			if isRealTest {
				// Clean all related tables before each test to avoid foreign key issues
				err := db.Exec("DELETE FROM puzzle_queue").Error
				require.NoError(t, err, "Failed to clean puzzle_queue before test")
				err = db.Exec("DELETE FROM puzzles").Error
				require.NoError(t, err, "Failed to clean puzzles before test")
				err = db.Exec("DELETE FROM games").Error
				require.NoError(t, err, "Failed to clean games before test")
				err = db.Exec("DELETE FROM users WHERE id LIKE 'user-%'").Error
				require.NoError(t, err, "Failed to clean test users before test")

				// Setup test data for each test
				setupTestPuzzles(t)

				// Start a fresh transaction
				tx = db.Begin()
				require.NoError(t, tx.Error)
				defer tx.Rollback()

				// Create repo using the transaction DB
				currentRepo = repository.NewPuzzleQueueRepository(tx)
			} else {
				// For fake tests, get a fresh repo instance
				currentRepo = provider.GetPuzzleQueueRepository(t)
			}

			testFunc(t, currentRepo)
		})
	}

	runTestCase("AddPuzzlesToQueue", func(t *testing.T, repo repository.IPuzzleQueueRepository) {
		userID := "user-1"
		puzzles := []models.PuzzleQueueEntry{
			{
				ID:          uuid.New().String(),
				UserID:      userID,
				PuzzleID:    "puzzle-1",
				PuzzleTheme: models.OpponentMistakeMissed,
				MistakeBy:   "opponent",
			},
			{
				ID:          uuid.New().String(),
				UserID:      userID,
				PuzzleID:    "puzzle-2",
				PuzzleTheme: models.OwnMistakePunished,
				MistakeBy:   "own",
			},
		}

		// Add puzzles to queue
		addedCount, err := repo.AddPuzzlesToQueue(ctx, userID, puzzles)
		require.NoError(t, err)
		assert.Equal(t, 2, addedCount)

		// Verify puzzles were added
		stats, err := repo.GetQueueStats(ctx, userID)
		require.NoError(t, err)
		assert.Equal(t, int64(2), stats.TotalQueued)
		assert.Equal(t, int64(1), stats.OpponentMistakes)
		assert.Equal(t, int64(1), stats.OwnMistakes)
	})

	runTestCase("AddDuplicatePuzzles", func(t *testing.T, repo repository.IPuzzleQueueRepository) {
		userID := "user-2"
		puzzle := models.PuzzleQueueEntry{
			ID:          uuid.New().String(),
			UserID:      userID,
			PuzzleID:    "puzzle-duplicate",
			PuzzleTheme: models.OpponentMistakeMissed,
			MistakeBy:   "opponent",
		}

		// Add puzzle first time
		addedCount, err := repo.AddPuzzlesToQueue(ctx, userID, []models.PuzzleQueueEntry{puzzle})
		require.NoError(t, err)
		assert.Equal(t, 1, addedCount)

		// Try to add same puzzle again (should fail due to unique constraint)
		puzzle.ID = uuid.New().String() // New ID but same user_id + puzzle_id
		_, err = repo.AddPuzzlesToQueue(ctx, userID, []models.PuzzleQueueEntry{puzzle})

		if isRealTest {
			// Real database should enforce unique constraint
			assert.Error(t, err)
		}
		// For fake repository, we don't enforce constraints in unit tests
	})

	runTestCase("GetDuePuzzles", func(t *testing.T, repo repository.IPuzzleQueueRepository) {
		userID := "user-3"
		now := time.Now()

		// Add puzzles with different due dates and mistake types
		puzzles := []models.PuzzleQueueEntry{
			{
				ID:          uuid.New().String(),
				UserID:      userID,
				PuzzleID:    "puzzle-due-opponent",
				PuzzleTheme: models.OpponentMistakeMissed,
				MistakeBy:   "opponent",
				DueAt:       now.Add(-1 * time.Hour), // Due 1 hour ago
			},
			{
				ID:          uuid.New().String(),
				UserID:      userID,
				PuzzleID:    "puzzle-due-own",
				PuzzleTheme: models.OwnMistakePunished,
				MistakeBy:   "own",
				DueAt:       now.Add(-30 * time.Minute), // Due 30 minutes ago
			},
			{
				ID:          uuid.New().String(),
				UserID:      userID,
				PuzzleID:    "puzzle-future",
				PuzzleTheme: models.OpponentBlunderMissed,
				MistakeBy:   "opponent",
				DueAt:       now.Add(1 * time.Hour), // Due in 1 hour
			},
		}

		_, err := repo.AddPuzzlesToQueue(ctx, userID, puzzles)
		require.NoError(t, err)

		// Get all due puzzles
		duePuzzles, err := repo.GetDuePuzzles(ctx, userID, nil, 10)
		require.NoError(t, err)
		assert.Len(t, duePuzzles, 2) // Only 2 should be due

		// Get due puzzles filtered by mistake_by = "opponent"
		opponentPuzzles, err := repo.GetDuePuzzles(ctx, userID, stringPtr("opponent"), 10)
		require.NoError(t, err)
		assert.Len(t, opponentPuzzles, 1)
		assert.Equal(t, "opponent", opponentPuzzles[0].MistakeBy)

		// Get due puzzles filtered by mistake_by = "own"
		ownPuzzles, err := repo.GetDuePuzzles(ctx, userID, stringPtr("own"), 10)
		require.NoError(t, err)
		assert.Len(t, ownPuzzles, 1)
		assert.Equal(t, "own", ownPuzzles[0].MistakeBy)
	})

	runTestCase("UpdateAfterAttempt", func(t *testing.T, repo repository.IPuzzleQueueRepository) {
		userID := "user-4"
		puzzleID := "puzzle-update"

		// Add a puzzle to queue
		puzzle := models.PuzzleQueueEntry{
			ID:                 uuid.New().String(),
			UserID:             userID,
			PuzzleID:           puzzleID,
			PuzzleTheme:        models.OpponentMistakeMissed,
			MistakeBy:          "opponent",
			DueAt:              time.Now(),
			AttemptsSinceAdded: 0,
			ConsecutiveCorrect: 0,
		}

		_, err := repo.AddPuzzlesToQueue(ctx, userID, []models.PuzzleQueueEntry{puzzle})
		require.NoError(t, err)

		// Test correct attempt
		err = repo.UpdateAfterAttempt(ctx, userID, puzzleID, true)
		require.NoError(t, err)

		// Verify puzzle was updated
		entry, err := repo.GetByUserIDAndPuzzleID(ctx, userID, puzzleID)
		require.NoError(t, err)
		assert.Equal(t, 1, entry.AttemptsSinceAdded)
		assert.Equal(t, 1, entry.ConsecutiveCorrect)
		assert.True(t, entry.DueAt.After(time.Now().Add(24*time.Hour))) // Should be due in ~2 days

		// Test incorrect attempt
		err = repo.UpdateAfterAttempt(ctx, userID, puzzleID, false)
		require.NoError(t, err)

		// Verify puzzle was updated
		entry, err = repo.GetByUserIDAndPuzzleID(ctx, userID, puzzleID)
		require.NoError(t, err)
		assert.Equal(t, 2, entry.AttemptsSinceAdded)
		assert.Equal(t, 0, entry.ConsecutiveCorrect)                    // Reset to 0
		assert.True(t, entry.DueAt.After(time.Now().Add(20*time.Hour))) // Should be due in ~24 hours
	})

	runTestCase("UpdateAfterAttemptMastery", func(t *testing.T, repo repository.IPuzzleQueueRepository) {
		userID := "user-5"
		puzzleID := "puzzle-mastery"

		// Add a puzzle to queue with 4 consecutive correct attempts
		puzzle := models.PuzzleQueueEntry{
			ID:                 uuid.New().String(),
			UserID:             userID,
			PuzzleID:           puzzleID,
			PuzzleTheme:        models.OwnMistakePunished,
			MistakeBy:          "own",
			DueAt:              time.Now(),
			AttemptsSinceAdded: 4,
			ConsecutiveCorrect: 4,
		}

		_, err := repo.AddPuzzlesToQueue(ctx, userID, []models.PuzzleQueueEntry{puzzle})
		require.NoError(t, err)

		// Verify puzzle exists
		entry, err := repo.GetByUserIDAndPuzzleID(ctx, userID, puzzleID)
		require.NoError(t, err)
		assert.NotNil(t, entry)

		// Make 5th correct attempt (should remove from queue)
		err = repo.UpdateAfterAttempt(ctx, userID, puzzleID, true)
		require.NoError(t, err)

		// Verify puzzle was removed from queue
		_, err = repo.GetByUserIDAndPuzzleID(ctx, userID, puzzleID)
		assert.Error(t, err)
		assert.True(t, err == gorm.ErrRecordNotFound || err.Error() == "record not found")
	})

	runTestCase("RemoveFromQueue", func(t *testing.T, repo repository.IPuzzleQueueRepository) {
		userID := "user-6"
		puzzleID := "puzzle-remove"

		// Add a puzzle to queue
		puzzle := models.PuzzleQueueEntry{
			ID:          uuid.New().String(),
			UserID:      userID,
			PuzzleID:    puzzleID,
			PuzzleTheme: models.OpponentBlunderMissed,
			MistakeBy:   "opponent",
		}

		_, err := repo.AddPuzzlesToQueue(ctx, userID, []models.PuzzleQueueEntry{puzzle})
		require.NoError(t, err)

		// Verify puzzle exists
		entry, err := repo.GetByUserIDAndPuzzleID(ctx, userID, puzzleID)
		require.NoError(t, err)
		assert.NotNil(t, entry)

		// Remove puzzle from queue
		err = repo.RemoveFromQueue(ctx, userID, puzzleID)
		require.NoError(t, err)

		// Verify puzzle was removed
		_, err = repo.GetByUserIDAndPuzzleID(ctx, userID, puzzleID)
		assert.Error(t, err)
	})

	runTestCase("GetQueueStats", func(t *testing.T, repo repository.IPuzzleQueueRepository) {
		userID := "user-7"
		now := time.Now()

		// Add various puzzles
		puzzles := []models.PuzzleQueueEntry{
			{
				ID:          uuid.New().String(),
				UserID:      userID,
				PuzzleID:    "puzzle-stats-1",
				PuzzleTheme: models.OpponentMistakeMissed,
				MistakeBy:   "opponent",
				DueAt:       now.Add(-1 * time.Hour), // Due
			},
			{
				ID:          uuid.New().String(),
				UserID:      userID,
				PuzzleID:    "puzzle-stats-2",
				PuzzleTheme: models.OwnMistakePunished,
				MistakeBy:   "own",
				DueAt:       now.Add(-30 * time.Minute), // Due
			},
			{
				ID:          uuid.New().String(),
				UserID:      userID,
				PuzzleID:    "puzzle-stats-3",
				PuzzleTheme: models.OpponentBlunderMissed,
				MistakeBy:   "opponent",
				DueAt:       now.Add(1 * time.Hour), // Future
			},
		}

		_, err := repo.AddPuzzlesToQueue(ctx, userID, puzzles)
		require.NoError(t, err)

		// Get queue statistics
		stats, err := repo.GetQueueStats(ctx, userID)
		require.NoError(t, err)

		assert.Equal(t, int64(3), stats.TotalQueued)
		assert.Equal(t, int64(2), stats.OpponentMistakes)
		assert.Equal(t, int64(1), stats.OwnMistakes)
		// Due today count might vary based on implementation
		assert.GreaterOrEqual(t, stats.DueToday, int64(2))
	})
}

// Helper function to create string pointer
func stringPtr(s string) *string {
	return &s
}
