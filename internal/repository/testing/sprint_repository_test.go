package testing

import (
	"context"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/repository/fake"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestSprintRepository(t *testing.T) {
	providers := []struct {
		name     string
		provider TestDBProvider
	}{
		{"PostgreSQL", NewPostgresTestDBProvider()},
		{"SQLite", NewSQLiteTestDBProvider()},
	}

	for _, p := range providers {
		t.Run(p.name, func(t *testing.T) {
			testSprintRepository(t, p.provider, p.name)
		})
	}
}

func testSprintRepository(t *testing.T, provider TestDBProvider, providerName string) {
	db := provider.GetDB(t)
	defer provider.Cleanup(t)

	var sprintRepo repository.ISprintRepository
	if providerName == "SQLite" {
		fakeDB := &fake.DB{DB: db}
		sprintRepo = fake.NewSprintRepository(fakeDB)
	} else {
		sprintRepo = repository.NewSprintRepository(db)
	}

	ctx := context.Background()

	// Create a test user first with unique email
	user := &models.User{
		ID:    uuid.New().String(),
		Email: "test-" + uuid.New().String() + "@example.com",
	}
	require.NoError(t, db.Create(user).Error)

	t.Run("Create and GetByID", func(t *testing.T) {
		sprint := &models.Sprint{
			UserID:           user.ID,
			EloType:          "overall_intuition",
			Status:           models.SprintStatusActive,
			TargetPuzzles:    20,
			TimeLimitSeconds: 600,
			PuzzlesSolved:    0,
			MistakesMade:     0,
			StartedAt:        time.Now(),
			EloRatingBefore:  1200,
		}

		// Create sprint
		err := sprintRepo.Create(ctx, sprint)
		require.NoError(t, err)
		assert.NotEmpty(t, sprint.ID)
		assert.False(t, sprint.CreatedAt.IsZero())
		assert.False(t, sprint.UpdatedAt.IsZero())

		// Get sprint by ID
		retrieved, err := sprintRepo.GetByID(ctx, sprint.ID)
		require.NoError(t, err)
		assert.Equal(t, sprint.ID, retrieved.ID)
		assert.Equal(t, sprint.UserID, retrieved.UserID)
		assert.Equal(t, sprint.EloType, retrieved.EloType)
		assert.Equal(t, sprint.Status, retrieved.Status)
		assert.Equal(t, sprint.TargetPuzzles, retrieved.TargetPuzzles)
		assert.Equal(t, sprint.TimeLimitSeconds, retrieved.TimeLimitSeconds)
		assert.Equal(t, sprint.EloRatingBefore, retrieved.EloRatingBefore)
	})

	t.Run("Update", func(t *testing.T) {
		sprint := &models.Sprint{
			UserID:           user.ID,
			EloType:          "overall_intuition",
			Status:           models.SprintStatusActive,
			TargetPuzzles:    20,
			TimeLimitSeconds: 600,
			PuzzlesSolved:    0,
			MistakesMade:     0,
			StartedAt:        time.Now(),
			EloRatingBefore:  1200,
		}

		// Create sprint
		err := sprintRepo.Create(ctx, sprint)
		require.NoError(t, err)

		// Update sprint
		originalUpdatedAt := sprint.UpdatedAt
		time.Sleep(time.Millisecond) // Ensure different timestamp
		sprint.PuzzlesSolved = 5
		sprint.MistakesMade = 1
		sprint.Status = models.SprintStatusCompletedSuccess
		endTime := time.Now()
		sprint.EndedAt = &endTime
		duration := 300
		sprint.DurationSeconds = &duration
		eloAfter := 1220
		sprint.EloRatingAfter = &eloAfter
		eloChange := 20
		sprint.EloChange = &eloChange

		err = sprintRepo.Update(ctx, sprint)
		require.NoError(t, err)
		assert.True(t, sprint.UpdatedAt.After(originalUpdatedAt))

		// Verify update
		retrieved, err := sprintRepo.GetByID(ctx, sprint.ID)
		require.NoError(t, err)
		assert.Equal(t, 5, retrieved.PuzzlesSolved)
		assert.Equal(t, 1, retrieved.MistakesMade)
		assert.Equal(t, models.SprintStatusCompletedSuccess, retrieved.Status)
		assert.NotNil(t, retrieved.EndedAt)
		assert.NotNil(t, retrieved.DurationSeconds)
		assert.Equal(t, 300, *retrieved.DurationSeconds)
		assert.NotNil(t, retrieved.EloRatingAfter)
		assert.Equal(t, 1220, *retrieved.EloRatingAfter)
		assert.NotNil(t, retrieved.EloChange)
		assert.Equal(t, 20, *retrieved.EloChange)
	})

	t.Run("ListByUserID", func(t *testing.T) {
		// Create multiple sprints for the user
		sprints := []*models.Sprint{
			{
				UserID:           user.ID,
				EloType:          "overall_intuition",
				Status:           models.SprintStatusCompletedSuccess,
				TargetPuzzles:    20,
				TimeLimitSeconds: 600,
				StartedAt:        time.Now().Add(-2 * time.Hour),
				EloRatingBefore:  1200,
			},
			{
				UserID:           user.ID,
				EloType:          "overall_intuition",
				Status:           models.SprintStatusActive,
				TargetPuzzles:    20,
				TimeLimitSeconds: 600,
				StartedAt:        time.Now().Add(-1 * time.Hour),
				EloRatingBefore:  1220,
			},
		}

		for _, sprint := range sprints {
			err := sprintRepo.Create(ctx, sprint)
			require.NoError(t, err)
		}

		// Test basic listing
		result, total, err := sprintRepo.ListByUserID(ctx, user.ID, repository.SprintFilter{}, 0, 10)
		require.NoError(t, err)
		assert.GreaterOrEqual(t, len(result), 2)
		assert.GreaterOrEqual(t, total, int64(2))

		// Test filtering by status
		filter := repository.SprintFilter{
			Status: []models.SprintStatus{models.SprintStatusActive},
		}
		result, _, err = sprintRepo.ListByUserID(ctx, user.ID, filter, 0, 10)
		require.NoError(t, err)
		assert.GreaterOrEqual(t, len(result), 1)
		for _, sprint := range result {
			assert.Equal(t, models.SprintStatusActive, sprint.Status)
		}

		// Test filtering by elo type
		eloType := "overall_intuition"
		filter = repository.SprintFilter{
			EloType: &eloType,
		}
		result, _, err = sprintRepo.ListByUserID(ctx, user.ID, filter, 0, 10)
		require.NoError(t, err)
		assert.GreaterOrEqual(t, len(result), 2)
		for _, sprint := range result {
			assert.Equal(t, "overall_intuition", sprint.EloType)
		}
	})
}
