package testing

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/dbtypes"
	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository" // Needed for setup
	"github.com/chessticize/chessticize-server/internal/repository/fake"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"
)

// TestTaskRepository runs all tests for both real and fake task repositories
func TestTaskRepository(t *testing.T) {
	// Test with SQLite by default for faster tests
	provider := GetTestDBProvider(FakeRepository)
	defer provider.Cleanup(t)

	RunTaskRepositoryTests(t, provider)
}

// TestRealTaskRepository runs tests against the real PostgreSQL repository implementation
// This will be skipped if the test database is not available
func TestRealTaskRepository(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping real database tests")
	}
	provider := GetTestDBProvider(RealRepository)
	defer provider.Cleanup(t)

	RunTaskRepositoryTests(t, provider)
}

// RunTaskRepositoryTests runs all tests for a task repository
func RunTaskRepositoryTests(t *testing.T, provider TestDBProvider) {
	// For real repository tests, use a transaction that gets rolled back.
	var db *gorm.DB
	isRealTest := false

	if p, ok := provider.(*PostgresTestDBProvider); ok {
		isRealTest = true
		db = p.GetDB(t) // Get the DB connection for transactions

		// Clean the tasks table for real tests
		err := db.Exec("DELETE FROM tasks").Error
		require.NoError(t, err, "Failed to clean tasks table before tests")
	}

	// Get a shared context
	ctx := context.Background()

	// Function to run a test case, potentially within a transaction.
	runTestCase := func(name string, testFunc func(t *testing.T, repo repository.ITaskRepository)) {
		t.Run(name, func(t *testing.T) {
			var currentRepo repository.ITaskRepository
			var tx *gorm.DB

			if isRealTest {
				// Clean tasks table before each test to prevent interference
				err := db.Exec("DELETE FROM tasks").Error
				require.NoError(t, err, "Failed to clean tasks before test")

				// Start a fresh transaction
				tx = db.Begin()
				require.NoError(t, tx.Error)
				defer tx.Rollback() // Ensure rollback

				// Create repo using the transaction DB
				currentRepo = repository.NewTaskRepository(tx)
			} else {
				// For fake tests, get a fresh repo instance
				currentRepo = provider.GetTaskRepository(t)
			}

			// Pass the current context too
			testFunc(t, currentRepo)
		})
	}

	// --- Use runTestCase for all subtests ---

	runTestCase("CreateAndGetTask", func(t *testing.T, repo repository.ITaskRepository) {
		taskData := models.FetchChessGamesData{ChessProfileID: "123"}

		taskToCreate := &models.Task{
			UserID:   "1",
			TaskType: models.FetchChessGamesTask,
			TaskData: mustMarshal(t, taskData),
		}

		// Declare err before using it in repo.Create
		var err error
		err = repo.Create(ctx, taskToCreate)
		require.NoError(t, err, "Failed to create task")
		require.NotEmpty(t, taskToCreate.ID, "Task ID should be set after creation")
		require.Equal(t, models.TaskStatusPending, taskToCreate.Status, "Task status should be pending after creation")
		require.NotZero(t, taskToCreate.CreatedAt, "CreatedAt should be set")
		require.WithinDuration(t, time.Now(), taskToCreate.CreatedAt, 10*time.Second, "CreatedAt should be recent")
		require.WithinDuration(t, taskToCreate.CreatedAt, taskToCreate.UpdatedAt, 5*time.Second, "UpdatedAt should be close to CreatedAt on creation")
		require.Equal(t, taskToCreate.CreatedAt, taskToCreate.ScheduledAt, "ScheduledAt should default to CreatedAt")

		// Get the task back
		retrievedTask, err := repo.GetByID(ctx, taskToCreate.ID)
		require.NoError(t, err, "Failed to get task by ID")
		require.NotNil(t, retrievedTask, "Retrieved task should not be nil")

		// Assertions
		assert.Equal(t, taskToCreate.ID, retrievedTask.ID)
		assert.Equal(t, taskToCreate.UserID, retrievedTask.UserID)
		assert.Equal(t, taskToCreate.TaskType, retrievedTask.TaskType)
		assert.Equal(t, models.TaskStatusPending, retrievedTask.Status)
		assert.Equal(t, 0, retrievedTask.Attempts)
		assert.False(t, retrievedTask.WorkerID.Valid)
		assert.False(t, retrievedTask.PickedUpAt.Valid)
		// assert the updatedAt is within a reasonable time range
		assert.WithinDuration(t, taskToCreate.UpdatedAt, retrievedTask.UpdatedAt, 10*time.Second, "UpdatedAt should be recent")
		assert.False(t, retrievedTask.CreatedAt.IsZero(), "Retrieved CreatedAt should not be zero")
		assert.False(t, retrievedTask.UpdatedAt.IsZero(), "Retrieved UpdatedAt should not be zero")
		assert.Equal(t, retrievedTask.CreatedAt, retrievedTask.ScheduledAt, "Retrieved ScheduledAt should equal CreatedAt on default creation")

		// Verify data unmarshaling
		var retrievedData models.FetchChessGamesData
		err = json.Unmarshal(retrievedTask.TaskData, &retrievedData)
		require.NoError(t, err)
		assert.Equal(t, taskData.ChessProfileID, retrievedData.ChessProfileID)
	})

	runTestCase("GetNonExistentTask", func(t *testing.T, repo repository.ITaskRepository) {
		retrievedTask, err := repo.GetByID(ctx, "99999") // Use an unlikely ID

		assert.ErrorIs(t, err, repository.ErrNotFound, "Expected ErrNotFound for non-existent ID")
		assert.Nil(t, retrievedTask, "Retrieved task should be nil for non-existent ID")
	})

	runTestCase("ListByUserID", func(t *testing.T, repo repository.ITaskRepository) {
		// For real tests, clean any existing tasks that might interfere
		if isRealTest {
			// Delete existing tasks for user 1, 2, and 3 that might interfere with test
			err := db.Exec("DELETE FROM tasks WHERE user_id IN ('1001', '1002', '1003')").Error
			require.NoError(t, err, "Failed to clean user tasks before test")
		}

		// Create test users' tasks with unique user IDs to isolate from other tests
		// Using high IDs to avoid conflicts with other tests
		user1 := "1001"
		user2 := "1002"
		user3 := "1003"

		// We'll create these tasks in a specific order to test the task ordering
		var task1, task2 *models.Task

		// Create tasks for user 1
		// Task 1 - pending (older)
		task1 = createTask(t, repo, ctx, user1, models.FetchChessGamesTask, map[string]interface{}{
			"chess_profile_id": "1",
		})

		// Sleep a bit to ensure different creation times in real databases
		if isRealTest {
			time.Sleep(20 * time.Millisecond)
		}

		// Task 2 - completed (newer)
		task2 = createTask(t, repo, ctx, user1, models.EvaluateChessGameTask, map[string]interface{}{
			"game_id": "10",
		})
		task2.Status = models.TaskStatusCompleted
		current, err := repo.GetByID(ctx, task2.ID)
		require.NoError(t, err)
		err = repo.Update(ctx, task2, current.UpdatedAt)
		require.NoError(t, err)

		// Create failed task for user 1
		taskFailed := createTask(t, repo, ctx, user1, models.FetchChessGamesTask, map[string]interface{}{
			"chess_profile_id": "1",
		})
		taskFailed.Status = models.TaskStatusFailed
		taskFailed.Error = dbtypes.NullString{NullString: sql.NullString{String: "Something went wrong", Valid: true}}
		current, err = repo.GetByID(ctx, taskFailed.ID)
		require.NoError(t, err)
		err = repo.Update(ctx, taskFailed, current.UpdatedAt)
		require.NoError(t, err)

		// Create task for user 2
		createTask(t, repo, ctx, user2, models.FetchChessGamesTask, map[string]interface{}{
			"chess_profile_id": "2",
		})

		// For test verification, fetch the actual tasks from DB
		task1Fetched, err := repo.GetByID(ctx, task1.ID)
		require.NoError(t, err)
		task2Fetched, err := repo.GetByID(ctx, task2.ID)
		require.NoError(t, err)

		// Define a test case helper to avoid duplication
		type testCase struct {
			name             string
			userID           string
			status           []models.TaskStatus // nil means all statuses
			expectedCount    int                 // how many tasks should be returned
			checkOrder       bool                // whether to check the order of tasks
			expectedOrderIDs []string            // expected task IDs in the correct order
		}

		testCases := []testCase{
			{
				name:          "User 1, Pending status",
				userID:        user1,
				status:        []models.TaskStatus{models.TaskStatusPending},
				expectedCount: 1,
			},
			{
				name:          "User 1, Completed status",
				userID:        user1,
				status:        []models.TaskStatus{models.TaskStatusCompleted},
				expectedCount: 1,
			},
			{
				name:             "User 1, All statuses",
				userID:           user1,
				status:           []models.TaskStatus{models.TaskStatusPending, models.TaskStatusCompleted}, // Explicitly include only pending and completed
				expectedCount:    2,                                                                         // Only count pending and completed, not failed
				checkOrder:       true,
				expectedOrderIDs: []string{task2Fetched.ID, task1Fetched.ID}, // Most recent (task2) first
			},
			{
				name:          "User 2, Pending status",
				userID:        user2,
				status:        []models.TaskStatus{models.TaskStatusPending},
				expectedCount: 1,
			},
			{
				name:          "User 2, All statuses",
				userID:        user2,
				status:        nil,
				expectedCount: 1,
			},
			{
				name:          "User 3 (no tasks), All statuses",
				userID:        user3,
				status:        nil,
				expectedCount: 0,
			},
			{
				name:          "User 1, Non-matching status",
				userID:        user1,
				status:        []models.TaskStatus{models.TaskStatusInProgress},
				expectedCount: 0,
			},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				// Run the ListByUserID method
				tasks, err := repo.ListByUserID(ctx, tc.userID, tc.status)
				require.NoError(t, err)

				assert.Equal(t, tc.expectedCount, len(tasks), "Expected %d tasks but got %d: %v",
					tc.expectedCount, len(tasks), tasks)

				// If we need to check order, verify the task IDs match the expected order
				if tc.checkOrder && len(tasks) > 0 && len(tc.expectedOrderIDs) > 0 {
					// For the all statuses case, we only care about the specific expected IDs
					if len(tc.expectedOrderIDs) == len(tasks) {
						var actualIDs []string
						for _, task := range tasks {
							actualIDs = append(actualIDs, task.ID)
						}
						assert.Equal(t, tc.expectedOrderIDs, actualIDs, "Task order incorrect")
					} else {
						// For other cases where we might get more tasks than expected,
						// check that the expected IDs are present in the right order
						var foundIDs []string
						taskMap := make(map[string]struct{})

						// First build a map of expected IDs
						for _, id := range tc.expectedOrderIDs {
							taskMap[id] = struct{}{}
						}

						// Collect matching IDs in the order they appear
						for _, task := range tasks {
							if _, ok := taskMap[task.ID]; ok {
								foundIDs = append(foundIDs, task.ID)
							}
						}

						// Make sure the ones we found match the expected order
						assert.Equal(t, tc.expectedOrderIDs, foundIDs, "Task order incorrect")
					}
				}
			})
		}
	})

	runTestCase("ClaimNextPending", func(t *testing.T, repo repository.ITaskRepository) {
		// For real tests, ensure no pending tasks exist that could interfere with the test
		if isRealTest {
			// Delete all existing tasks that might interfere with this test
			err := db.Exec("DELETE FROM tasks").Error
			require.NoError(t, err, "Failed to clean tasks before test")
		}

		// 1. First verify no tasks exist initially
		tasks, err := repo.ListByUserID(ctx, "9999", nil) // Use a user ID unlikely to have tasks
		require.NoError(t, err)
		require.Len(t, tasks, 0, "Expected no tasks initially")

		// Attempt to claim - should get nil result
		claimed, err := repo.ClaimNextPending(ctx, "worker-1")
		require.NoError(t, err)
		require.Nil(t, claimed, "Should return nil when no pending tasks")

		// 2. Create tasks with unique user IDs to prevent interference
		// We don't need to use the task1/task2 variables directly in this test
		// since we're testing the user-based selection, not specific task selection
		_ = createTask(t, repo, ctx, "2001", models.FetchChessGamesTask, models.FetchChessGamesData{ChessProfileID: "1"})
		if isRealTest {
			time.Sleep(50 * time.Millisecond) // Longer sleep for more reliable ordering
		}
		_ = createTask(t, repo, ctx, "2001", models.EvaluateChessGameTask, models.EvaluateChessGameData{GameID: "10"})
		if isRealTest {
			time.Sleep(50 * time.Millisecond)
		}
		task3 := createTask(t, repo, ctx, "2002", models.GenerateChessPuzzlesTask, models.GenerateChessPuzzlesData{GameID: "20"})

		// Mark task3 as completed
		task3.Status = models.TaskStatusCompleted
		currentTask3, err := repo.GetByID(ctx, task3.ID)
		require.NoError(t, err)
		err = repo.Update(ctx, task3, currentTask3.UpdatedAt)
		require.NoError(t, err)

		// Verify our tasks exist as expected
		pendingTasks, err := repo.ListByUserID(ctx, "2001", []models.TaskStatus{models.TaskStatusPending})
		require.NoError(t, err)
		require.Len(t, pendingTasks, 2, "Should have 2 pending tasks")

		// 3. Claim a pending task - with the new logic, it should be from user 2001 since that's the only user with pending tasks
		claimed1, err := repo.ClaimNextPending(ctx, "worker-1")
		require.NoError(t, err)
		require.NotNil(t, claimed1, "Should have claimed a task")

		// Should be from user 2001 (either task1 or task2)
		assert.Equal(t, "2001", claimed1.UserID, "Should claim a task from user 2001")
		assert.Equal(t, models.TaskStatusInProgress, claimed1.Status)
		assert.True(t, claimed1.WorkerID.Valid)
		assert.Equal(t, "worker-1", claimed1.WorkerID.String)
		assert.True(t, claimed1.PickedUpAt.Valid)
		assert.False(t, claimed1.PickedUpAt.Time.IsZero())

		// Verify in DB
		checkClaimed1, err := repo.GetByID(ctx, claimed1.ID)
		require.NoError(t, err)
		assert.Equal(t, models.TaskStatusInProgress, checkClaimed1.Status)
		assert.Equal(t, "worker-1", checkClaimed1.WorkerID.String)

		// 4. Claim the next pending task - should be the remaining task from user 2001
		claimed2, err := repo.ClaimNextPending(ctx, "worker-2")
		require.NoError(t, err)
		require.NotNil(t, claimed2, "Should have claimed another task")
		assert.Equal(t, "2001", claimed2.UserID, "Should claim a task from user 2001")
		assert.NotEqual(t, claimed1.ID, claimed2.ID, "Should claim a different task")
		assert.Equal(t, models.TaskStatusInProgress, claimed2.Status)
		assert.Equal(t, "worker-2", claimed2.WorkerID.String)

		// 5. No more pending tasks
		claimed3, err := repo.ClaimNextPending(ctx, "worker-3")
		require.NoError(t, err)
		require.Nil(t, claimed3, "Should return nil when no more pending tasks")
	})

	runTestCase("ClaimNextPendingRandomUser", func(t *testing.T, repo repository.ITaskRepository) {
		// For real tests, ensure no pending tasks exist that could interfere with the test
		if isRealTest {
			// Delete all existing tasks that might interfere with this test
			err := db.Exec("DELETE FROM tasks").Error
			require.NoError(t, err, "Failed to clean tasks before test")
		}

		// Create tasks for multiple users
		// User 1 tasks
		_ = createTask(t, repo, ctx, "4001", models.FetchChessGamesTask, models.FetchChessGamesData{ChessProfileID: "1"})
		if isRealTest {
			time.Sleep(20 * time.Millisecond)
		}
		_ = createTask(t, repo, ctx, "4001", models.EvaluateChessGameTask, models.EvaluateChessGameData{GameID: "10"})

		// User 2 tasks
		_ = createTask(t, repo, ctx, "4002", models.FetchChessGamesTask, models.FetchChessGamesData{ChessProfileID: "2"})
		if isRealTest {
			time.Sleep(20 * time.Millisecond)
		}
		_ = createTask(t, repo, ctx, "4002", models.EvaluateChessGameTask, models.EvaluateChessGameData{GameID: "20"})

		// User 3 tasks
		_ = createTask(t, repo, ctx, "4003", models.FetchChessGamesTask, models.FetchChessGamesData{ChessProfileID: "3"})
		if isRealTest {
			time.Sleep(20 * time.Millisecond)
		}
		_ = createTask(t, repo, ctx, "4003", models.EvaluateChessGameTask, models.EvaluateChessGameData{GameID: "30"})

		// Verify tasks were created
		// Note: For the fake repository, the ListAll method has hardcoded counts
		// that don't match our actual test data, so we'll check each user's tasks individually
		user1Tasks, err := repo.ListByUserID(ctx, "4001", []models.TaskStatus{models.TaskStatusPending})
		require.NoError(t, err)
		require.Len(t, user1Tasks, 2, "User 4001 should have 2 pending tasks")

		user2Tasks, err := repo.ListByUserID(ctx, "4002", []models.TaskStatus{models.TaskStatusPending})
		require.NoError(t, err)
		require.Len(t, user2Tasks, 2, "User 4002 should have 2 pending tasks")

		user3Tasks, err := repo.ListByUserID(ctx, "4003", []models.TaskStatus{models.TaskStatusPending})
		require.NoError(t, err)
		require.Len(t, user3Tasks, 2, "User 4003 should have 2 pending tasks")

		// Track which users' tasks are claimed
		claimedUserIDs := make(map[string]int)

		// Claim tasks multiple times to verify random user selection
		// We'll claim 10 tasks, which should result in tasks from different users being claimed
		for i := 0; i < 10; i++ {
			claimed, err := repo.ClaimNextPending(ctx, fmt.Sprintf("worker-random-%d", i))
			require.NoError(t, err)

			// If we've claimed all tasks, break out of the loop
			if claimed == nil {
				break
			}

			// Track which user's task was claimed
			claimedUserIDs[claimed.UserID]++

			// Mark the task as completed to allow claiming more tasks
			claimed.Status = models.TaskStatusCompleted
			currentTask, err := repo.GetByID(ctx, claimed.ID)
			require.NoError(t, err)
			err = repo.Update(ctx, claimed, currentTask.UpdatedAt)
			require.NoError(t, err)
		}

		// Verify that tasks from at least 2 different users were claimed
		// This is a probabilistic test, but with 3 users and 10 claims, it's very likely
		// that we'll see tasks from at least 2 different users
		assert.GreaterOrEqual(t, len(claimedUserIDs), 2,
			"Should have claimed tasks from at least 2 different users, got: %v", claimedUserIDs)
	})

	runTestCase("ClaimNextPendingPrioritizeGeneratePuzzles", func(t *testing.T, repo repository.ITaskRepository) {
		// For real tests, ensure no pending tasks exist that could interfere with the test
		if isRealTest {
			// Delete all existing tasks that might interfere with this test
			err := db.Exec("DELETE FROM tasks").Error
			require.NoError(t, err, "Failed to clean tasks before test")
		}

		// Create tasks for a single user with different task types
		// Create older FetchChessGames task
		fetchTask := createTask(t, repo, ctx, "3001", models.FetchChessGamesTask, models.FetchChessGamesData{ChessProfileID: "1"})
		if isRealTest {
			time.Sleep(50 * time.Millisecond)
		}

		// Create older EvaluateChessGame task
		evalTask := createTask(t, repo, ctx, "3001", models.EvaluateChessGameTask, models.EvaluateChessGameData{GameID: "10"})
		if isRealTest {
			time.Sleep(50 * time.Millisecond)
		}

		// Create newer GenerateChessPuzzles task - should be prioritized despite being newer
		genPuzzleTask := createTask(t, repo, ctx, "3001", models.GenerateChessPuzzlesTask, models.GenerateChessPuzzlesData{GameID: "20"})

		// Verify all tasks are pending
		pendingTasks, err := repo.ListByUserID(ctx, "3001", []models.TaskStatus{models.TaskStatusPending})
		require.NoError(t, err)
		require.Len(t, pendingTasks, 3, "Should have 3 pending tasks")

		// Claim a task - should prioritize the GenerateChessPuzzles task
		claimed, err := repo.ClaimNextPending(ctx, "worker-puzzle-1")
		require.NoError(t, err)
		require.NotNil(t, claimed, "Should have claimed a task")

		// Verify it's the GenerateChessPuzzles task
		assert.Equal(t, genPuzzleTask.ID, claimed.ID, "Should prioritize GenerateChessPuzzles task")
		assert.Equal(t, models.GenerateChessPuzzlesTask, claimed.TaskType)
		assert.Equal(t, models.TaskStatusInProgress, claimed.Status)

		// Claim another task - should get one of the other tasks
		claimed2, err := repo.ClaimNextPending(ctx, "worker-puzzle-2")
		require.NoError(t, err)
		require.NotNil(t, claimed2, "Should have claimed another task")
		assert.NotEqual(t, genPuzzleTask.ID, claimed2.ID, "Should not claim the same task again")
		assert.True(t, claimed2.ID == fetchTask.ID || claimed2.ID == evalTask.ID,
			"Should claim either the fetch task or eval task")

		// Claim the last task
		claimed3, err := repo.ClaimNextPending(ctx, "worker-puzzle-3")
		require.NoError(t, err)
		require.NotNil(t, claimed3, "Should have claimed the last task")
		assert.NotEqual(t, genPuzzleTask.ID, claimed3.ID, "Should not claim the puzzle task again")
		assert.NotEqual(t, claimed2.ID, claimed3.ID, "Should claim a different task")
		assert.True(t, claimed3.ID == fetchTask.ID || claimed3.ID == evalTask.ID,
			"Should claim either the fetch task or eval task")

		// No more tasks to claim
		claimed4, err := repo.ClaimNextPending(ctx, "worker-puzzle-4")
		require.NoError(t, err)
		require.Nil(t, claimed4, "Should not have any more tasks to claim")
	})

	runTestCase("ClaimNextPendingScheduled", func(t *testing.T, repo repository.ITaskRepository) {
		// Ensure clean state for this specific test
		if isRealTest {
			err := db.Exec("DELETE FROM tasks").Error
			require.NoError(t, err, "Failed to clean tasks before scheduled claim test")
		}

		now := time.Now()
		futureTime := now.Add(1 * time.Hour)
		pastTime := now.Add(-1 * time.Minute)

		// Task scheduled for the future
		taskFuture := createTask(t, repo, ctx, "scheduleUser", models.FetchChessGamesTask, nil)
		currentFuture, err := repo.GetByID(ctx, taskFuture.ID)
		require.NoError(t, err)
		taskFuture.ScheduledAt = futureTime
		err = repo.Update(ctx, taskFuture, currentFuture.UpdatedAt)
		require.NoError(t, err, "Failed to update future task's scheduled time")

		// Task scheduled for now/past (should be claimable)
		taskNow := createTask(t, repo, ctx, "scheduleUser", models.EvaluateChessGameTask, nil)
		currentNow, err := repo.GetByID(ctx, taskNow.ID)
		require.NoError(t, err)
		taskNow.ScheduledAt = pastTime // Set to past time
		err = repo.Update(ctx, taskNow, currentNow.UpdatedAt)
		require.NoError(t, err, "Failed to update past task's scheduled time")

		// Task created with default schedule (now)
		taskDefault := createTask(t, repo, ctx, "scheduleUser", models.GenerateChessPuzzlesTask, nil)

		// Attempt to claim - should only get taskNow or taskDefault
		claimed1, err := repo.ClaimNextPending(ctx, "worker-schedule-1")
		require.NoError(t, err)
		require.NotNil(t, claimed1, "Should claim a task scheduled for the past or now")
		assert.NotEqual(t, taskFuture.ID, claimed1.ID, "Should not claim the future-scheduled task first")
		assert.True(t, claimed1.ID == taskNow.ID || claimed1.ID == taskDefault.ID, "Claimed task should be taskNow or taskDefault")

		// Verify the claimed task's scheduled time was indeed in the past or now
		claimed1Fetched, err := repo.GetByID(ctx, claimed1.ID)
		require.NoError(t, err)
		assert.True(t, !claimed1Fetched.ScheduledAt.After(time.Now()), "Claimed task ScheduledAt (%v) should not be after now (%v)", claimed1Fetched.ScheduledAt, time.Now())

		// Attempt to claim again
		claimed2, err := repo.ClaimNextPending(ctx, "worker-schedule-2")
		require.NoError(t, err)
		require.NotNil(t, claimed2, "Should claim the second available task")
		assert.NotEqual(t, taskFuture.ID, claimed2.ID, "Should not claim the future-scheduled task second")
		assert.NotEqual(t, claimed1.ID, claimed2.ID, "Should claim a different task second")
		assert.True(t, claimed2.ID == taskNow.ID || claimed2.ID == taskDefault.ID, "Second claimed task should be taskNow or taskDefault")

		// Attempt to claim again - should get nil as only future task remains
		claimed3, err := repo.ClaimNextPending(ctx, "worker-schedule-3")
		require.NoError(t, err)
		assert.Nil(t, claimed3, "Should not claim any more tasks (only future task left)")

		// Verify the future task is still pending
		futureTaskCheck, err := repo.GetByID(ctx, taskFuture.ID)
		require.NoError(t, err)
		assert.Equal(t, models.TaskStatusPending, futureTaskCheck.Status, "Future task should remain pending")
	})

	runTestCase("UpdateTask", func(t *testing.T, repo repository.ITaskRepository) {
		// Create a task
		originalTask := createTask(t, repo, ctx, "1", models.FetchChessGamesTask, models.FetchChessGamesData{ChessProfileID: "1"})
		originalUpdatedAt := originalTask.UpdatedAt
		originalCreatedAt := originalTask.CreatedAt
		originalScheduledAt := originalTask.ScheduledAt // Capture original scheduled at
		assert.False(t, originalCreatedAt.IsZero(), "Initial CreatedAt should not be zero")
		assert.False(t, originalUpdatedAt.IsZero(), "Initial UpdatedAt should not be zero")
		assert.Equal(t, originalCreatedAt, originalScheduledAt, "Initial ScheduledAt should equal CreatedAt")

		// Update status, error, and scheduled time
		newScheduledTime := time.Now().Add(24 * time.Hour)
		originalTask.Status = models.TaskStatusFailed
		originalTask.Error.String = "Something went wrong"
		originalTask.Error.Valid = true
		originalTask.ScheduledAt = newScheduledTime // Update scheduled time

		// Artificial delay to ensure UpdatedAt changes (more important for real db)
		if isRealTest {
			time.Sleep(10 * time.Millisecond)
		}

		err := repo.Update(ctx, originalTask, originalUpdatedAt)
		require.NoError(t, err, "Failed to update task")

		// Get the updated task
		updatedTask, err := repo.GetByID(ctx, originalTask.ID)
		require.NoError(t, err)
		require.NotNil(t, updatedTask)

		assert.Equal(t, models.TaskStatusFailed, updatedTask.Status)
		assert.True(t, updatedTask.Error.Valid)
		assert.Equal(t, "Something went wrong", updatedTask.Error.String)
		assert.Equal(t, originalCreatedAt, updatedTask.CreatedAt, "CreatedAt should not change on update") // Check CreatedAt unchanged
		assert.True(t, updatedTask.UpdatedAt.After(originalUpdatedAt), "UpdatedAt should have been updated")
		assert.WithinDuration(t, time.Now(), updatedTask.UpdatedAt, 10*time.Second, "Updated UpdatedAt should be recent")
		// Check updated scheduled time - use WithinDuration for potential slight clock differences
		assert.WithinDuration(t, newScheduledTime, updatedTask.ScheduledAt, time.Second, "ScheduledAt should have been updated")
	})

	runTestCase("UpdateTaskOptimisticLockFail", func(t *testing.T, repo repository.ITaskRepository) {
		// Create a task
		originalTask := createTask(t, repo, ctx, "1", models.FetchChessGamesTask, models.FetchChessGamesData{ChessProfileID: "1"})
		originalUpdatedAt := originalTask.UpdatedAt
		originalCreatedAt := originalTask.CreatedAt
		assert.False(t, originalCreatedAt.IsZero(), "Initial CreatedAt should not be zero")
		assert.False(t, originalUpdatedAt.IsZero(), "Initial UpdatedAt should not be zero")

		// Simulate an update happening elsewhere by updating the task again
		if isRealTest {
			time.Sleep(10 * time.Millisecond)
		}
		originalTask.Status = models.TaskStatusCompleted
		// Need to get the updated time for the real repo optimistic lock
		currentTask, err := repo.GetByID(ctx, originalTask.ID)
		require.NoError(t, err)
		err = repo.Update(ctx, originalTask, currentTask.UpdatedAt)
		require.NoError(t, err, "First update should succeed")
		updatedTask1, _ := repo.GetByID(ctx, originalTask.ID) // Get state after first update

		// Now try to update with the *original* UpdatedAt timestamp
		if isRealTest {
			time.Sleep(10 * time.Millisecond)
		}
		updatedTask1.Status = models.TaskStatusFailed           // Try another update on the fetched task
		err = repo.Update(ctx, updatedTask1, originalUpdatedAt) // Use the stale timestamp

		assert.ErrorIs(t, err, repository.ErrTaskUpdateConflict, "Expected optimistic lock conflict")

		// Verify the task status remains as Completed from the first update
		finalTask, err := repo.GetByID(ctx, originalTask.ID)
		require.NoError(t, err)
		assert.Equal(t, models.TaskStatusCompleted, finalTask.Status)

		// Verify timestamps after reset
		assert.Equal(t, originalCreatedAt, finalTask.CreatedAt, "CreatedAt should not change on reset")
		assert.True(t, finalTask.UpdatedAt.After(originalUpdatedAt), "UpdatedAt should be updated after reset")
		assert.WithinDuration(t, time.Now(), finalTask.UpdatedAt, 10*time.Second, "Reset UpdatedAt should be recent")
	})

	runTestCase("ResetHanging", func(t *testing.T, repo repository.ITaskRepository) {
		// Create tasks
		taskPending := createTask(t, repo, ctx, "1", models.FetchChessGamesTask, nil)   // Should not be reset
		taskCompleted := createTask(t, repo, ctx, "1", models.FetchChessGamesTask, nil) // Should not be reset
		taskHanging1 := createTask(t, repo, ctx, "1", models.FetchChessGamesTask, nil)  // Should be reset
		taskHanging2 := createTask(t, repo, ctx, "2", models.FetchChessGamesTask, nil)  // Should be reset
		taskRecent := createTask(t, repo, ctx, "2", models.FetchChessGamesTask, nil)    // Should not be reset

		// Mark tasks with appropriate statuses and pickup times
		taskCompleted.Status = models.TaskStatusCompleted
		currentTaskCompleted, err := repo.GetByID(ctx, taskCompleted.ID)
		require.NoError(t, err)
		err = repo.Update(ctx, taskCompleted, currentTaskCompleted.UpdatedAt)
		require.NoError(t, err)

		hangingTime := time.Now().Add(-2 * time.Hour)  // Simulate picked up 2 hours ago
		recentTime := time.Now().Add(-5 * time.Minute) // Simulate picked up 5 minutes ago

		// Helper to update status/pickup time
		updateStatusAndPickup := func(task *models.Task, status models.TaskStatus, pickupTime time.Time) {
			taskToUpdate, err := repo.GetByID(ctx, task.ID) // Get current state for timestamp
			require.NoError(t, err)
			taskToUpdate.Status = status
			taskToUpdate.PickedUpAt.Time = pickupTime.UTC() // Ensure UTC
			taskToUpdate.PickedUpAt.Valid = true
			taskToUpdate.WorkerID.String = "worker-test"
			taskToUpdate.WorkerID.Valid = true
			err = repo.Update(ctx, taskToUpdate, taskToUpdate.UpdatedAt)
			require.NoError(t, err)
		}

		updateStatusAndPickup(taskHanging1, models.TaskStatusInProgress, hangingTime)
		updateStatusAndPickup(taskHanging2, models.TaskStatusInProgress, hangingTime)
		updateStatusAndPickup(taskRecent, models.TaskStatusInProgress, recentTime)

		// Reset hanging tasks with a 1-hour timeout
		resetCount, err := repo.ResetHanging(ctx, time.Hour)
		require.NoError(t, err)
		assert.Equal(t, int64(2), resetCount, "Expected 2 tasks to be reset")

		// Verify statuses
		checkPending, err := repo.GetByID(ctx, taskPending.ID)
		require.NoError(t, err)
		assert.Equal(t, models.TaskStatusPending, checkPending.Status)
		assert.Equal(t, 0, checkPending.Attempts)

		checkCompletedAfterReset, err := repo.GetByID(ctx, taskCompleted.ID)
		require.NoError(t, err)
		assert.Equal(t, models.TaskStatusCompleted, checkCompletedAfterReset.Status)

		checkHanging1, err := repo.GetByID(ctx, taskHanging1.ID)
		require.NoError(t, err)
		assert.Equal(t, models.TaskStatusPending, checkHanging1.Status, "Hanging task 1 should be pending")
		assert.False(t, checkHanging1.WorkerID.Valid)
		assert.False(t, checkHanging1.PickedUpAt.Valid)
		assert.Equal(t, 1, checkHanging1.Attempts, "Attempts should be incremented for reset task 1")
		assert.True(t, checkHanging1.UpdatedAt.After(taskHanging1.UpdatedAt), "UpdatedAt should be updated after reset 1")
		assert.WithinDuration(t, time.Now(), checkHanging1.UpdatedAt, 10*time.Second, "Reset UpdatedAt 1 should be recent")
		assert.Equal(t, taskHanging1.CreatedAt, checkHanging1.CreatedAt, "CreatedAt should not change on reset 1")

		checkHanging2, err := repo.GetByID(ctx, taskHanging2.ID)
		require.NoError(t, err)
		assert.Equal(t, models.TaskStatusPending, checkHanging2.Status, "Hanging task 2 should be pending")
		assert.False(t, checkHanging2.WorkerID.Valid)
		assert.False(t, checkHanging2.PickedUpAt.Valid)
		assert.Equal(t, 1, checkHanging2.Attempts, "Attempts should be incremented for reset task 2")
		assert.True(t, checkHanging2.UpdatedAt.After(taskHanging2.UpdatedAt), "UpdatedAt should be updated after reset 2")
		assert.WithinDuration(t, time.Now(), checkHanging2.UpdatedAt, 10*time.Second, "Reset UpdatedAt 2 should be recent")
		assert.Equal(t, taskHanging2.CreatedAt, checkHanging2.CreatedAt, "CreatedAt should not change on reset 2")

		checkRecent, err := repo.GetByID(ctx, taskRecent.ID)
		require.NoError(t, err)
		assert.Equal(t, models.TaskStatusInProgress, checkRecent.Status, "Recently picked up task should not be reset")
		assert.Equal(t, 0, checkRecent.Attempts)
	})

	runTestCase("ListAllPaginated", func(t *testing.T, repo repository.ITaskRepository) {
		// For real tests, ensure we have a clean state for reliable pagination testing
		if isRealTest {
			// Delete all existing tasks to ensure a clean slate
			err := db.Exec("DELETE FROM tasks").Error
			require.NoError(t, err, "Failed to clean tasks before pagination test")
		}

		// Create 5 tasks
		var taskIDs []string
		var createdTasks []*models.Task
		for i := 0; i < 5; i++ {
			task := createTask(t, repo, ctx, "1", models.FetchChessGamesTask, models.FetchChessGamesData{ChessProfileID: "1"})
			createdTasks = append(createdTasks, task)
			taskIDs = append(taskIDs, task.ID)
			if i%2 == 0 { // Mark even indexed tasks as completed
				task.Status = models.TaskStatusCompleted
				// Need to get current state for timestamp
				currentTask, err := repo.GetByID(ctx, task.ID)
				require.NoError(t, err)
				err = repo.Update(ctx, task, currentTask.UpdatedAt)
				require.NoError(t, err)
				// Update the task in our slice too
				createdTasks[i], _ = repo.GetByID(ctx, task.ID)
			}
			// Ensure creation order for sorting checks (more relevant for real db)
			if isRealTest {
				time.Sleep(50 * time.Millisecond) // Increase sleep duration for more reliable ordering
			}
		}
		// Expected order (newest first ID): taskIDs[4], taskIDs[3], taskIDs[2], taskIDs[1], taskIDs[0]

		tests := []struct {
			name            string
			statuses        []models.TaskStatus
			offset          int
			limit           int
			expectedCount   int64    // Total matching tasks
			expectedIDs     []string // IDs in the returned page (desc order)
			expectedPageLen int
		}{
			{
				name:            "All tasks, first page (limit 2)",
				statuses:        nil,
				offset:          0,
				limit:           2,
				expectedCount:   5,
				expectedIDs:     []string{taskIDs[4], taskIDs[3]},
				expectedPageLen: 2,
			},
			{
				name:            "All tasks, second page (limit 2)",
				statuses:        nil,
				offset:          2,
				limit:           2,
				expectedCount:   5,
				expectedIDs:     []string{taskIDs[2], taskIDs[1]},
				expectedPageLen: 2,
			},
			{
				name:            "All tasks, last page (limit 2)",
				statuses:        nil,
				offset:          4,
				limit:           2,
				expectedCount:   5,
				expectedIDs:     []string{taskIDs[0]},
				expectedPageLen: 1,
			},
			{
				name:            "All tasks, offset beyond bounds",
				statuses:        nil,
				offset:          10,
				limit:           2,
				expectedCount:   5,
				expectedIDs:     []string{},
				expectedPageLen: 0,
			},
			{
				name:            "Completed tasks, first page (limit 2)",
				statuses:        []models.TaskStatus{models.TaskStatusCompleted},
				offset:          0,
				limit:           2,
				expectedCount:   3,                                // tasks 0, 2, 4 are completed
				expectedIDs:     []string{taskIDs[4], taskIDs[2]}, // Desc order by create time
				expectedPageLen: 2,
			},
			{
				name:            "Completed tasks, second page (limit 2)",
				statuses:        []models.TaskStatus{models.TaskStatusCompleted},
				offset:          2,
				limit:           2,
				expectedCount:   3,
				expectedIDs:     []string{taskIDs[0]},
				expectedPageLen: 1,
			},
			{
				name:            "Pending tasks, first page (limit 5)",
				statuses:        []models.TaskStatus{models.TaskStatusPending},
				offset:          0,
				limit:           5,
				expectedCount:   2,                                // tasks 1, 3 are pending
				expectedIDs:     []string{taskIDs[3], taskIDs[1]}, // Desc order by create time
				expectedPageLen: 2,
			},
			{
				name:            "All tasks, default limit (0 -> 10)",
				statuses:        nil,
				offset:          0,
				limit:           0,
				expectedCount:   5,
				expectedIDs:     []string{taskIDs[4], taskIDs[3], taskIDs[2], taskIDs[1], taskIDs[0]},
				expectedPageLen: 5,
			},
		}

		for _, tc := range tests {
			t.Run(tc.name, func(t *testing.T) {
				tasks, totalCount, err := repo.ListAll(ctx, nil, tc.statuses, tc.offset, tc.limit)
				require.NoError(t, err)
				assert.Equal(t, tc.expectedCount, totalCount, "Total count mismatch")
				require.Len(t, tasks, tc.expectedPageLen, "Returned page length mismatch")

				actualIDs := make([]string, len(tasks))
				for i, task := range tasks {
					actualIDs[i] = task.ID
				}
				assert.Equal(t, tc.expectedIDs, actualIDs, "Task IDs mismatch (order matters)")
			})
		}
	})

	runTestCase("DeleteOldTasks", func(t *testing.T, repo repository.ITaskRepository) {
		// Helper to create tasks with specific created_at timestamps
		createTaskWithTime := func(status models.TaskStatus, age time.Duration) *models.Task {
			// Define the old timestamp we want - ensure it's far enough in the past
			now := time.Now() // Recalculate now each time
			oldTime := now.Add(-age)
			oldTimeUTC := oldTime.UTC() // Use UTC

			// Prepare the task object
			task := &models.Task{
				UserID:   "1",
				TaskType: models.FetchChessGamesTask,
				TaskData: mustMarshal(t, models.FetchChessGamesData{}),
				Status:   status, // Set the status immediately to what we want
			}

			var err error // Declare err variable here

			// Create the task with the correct timestamp depending on the repo type
			if fakeRepo, ok := repo.(*fake.FakeTaskRepository); ok {
				// For fake repo, use the dedicated test helper
				err = fakeRepo.CreateWithTimestamp(ctx, task, oldTime) // Use =
				require.NoError(t, err, "Failed to create task with timestamp in fake repo")
			} else if isRealTest {
				// For real DB, set the timestamp BEFORE creating to try and override DB defaults
				task.CreatedAt = oldTimeUTC
				task.UpdatedAt = oldTimeUTC

				// Create normally, hoping GORM respects the pre-set timestamps
				err = repo.Create(ctx, task) // Use =
				require.NoError(t, err)
				require.NotEmpty(t, task.ID, "Task ID should be non-empty after create")
			} else {
				// Fallback/Error case - should not happen if provider is setup correctly
				t.Fatalf("Unexpected repository type: %T", repo)
			}

			// Fetch the task again to get its final state from the repository
			// This ensures we have the ID and the confirmed timestamps.
			var createdTask *models.Task
			createdTask, err = repo.GetByID(ctx, task.ID) // Ensure this uses = for err
			require.NoError(t, err)
			require.NotNil(t, createdTask)

			// Verify timestamp was set correctly (using tolerance) comparing UTC times
			require.WithinDuration(t, oldTimeUTC, createdTask.CreatedAt.UTC(), time.Second, // Compare UTC
				"Task CreatedAt (%v / UTC: %v) should be close to oldTimeUTC (%v) after creation/update",
				createdTask.CreatedAt, createdTask.CreatedAt.UTC(), oldTimeUTC)

			return createdTask
		}

		// --- Start of Test Cases ---

		// Cutoff time for deletion (e.g., 2 hours ago)
		cutoff := time.Now().Add(-2 * time.Hour)

		// Create old tasks (3+ hours old) - should be deleted by cutoff
		taskOldCompleted1 := createTaskWithTime(models.TaskStatusCompleted, 3*time.Hour)
		taskOldCompleted2 := createTaskWithTime(models.TaskStatusCompleted, 4*time.Hour)
		taskOldFailed := createTaskWithTime(models.TaskStatusFailed, 5*time.Hour)

		// Create tasks that should *not* be deleted
		taskOldPending := createTaskWithTime(models.TaskStatusPending, 3*time.Hour)
		taskRecentCompleted := createTaskWithTime(models.TaskStatusCompleted, 30*time.Minute)

		// Verify prerequisite: Check timestamps relative to cutoff *before* deletion attempt
		require.True(t, taskOldCompleted1.CreatedAt.Before(cutoff),
			"Prereq Fail: taskOldCompleted1 should be older than cutoff. Task: %v, Cutoff: %v", taskOldCompleted1.CreatedAt, cutoff)
		require.True(t, taskOldCompleted2.CreatedAt.Before(cutoff),
			"Prereq Fail: taskOldCompleted2 should be older than cutoff. Task: %v, Cutoff: %v", taskOldCompleted2.CreatedAt, cutoff)
		require.True(t, taskOldFailed.CreatedAt.Before(cutoff),
			"Prereq Fail: taskOldFailed should be older than cutoff. Task: %v, Cutoff: %v", taskOldFailed.CreatedAt, cutoff)
		require.True(t, taskOldPending.CreatedAt.Before(cutoff),
			"Prereq Fail: taskOldPending should be older than cutoff. Task: %v, Cutoff: %v", taskOldPending.CreatedAt, cutoff)
		require.True(t, taskRecentCompleted.CreatedAt.After(cutoff), // Note: After
			"Prereq Fail: taskRecentCompleted should be newer than cutoff. Task: %v, Cutoff: %v", taskRecentCompleted.CreatedAt, cutoff)

		// Case 1: Delete old completed/failed tasks (No Limit)
		t.Run("Delete Old Completed/Failed No Limit", func(t *testing.T) {
			// Get IDs before potential deletion
			oldCompleted1ID := taskOldCompleted1.ID
			oldCompleted2ID := taskOldCompleted2.ID
			oldFailedID := taskOldFailed.ID
			oldPendingID := taskOldPending.ID
			recentCompletedID := taskRecentCompleted.ID

			deletedCount, err := repo.DeleteOldTasks(ctx, cutoff,
				[]models.TaskStatus{models.TaskStatusCompleted, models.TaskStatusFailed}, 0) // limit=0 means no limit
			require.NoError(t, err)
			assert.Equal(t, int64(3), deletedCount, "Should delete 3 old tasks (completed/failed)")

			// Verify tasks were deleted by checking for ErrNotFound
			_, err = repo.GetByID(ctx, oldCompleted1ID)
			assert.ErrorIs(t, err, repository.ErrNotFound, "taskOldCompleted1 should be deleted")
			_, err = repo.GetByID(ctx, oldCompleted2ID)
			assert.ErrorIs(t, err, repository.ErrNotFound, "taskOldCompleted2 should be deleted")
			_, err = repo.GetByID(ctx, oldFailedID)
			assert.ErrorIs(t, err, repository.ErrNotFound, "taskOldFailed should be deleted")

			// Verify tasks that should remain
			_, err = repo.GetByID(ctx, oldPendingID)
			assert.NoError(t, err, "taskOldPending should remain")
			_, err = repo.GetByID(ctx, recentCompletedID)
			assert.NoError(t, err, "taskRecentCompleted should remain")
		})

		// Clean up tasks created in the first subtest before running the next ones
		// This prevents interference between subtests, especially the limited deletion one.
		if isRealTest {
			// Direct DB cleanup is most reliable for real tests
			err := db.Exec("DELETE FROM tasks WHERE id IN (?, ?, ?, ?, ?)",
				taskOldCompleted1.ID, taskOldCompleted2.ID, taskOldFailed.ID,
				taskOldPending.ID, taskRecentCompleted.ID).Error
			require.NoError(t, err, "Failed to clean up tasks after 'No Limit' subtest (real DB)")
		} else if fakeRepo, ok := repo.(*fake.FakeTaskRepository); ok {
			// For fake repo, state persists. Rely on recreating tasks with new names.
			// No explicit cleanup needed here if subtests create distinct tasks.
			_ = fakeRepo // Avoid unused variable warning
			// idsToDelete := []string{
			// 	taskOldCompleted1.ID, taskOldCompleted2.ID, taskOldFailed.ID,
			// 	taskOldPending.ID, taskRecentCompleted.ID,
			// }
			// fakeRepo.DeleteTasksByIDForTesting(t, idsToDelete) // Method doesn't exist
		}

		// --- Recreate tasks for subsequent tests ---
		cutoffForLimitTest := time.Now().Add(-2 * time.Hour) // Recalculate cutoff for consistency

		// Create tasks specifically for the limit test
		taskOldC1_limit := createTaskWithTime(models.TaskStatusCompleted, 3*time.Hour) // Should remain
		taskOldC2_limit := createTaskWithTime(models.TaskStatusCompleted, 4*time.Hour) // Should remain
		taskOldF1_limit := createTaskWithTime(models.TaskStatusFailed, 5*time.Hour)    // Should be deleted (oldest)

		// Verify prerequisites for limit test tasks
		require.True(t, taskOldF1_limit.CreatedAt.Before(cutoffForLimitTest))
		require.True(t, taskOldC2_limit.CreatedAt.Before(cutoffForLimitTest))
		require.True(t, taskOldC1_limit.CreatedAt.Before(cutoffForLimitTest))

		// Case 2: Delete old completed/failed tasks (Limited to 1)
		t.Run("Delete Old Completed Limited", func(t *testing.T) {
			deletedCount, err := repo.DeleteOldTasks(ctx, cutoffForLimitTest,
				[]models.TaskStatus{models.TaskStatusCompleted, models.TaskStatusFailed}, 1) // limit=1
			require.NoError(t, err)
			assert.Equal(t, int64(1), deletedCount, "Should delete only 1 task due to limit")

			// Since the delete is based on created_at ordering (oldest first),
			// the oldest task (taskOldF1_limit) should be deleted.
			_, err = repo.GetByID(ctx, taskOldF1_limit.ID)
			assert.ErrorIs(t, err, repository.ErrNotFound, "taskOldF1_limit should be deleted")

			// The other two should remain
			_, err = repo.GetByID(ctx, taskOldC2_limit.ID)
			assert.NoError(t, err, "taskOldC2_limit should remain")
			_, err = repo.GetByID(ctx, taskOldC1_limit.ID)
			assert.NoError(t, err, "taskOldC1_limit should remain")
		})

		// Clean up tasks from the limit test
		if isRealTest {
			err := db.Exec("DELETE FROM tasks WHERE id IN (?, ?, ?)",
				taskOldC1_limit.ID, taskOldC2_limit.ID, taskOldF1_limit.ID).Error
			require.NoError(t, err, "Failed to clean up tasks after 'Limited' subtest (real DB)")
		} else if fakeRepo, ok := repo.(*fake.FakeTaskRepository); ok {
			// Rely on state persistence and distinct task creation
			_ = fakeRepo // Avoid unused variable warning
			// idsToDelete := []string{taskOldC1_limit.ID, taskOldC2_limit.ID, taskOldF1_limit.ID}
			// fakeRepo.DeleteTasksByIDForTesting(t, idsToDelete)
		}

		// --- Recreate task for next test ---
		cutoffForPendingTest := time.Now().Add(-2 * time.Hour)
		taskOldP_status := createTaskWithTime(models.TaskStatusPending, 3*time.Hour)

		// Case 3: Attempt to delete old pending tasks (should not happen based on specified statuses)
		t.Run("Attempt Delete Old Pending", func(t *testing.T) {
			deletedCount, err := repo.DeleteOldTasks(ctx, cutoffForPendingTest,
				[]models.TaskStatus{models.TaskStatusPending}, 0) // Only specify Pending status
			require.NoError(t, err)
			// The fake repo implementation has a safety check preventing deletion of Pending/InProgress tasks
			// The real repo query also likely excludes these based on common practice.
			assert.Equal(t, int64(0), deletedCount, "Should not delete pending tasks when DeleteOldTasks is limited to only pending status")

			_, err = repo.GetByID(ctx, taskOldP_status.ID)
			assert.NoError(t, err, "taskOldPending should still remain")
		})

		// Clean up task from pending test
		if isRealTest {
			err := db.Exec("DELETE FROM tasks WHERE id = ?", taskOldP_status.ID).Error
			require.NoError(t, err, "Failed to clean up task after 'Pending' subtest (real DB)")
		} else if fakeRepo, ok := repo.(*fake.FakeTaskRepository); ok {
			// Rely on state persistence and distinct task creation
			_ = fakeRepo // Avoid unused variable warning
			// idsToDelete := []string{taskOldP_status.ID}
			// fakeRepo.DeleteTasksByIDForTesting(t, idsToDelete)
		}

		// Case 4: No matching tasks to delete
		t.Run("Delete No Matching", func(t *testing.T) {
			// Use a very old cutoff and a status that shouldn't match any remaining tasks
			veryOldCutoff := time.Now().Add(-10 * time.Hour)
			deletedCount, err := repo.DeleteOldTasks(ctx, veryOldCutoff,
				[]models.TaskStatus{models.TaskStatusInProgress}, 0)
			require.NoError(t, err)
			assert.Equal(t, int64(0), deletedCount, "Should not delete any tasks with old cutoff and status mismatch")
		})

		// Note: No cleanup needed after Case 4 as no tasks should have been created/deleted specifically for it.
	})
}

// TestRealTaskRepository_ClaimNextPendingConcurrent tests concurrent claims specifically against the real repository.
// It uses the base DB connection pool rather than a single transaction.
func TestRealTaskRepository_ClaimNextPendingConcurrent(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping real database tests")
	}
	provider := GetTestDBProvider(RealRepository)
	defer provider.Cleanup(t)

	p, ok := provider.(*PostgresTestDBProvider)
	if !ok {
		t.Skip("Skipping concurrent real repository test as provider is not Postgres")
		return
	}
	db := p.GetDB(t) // Get the base DB connection

	// Ensure clean state before the test
	err := db.Exec("DELETE FROM tasks").Error
	require.NoError(t, err, "Failed to clean tasks before concurrent claim test")

	// Create repo using the base DB connection pool
	repo := repository.NewTaskRepository(db)

	// Defer cleanup after the test
	defer func() {
		err := db.Exec("DELETE FROM tasks").Error
		require.NoError(t, err, "Failed to clean tasks after concurrent claim test")
	}()

	ctx := context.Background()
	numTasks := 5
	numWorkers := 10 // More workers than tasks to ensure contention

	// 1. Create pending tasks using the main repo instance
	pendingTaskIDs := make(map[string]struct{}) // Using a map for quick lookup
	taskCreationTimes := make(map[string]time.Time)
	for i := 0; i < numTasks; i++ {
		// Use the createTask helper, but it needs the repo instance now
		task := createTask(t, repo, ctx, "3001", models.FetchChessGamesTask, models.FetchChessGamesData{ChessProfileID: "1"})
		require.Equal(t, models.TaskStatusPending, task.Status)
		pendingTaskIDs[task.ID] = struct{}{}
		taskCreationTimes[task.ID] = task.CreatedAt
		// Add slight delay for real DB to help distinguish creation times if needed
		time.Sleep(10 * time.Millisecond)
	}
	require.Len(t, pendingTaskIDs, numTasks, "Should have created %d tasks", numTasks)

	// 2. Setup for concurrent execution
	var wg sync.WaitGroup
	claimedTaskIDsChan := make(chan string, numWorkers) // Buffered channel to collect claimed IDs
	claimErrorsChan := make(chan error, numWorkers)     // Buffered channel for errors

	// 3. Launch worker goroutines - they all use the *same* repo instance
	wg.Add(numWorkers)
	for i := 0; i < numWorkers; i++ {
		workerID := fmt.Sprintf("concurrent-worker-%d", i+1)
		go func(wID string) {
			defer wg.Done()
			// Claim using the shared repo instance
			claimedTask, err := repo.ClaimNextPending(ctx, wID)

			if err != nil {
				claimErrorsChan <- fmt.Errorf("worker %s ClaimNextPending error: %w", wID, err)
				return
			}
			if claimedTask != nil {
				claimedTaskIDsChan <- claimedTask.ID
			}
		}(workerID)
	}

	// 4. Wait for all workers to finish and close channels
	wg.Wait()
	close(claimedTaskIDsChan)
	close(claimErrorsChan)

	// 5. Collect and verify results
	finalClaimedIDs := make(map[string]int)
	for id := range claimedTaskIDsChan {
		finalClaimedIDs[id]++
	}

	var encounteredErrors []error
	for err := range claimErrorsChan {
		encounteredErrors = append(encounteredErrors, err)
	}
	require.Empty(t, encounteredErrors, "Encountered errors during concurrent claims: %v", encounteredErrors)

	// --- Assertions ---
	// a) Correct number of unique tasks claimed
	assert.Equal(t, numTasks, len(finalClaimedIDs), "Expected %d unique tasks to be claimed, but got %d. Claimed map: %v", numTasks, len(finalClaimedIDs), finalClaimedIDs)

	// b) Every task initially created was claimed exactly once
	claimedCountTotal := 0
	for taskID := range pendingTaskIDs {
		count, found := finalClaimedIDs[taskID]
		assert.True(t, found, "Task ID %s (created at %v) was not claimed", taskID, taskCreationTimes[taskID])
		if found {
			assert.Equal(t, 1, count, "Task ID %s (created at %v) was claimed %d times, expected exactly once", taskID, taskCreationTimes[taskID], count)
			claimedCountTotal += count
		}
	}
	assert.Equal(t, numTasks, claimedCountTotal, "Total claims (%d) do not match the number of tasks (%d)", claimedCountTotal, numTasks)

	// c) Verify task statuses in the database
	for taskID := range pendingTaskIDs {
		// Use a fresh GetByID call for verification
		finalTaskState, err := repo.GetByID(ctx, taskID)
		require.NoError(t, err, "Failed to get final state for task %s", taskID)
		require.NotNil(t, finalTaskState)
		if _, wasClaimed := finalClaimedIDs[taskID]; wasClaimed {
			assert.Equal(t, models.TaskStatusInProgress, finalTaskState.Status, "Task %s should be InProgress after being claimed", taskID)
			assert.True(t, finalTaskState.WorkerID.Valid, "Task %s should have a valid WorkerID", taskID)
			assert.Contains(t, finalTaskState.WorkerID.String, "concurrent-worker-", "Worker ID for task %s seems incorrect: %s", taskID, finalTaskState.WorkerID.String)
			assert.True(t, finalTaskState.PickedUpAt.Valid, "Task %s should have a valid PickedUpAt time", taskID)
		} else {
			assert.Equal(t, models.TaskStatusPending, finalTaskState.Status, "Unclaimed task %s should still be Pending", taskID)
		}
	}

	// d) Ensure no more pending tasks can be claimed
	finalClaim, err := repo.ClaimNextPending(ctx, "final-checker")
	require.NoError(t, err)
	assert.Nil(t, finalClaim, "No more tasks should be available for claiming after concurrent test")
}

// Helper function to marshal data for tests
func mustMarshal(t *testing.T, v interface{}) json.RawMessage {
	// Ensure consistent JSON formatting with no whitespace
	d, err := json.Marshal(v)
	require.NoError(t, err)

	// Compact the JSON to ensure no whitespace in the output
	var buf bytes.Buffer
	if err := json.Compact(&buf, d); err != nil {
		require.NoError(t, err)
	}

	return buf.Bytes()
}

// Helper to create a task for tests
func createTask(t *testing.T, repo repository.ITaskRepository, ctx context.Context, userID string, taskType models.TaskType, data interface{}) *models.Task {
	task := &models.Task{
		UserID:   userID,
		TaskType: taskType,
		TaskData: mustMarshal(t, data),
	}
	err := repo.Create(ctx, task)
	require.NoError(t, err)
	require.NotEmpty(t, task.ID)
	// Need to get the task again to have accurate CreatedAt/UpdatedAt/ScheduledAt from the repo
	// This is especially important when running within a transaction
	createdTask, err := repo.GetByID(ctx, task.ID)
	require.NoError(t, err)
	return createdTask
}
