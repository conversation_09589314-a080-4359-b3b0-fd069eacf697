package testing

import (
	"context"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/repository/common"
	"github.com/chessticize/chessticize-server/internal/utils"
	"github.com/google/uuid"
	"github.com/lib/pq"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestPuzzleGamePGNOmission tests that PGN is omitted when loading puzzles
func TestPuzzleGamePGNOmission(t *testing.T) {
	// Test with fake repository
	provider := GetTestDBProvider(FakeRepository)
	defer provider.Cleanup(t)

	// Create test data
	user, _, puzzles := setupPGNTestData(t, provider)
	puzzleRepo := provider.GetPuzzleRepository(t)

	// Test that PGN is omitted when loading puzzles
	loadedPuzzles, count, err := puzzleRepo.ListByUserID(context.Background(), user.ID, repository.PuzzleFilter{}, 0, 10)
	require.NoError(t, err)
	assert.Equal(t, int64(len(puzzles)), count)
	assert.Equal(t, len(puzzles), len(loadedPuzzles))

	// In the fake repository implementation, we're omitting the Game.CompressedPGN field
	// but we need to verify that the puzzles themselves are loaded correctly
	for _, puzzle := range loadedPuzzles {
		assert.NotEmpty(t, puzzle.ID)
		assert.NotEmpty(t, puzzle.GameID)
		assert.Equal(t, user.ID, puzzle.UserID)
	}
}

// TestGroupingByGameTime tests that grouping works correctly with game time
func TestGroupingByGameTime(t *testing.T) {
	// Test with fake repository
	provider := GetTestDBProvider(FakeRepository)
	defer provider.Cleanup(t)

	// Create test data with specific game times
	user, _, _ := setupPGNTestData(t, provider)
	puzzleRepo := provider.GetPuzzleRepository(t)

	// Create a time range for testing
	startTime := time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)
	endTime := time.Date(2023, 3, 31, 23, 59, 59, 0, time.UTC)

	// Create a filter with the time range
	filter := repository.PuzzleFilter{
		GameStartTime: &startTime,
		GameEndTime:   &endTime,
	}

	// Test grouping by day
	t.Run("Group by Day", func(t *testing.T) {
		grouping := &common.TimeGrouping{
			Unit:   common.TimeGroupingDay,
			Length: 1,
		}

		statsArray, err := puzzleRepo.GetPuzzleStats(context.Background(), user.ID, filter, 0, 100, grouping)
		require.NoError(t, err)

		// Verify that we have stats for each day with puzzles
		assert.Greater(t, len(statsArray), 1, "Expected multiple stats objects when grouping by day")

		// Verify that each stats object has the correct period start and end times
		for _, stats := range statsArray {
			assert.NotNil(t, stats.PeriodStart, "PeriodStart should not be nil")
			assert.NotNil(t, stats.PeriodEnd, "PeriodEnd should not be nil")

			// The period end should be 1 day after the period start
			expectedEnd := stats.PeriodStart.AddDate(0, 0, 1)
			assert.Equal(t, expectedEnd, *stats.PeriodEnd)
		}
	})
}

// setupPGNTestData creates test data for PGN omission and grouping tests
// Returns user, games, puzzles
func setupPGNTestData(t *testing.T, provider TestDBProvider) (*models.User, []*models.Game, []*models.Puzzle) {
	userRepo := provider.GetUserRepository(t)
	gameRepo := provider.GetGameRepository(t)
	puzzleRepo := provider.GetPuzzleRepository(t)

	// Create a test user
	user := CreateTestUser(t, userRepo)

	// Create games with different properties and specific game times
	games := make([]*models.Game, 3)
	puzzles := make([]*models.Puzzle, 0)

	// Create games with different dates
	for i := 0; i < 3; i++ {
		// Create a simple PGN
		pgn := "1. e4 e5 2. Nf3 Nc6 3. Bb5 a6"
		compressedPGN, err := utils.CompressPGN(pgn)
		require.NoError(t, err)

		// Create game with specific game time
		gameTime := time.Date(2023, 1, i+1, 12, 0, 0, 0, time.UTC)

		game := &models.Game{
			ID:            uuid.New().String(),
			UserID:        user.ID,
			Platform:      models.ChessDotCom,
			ChessUsername: "testuser",
			UserColor:     models.White,
			GameTime:      gameTime,
			CompressedPGN: compressedPGN,
			TimeControl:   "10+0",
			Rated:         true,
			WhitePlayer:   `{"username":"testuser","rating":1500}`,
			BlackPlayer:   `{"username":"opponent","rating":1600}`,
			Winner:        models.WinnerWhite,
			Result:        models.Mate,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		}
		err = gameRepo.Create(context.Background(), game)
		require.NoError(t, err)
		games[i] = game

		// Create 2 puzzles for each game
		for j := 0; j < 2; j++ {
			puzzle := &models.Puzzle{
				GameID:      game.ID,
				UserID:      user.ID,
				GameMove:    10 + j,
				FEN:         "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
				Moves:       pq.StringArray{"e2e4", "e7e5"},
				PrevCP:      0,
				CP:          100,
				Theme:       models.OpponentBlunderMissed,
				UserColor:   models.White,
				PuzzleColor: models.Black,
				Zugzwang:    false,
				Tags:        pq.StringArray{"opening", "tactical"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			}
			err = puzzleRepo.Create(context.Background(), puzzle)
			require.NoError(t, err)
			puzzles = append(puzzles, puzzle)
		}
	}

	return user, games, puzzles
}
