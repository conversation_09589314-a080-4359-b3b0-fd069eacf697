package testing

import (
	"context"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/repository/common"
	"github.com/chessticize/chessticize-server/internal/utils"
	"github.com/google/uuid"
	"github.com/lib/pq"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestPuzzleTimeGroupingBehavior tests the behavior of puzzle time grouping
func TestPuzzleTimeGroupingBehavior(t *testing.T) {
	// Test with fake repository
	provider := GetTestDBProvider(FakeRepository)
	defer provider.Cleanup(t)

	RunPuzzleTimeGroupingBehaviorTest(t, provider)
}

// TestRealPuzzleTimeGroupingBehavior tests the behavior of puzzle time grouping with real repository
func TestRealPuzzleTimeGroupingBehavior(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping real database tests")
	}
	provider := GetTestDBProvider(RealRepository)
	defer provider.Cleanup(t)

	RunPuzzleTimeGroupingBehaviorTest(t, provider)
}

// RunPuzzleTimeGroupingBehaviorTest tests the behavior of puzzle time grouping
func RunPuzzleTimeGroupingBehaviorTest(t *testing.T, provider TestDBProvider) {
	userRepo := provider.GetUserRepository(t)
	gameRepo := provider.GetGameRepository(t)
	puzzleRepo := provider.GetPuzzleRepository(t)

	// Create a test user
	user := CreateTestUser(t, userRepo)

	// Create games with specific game times
	// Day 1: 2 games (4 puzzles)
	// Day 2: 0 games (0 puzzles)
	// Day 3: 1 game (2 puzzles)
	gameDates := []time.Time{
		time.Date(2023, 1, 1, 10, 30, 0, 0, time.UTC),
		time.Date(2023, 1, 1, 15, 45, 0, 0, time.UTC),
		time.Date(2023, 1, 3, 12, 0, 0, 0, time.UTC),
	}

	games := make([]*models.Game, len(gameDates))

	for i, gameTime := range gameDates {
		// Create a simple PGN
		pgn := "1. e4 e5 2. Nf3 Nc6 3. Bb5 a6"
		compressedPGN, err := utils.CompressPGN(pgn)
		require.NoError(t, err)

		game := &models.Game{
			ID:            uuid.New().String(),
			UserID:        user.ID,
			Platform:      models.ChessDotCom,
			ChessUsername: "testuser",
			UserColor:     models.White,
			GameTime:      gameTime,
			CompressedPGN: compressedPGN,
			TimeControl:   "10+0",
			Rated:         true,
			WhitePlayer:   `{"username":"testuser","rating":1500}`,
			BlackPlayer:   `{"username":"opponent","rating":1600}`,
			Winner:        models.WinnerWhite,
			Result:        models.Mate,
			CreatedAt:     time.Now(), // Use current time for CreatedAt
			UpdatedAt:     time.Now(),
		}
		err = gameRepo.Create(context.Background(), game)
		require.NoError(t, err)
		games[i] = game

		// Create 2 puzzles for each game
		for j := 0; j < 2; j++ {
			puzzle := &models.Puzzle{
				GameID:      game.ID,
				UserID:      user.ID,
				GameMove:    10 + j,
				FEN:         "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
				Moves:       pq.StringArray{"e2e4", "e7e5"},
				PrevCP:      0,
				CP:          100,
				Theme:       models.OpponentBlunderMissed,
				UserColor:   models.White,
				PuzzleColor: models.Black,
				Zugzwang:    false,
				Tags:        pq.StringArray{"opening", "tactical"},
				CreatedAt:   time.Now(), // Use current time for CreatedAt
				UpdatedAt:   time.Now(),
			}
			err = puzzleRepo.Create(context.Background(), puzzle)
			require.NoError(t, err)
		}
	}

	// Create a time range for testing
	startTime := time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)
	endTime := time.Date(2023, 1, 3, 23, 59, 59, 0, time.UTC)

	// Create a filter with the time range
	filter := repository.PuzzleFilter{
		GameStartTime: &startTime,
		GameEndTime:   &endTime,
	}

	// Test grouping by day
	t.Run("Group by Day", func(t *testing.T) {
		grouping := &common.TimeGrouping{
			Unit:   common.TimeGroupingDay,
			Length: 1,
		}

		statsArray, err := puzzleRepo.GetPuzzleStats(context.Background(), user.ID, filter, 0, 100, grouping)
		require.NoError(t, err)

		// With backward calculation, we should have 3 days (Jan 1-3, 2023)
		// No extra empty period since we don't round down startTime
		assert.Equal(t, 3, len(statsArray), "Expected 3 consecutive days of stats")

		// Verify that each day has the correct number of puzzles
		dayMap := make(map[string]*models.PuzzleStats)
		for _, stats := range statsArray {
			assert.NotNil(t, stats.PeriodStart, "PeriodStart should not be nil")
			// Use date string as key to avoid time zone issues
			dateStr := stats.PeriodStart.Format("2006-01-02")
			dayMap[dateStr] = stats
		}

		// Check Jan 1 (should have 4 puzzles from Jan 1 games)
		assert.Contains(t, dayMap, "2023-01-01", "Stats should include Jan 1")
		if stats, ok := dayMap["2023-01-01"]; ok {
			assert.Equal(t, int64(4), stats.TotalCount, "Jan 1 should have 4 puzzles")
		}

		// Check Jan 2 (should have 0 puzzles)
		assert.Contains(t, dayMap, "2023-01-02", "Stats should include Jan 2")
		if stats, ok := dayMap["2023-01-02"]; ok {
			assert.Equal(t, int64(0), stats.TotalCount, "Jan 2 should have 0 puzzles")
		}

		// Check Jan 3 (should have 2 puzzles from Jan 3 games)
		assert.Contains(t, dayMap, "2023-01-03", "Stats should include Jan 3")
		if stats, ok := dayMap["2023-01-03"]; ok {
			assert.Equal(t, int64(2), stats.TotalCount, "Jan 3 should have 2 puzzles")
		}
	})

	// Test with empty result
	t.Run("Empty Result", func(t *testing.T) {
		// Create a filter with a time range that doesn't match any puzzles
		emptyStartTime := time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC)
		emptyEndTime := time.Date(2022, 1, 31, 23, 59, 59, 0, time.UTC)
		emptyFilter := repository.PuzzleFilter{
			GameStartTime: &emptyStartTime,
			GameEndTime:   &emptyEndTime,
		}

		grouping := &common.TimeGrouping{
			Unit:   common.TimeGroupingDay,
			Length: 1,
		}

		statsArray, err := puzzleRepo.GetPuzzleStats(context.Background(), user.ID, emptyFilter, 0, 100, grouping)
		require.NoError(t, err)

		// We should have 0 stats objects
		assert.Equal(t, 0, len(statsArray), "Expected 0 stats objects when no puzzles match the filter")
	})
}

// TestGameTimeGroupingBehavior tests the behavior of game time grouping
func TestGameTimeGroupingBehavior(t *testing.T) {
	// Test with fake repository
	provider := GetTestDBProvider(FakeRepository)
	defer provider.Cleanup(t)

	RunGameTimeGroupingBehaviorTest(t, provider)
}

// TestRealGameTimeGroupingBehavior tests the behavior of game time grouping with real repository
func TestRealGameTimeGroupingBehavior(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping real database tests")
	}
	provider := GetTestDBProvider(RealRepository)
	defer provider.Cleanup(t)

	RunGameTimeGroupingBehaviorTest(t, provider)
}

// RunGameTimeGroupingBehaviorTest tests the behavior of game time grouping
func RunGameTimeGroupingBehaviorTest(t *testing.T, provider TestDBProvider) {
	userRepo := provider.GetUserRepository(t)
	gameRepo := provider.GetGameRepository(t)

	// Create a test user
	user := CreateTestUser(t, userRepo)

	// Create games with specific game times
	// Day 1: 2 games
	// Day 2: 0 games
	// Day 3: 1 game
	gameDates := []time.Time{
		time.Date(2023, 1, 1, 10, 30, 0, 0, time.UTC),
		time.Date(2023, 1, 1, 15, 45, 0, 0, time.UTC),
		time.Date(2023, 1, 3, 12, 0, 0, 0, time.UTC),
	}

	games := make([]*models.Game, len(gameDates))

	for i, gameTime := range gameDates {
		// Create a simple PGN
		pgn := "1. e4 e5 2. Nf3 Nc6 3. Bb5 a6"
		compressedPGN, err := utils.CompressPGN(pgn)
		require.NoError(t, err)

		game := &models.Game{
			ID:            uuid.New().String(),
			UserID:        user.ID,
			Platform:      models.ChessDotCom,
			ChessUsername: "testuser",
			UserColor:     models.White,
			GameTime:      gameTime,
			CompressedPGN: compressedPGN,
			TimeControl:   "10+0",
			Rated:         true,
			WhitePlayer:   `{"username":"testuser","rating":1500}`,
			BlackPlayer:   `{"username":"opponent","rating":1600}`,
			Winner:        models.WinnerWhite,
			Result:        models.Mate,
			CreatedAt:     time.Now(), // Use current time for CreatedAt
			UpdatedAt:     time.Now(),
		}
		err = gameRepo.Create(context.Background(), game)
		require.NoError(t, err)
		games[i] = game
	}

	// Create a time range for testing
	startTime := time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)
	endTime := time.Date(2023, 1, 3, 23, 59, 59, 0, time.UTC)

	// Create a filter with the time range
	filter := repository.GameFilter{
		StartTime: &startTime,
		EndTime:   &endTime,
	}

	// Test grouping by day
	t.Run("Group by Day", func(t *testing.T) {
		grouping := &common.TimeGrouping{
			Unit:   common.TimeGroupingDay,
			Length: 1,
		}

		statsArray, err := gameRepo.GetGameStats(context.Background(), user.ID, filter, 0, 100, grouping)
		require.NoError(t, err)

		// With backward calculation, we should have 3 days (Jan 1-3, 2023)
		// No extra empty period since we don't round down startTime
		assert.Equal(t, 3, len(statsArray), "Expected 3 consecutive days of stats")

		// Verify that each day has the correct number of games
		dayMap := make(map[string]*models.GameStats)
		for _, stats := range statsArray {
			assert.NotNil(t, stats.PeriodStart, "PeriodStart should not be nil")
			// Use date string as key to avoid time zone issues
			dateStr := stats.PeriodStart.Format("2006-01-02")
			dayMap[dateStr] = stats
		}

		// Check Jan 1 (should have 2 games from Jan 1)
		assert.Contains(t, dayMap, "2023-01-01", "Stats should include Jan 1")
		if stats, ok := dayMap["2023-01-01"]; ok {
			assert.Equal(t, int64(2), stats.TotalCount, "Jan 1 should have 2 games")
		}

		// Check Jan 2 (should have 0 games)
		assert.Contains(t, dayMap, "2023-01-02", "Stats should include Jan 2")
		if stats, ok := dayMap["2023-01-02"]; ok {
			assert.Equal(t, int64(0), stats.TotalCount, "Jan 2 should have 0 games")
		}

		// Check Jan 3 (should have 1 game from Jan 3)
		assert.Contains(t, dayMap, "2023-01-03", "Stats should include Jan 3")
		if stats, ok := dayMap["2023-01-03"]; ok {
			assert.Equal(t, int64(1), stats.TotalCount, "Jan 3 should have 1 game")
		}
	})

	// Test with empty result
	t.Run("Empty Result", func(t *testing.T) {
		// Create a filter with a time range that doesn't match any games
		emptyStartTime := time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC)
		emptyEndTime := time.Date(2022, 1, 31, 23, 59, 59, 0, time.UTC)
		emptyFilter := repository.GameFilter{
			StartTime: &emptyStartTime,
			EndTime:   &emptyEndTime,
		}

		grouping := &common.TimeGrouping{
			Unit:   common.TimeGroupingDay,
			Length: 1,
		}

		statsArray, err := gameRepo.GetGameStats(context.Background(), user.ID, emptyFilter, 0, 100, grouping)
		require.NoError(t, err)

		// We should have 0 stats objects
		assert.Equal(t, 0, len(statsArray), "Expected 0 stats objects when no games match the filter")
	})
}
