package testing

import (
	"context"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/lib/pq"
	"github.com/stretchr/testify/assert"
)

// TestPuzzleRepository runs all tests for both real and fake puzzle repositories
func TestPuzzleRepository(t *testing.T) {
	// Test with SQLite by default for faster tests
	provider := GetTestDBProvider(FakeRepository)
	defer provider.Cleanup(t)

	RunPuzzleRepositoryTests(t, provider)
}

// TestRealPuzzleRepository runs tests against the real PostgreSQL repository implementation
// This will be skipped if the test database is not available
func TestRealPuzzleRepository(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping real database tests")
	}
	provider := GetTestDBProvider(RealRepository)
	defer provider.Cleanup(t)

	RunPuzzleRepositoryTests(t, provider)
}

// RunPuzzleRepositoryTests runs all tests for a puzzle repository
func RunPuzzleRepositoryTests(t *testing.T, provider TestDBProvider) {
	userRepo := provider.GetUserRepository(t)
	gameRepo := provider.GetGameRepository(t)
	puzzleRepo := provider.GetPuzzleRepository(t)

	t.Run("Create and GetByID", func(t *testing.T) {
		user := CreateTestUser(t, userRepo)
		game := CreateTestGame(t, gameRepo, user.ID)
		puzzle := CreateTestPuzzle(t, puzzleRepo, game.ID, user.ID)
		found, err := puzzleRepo.GetByID(context.Background(), puzzle.ID)
		assert.NoError(t, err)
		assert.Equal(t, puzzle.ID, found.ID)
		assert.Equal(t, puzzle.GameID, found.GameID)
		assert.Equal(t, puzzle.UserID, found.UserID)
		// Check timestamps
		assert.False(t, found.CreatedAt.IsZero(), "CreatedAt should not be zero")
		assert.False(t, found.UpdatedAt.IsZero(), "UpdatedAt should not be zero")
		assert.WithinDuration(t, found.CreatedAt, found.UpdatedAt, 5*time.Second, "UpdatedAt should be close to CreatedAt initially")
		assert.WithinDuration(t, time.Now(), found.CreatedAt, 10*time.Second, "CreatedAt should be recent")
	})

	t.Run("GetByGameID", func(t *testing.T) {
		user := CreateTestUser(t, userRepo)
		game := CreateTestGame(t, gameRepo, user.ID)
		puzzle1 := CreateTestPuzzle(t, puzzleRepo, game.ID, user.ID)
		puzzle2 := CreateTestPuzzle(t, puzzleRepo, game.ID, user.ID)

		puzzles, err := puzzleRepo.GetByGameID(context.Background(), game.ID)
		assert.NoError(t, err)
		assert.Len(t, puzzles, 2)
		assert.Contains(t, []string{puzzles[0].ID, puzzles[1].ID}, puzzle1.ID)
		assert.Contains(t, []string{puzzles[0].ID, puzzles[1].ID}, puzzle2.ID)
	})

	t.Run("Update", func(t *testing.T) {
		user := CreateTestUser(t, userRepo)
		game := CreateTestGame(t, gameRepo, user.ID)
		puzzle := CreateTestPuzzle(t, puzzleRepo, game.ID, user.ID)

		// Get initial timestamps
		initialPuzzle, err := puzzleRepo.GetByID(context.Background(), puzzle.ID)
		assert.NoError(t, err)
		initialCreatedAt := initialPuzzle.CreatedAt
		initialUpdatedAt := initialPuzzle.UpdatedAt
		assert.False(t, initialCreatedAt.IsZero(), "Initial CreatedAt should not be zero")
		assert.False(t, initialUpdatedAt.IsZero(), "Initial UpdatedAt should not be zero")

		// Update the puzzle with a new FEN
		newFEN := RandomString(50)
		puzzle.FEN = newFEN
		// Wait a bit to ensure UpdatedAt changes noticeably
		time.Sleep(10 * time.Millisecond)
		err = puzzleRepo.Update(context.Background(), puzzle)
		assert.NoError(t, err)

		// Verify the update and timestamps
		found, err := puzzleRepo.GetByID(context.Background(), puzzle.ID)
		assert.NoError(t, err)
		assert.Equal(t, newFEN, found.FEN)
		// Check timestamps after update
		assert.Equal(t, initialCreatedAt, found.CreatedAt, "CreatedAt should not change on update")
		assert.True(t, found.UpdatedAt.After(initialUpdatedAt), "UpdatedAt (%v) should be later than initial UpdatedAt (%v)", found.UpdatedAt, initialUpdatedAt)
		assert.WithinDuration(t, time.Now(), found.UpdatedAt, 10*time.Second, "UpdatedAt should be recent after update")
	})

	t.Run("Delete", func(t *testing.T) {
		user := CreateTestUser(t, userRepo)
		game := CreateTestGame(t, gameRepo, user.ID)
		puzzle := CreateTestPuzzle(t, puzzleRepo, game.ID, user.ID)

		// Delete the puzzle
		err := puzzleRepo.Delete(context.Background(), puzzle.ID)
		assert.NoError(t, err)

		// Verify the deletion
		_, err = puzzleRepo.GetByID(context.Background(), puzzle.ID)
		assert.Error(t, err)
	})

	t.Run("ListByUserID", func(t *testing.T) {
		user := CreateTestUser(t, userRepo)
		now := time.Now().UTC().Truncate(time.Microsecond)
		past := now.Add(-2 * time.Hour)
		future := now.Add(2 * time.Hour)

		// Create games at different times
		gamePast := CreateTestGame(t, gameRepo, user.ID)
		gamePast.GameTime = past
		err := gameRepo.Update(context.Background(), gamePast)
		assert.NoError(t, err)

		gameNow := CreateTestGame(t, gameRepo, user.ID)
		gameNow.GameTime = now
		err = gameRepo.Update(context.Background(), gameNow)
		assert.NoError(t, err)

		gameFuture := CreateTestGame(t, gameRepo, user.ID)
		gameFuture.GameTime = future
		err = gameRepo.Update(context.Background(), gameFuture)
		assert.NoError(t, err)

		// Create puzzles with different tags associated with different games
		puzzle1 := CreateTestPuzzle(t, puzzleRepo, gamePast.ID, user.ID)
		puzzle1.Tags = pq.StringArray{"opening", "tactic"}
		err = puzzleRepo.Update(context.Background(), puzzle1)
		assert.NoError(t, err)

		puzzle2 := CreateTestPuzzle(t, puzzleRepo, gameNow.ID, user.ID)
		puzzle2.Tags = pq.StringArray{"middlegame", "tactic"}
		err = puzzleRepo.Update(context.Background(), puzzle2)
		assert.NoError(t, err)

		puzzle3 := CreateTestPuzzle(t, puzzleRepo, gameFuture.ID, user.ID)
		puzzle3.Tags = pq.StringArray{"endgame", "mate"}
		err = puzzleRepo.Update(context.Background(), puzzle3)
		assert.NoError(t, err)

		// Test case 1: No filters, get all puzzles for the user, default pagination
		puzzles, total, err := puzzleRepo.ListByUserID(context.Background(), user.ID, repository.PuzzleFilter{}, 0, 10)
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, total, int64(3)) // Might be other puzzles
		assert.GreaterOrEqual(t, len(puzzles), 3)
		// Check if our created puzzles are present (order depends on created_at DESC)
		foundIDs := make(map[string]bool)
		for _, p := range puzzles {
			foundIDs[p.ID] = true
		}
		assert.True(t, foundIDs[puzzle1.ID])
		assert.True(t, foundIDs[puzzle2.ID])
		assert.True(t, foundIDs[puzzle3.ID])

		// Test case 2: Filter by a single tag ("tactic")
		filter := repository.PuzzleFilter{Tags: []string{"tactic"}}
		puzzles, total, err = puzzleRepo.ListByUserID(context.Background(), user.ID, filter, 0, 10)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), total) // puzzle1 and puzzle2
		assert.Len(t, puzzles, 2)
		foundIDs = make(map[string]bool)
		for _, p := range puzzles {
			foundIDs[p.ID] = true
		}
		assert.True(t, foundIDs[puzzle1.ID])
		assert.True(t, foundIDs[puzzle2.ID])

		// Test case 3: Filter by multiple tags ("opening" or "endgame")
		filter = repository.PuzzleFilter{Tags: []string{"opening", "endgame"}}
		puzzles, total, err = puzzleRepo.ListByUserID(context.Background(), user.ID, filter, 0, 10)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), total) // puzzle1 and puzzle3
		assert.Len(t, puzzles, 2)
		foundIDs = make(map[string]bool)
		for _, p := range puzzles {
			foundIDs[p.ID] = true
		}
		assert.True(t, foundIDs[puzzle1.ID])
		assert.True(t, foundIDs[puzzle3.ID])

		// Test case 4: Filter by game start time (associated game)
		startTime := now.Add(-1 * time.Hour) // Should include puzzles from gameNow and gameFuture
		filter = repository.PuzzleFilter{GameStartTime: &startTime}
		puzzles, total, err = puzzleRepo.ListByUserID(context.Background(), user.ID, filter, 0, 10)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), total) // puzzle2 and puzzle3
		assert.Len(t, puzzles, 2)
		foundIDs = make(map[string]bool)
		for _, p := range puzzles {
			foundIDs[p.ID] = true
		}
		assert.True(t, foundIDs[puzzle2.ID])
		assert.True(t, foundIDs[puzzle3.ID])

		// Test case 5: Filter by game end time (associated game)
		endTime := now.Add(1 * time.Hour) // Should include puzzles from gamePast and gameNow
		filter = repository.PuzzleFilter{GameEndTime: &endTime}
		puzzles, total, err = puzzleRepo.ListByUserID(context.Background(), user.ID, filter, 0, 10)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), total) // puzzle1 and puzzle2
		assert.Len(t, puzzles, 2)
		foundIDs = make(map[string]bool)
		for _, p := range puzzles {
			foundIDs[p.ID] = true
		}
		assert.True(t, foundIDs[puzzle1.ID])
		assert.True(t, foundIDs[puzzle2.ID])

		// Test case 6: Filter by tag and game time range
		// Tag "tactic", game time between past-1h and now+1h
		// Corrected expectation: tag=tactic AND game time >= past AND game time <= future
		// puzzle1: tactic, past -> YES
		// puzzle2: tactic, now -> YES
		// puzzle3: no tactic -> NO
		filter = repository.PuzzleFilter{
			Tags:          []string{"tactic"},
			GameStartTime: &past,
			GameEndTime:   &now, // Only include up to 'now'
		}
		puzzles, total, err = puzzleRepo.ListByUserID(context.Background(), user.ID, filter, 0, 10)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), total) // puzzle1 and puzzle2
		assert.Len(t, puzzles, 2)
		foundIDs = make(map[string]bool)
		for _, p := range puzzles {
			foundIDs[p.ID] = true
		}
		assert.True(t, foundIDs[puzzle1.ID])
		assert.True(t, foundIDs[puzzle2.ID])

		// Test case 7: Pagination (Limit)
		puzzles, total, err = puzzleRepo.ListByUserID(context.Background(), user.ID, repository.PuzzleFilter{}, 0, 1)
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, total, int64(3))
		assert.Len(t, puzzles, 1)
		// Order is puzzles.created_at DESC. puzzle3 was created last.
		assert.Equal(t, puzzle3.ID, puzzles[0].ID)

		// Test case 8: Pagination (Offset and Limit)
		puzzles, total, err = puzzleRepo.ListByUserID(context.Background(), user.ID, repository.PuzzleFilter{}, 1, 1)
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, total, int64(3))
		assert.Len(t, puzzles, 1)
		// Second puzzle created should be puzzle2
		assert.Equal(t, puzzle2.ID, puzzles[0].ID)

		// Test case 9: No results found (non-existent tag)
		filter = repository.PuzzleFilter{Tags: []string{"nonexistent"}}
		puzzles, total, err = puzzleRepo.ListByUserID(context.Background(), user.ID, filter, 0, 10)
		assert.NoError(t, err)
		assert.Equal(t, int64(0), total)
		assert.Len(t, puzzles, 0)

		// Create a new game for our special puzzle
		specialGame := CreateTestGame(t, gameRepo, user.ID)

		// Test case 10: Test all filters with a single puzzle
		// First, create a puzzle with specific attributes that we'll filter for
		specialPuzzle := &models.Puzzle{
			GameID:      specialGame.ID,
			UserID:      user.ID,
			GameMove:    20, // Specific game move
			FEN:         "special_test_position",
			Moves:       []string{"e2e4", "e7e5"},
			PrevCP:      50,                           // Specific prevCP
			CP:          350,                          // CP change of 300
			Theme:       models.OpponentBlunderCaught, // Specific theme
			UserColor:   models.White,                 // Specific user color
			PuzzleColor: models.Black,                 // Specific puzzle color
			Zugzwang:    true,
			Tags:        []string{"special", "test"},
		}
		err = puzzleRepo.Create(context.Background(), specialPuzzle)
		assert.NoError(t, err)

		// Make sure the other puzzles have different values
		puzzle1.Theme = models.OpponentMistakeCaught // Different theme
		puzzle1.GameMove = 10                        // Different game move
		puzzle1.PrevCP = -100                        // Different prevCP
		puzzle1.CP = -50                             // Different CP change
		err = puzzleRepo.Update(context.Background(), puzzle1)
		assert.NoError(t, err)

		puzzle2.UserColor = models.Black // Different user color
		puzzle2.GameMove = 30            // Different game move
		puzzle2.PrevCP = 200             // Different prevCP
		puzzle2.CP = 220                 // Different CP change
		err = puzzleRepo.Update(context.Background(), puzzle2)
		assert.NoError(t, err)

		puzzle3.PuzzleColor = models.White // Different puzzle color
		puzzle3.GameMove = 5               // Different game move
		puzzle3.PrevCP = -200              // Different prevCP
		puzzle3.CP = 0                     // Different CP change
		err = puzzleRepo.Update(context.Background(), puzzle3)
		assert.NoError(t, err)

		// Test filter by theme
		theme := models.OpponentBlunderCaught
		filter = repository.PuzzleFilter{Themes: []models.PuzzleTheme{theme}}
		puzzles, total, err = puzzleRepo.ListByUserID(context.Background(), user.ID, filter, 0, 10)
		assert.NoError(t, err)
		assert.Equal(t, int64(1), total)
		assert.Len(t, puzzles, 1)
		assert.Equal(t, specialPuzzle.ID, puzzles[0].ID)

		// Test filter by user color
		whiteColor := models.White
		filter = repository.PuzzleFilter{UserColor: &whiteColor}
		puzzles, total, err = puzzleRepo.ListByUserID(context.Background(), user.ID, filter, 0, 10)
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, total, int64(3)) // specialPuzzle, puzzle1, puzzle3
		assert.GreaterOrEqual(t, len(puzzles), 3)

		// Test filter by puzzle color
		blackColor := models.Black
		filter = repository.PuzzleFilter{PuzzleColor: &blackColor}
		puzzles, total, err = puzzleRepo.ListByUserID(context.Background(), user.ID, filter, 0, 10)
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, total, int64(3)) // specialPuzzle, puzzle1, puzzle2
		assert.GreaterOrEqual(t, len(puzzles), 3)

		// Test filter by game move range
		gameMove15 := 15
		gameMove25 := 25
		filter = repository.PuzzleFilter{GameMoveMin: &gameMove15, GameMoveMax: &gameMove25}
		puzzles, total, err = puzzleRepo.ListByUserID(context.Background(), user.ID, filter, 0, 10)
		assert.NoError(t, err)
		assert.Equal(t, int64(1), total) // Only specialPuzzle should match
		assert.Len(t, puzzles, 1)
		assert.Equal(t, specialPuzzle.ID, puzzles[0].ID)

		// Test filter by previous CP range
		prevCpMin := 40
		prevCpMax := 60
		filter = repository.PuzzleFilter{PrevCpMin: &prevCpMin, PrevCpMax: &prevCpMax}
		puzzles, total, err = puzzleRepo.ListByUserID(context.Background(), user.ID, filter, 0, 10)
		assert.NoError(t, err)
		assert.Equal(t, int64(1), total) // Only specialPuzzle should match
		assert.Len(t, puzzles, 1)
		assert.Equal(t, specialPuzzle.ID, puzzles[0].ID)

		// Test filter by CP change range
		cpChangeMin := 250
		cpChangeMax := 350
		filter = repository.PuzzleFilter{CpChangeMin: &cpChangeMin, CpChangeMax: &cpChangeMax}
		puzzles, total, err = puzzleRepo.ListByUserID(context.Background(), user.ID, filter, 0, 10)
		assert.NoError(t, err)
		assert.Equal(t, int64(1), total) // Only specialPuzzle should match
		assert.Len(t, puzzles, 1)
		assert.Equal(t, specialPuzzle.ID, puzzles[0].ID)

		// Test combined filters
		filter = repository.PuzzleFilter{
			Themes:      []models.PuzzleTheme{theme},
			UserColor:   &whiteColor,
			PuzzleColor: &blackColor,
			GameMoveMin: &gameMove15,
			GameMoveMax: &gameMove25,
			PrevCpMin:   &prevCpMin,
			PrevCpMax:   &prevCpMax,
			CpChangeMin: &cpChangeMin,
			CpChangeMax: &cpChangeMax,
		}
		puzzles, total, err = puzzleRepo.ListByUserID(context.Background(), user.ID, filter, 0, 10)
		assert.NoError(t, err)
		assert.Equal(t, int64(1), total) // Only specialPuzzle should match all criteria
		assert.Len(t, puzzles, 1)
		assert.Equal(t, specialPuzzle.ID, puzzles[0].ID)
	})
}
