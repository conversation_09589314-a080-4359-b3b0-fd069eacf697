package testing

import (
	"context"
	"net/http"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/repository/fake"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"
)

// TestIdempotencyRepository runs all tests for both real and fake idempotency repositories
func TestIdempotencyRepository(t *testing.T) {
	// Test with SQLite by default for faster tests
	provider := GetTestDBProvider(FakeRepository)
	defer provider.Cleanup(t)

	RunIdempotencyRepositoryTests(t, provider)
}

// TestRealIdempotencyRepository runs tests against the real PostgreSQL repository implementation
func TestRealIdempotencyRepository(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping real database tests")
	}
	provider := GetTestDBProvider(RealRepository)
	defer provider.Cleanup(t)

	RunIdempotencyRepositoryTests(t, provider)
}

// RunIdempotencyRepositoryTests runs all tests for an idempotency repository
func RunIdempotencyRepositoryTests(t *testing.T, provider TestDBProvider) {
	var db *gorm.DB
	isRealTest := false

	if p, ok := provider.(*PostgresTestDBProvider); ok {
		isRealTest = true
		db = p.GetDB(t)
		// Clean the table for real tests
		err := db.Exec("DELETE FROM idempotency_records").Error
		require.NoError(t, err, "Failed to clean idempotency_records table before tests")
	}

	ctx := context.Background()

	runTestCase := func(name string, testFunc func(t *testing.T, repo repository.IIdempotencyRepository)) {
		t.Run(name, func(t *testing.T) {
			var currentRepo repository.IIdempotencyRepository

			if isRealTest {
				// Clean table before each real test to prevent interference
				err := db.Exec("DELETE FROM idempotency_records").Error
				require.NoError(t, err, "Failed to clean idempotency_records before test")

				// Create repo using the base DB connection, not a transaction
				currentRepo = repository.NewIdempotencyRepository(db)
			} else {
				// For fake tests, get a fresh repo instance (provider handles this)
				currentRepo = provider.GetIdempotencyRepository(t)
			}

			testFunc(t, currentRepo)
		})
	}

	// --- Test Cases --- //

	runTestCase("CreateAndGetRecord", func(t *testing.T, repo repository.IIdempotencyRepository) {
		recordToCreate := &models.IdempotencyRecord{
			UserID:             "user-123",
			IdempotencyKey:     uuid.NewString(),
			RequestMethod:      http.MethodPost,
			RequestPath:        "/api/v1/items",
			ResponseStatusCode: http.StatusOK,
			ResponseBody:       []byte(`{"message":"Success"}`),
			// CreatedAt and ExpiresAt will be set by repo or default
		}

		err := repo.Create(ctx, recordToCreate)
		require.NoError(t, err, "Failed to create record")
		require.NotEmpty(t, recordToCreate.ID, "Record ID should be set after creation")
		require.False(t, recordToCreate.CreatedAt.IsZero(), "CreatedAt should be set")
		require.False(t, recordToCreate.ExpiresAt.IsZero(), "ExpiresAt should be set")
		assert.True(t, recordToCreate.ExpiresAt.After(recordToCreate.CreatedAt), "ExpiresAt should be after CreatedAt")

		// Get the record back
		retrievedRecord, err := repo.Get(ctx, recordToCreate.IdempotencyKey, recordToCreate.UserID, recordToCreate.RequestMethod, recordToCreate.RequestPath)
		require.NoError(t, err, "Failed to get record")
		require.NotNil(t, retrievedRecord, "Retrieved record should not be nil")

		// Assertions
		assert.Equal(t, recordToCreate.ID, retrievedRecord.ID)
		assert.Equal(t, recordToCreate.UserID, retrievedRecord.UserID)
		assert.Equal(t, recordToCreate.IdempotencyKey, retrievedRecord.IdempotencyKey)
		assert.Equal(t, recordToCreate.RequestMethod, retrievedRecord.RequestMethod)
		assert.Equal(t, recordToCreate.RequestPath, retrievedRecord.RequestPath)
		assert.Equal(t, recordToCreate.ResponseStatusCode, retrievedRecord.ResponseStatusCode)
		assert.Equal(t, recordToCreate.ResponseBody, retrievedRecord.ResponseBody)
		assert.WithinDuration(t, recordToCreate.CreatedAt, retrievedRecord.CreatedAt, time.Second)
		assert.WithinDuration(t, recordToCreate.ExpiresAt, retrievedRecord.ExpiresAt, time.Second)
	})

	runTestCase("GetNotFound", func(t *testing.T, repo repository.IIdempotencyRepository) {
		retrievedRecord, err := repo.Get(ctx, uuid.NewString(), "nonexistent-user", http.MethodGet, "/api/v1/notfound")

		assert.ErrorIs(t, err, repository.ErrNotFound, "Expected ErrNotFound")
		assert.Nil(t, retrievedRecord, "Retrieved record should be nil for non-existent key/user/method/path")
	})

	runTestCase("CreateDuplicateKeyForUser", func(t *testing.T, repo repository.IIdempotencyRepository) {
		userID := "user-duplicate-test"
		idempotencyKey := uuid.NewString()

		record1 := &models.IdempotencyRecord{
			UserID:             userID,
			IdempotencyKey:     idempotencyKey,
			RequestMethod:      http.MethodPost,
			RequestPath:        "/api/v1/dupe",
			ResponseStatusCode: http.StatusCreated,
			ResponseBody:       []byte(`{"id":"1"}`),
		}
		err := repo.Create(ctx, record1)
		require.NoError(t, err, "Failed to create first record")

		// Attempt to create another record with the same user ID, key, method, and path
		record2 := &models.IdempotencyRecord{
			UserID:             userID,
			IdempotencyKey:     idempotencyKey,
			RequestMethod:      record1.RequestMethod,
			RequestPath:        record1.RequestPath,
			ResponseStatusCode: http.StatusConflict,
			ResponseBody:       []byte(`{"id":"2"}`),
		}
		err = repo.Create(ctx, record2)
		require.Error(t, err, "Expected error when creating duplicate record")

		// Depending on DB (Postgres vs SQLite), the error might differ.
		// GORM might return a generic error, or a driver-specific unique constraint violation.
		// For now, just check that *an* error occurred.
		// TODO: Check for specific unique constraint error if needed.

		// --- Start: Added Check ---
		// Check that the first record still exists and is unchanged
		// after the second (duplicate) create attempt failed.
		// NOTE: This might fail in real transaction tests if the transaction is aborted.
		retrievedRecord1, getErr := repo.Get(ctx, idempotencyKey, userID, record1.RequestMethod, record1.RequestPath)
		require.NoError(t, getErr, "Failed to get first record after duplicate attempt")
		require.NotNil(t, retrievedRecord1, "First record should not be nil after duplicate attempt")
		assert.Equal(t, record1.ID, retrievedRecord1.ID, "ID of retrieved first record should match original")
		assert.Equal(t, record1.ResponseStatusCode, retrievedRecord1.ResponseStatusCode, "Status code of retrieved first record should match original")
		assert.Equal(t, record1.ResponseBody, retrievedRecord1.ResponseBody, "Body of retrieved first record should match original")
		// --- End: Added Check ---
	})

	runTestCase("CreateSameKeyUserMethodPath", func(t *testing.T, repo repository.IIdempotencyRepository) {
		idempotencyKey := uuid.NewString()
		userID := "user-multi-test"
		path := "/api/v1/multi"
		method := http.MethodPost

		// Record 1: Base
		record1 := &models.IdempotencyRecord{
			UserID:             userID,
			IdempotencyKey:     idempotencyKey,
			RequestMethod:      method,
			RequestPath:        path,
			ResponseStatusCode: http.StatusOK,
		}
		err := repo.Create(ctx, record1)
		require.NoError(t, err, "Failed to create record1")

		// Record 2: Different User
		record2 := &models.IdempotencyRecord{
			UserID:             "user-other",
			IdempotencyKey:     idempotencyKey,
			RequestMethod:      method,
			RequestPath:        path,
			ResponseStatusCode: http.StatusOK,
		}
		err = repo.Create(ctx, record2)
		require.NoError(t, err, "Failed to create record2 (different user)")

		// Record 3: Different Method
		record3 := &models.IdempotencyRecord{
			UserID:             userID,
			IdempotencyKey:     idempotencyKey,
			RequestMethod:      http.MethodPut,
			RequestPath:        path,
			ResponseStatusCode: http.StatusOK,
		}
		err = repo.Create(ctx, record3)
		require.NoError(t, err, "Failed to create record3 (different method)")

		// Record 4: Different Path
		record4 := &models.IdempotencyRecord{
			UserID:             userID,
			IdempotencyKey:     idempotencyKey,
			RequestMethod:      method,
			RequestPath:        "/api/v1/multi-other",
			ResponseStatusCode: http.StatusOK,
		}
		err = repo.Create(ctx, record4)
		require.NoError(t, err, "Failed to create record4 (different path)")

		// Verify all records exist
		retrieved1, err := repo.Get(ctx, idempotencyKey, userID, method, path)
		require.NoError(t, err)
		assert.NotNil(t, retrieved1)
		assert.Equal(t, record1.ID, retrieved1.ID)

		retrieved2, err := repo.Get(ctx, idempotencyKey, record2.UserID, method, path)
		require.NoError(t, err)
		assert.NotNil(t, retrieved2)
		assert.Equal(t, record2.ID, retrieved2.ID)

		retrieved3, err := repo.Get(ctx, idempotencyKey, userID, record3.RequestMethod, path)
		require.NoError(t, err)
		assert.NotNil(t, retrieved3)
		assert.Equal(t, record3.ID, retrieved3.ID)

		retrieved4, err := repo.Get(ctx, idempotencyKey, userID, method, record4.RequestPath)
		require.NoError(t, err)
		assert.NotNil(t, retrieved4)
		assert.Equal(t, record4.ID, retrieved4.ID)
	})

	runTestCase("DeleteExpired", func(t *testing.T, repo repository.IIdempotencyRepository) {
		now := time.Now()
		past := now.Add(-1 * time.Hour)
		future := now.Add(1 * time.Hour)

		// Need a way to create with specific timestamps, especially for fake repo
		createRecordWithTime := func(expiresAt time.Time) *models.IdempotencyRecord {
			rec := &models.IdempotencyRecord{
				UserID:         "user-expiry-test",
				IdempotencyKey: uuid.NewString(),
				RequestMethod:  http.MethodDelete,
				RequestPath:    "/api/v1/oldstuff",
				CreatedAt:      now.Add(-2 * time.Hour), // Assume created earlier
				ExpiresAt:      expiresAt,
			}
			var err error
			if fakeRepo, ok := repo.(*fake.FakeIdempotencyRepository); ok {
				// Use fake repo's test helper if available
				err = fakeRepo.CreateWithTimestamp(ctx, rec, rec.CreatedAt, rec.ExpiresAt)
			} else {
				// For real repo, create normally (though timestamp control is trickier)
				err = repo.Create(ctx, rec)
			}
			require.NoError(t, err, "Failed to create record for expiry test")
			return rec
		}

		// Create records
		recExpired1 := createRecordWithTime(past)
		recExpired2 := createRecordWithTime(now.Add(-1 * time.Minute))
		recActive1 := createRecordWithTime(future)
		recActive2 := createRecordWithTime(now.Add(1 * time.Minute))

		// Delete expired records
		deletedCount, err := repo.DeleteExpired(ctx)
		require.NoError(t, err, "Failed to delete expired records")
		assert.Equal(t, int64(2), deletedCount, "Expected 2 expired records to be deleted")

		// Verify expired records are gone
		_, err = repo.Get(ctx, recExpired1.IdempotencyKey, recExpired1.UserID, recExpired1.RequestMethod, recExpired1.RequestPath)
		assert.ErrorIs(t, err, repository.ErrNotFound, "recExpired1 should be deleted")
		_, err = repo.Get(ctx, recExpired2.IdempotencyKey, recExpired2.UserID, recExpired2.RequestMethod, recExpired2.RequestPath)
		assert.ErrorIs(t, err, repository.ErrNotFound, "recExpired2 should be deleted")

		// Verify active records remain
		_, err = repo.Get(ctx, recActive1.IdempotencyKey, recActive1.UserID, recActive1.RequestMethod, recActive1.RequestPath)
		assert.NoError(t, err, "recActive1 should remain")
		_, err = repo.Get(ctx, recActive2.IdempotencyKey, recActive2.UserID, recActive2.RequestMethod, recActive2.RequestPath)
		assert.NoError(t, err, "recActive2 should remain")
	})
}
