package testing

import (
	"context"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/repository/common"
	"github.com/chessticize/chessticize-server/internal/utils"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestConsecutivePeriodsForPuzzles tests that puzzle grouping returns consecutive and evenly spaced periods
func TestConsecutivePeriodsForPuzzles(t *testing.T) {
	provider := GetTestDBProvider(FakeRepository)
	defer provider.Cleanup(t)

	RunConsecutivePeriodsForPuzzlesTest(t, provider)
}

// TestRealConsecutivePeriodsForPuzzles tests that puzzle grouping returns consecutive and evenly spaced periods with real repository
func TestRealConsecutivePeriodsForPuzzles(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping real database tests")
	}
	provider := GetTestDBProvider(RealRepository)
	defer provider.Cleanup(t)

	RunConsecutivePeriodsForPuzzlesTest(t, provider)
}

// RunConsecutivePeriodsForPuzzlesTest tests that puzzle grouping returns consecutive and evenly spaced periods
func RunConsecutivePeriodsForPuzzlesTest(t *testing.T, provider TestDBProvider) {
	userRepo := provider.GetUserRepository(t)
	gameRepo := provider.GetGameRepository(t)
	puzzleRepo := provider.GetPuzzleRepository(t)

	// Create a test user
	user := CreateTestUser(t, userRepo)

	// Create games and puzzles with gaps to test consecutive periods
	// We'll create data on Jan 1, skip Jan 2-3, create on Jan 4, skip Jan 5-6, create on Jan 7
	testDates := []time.Time{
		time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC), // Jan 1
		time.Date(2023, 1, 4, 12, 0, 0, 0, time.UTC), // Jan 4 (skip 2-3)
		time.Date(2023, 1, 7, 12, 0, 0, 0, time.UTC), // Jan 7 (skip 5-6)
	}

	for _, gameTime := range testDates {
		// Create a simple PGN
		pgn := "1. e4 e5 2. Nf3 Nc6 3. Bb5 a6"
		compressedPGN, err := utils.CompressPGN(pgn)
		require.NoError(t, err)

		game := &models.Game{
			ID:            uuid.New().String(),
			UserID:        user.ID,
			Platform:      models.ChessDotCom,
			ChessUsername: "testuser",
			UserColor:     models.White,
			GameTime:      gameTime,
			CompressedPGN: compressedPGN,
			TimeControl:   "10+0",
			Rated:         true,
			WhitePlayer:   `{"username":"testuser","rating":1500}`,
			BlackPlayer:   `{"username":"opponent","rating":1600}`,
			Winner:        models.WinnerWhite,
			Result:        models.Mate,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		}
		err = gameRepo.Create(context.Background(), game)
		require.NoError(t, err)

		// Create 2 puzzles for each game
		for j := 0; j < 2; j++ {
			puzzle := &models.Puzzle{
				ID:          uuid.New().String(),
				GameID:      game.ID,
				UserID:      user.ID,
				GameMove:    10 + j,
				FEN:         "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
				Moves:       []string{"e2e4", "e7e5"},
				PrevCP:      0,
				CP:          100,
				Theme:       models.OpponentBlunderMissed,
				UserColor:   models.White,
				PuzzleColor: models.Black,
				Zugzwang:    false,
				Tags:        []string{"opening", "tactical"},
				CreatedAt:   gameTime,
				UpdatedAt:   gameTime,
			}
			err = puzzleRepo.Create(context.Background(), puzzle)
			require.NoError(t, err)
		}
	}

	// Create a time range for testing (Jan 1 to Jan 7)
	startTime := time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)
	endTime := time.Date(2023, 1, 7, 23, 59, 59, 0, time.UTC)

	filter := repository.PuzzleFilter{
		GameStartTime: &startTime,
		GameEndTime:   &endTime,
	}

	t.Run("Daily Grouping - Consecutive Periods", func(t *testing.T) {
		grouping := &common.TimeGrouping{
			Unit:   common.TimeGroupingDay,
			Length: 1,
		}

		statsArray, err := puzzleRepo.GetPuzzleStats(context.Background(), user.ID, filter, 0, 100, grouping)
		require.NoError(t, err)

		// With backward calculation, we should have 7 days (Jan 1-7, 2023)
		// No extra empty period since we don't round down startTime
		assert.Equal(t, 7, len(statsArray), "Expected 7 consecutive days of stats")

		// Verify that periods are consecutive and evenly spaced
		expectedDates := []string{
			"2023-01-01", "2023-01-02", "2023-01-03",
			"2023-01-04", "2023-01-05", "2023-01-06", "2023-01-07",
		}

		for i, stats := range statsArray {
			assert.NotNil(t, stats.PeriodStart, "PeriodStart should not be nil")
			assert.NotNil(t, stats.PeriodEnd, "PeriodEnd should not be nil")

			// Check that the date matches expected sequence
			actualDate := stats.PeriodStart.Format("2006-01-02")
			assert.Equal(t, expectedDates[i], actualDate, "Period %d should be %s", i, expectedDates[i])

			// Check that period end is exactly 1 day after period start
			expectedEnd := stats.PeriodStart.AddDate(0, 0, 1)
			assert.Equal(t, expectedEnd, *stats.PeriodEnd, "Period end should be exactly 1 day after start")

			// Check puzzle counts - puzzles should be in their correct dates
			switch actualDate {
			case "2023-01-01", "2023-01-04", "2023-01-07":
				assert.Equal(t, int64(2), stats.TotalCount, "Date %s should have 2 puzzles", actualDate)
			default:
				assert.Equal(t, int64(0), stats.TotalCount, "Date %s should have 0 puzzles", actualDate)
			}
		}
	})

	t.Run("Weekly Grouping - Consecutive Periods", func(t *testing.T) {
		grouping := &common.TimeGrouping{
			Unit:   common.TimeGroupingWeek,
			Length: 1,
		}

		statsArray, err := puzzleRepo.GetPuzzleStats(context.Background(), user.ID, filter, 0, 100, grouping)
		require.NoError(t, err)

		// We should have at least 1 week of stats
		assert.GreaterOrEqual(t, len(statsArray), 1, "Expected at least 1 week of stats")

		// Verify that periods are consecutive
		for i := 1; i < len(statsArray); i++ {
			prevEnd := *statsArray[i-1].PeriodEnd
			currentStart := *statsArray[i].PeriodStart
			assert.Equal(t, prevEnd, currentStart, "Periods should be consecutive")
		}

		// Check that each period end is exactly 1 week after period start
		for _, stats := range statsArray {
			assert.NotNil(t, stats.PeriodStart, "PeriodStart should not be nil")
			assert.NotNil(t, stats.PeriodEnd, "PeriodEnd should not be nil")

			expectedEnd := stats.PeriodStart.AddDate(0, 0, 7)
			assert.Equal(t, expectedEnd, *stats.PeriodEnd, "Period end should be exactly 1 week after start")
		}

		// Total puzzles across all periods should be 6
		totalPuzzles := int64(0)
		for _, stats := range statsArray {
			totalPuzzles += stats.TotalCount
		}
		assert.Equal(t, int64(6), totalPuzzles, "Total puzzles across all periods should be 6")
	})

	t.Run("Monthly Grouping - Consecutive Periods", func(t *testing.T) {
		// Create additional data in different months to test month boundaries
		additionalDates := []time.Time{
			time.Date(2023, 2, 15, 12, 0, 0, 0, time.UTC), // February
			time.Date(2023, 3, 10, 12, 0, 0, 0, time.UTC), // March
		}

		for _, gameTime := range additionalDates {
			// Create a simple PGN
			pgn := "1. e4 e5 2. Nf3 Nc6 3. Bb5 a6"
			compressedPGN, err := utils.CompressPGN(pgn)
			require.NoError(t, err)

			game := &models.Game{
				ID:            uuid.New().String(),
				UserID:        user.ID,
				Platform:      models.ChessDotCom,
				ChessUsername: "testuser",
				UserColor:     models.White,
				GameTime:      gameTime,
				CompressedPGN: compressedPGN,
				TimeControl:   "10+0",
				Rated:         true,
				WhitePlayer:   `{"username":"testuser","rating":1500}`,
				BlackPlayer:   `{"username":"opponent","rating":1600}`,
				Winner:        models.WinnerWhite,
				Result:        models.Mate,
				CreatedAt:     time.Now(),
				UpdatedAt:     time.Now(),
			}
			err = gameRepo.Create(context.Background(), game)
			require.NoError(t, err)

			// Create 1 puzzle for each additional game
			puzzle := &models.Puzzle{
				ID:          uuid.New().String(),
				GameID:      game.ID,
				UserID:      user.ID,
				GameMove:    15,
				FEN:         "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
				Moves:       []string{"e2e4", "e7e5"},
				PrevCP:      0,
				CP:          100,
				Theme:       models.OpponentBlunderMissed,
				UserColor:   models.White,
				PuzzleColor: models.Black,
				Zugzwang:    false,
				Tags:        []string{"opening", "tactical"},
				CreatedAt:   gameTime,
				UpdatedAt:   gameTime,
			}
			err = puzzleRepo.Create(context.Background(), puzzle)
			require.NoError(t, err)
		}

		// Create a time range spanning multiple months
		monthlyStartTime := time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)
		monthlyEndTime := time.Date(2023, 3, 31, 23, 59, 59, 0, time.UTC)

		monthlyFilter := repository.PuzzleFilter{
			GameStartTime: &monthlyStartTime,
			GameEndTime:   &monthlyEndTime,
		}

		grouping := &common.TimeGrouping{
			Unit:   common.TimeGroupingMonth,
			Length: 1,
		}

		statsArray, err := puzzleRepo.GetPuzzleStats(context.Background(), user.ID, monthlyFilter, 0, 100, grouping)
		require.NoError(t, err)

		// With backward calculation, we should have 3 months based on actual data
		// No extra empty period since we don't round down startTime
		assert.Equal(t, 3, len(statsArray), "Expected 3 consecutive months of stats")

		expectedMonths := []string{"2022-12-11", "2023-01-11", "2023-02-11"}

		for i, stats := range statsArray {
			assert.NotNil(t, stats.PeriodStart, "PeriodStart should not be nil")
			assert.NotNil(t, stats.PeriodEnd, "PeriodEnd should not be nil")

			// Check that the month matches expected sequence
			actualMonth := stats.PeriodStart.Format("2006-01-02")
			assert.Equal(t, expectedMonths[i], actualMonth, "Period %d should start on %s", i, expectedMonths[i])

			// Check that period end is exactly 1 month after period start
			expectedEnd := stats.PeriodStart.AddDate(0, 1, 0)
			assert.Equal(t, expectedEnd, *stats.PeriodEnd, "Period end should be exactly 1 month after start")

			// Check puzzle counts - puzzles should be in their correct months
			switch actualMonth {
			case "2022-12-11":
				assert.Equal(t, int64(6), stats.TotalCount, "December should have 6 puzzles")
			case "2023-01-11":
				assert.Equal(t, int64(0), stats.TotalCount, "January should have 0 puzzles")
			case "2023-02-11":
				assert.Equal(t, int64(2), stats.TotalCount, "February should have 2 puzzles")
			default:
				assert.Equal(t, int64(0), stats.TotalCount, "Month %s should have 0 puzzles", actualMonth)
			}
		}
	})
}

// TestConsecutivePeriodsForGames tests that game grouping returns consecutive and evenly spaced periods
func TestConsecutivePeriodsForGames(t *testing.T) {
	provider := GetTestDBProvider(FakeRepository)
	defer provider.Cleanup(t)

	RunConsecutivePeriodsForGamesTest(t, provider)
}

// TestRealConsecutivePeriodsForGames tests that game grouping returns consecutive and evenly spaced periods with real repository
func TestRealConsecutivePeriodsForGames(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping real database tests")
	}
	provider := GetTestDBProvider(RealRepository)
	defer provider.Cleanup(t)

	RunConsecutivePeriodsForGamesTest(t, provider)
}

// RunConsecutivePeriodsForGamesTest tests that game grouping returns consecutive and evenly spaced periods
func RunConsecutivePeriodsForGamesTest(t *testing.T, provider TestDBProvider) {
	userRepo := provider.GetUserRepository(t)
	gameRepo := provider.GetGameRepository(t)

	// Create a test user
	user := CreateTestUser(t, userRepo)

	// Create games with gaps to test consecutive periods
	// We'll create data on Jan 1, skip Jan 2-3, create on Jan 4, skip Jan 5-6, create on Jan 7
	testDates := []time.Time{
		time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC), // Jan 1
		time.Date(2023, 1, 4, 12, 0, 0, 0, time.UTC), // Jan 4 (skip 2-3)
		time.Date(2023, 1, 7, 12, 0, 0, 0, time.UTC), // Jan 7 (skip 5-6)
	}

	for _, gameTime := range testDates {
		// Create a simple PGN
		pgn := "1. e4 e5 2. Nf3 Nc6 3. Bb5 a6"
		compressedPGN, err := utils.CompressPGN(pgn)
		require.NoError(t, err)

		game := &models.Game{
			ID:            uuid.New().String(),
			UserID:        user.ID,
			Platform:      models.ChessDotCom,
			ChessUsername: "testuser",
			UserColor:     models.White,
			GameTime:      gameTime,
			CompressedPGN: compressedPGN,
			TimeControl:   "10+0",
			Rated:         true,
			WhitePlayer:   `{"username":"testuser","rating":1500}`,
			BlackPlayer:   `{"username":"opponent","rating":1600}`,
			Winner:        models.WinnerWhite,
			Result:        models.Mate,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		}
		err = gameRepo.Create(context.Background(), game)
		require.NoError(t, err)
	}

	// Create a time range for testing (Jan 1 to Jan 7)
	startTime := time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)
	endTime := time.Date(2023, 1, 7, 23, 59, 59, 0, time.UTC)

	filter := repository.GameFilter{
		StartTime: &startTime,
		EndTime:   &endTime,
	}

	t.Run("Daily Grouping - Consecutive Periods", func(t *testing.T) {
		grouping := &common.TimeGrouping{
			Unit:   common.TimeGroupingDay,
			Length: 1,
		}

		statsArray, err := gameRepo.GetGameStats(context.Background(), user.ID, filter, 0, 100, grouping)
		require.NoError(t, err)

		// With backward calculation, we should have 7 days (Jan 1-7, 2023)
		// No extra empty period since we don't round down startTime
		assert.Equal(t, 7, len(statsArray), "Expected 7 consecutive days of stats")

		// Verify that periods are consecutive and evenly spaced
		expectedDates := []string{
			"2023-01-01", "2023-01-02", "2023-01-03",
			"2023-01-04", "2023-01-05", "2023-01-06", "2023-01-07",
		}

		for i, stats := range statsArray {
			assert.NotNil(t, stats.PeriodStart, "PeriodStart should not be nil")
			assert.NotNil(t, stats.PeriodEnd, "PeriodEnd should not be nil")

			// Check that the date matches expected sequence
			actualDate := stats.PeriodStart.Format("2006-01-02")
			assert.Equal(t, expectedDates[i], actualDate, "Period %d should be %s", i, expectedDates[i])

			// Check that period end is exactly 1 day after period start
			expectedEnd := stats.PeriodStart.AddDate(0, 0, 1)
			assert.Equal(t, expectedEnd, *stats.PeriodEnd, "Period end should be exactly 1 day after start")

			// Check game counts - games should be in their correct dates
			switch actualDate {
			case "2023-01-01", "2023-01-04", "2023-01-07":
				assert.Equal(t, int64(1), stats.TotalCount, "Date %s should have 1 game", actualDate)
			default:
				assert.Equal(t, int64(0), stats.TotalCount, "Date %s should have 0 games", actualDate)
			}
		}
	})
}

// TestBackwardMonthCalculation tests specific backward calculation scenarios for months
func TestBackwardMonthCalculation(t *testing.T) {
	provider := GetTestDBProvider(FakeRepository)
	defer provider.Cleanup(t)

	RunBackwardMonthCalculationTest(t, provider)
}

// TestRealBackwardMonthCalculation tests specific backward calculation scenarios for months with real repository
func TestRealBackwardMonthCalculation(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping real database tests")
	}
	provider := GetTestDBProvider(RealRepository)
	defer provider.Cleanup(t)

	RunBackwardMonthCalculationTest(t, provider)
}

// RunBackwardMonthCalculationTest tests specific backward calculation scenarios for months
func RunBackwardMonthCalculationTest(t *testing.T, provider TestDBProvider) {
	userRepo := provider.GetUserRepository(t)
	gameRepo := provider.GetGameRepository(t)
	puzzleRepo := provider.GetPuzzleRepository(t)

	// Create a test user
	user := CreateTestUser(t, userRepo)

	t.Run("MaxTime 3/15/2025, MinTime 1/1/2025", func(t *testing.T) {
		// Create test data on specific dates
		testDates := []time.Time{
			time.Date(2025, 1, 1, 12, 0, 0, 0, time.UTC),  // Jan 1
			time.Date(2025, 2, 10, 12, 0, 0, 0, time.UTC), // Feb 10
			time.Date(2025, 3, 15, 12, 0, 0, 0, time.UTC), // Mar 15
		}

		for _, gameTime := range testDates {
			// Create a simple PGN
			pgn := "1. e4 e5 2. Nf3 Nc6 3. Bb5 a6"
			compressedPGN, err := utils.CompressPGN(pgn)
			require.NoError(t, err)

			game := &models.Game{
				ID:            uuid.New().String(),
				UserID:        user.ID,
				Platform:      models.ChessDotCom,
				ChessUsername: "testuser",
				UserColor:     models.White,
				GameTime:      gameTime,
				CompressedPGN: compressedPGN,
				TimeControl:   "10+0",
				Rated:         true,
				WhitePlayer:   `{"username":"testuser","rating":1500}`,
				BlackPlayer:   `{"username":"opponent","rating":1600}`,
				Winner:        models.WinnerWhite,
				Result:        models.Mate,
				CreatedAt:     time.Now(),
				UpdatedAt:     time.Now(),
			}
			err = gameRepo.Create(context.Background(), game)
			require.NoError(t, err)

			// Create 1 puzzle for each game
			puzzle := &models.Puzzle{
				ID:          uuid.New().String(),
				GameID:      game.ID,
				UserID:      user.ID,
				GameMove:    15,
				FEN:         "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
				Moves:       []string{"e2e4", "e7e5"},
				PrevCP:      0,
				CP:          100,
				Theme:       models.OpponentBlunderMissed,
				UserColor:   models.White,
				PuzzleColor: models.Black,
				Zugzwang:    false,
				Tags:        []string{"opening", "tactical"},
				CreatedAt:   gameTime,
				UpdatedAt:   gameTime,
			}
			err = puzzleRepo.Create(context.Background(), puzzle)
			require.NoError(t, err)
		}

		// Set up the time range: minTime = 1/1/2025, maxTime = 3/31/2025
		minTime := time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC)
		maxTime := time.Date(2025, 3, 31, 23, 59, 59, 0, time.UTC)

		filter := repository.PuzzleFilter{
			GameStartTime: &minTime,
			GameEndTime:   &maxTime,
		}

		grouping := &common.TimeGrouping{
			Unit:   common.TimeGroupingMonth,
			Length: 1,
		}

		statsArray, err := puzzleRepo.GetPuzzleStats(context.Background(), user.ID, filter, 0, 100, grouping)
		require.NoError(t, err)

		// Expected periods based on backward calculation from 3/15/2025 (last game):
		// Since we don't round down startTime, periods start from actual data dates
		require.Equal(t, 3, len(statsArray), "Expected 3 monthly periods")

		expectedPeriods := []struct {
			startDate string
			endDate   string
		}{
			{"2024-12-16", "2025-01-16"},
			{"2025-01-16", "2025-02-16"},
			{"2025-02-16", "2025-03-16"},
		}

		for i, stats := range statsArray {
			assert.NotNil(t, stats.PeriodStart, "PeriodStart should not be nil")
			assert.NotNil(t, stats.PeriodEnd, "PeriodEnd should not be nil")

			actualStart := stats.PeriodStart.Format("2006-01-02")
			actualEnd := stats.PeriodEnd.Format("2006-01-02")

			assert.Equal(t, expectedPeriods[i].startDate, actualStart, "Period %d start should be %s", i, expectedPeriods[i].startDate)
			assert.Equal(t, expectedPeriods[i].endDate, actualEnd, "Period %d end should be %s", i, expectedPeriods[i].endDate)

			// Verify consecutive periods
			if i > 0 {
				prevEnd := *statsArray[i-1].PeriodEnd
				currentStart := *stats.PeriodStart
				assert.Equal(t, prevEnd, currentStart, "Periods should be consecutive")
			}
		}
	})

	t.Run("MaxTime 3/31/2025, MinTime 1/1/2025", func(t *testing.T) {
		// Create test data on specific dates
		testDates := []time.Time{
			time.Date(2025, 1, 1, 12, 0, 0, 0, time.UTC),  // Jan 1
			time.Date(2025, 2, 25, 12, 0, 0, 0, time.UTC), // Feb 25
			time.Date(2025, 3, 31, 12, 0, 0, 0, time.UTC), // Mar 31
		}

		for _, gameTime := range testDates {
			// Create a simple PGN
			pgn := "1. e4 e5 2. Nf3 Nc6 3. Bb5 a6"
			compressedPGN, err := utils.CompressPGN(pgn)
			require.NoError(t, err)

			game := &models.Game{
				ID:            uuid.New().String(),
				UserID:        user.ID,
				Platform:      models.ChessDotCom,
				ChessUsername: "testuser2",
				UserColor:     models.White,
				GameTime:      gameTime,
				CompressedPGN: compressedPGN,
				TimeControl:   "10+0",
				Rated:         true,
				WhitePlayer:   `{"username":"testuser2","rating":1500}`,
				BlackPlayer:   `{"username":"opponent","rating":1600}`,
				Winner:        models.WinnerWhite,
				Result:        models.Mate,
				CreatedAt:     time.Now(),
				UpdatedAt:     time.Now(),
			}
			err = gameRepo.Create(context.Background(), game)
			require.NoError(t, err)

			// Create 1 puzzle for each game
			puzzle := &models.Puzzle{
				ID:          uuid.New().String(),
				GameID:      game.ID,
				UserID:      user.ID,
				GameMove:    15,
				FEN:         "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
				Moves:       []string{"e2e4", "e7e5"},
				PrevCP:      0,
				CP:          100,
				Theme:       models.OpponentBlunderMissed,
				UserColor:   models.White,
				PuzzleColor: models.Black,
				Zugzwang:    false,
				Tags:        []string{"opening", "tactical"},
				CreatedAt:   gameTime,
				UpdatedAt:   gameTime,
			}
			err = puzzleRepo.Create(context.Background(), puzzle)
			require.NoError(t, err)
		}

		// Set up the time range: minTime = 12/31/2024, maxTime = 4/31/2025
		minTime := time.Date(2024, 12, 31, 0, 0, 0, 0, time.UTC)
		maxTime := time.Date(2025, 4, 31, 23, 59, 59, 0, time.UTC)

		filter := repository.PuzzleFilter{
			GameStartTime: &minTime,
			GameEndTime:   &maxTime,
		}

		grouping := &common.TimeGrouping{
			Unit:   common.TimeGroupingMonth,
			Length: 1,
		}

		statsArray, err := puzzleRepo.GetPuzzleStats(context.Background(), user.ID, filter, 0, 100, grouping)
		require.NoError(t, err)

		// Expected periods based on backward calculation from 3/31/2025 (last game):
		// Since we don't round down startTime, periods start from actual data dates
		assert.Equal(t, 3, len(statsArray), "Expected 3 monthly periods")

		expectedPeriods := []struct {
			startDate string
			endDate   string
		}{
			{"2025-01-01", "2025-02-01"},
			{"2025-02-01", "2025-03-01"},
			{"2025-03-01", "2025-04-01"},
		}

		for i, stats := range statsArray {
			assert.NotNil(t, stats.PeriodStart, "PeriodStart should not be nil")
			assert.NotNil(t, stats.PeriodEnd, "PeriodEnd should not be nil")

			actualStart := stats.PeriodStart.Format("2006-01-02")
			actualEnd := stats.PeriodEnd.Format("2006-01-02")

			assert.Equal(t, expectedPeriods[i].startDate, actualStart, "Period %d start should be %s", i, expectedPeriods[i].startDate)
			assert.Equal(t, expectedPeriods[i].endDate, actualEnd, "Period %d end should be %s", i, expectedPeriods[i].endDate)

			// Verify consecutive periods
			if i > 0 {
				prevEnd := *statsArray[i-1].PeriodEnd
				currentStart := *stats.PeriodStart
				assert.Equal(t, prevEnd, currentStart, "Periods should be consecutive")
			}
		}
	})
}
