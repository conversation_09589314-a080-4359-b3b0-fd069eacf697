package testing

import (
	"context"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"
)

// TestInvitationCodeRepository runs all tests for both real and fake invitation code repositories
func TestInvitationCodeRepository(t *testing.T) {
	// Test with SQLite by default for faster tests
	provider := GetTestDBProvider(FakeRepository)
	defer provider.Cleanup(t)

	RunInvitationCodeRepositoryTests(t, provider)
}

// TestRealInvitationCodeRepository runs tests against the real PostgreSQL repository implementation
// This will be skipped if the test database is not available
func TestRealInvitationCodeRepository(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping real database tests")
	}
	provider := GetTestDBProvider(RealRepository)
	defer provider.Cleanup(t)

	RunInvitationCodeRepositoryTests(t, provider)
}

// RunInvitationCodeRepositoryTests runs all tests for an invitation code repository
func RunInvitationCodeRepositoryTests(t *testing.T, provider TestDBProvider) {
	invitationCodeRepo := provider.GetInvitationCodeRepository(t)
	userRepo := provider.GetUserRepository(t)
	ctx := context.Background()

	// Create a test user for the invitation code creator
	adminUser := &models.User{
		Email:        RandomEmail(),
		PasswordHash: RandomString(32),
	}
	err := userRepo.Create(ctx, adminUser)
	require.NoError(t, err)

	t.Run("Create and Get Invitation Code", func(t *testing.T) {
		// Create a new invitation code
		code := &models.InvitationCode{
			Code:            RandomString(32),
			CreatedByUserID: adminUser.ID,
		}
		err := invitationCodeRepo.Create(ctx, code)
		assert.NoError(t, err)
		assert.NotEmpty(t, code.ID, "Invitation code ID should be set after creation")
		assert.False(t, code.CreatedAt.IsZero(), "Invitation code CreatedAt should not be zero")

		// Get by ID
		retrieved, err := invitationCodeRepo.GetByID(ctx, code.ID)
		assert.NoError(t, err)
		assert.Equal(t, code.ID, retrieved.ID)
		assert.Equal(t, code.Code, retrieved.Code)
		assert.Equal(t, code.CreatedByUserID, retrieved.CreatedByUserID)
		assert.Nil(t, retrieved.UsedAt)
		assert.Nil(t, retrieved.UsedByUserID)

		// Get by code string
		retrievedByCode, err := invitationCodeRepo.GetByCode(ctx, code.Code)
		assert.NoError(t, err)
		assert.Equal(t, code.ID, retrievedByCode.ID)
	})

	t.Run("Update Invitation Code", func(t *testing.T) {
		// Create a new invitation code
		code := &models.InvitationCode{
			Code:            RandomString(32),
			CreatedByUserID: adminUser.ID,
		}
		err := invitationCodeRepo.Create(ctx, code)
		assert.NoError(t, err)

		// Create a user to use the code
		user := &models.User{
			Email:        RandomEmail(),
			PasswordHash: RandomString(32),
		}
		err = userRepo.Create(ctx, user)
		require.NoError(t, err)

		// Mark the code as used
		code.MarkAsUsed(user.ID)
		err = invitationCodeRepo.Update(ctx, code)
		assert.NoError(t, err)

		// Verify the code is marked as used
		retrieved, err := invitationCodeRepo.GetByID(ctx, code.ID)
		assert.NoError(t, err)
		assert.NotNil(t, retrieved.UsedAt)
		assert.NotNil(t, retrieved.UsedByUserID)
		assert.Equal(t, user.ID, *retrieved.UsedByUserID)
	})

	t.Run("List Valid Invitation Codes", func(t *testing.T) {
		// Create several invitation codes with different states
		// 1. Valid code (not used, no expiration)
		validCode := &models.InvitationCode{
			Code:            "valid-" + RandomString(16),
			CreatedByUserID: adminUser.ID,
		}
		err := invitationCodeRepo.Create(ctx, validCode)
		assert.NoError(t, err)

		// 2. Used code
		usedCode := &models.InvitationCode{
			Code:            "used-" + RandomString(16),
			CreatedByUserID: adminUser.ID,
		}
		err = invitationCodeRepo.Create(ctx, usedCode)
		assert.NoError(t, err)
		now := time.Now()
		userID := "test-user-id"
		usedCode.UsedAt = &now
		usedCode.UsedByUserID = &userID
		err = invitationCodeRepo.Update(ctx, usedCode)
		assert.NoError(t, err)

		// 3. Expired code
		expiredCode := &models.InvitationCode{
			Code:            "expired-" + RandomString(16),
			CreatedByUserID: adminUser.ID,
		}
		expiredTime := time.Now().Add(-24 * time.Hour)
		expiredCode.ExpiresAt = &expiredTime
		err = invitationCodeRepo.Create(ctx, expiredCode)
		assert.NoError(t, err)

		// List valid codes
		validCodes, count, err := invitationCodeRepo.ListValid(ctx, 0, 10)
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, count, int64(1), "Should have at least one valid code")

		// Check if the valid code is in the list
		var foundValid bool
		for _, c := range validCodes {
			if c.ID == validCode.ID {
				foundValid = true
				break
			}
		}
		assert.True(t, foundValid, "Valid code should be in the list")

		// Check that used and expired codes are not in the list
		for _, c := range validCodes {
			assert.NotEqual(t, usedCode.ID, c.ID, "Used code should not be in valid list")
			assert.NotEqual(t, expiredCode.ID, c.ID, "Expired code should not be in valid list")
		}
	})

	t.Run("List All Invitation Codes", func(t *testing.T) {
		// Create a new invitation code
		code := &models.InvitationCode{
			Code:            RandomString(32),
			CreatedByUserID: adminUser.ID,
		}
		err := invitationCodeRepo.Create(ctx, code)
		assert.NoError(t, err)

		// List all codes
		allCodes, count, err := invitationCodeRepo.ListAll(ctx, 0, 10)
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, count, int64(1), "Should have at least one code")
		assert.GreaterOrEqual(t, len(allCodes), 1, "Should have at least one code")
	})

	t.Run("Get Non-Existent Invitation Code", func(t *testing.T) {
		_, err := invitationCodeRepo.GetByID(ctx, "non-existent-id")
		assert.Error(t, err)
		assert.ErrorIs(t, err, gorm.ErrRecordNotFound)

		_, err = invitationCodeRepo.GetByCode(ctx, "non-existent-code")
		assert.Error(t, err)
		assert.ErrorIs(t, err, gorm.ErrRecordNotFound)
	})

	t.Run("Invitation Code Validity Check", func(t *testing.T) {
		// 1. Valid code
		validCode := &models.InvitationCode{
			Code:            "valid-check-" + RandomString(16),
			CreatedByUserID: adminUser.ID,
		}
		assert.True(t, validCode.IsValid(), "New code should be valid")

		// 2. Used code
		usedCode := &models.InvitationCode{
			Code:            "used-check-" + RandomString(16),
			CreatedByUserID: adminUser.ID,
		}
		now := time.Now()
		userID := "test-user-id"
		usedCode.UsedAt = &now
		usedCode.UsedByUserID = &userID
		assert.False(t, usedCode.IsValid(), "Used code should not be valid")

		// 3. Expired code
		expiredCode := &models.InvitationCode{
			Code:            "expired-check-" + RandomString(16),
			CreatedByUserID: adminUser.ID,
		}
		expiredTime := time.Now().Add(-24 * time.Hour)
		expiredCode.ExpiresAt = &expiredTime
		assert.False(t, expiredCode.IsValid(), "Expired code should not be valid")

		// 4. Future expiration
		futureCode := &models.InvitationCode{
			Code:            "future-check-" + RandomString(16),
			CreatedByUserID: adminUser.ID,
		}
		futureTime := time.Now().Add(24 * time.Hour)
		futureCode.ExpiresAt = &futureTime
		assert.True(t, futureCode.IsValid(), "Code with future expiration should be valid")
	})
}
