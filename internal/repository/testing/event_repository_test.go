package testing

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"
)

func TestEventRepository(t *testing.T) {
	// Test with SQLite by default for faster tests
	provider := GetTestDBProvider(FakeRepository)
	defer provider.Cleanup(t)

	testEventRepository(t, provider)
}

// TestRealEventRepository runs tests against the real PostgreSQL repository implementation
// This will be skipped if the test database is not available
func TestRealEventRepository(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping real database tests")
	}
	provider := GetTestDBProvider(RealRepository)
	defer provider.Cleanup(t)

	testEventRepository(t, provider)
}

func testEventRepository(t *testing.T, provider TestDBProvider) {
	eventRepo := provider.GetEventRepository(t)
	userRepo := provider.GetUserRepository(t)
	gameRepo := provider.GetGameRepository(t)
	puzzleRepo := provider.GetPuzzleRepository(t)
	ctx := context.Background()

	// Create a test user first
	user := &models.User{
		Email:        RandomEmail(),
		PasswordHash: "hashedpassword",
	}
	err := userRepo.Create(ctx, user)
	require.NoError(t, err)

	// Create test game and puzzles for puzzle events
	game := CreateTestGame(t, gameRepo, user.ID)
	puzzle123 := CreateTestPuzzle(t, puzzleRepo, game.ID, user.ID)
	puzzle456 := CreateTestPuzzle(t, puzzleRepo, game.ID, user.ID)

	t.Run("Create Event", func(t *testing.T) {
		eventID := uuid.New().String()
		eventData := models.SignInEventData{
			UserAgent: "Mozilla/5.0",
		}
		eventDataJSON, err := json.Marshal(eventData)
		require.NoError(t, err)

		event := &models.Event{
			ID:        eventID,
			UserID:    user.ID,
			EventType: models.EventTypeSignIn,
			EventData: eventDataJSON,
		}

		err = eventRepo.Create(ctx, event)
		assert.NoError(t, err)
		assert.NotEmpty(t, event.CreatedAt)
		assert.NotEmpty(t, event.UpdatedAt)
		assert.NotEmpty(t, event.EventTime)
	})

	t.Run("Create Event - Idempotency", func(t *testing.T) {
		eventID := uuid.New().String()
		event1 := &models.Event{
			ID:        eventID,
			UserID:    user.ID,
			EventType: models.EventTypeSignIn,
			EventData: json.RawMessage(`{"ip_address":"***********"}`),
		}

		// First creation should succeed
		err := eventRepo.Create(ctx, event1)
		assert.NoError(t, err)

		// Second creation with same ID should fail
		event2 := &models.Event{
			ID:        eventID,
			UserID:    user.ID,
			EventType: models.EventTypeGameReview,
			EventData: json.RawMessage(`{"game_id":"different-game"}`),
		}

		err = eventRepo.Create(ctx, event2)
		assert.Error(t, err)
		assert.Equal(t, repository.ErrEventAlreadyExists, err)
	})

	t.Run("Create Event - Missing ID", func(t *testing.T) {
		event := &models.Event{
			UserID:    user.ID,
			EventType: models.EventTypeSignIn,
			EventData: json.RawMessage(`{}`),
		}

		err := eventRepo.Create(ctx, event)
		assert.Error(t, err)
		assert.Equal(t, repository.ErrEventIDRequired, err)
	})

	t.Run("GetByID", func(t *testing.T) {
		// Create an event first
		eventID := uuid.New().String()
		expectedEventData := `{"puzzle_id":"` + puzzle123.ID + `","solved":true}`
		originalEvent := &models.Event{
			ID:        eventID,
			UserID:    user.ID,
			EventType: models.EventTypePuzzle,
			EventData: json.RawMessage(expectedEventData),
		}

		err := eventRepo.Create(ctx, originalEvent)
		require.NoError(t, err)

		// Retrieve the event
		retrievedEvent, err := eventRepo.GetByID(ctx, eventID)
		assert.NoError(t, err)
		assert.Equal(t, eventID, retrievedEvent.ID)
		assert.Equal(t, user.ID, retrievedEvent.UserID)
		assert.Equal(t, models.EventTypePuzzle, retrievedEvent.EventType)
		assert.JSONEq(t, expectedEventData, string(retrievedEvent.EventData))
	})

	t.Run("GetByID - Not Found", func(t *testing.T) {
		nonExistentID := uuid.New().String()
		_, err := eventRepo.GetByID(ctx, nonExistentID)
		assert.Error(t, err)
		assert.Equal(t, gorm.ErrRecordNotFound, err)
	})

	t.Run("ListByUserID", func(t *testing.T) {
		// Create multiple events for the user
		events := []models.Event{
			{
				ID:        uuid.New().String(),
				UserID:    user.ID,
				EventType: models.EventTypeSignIn,
				EventData: json.RawMessage(`{"ip_address":"***********"}`),
			},
			{
				ID:        uuid.New().String(),
				UserID:    user.ID,
				EventType: models.EventTypeGameReview,
				EventData: json.RawMessage(`{"game_id":"game-123"}`),
			},
			{
				ID:        uuid.New().String(),
				UserID:    user.ID,
				EventType: models.EventTypePuzzle,
				EventData: json.RawMessage(`{"puzzle_id":"` + puzzle456.ID + `"}`),
			},
		}

		for _, event := range events {
			err := eventRepo.Create(ctx, &event)
			require.NoError(t, err)
		}

		// List all events for the user
		filter := repository.EventFilter{}
		retrievedEvents, totalCount, err := eventRepo.ListByUserID(ctx, user.ID, filter, 0, 10)
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, int(totalCount), 3) // At least the 3 we just created
		assert.GreaterOrEqual(t, len(retrievedEvents), 3)

		// Test pagination
		firstPage, _, err := eventRepo.ListByUserID(ctx, user.ID, filter, 0, 2)
		assert.NoError(t, err)
		assert.Len(t, firstPage, 2)

		secondPage, _, err := eventRepo.ListByUserID(ctx, user.ID, filter, 2, 2)
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, len(secondPage), 1)
	})

	t.Run("ListByUserID - Filter by EventType", func(t *testing.T) {
		// Create events of different types
		signInEvent := &models.Event{
			ID:        uuid.New().String(),
			UserID:    user.ID,
			EventType: models.EventTypeSignIn,
			EventData: json.RawMessage(`{}`),
		}
		puzzleEvent := &models.Event{
			ID:        uuid.New().String(),
			UserID:    user.ID,
			EventType: models.EventTypePuzzle,
			EventData: json.RawMessage(`{}`),
		}

		err := eventRepo.Create(ctx, signInEvent)
		require.NoError(t, err)
		err = eventRepo.Create(ctx, puzzleEvent)
		require.NoError(t, err)

		// Filter by sign-in events only
		filter := repository.EventFilter{
			EventTypes: []models.EventType{models.EventTypeSignIn},
		}
		events, totalCount, err := eventRepo.ListByUserID(ctx, user.ID, filter, 0, 10)
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, int(totalCount), 1)

		// All returned events should be sign-in events
		for _, event := range events {
			assert.Equal(t, models.EventTypeSignIn, event.EventType)
		}
	})

	t.Run("ListByUserID - Filter by Time Range", func(t *testing.T) {
		now := time.Now()
		pastTime := now.Add(-1 * time.Hour)
		futureTime := now.Add(1 * time.Hour)

		// Create an event with a specific time
		event := &models.Event{
			ID:        uuid.New().String(),
			UserID:    user.ID,
			EventType: models.EventTypeSignIn,
			EventData: json.RawMessage(`{}`),
			EventTime: now,
		}

		err := eventRepo.Create(ctx, event)
		require.NoError(t, err)

		// Filter by time range that includes the event
		filter := repository.EventFilter{
			StartTime: &pastTime,
			EndTime:   &futureTime,
		}
		events, totalCount, err := eventRepo.ListByUserID(ctx, user.ID, filter, 0, 10)
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, int(totalCount), 1)
		assert.GreaterOrEqual(t, len(events), 1)

		// Filter by time range that excludes the event
		veryPastTime := now.Add(-2 * time.Hour)
		filter = repository.EventFilter{
			StartTime: &veryPastTime,
			EndTime:   &pastTime,
		}
		excludedEvents, excludedCount, err := eventRepo.ListByUserID(ctx, user.ID, filter, 0, 10)
		assert.NoError(t, err)
		// Should not find the event we just created
		for _, e := range excludedEvents {
			assert.NotEqual(t, event.ID, e.ID)
		}
		// Use the variables to avoid linting issues
		_ = excludedCount
	})

}
