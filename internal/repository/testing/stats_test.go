package testing

import (
	"context"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/google/uuid"
	"github.com/lib/pq"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// setupStatsTestData creates test data for stats tests
// Returns user, games, puzzles
func setupStatsTestData(t *testing.T, provider TestDBProvider) (*models.User, []*models.Game, []*models.Puzzle) {
	userRepo := provider.GetUserRepository(t)
	gameRepo := provider.GetGameRepository(t)
	puzzleRepo := provider.GetPuzzleRepository(t)

	// Create a test user
	user := CreateTestUser(t, userRepo)

	// Create games with different properties and specific game times
	game1 := CreateTestGame(t, gameRepo, user.ID)
	game1.TimeControl = "10+0"
	game1.Rated = true
	game1.UserColor = models.White
	game1.Result = models.Mate
	game1.Winner = models.WinnerWhite
	game1.Platform = models.ChessDotCom
	game1.WhitePlayer = `{"username":"testuser","rating":1500}`
	game1.BlackPlayer = `{"username":"opponent1","rating":1550}`
	// Set specific game time for grouping tests
	game1.GameTime = time.Date(2023, 1, 1, 10, 0, 0, 0, time.UTC)
	err := gameRepo.Update(context.Background(), game1)
	require.NoError(t, err)

	game2 := CreateTestGame(t, gameRepo, user.ID)
	game2.TimeControl = "5+0"
	game2.Rated = true
	game2.UserColor = models.Black
	game2.Result = models.Resign
	game2.Winner = models.WinnerBlack
	game2.Platform = models.LichessOrg
	game2.WhitePlayer = `{"username":"opponent2","rating":1600}`
	game2.BlackPlayer = `{"username":"testuser","rating":1550}`
	// Set specific game time for grouping tests (different day)
	game2.GameTime = time.Date(2023, 1, 15, 15, 0, 0, 0, time.UTC)
	err = gameRepo.Update(context.Background(), game2)
	require.NoError(t, err)

	game3 := CreateTestGame(t, gameRepo, user.ID)
	game3.TimeControl = "10+0"
	game3.Rated = false
	game3.UserColor = models.White
	game3.Result = models.Draw
	game3.Winner = models.WinnerNone
	game3.Platform = models.ChessDotCom
	game3.WhitePlayer = `{"username":"testuser","rating":1500}`
	game3.BlackPlayer = `{"username":"opponent3","rating":1500}`
	// Set specific game time for grouping tests (different month)
	game3.GameTime = time.Date(2023, 2, 1, 12, 0, 0, 0, time.UTC)
	err = gameRepo.Update(context.Background(), game3)
	require.NoError(t, err)

	// Create puzzles with different properties
	puzzle1 := &models.Puzzle{
		GameID:      game1.ID,
		UserID:      user.ID,
		GameMove:    5,
		FEN:         "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
		Moves:       []string{"e2e4", "e7e5"},
		PrevCP:      0,
		CP:          100,
		Theme:       models.OpponentBlunderMissed,
		UserColor:   models.White,
		PuzzleColor: models.Black,
		Zugzwang:    false,
		Tags:        pq.StringArray{"opening", "fork"},
	}
	err = puzzleRepo.Create(context.Background(), puzzle1)
	require.NoError(t, err)

	puzzle2 := &models.Puzzle{
		GameID:      game2.ID,
		UserID:      user.ID,
		GameMove:    15,
		FEN:         "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
		Moves:       []string{"d2d4", "d7d5", "c2c4"},
		PrevCP:      -200,
		CP:          300,
		Theme:       models.OpponentBlunderCaught,
		UserColor:   models.Black,
		PuzzleColor: models.White,
		Zugzwang:    false,
		Tags:        pq.StringArray{"middlegame", "pin"},
	}
	err = puzzleRepo.Create(context.Background(), puzzle2)
	require.NoError(t, err)

	puzzle3 := &models.Puzzle{
		GameID:      game3.ID,
		UserID:      user.ID,
		GameMove:    25,
		FEN:         "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
		Moves:       []string{"e2e4", "e7e5", "g1f3", "b8c6"},
		PrevCP:      100,
		CP:          -50,
		Theme:       models.OwnMistakePunished,
		UserColor:   models.White,
		PuzzleColor: models.Black,
		Zugzwang:    false,
		Tags:        pq.StringArray{"endgame", "tactical"},
	}
	err = puzzleRepo.Create(context.Background(), puzzle3)
	require.NoError(t, err)

	puzzle4 := &models.Puzzle{
		GameID:      game1.ID,
		UserID:      user.ID,
		GameMove:    35,
		FEN:         "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
		Moves:       []string{"d2d4", "d7d5", "c2c4", "e7e6", "g1f3", "g8f6"},
		PrevCP:      50,
		CP:          200,
		Theme:       models.OpponentMistakeCaught,
		UserColor:   models.White,
		PuzzleColor: models.Black,
		Zugzwang:    false,
		Tags:        pq.StringArray{"endgame", "fork"},
	}
	err = puzzleRepo.Create(context.Background(), puzzle4)
	require.NoError(t, err)

	return user, []*models.Game{game1, game2, game3}, []*models.Puzzle{puzzle1, puzzle2, puzzle3, puzzle4}
}

// TestPuzzleStats tests the puzzle stats functionality
func TestPuzzleStats(t *testing.T) {
	// Test with SQLite by default for faster tests
	provider := GetTestDBProvider(FakeRepository)
	defer provider.Cleanup(t)

	user, _, _ := setupStatsTestData(t, provider)
	puzzleRepo := provider.GetPuzzleRepository(t)

	// Test getting all stats without filters
	t.Run("Get All Stats", func(t *testing.T) {
		statsArray, err := puzzleRepo.GetPuzzleStats(context.Background(), user.ID, repository.PuzzleFilter{}, 0, 100, nil)
		require.NoError(t, err)
		require.Len(t, statsArray, 1, "Expected 1 stats object when not grouping")
		stats := statsArray[0]
		assert.Equal(t, int64(4), stats.TotalCount)

		// Check tag counts
		assert.GreaterOrEqual(t, len(stats.TagCounts), 4) // opening, fork, middlegame, pin, endgame, tactical

		// Check theme counts
		assert.GreaterOrEqual(t, len(stats.ThemeCounts), 3) // OpponentBlunderMissed, OpponentBlunderCaught, OwnMistakePunished

		// Check user color counts
		assert.Len(t, stats.UserColorCounts, 2) // WHITE, BLACK

		// Check game move buckets
		assert.Len(t, stats.GameMoveBuckets, 5) // Early opening, Late opening, Early middlegame, Late middlegame, Endgame

		// Check move length counts
		assert.GreaterOrEqual(t, len(stats.MoveLengthCounts), 2) // At least 2 different lengths

		// Check unique game count
		assert.Equal(t, int64(3), stats.UniqueGameCount) // 3 unique games (game1, game2, game3)
	})

	// Test filtering stats by time_control
	t.Run("Filter Stats by TimeControl", func(t *testing.T) {
		timeControl := "10+0"
		filter := repository.PuzzleFilter{
			TimeControl: &timeControl,
		}
		statsArray, err := puzzleRepo.GetPuzzleStats(context.Background(), user.ID, filter, 0, 100, nil)
		require.NoError(t, err)
		require.Len(t, statsArray, 1, "Expected 1 stats object when not grouping")
		stats := statsArray[0]
		assert.Equal(t, int64(3), stats.TotalCount) // puzzle1, puzzle3, puzzle4

		// Check user color counts
		assert.Len(t, stats.UserColorCounts, 1) // Only WHITE
		for _, colorCount := range stats.UserColorCounts {
			if colorCount.Color == models.White {
				assert.Equal(t, 3, colorCount.Count)
			}
		}

		// Check unique game count
		assert.Equal(t, int64(2), stats.UniqueGameCount) // 2 unique games (game1, game3)
	})

	// Test filtering stats by rated
	t.Run("Filter Stats by Rated", func(t *testing.T) {
		rated := true
		filter := repository.PuzzleFilter{
			Rated: &rated,
		}
		statsArray, err := puzzleRepo.GetPuzzleStats(context.Background(), user.ID, filter, 0, 100, nil)
		require.NoError(t, err)
		require.Len(t, statsArray, 1, "Expected 1 stats object when not grouping")
		stats := statsArray[0]
		assert.Equal(t, int64(3), stats.TotalCount) // puzzle1, puzzle2, puzzle4

		// Check theme counts
		foundThemes := make(map[models.PuzzleTheme]bool)
		for _, themeCount := range stats.ThemeCounts {
			foundThemes[themeCount.Theme] = true
		}
		assert.True(t, foundThemes[models.OpponentBlunderMissed])
		assert.True(t, foundThemes[models.OpponentBlunderCaught])
		assert.True(t, foundThemes[models.OpponentMistakeCaught])

		// Check unique game count
		assert.Equal(t, int64(2), stats.UniqueGameCount) // 2 unique games (game1, game2)
	})

	// Test filtering stats by time_control and rated
	t.Run("Filter Stats by TimeControl and Rated", func(t *testing.T) {
		timeControl := "10+0"
		rated := true
		filter := repository.PuzzleFilter{
			TimeControl: &timeControl,
			Rated:       &rated,
		}
		statsArray, err := puzzleRepo.GetPuzzleStats(context.Background(), user.ID, filter, 0, 100, nil)
		require.NoError(t, err)
		require.Len(t, statsArray, 1, "Expected 1 stats object when not grouping")
		stats := statsArray[0]
		assert.Equal(t, int64(2), stats.TotalCount) // puzzle1, puzzle4

		// Check user color counts
		assert.Len(t, stats.UserColorCounts, 1) // Only WHITE
		for _, colorCount := range stats.UserColorCounts {
			if colorCount.Color == models.White {
				assert.Equal(t, 2, colorCount.Count)
			}
		}

		// Check unique game count
		assert.Equal(t, int64(1), stats.UniqueGameCount) // 1 unique game (game1)
	})

	// Test pagination with stats
	t.Run("Paginate Stats", func(t *testing.T) {
		statsArray, err := puzzleRepo.GetPuzzleStats(context.Background(), user.ID, repository.PuzzleFilter{}, 0, 2, nil)
		require.NoError(t, err)
		require.Len(t, statsArray, 1, "Expected 1 stats object when not grouping")
		stats := statsArray[0]
		// The total count should still be 4 (all puzzles) even with pagination
		assert.Equal(t, int64(4), stats.TotalCount)

		// But the tag counts, theme counts, etc. should only reflect the paginated puzzles
		// This is implementation-dependent, so we don't test specific counts here
	})

	// Test unique game count with multiple puzzles from same game
	t.Run("Unique Game Count with Multiple Puzzles from Same Game", func(t *testing.T) {
		// Get the games from the existing setup (don't call setupStatsTestData again)
		games, _, err := provider.GetGameRepository(t).ListByUserID(context.Background(), user.ID, repository.GameFilter{}, 0, 100)
		require.NoError(t, err)
		require.GreaterOrEqual(t, len(games), 1)
		game1 := &games[0]

		// Create additional puzzles from the same games to test unique counting
		puzzle5 := &models.Puzzle{
			ID:          uuid.New().String(),
			UserID:      user.ID,
			GameID:      game1.ID, // Same game as puzzle1
			GameMove:    15,
			FEN:         "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
			Moves:       []string{"e2e4", "e7e5", "Nf3"},
			PrevCP:      50,
			CP:          150,
			Theme:       models.OpponentMistakeCaught,
			UserColor:   models.White,
			PuzzleColor: models.Black,
			Zugzwang:    false,
			Tags:        []string{"middlegame", "tactical"},
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		}
		err = puzzleRepo.Create(context.Background(), puzzle5)
		require.NoError(t, err)

		puzzle6 := &models.Puzzle{
			ID:          uuid.New().String(),
			UserID:      user.ID,
			GameID:      game1.ID, // Same game as puzzle1 and puzzle5
			GameMove:    25,
			FEN:         "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
			Moves:       []string{"e2e4", "e7e5", "Nf3", "Nc6"},
			PrevCP:      75,
			CP:          175,
			Theme:       models.OwnMistakePunished,
			UserColor:   models.White,
			PuzzleColor: models.Black,
			Zugzwang:    false,
			Tags:        []string{"endgame", "tactical"},
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		}
		err = puzzleRepo.Create(context.Background(), puzzle6)
		require.NoError(t, err)

		// Get stats for all puzzles
		statsArray, err := puzzleRepo.GetPuzzleStats(context.Background(), user.ID, repository.PuzzleFilter{}, 0, 100, nil)
		require.NoError(t, err)
		require.Len(t, statsArray, 1, "Expected 1 stats object when not grouping")
		stats := statsArray[0]

		// Should have 6 total puzzles but only 3 unique games
		assert.Equal(t, int64(6), stats.TotalCount)
		assert.Equal(t, int64(3), stats.UniqueGameCount) // Still 3 unique games (game1, game2, game3)
	})
}

// TestGameStats tests the game stats functionality
func TestGameStats(t *testing.T) {
	// Test with SQLite by default for faster tests
	provider := GetTestDBProvider(FakeRepository)
	defer provider.Cleanup(t)

	user, _, _ := setupStatsTestData(t, provider)
	gameRepo := provider.GetGameRepository(t)

	// Test getting all stats without filters
	t.Run("Get All Stats", func(t *testing.T) {
		statsArray, err := gameRepo.GetGameStats(context.Background(), user.ID, repository.GameFilter{}, 0, 100, nil)
		require.NoError(t, err)
		require.Len(t, statsArray, 1, "Expected 1 stats object when not grouping")
		stats := statsArray[0]
		assert.Equal(t, int64(3), stats.TotalCount)

		// Check platform counts
		assert.Len(t, stats.PlatformCounts, 2) // chess.com, lichess.org
		for _, platformCount := range stats.PlatformCounts {
			switch platformCount.Platform {
			case models.ChessDotCom:
				assert.Equal(t, 2, platformCount.Count) // game1 and game3
			case models.LichessOrg:
				assert.Equal(t, 1, platformCount.Count) // game2
			}
		}

		// Check user color counts
		assert.Len(t, stats.UserColorCounts, 2) // WHITE, BLACK
		for _, colorCount := range stats.UserColorCounts {
			switch colorCount.Color {
			case models.White:
				assert.Equal(t, 2, colorCount.Count) // game1 and game3
			case models.Black:
				assert.Equal(t, 1, colorCount.Count) // game2
			}
		}

		// Check result counts
		assert.Len(t, stats.ResultCounts, 3) // Mate, Resign, Draw
		for _, resultCount := range stats.ResultCounts {
			switch resultCount.Result {
			case models.Mate:
				assert.Equal(t, 1, resultCount.Count) // game1
			case models.Resign:
				assert.Equal(t, 1, resultCount.Count) // game2
			case models.Draw:
				assert.Equal(t, 1, resultCount.Count) // game3
			}
		}

		// Check time control counts
		assert.Len(t, stats.TimeControlCounts, 2) // 10+0, 5+0
		for _, timeControlCount := range stats.TimeControlCounts {
			switch timeControlCount.TimeControl {
			case "10+0":
				assert.Equal(t, 2, timeControlCount.Count) // game1 and game3
			case "5+0":
				assert.Equal(t, 1, timeControlCount.Count) // game2
			}
		}

		// Check rated counts
		assert.Len(t, stats.RatedCounts, 2) // true, false
		for _, ratedCount := range stats.RatedCounts {
			if ratedCount.Rated {
				assert.Equal(t, 2, ratedCount.Count) // game1 and game2
			} else {
				assert.Equal(t, 1, ratedCount.Count) // game3
			}
		}

		// Check average opponent rating
		// game1: opponent rating 1550
		// game2: opponent rating 1600
		// game3: opponent rating 1500
		// Average: (1550 + 1600 + 1500) / 3 = 1550
		assert.InDelta(t, 1550.0, stats.AverageOpponentRating, 0.1)
	})

	// Test filtering stats by time_control
	t.Run("Filter Stats by TimeControl", func(t *testing.T) {
		timeControl := "10+0"
		filter := repository.GameFilter{
			TimeControl: &timeControl,
		}
		statsArray, err := gameRepo.GetGameStats(context.Background(), user.ID, filter, 0, 100, nil)
		require.NoError(t, err)
		require.Len(t, statsArray, 1, "Expected 1 stats object when not grouping")
		stats := statsArray[0]
		assert.Equal(t, int64(2), stats.TotalCount) // game1, game3

		// Check platform counts
		assert.Len(t, stats.PlatformCounts, 1) // Only chess.com
		for _, platformCount := range stats.PlatformCounts {
			if platformCount.Platform == models.ChessDotCom {
				assert.Equal(t, 2, platformCount.Count)
			}
		}

		// Check average opponent rating
		// game1: opponent rating 1550
		// game3: opponent rating 1500
		// Average: (1550 + 1500) / 2 = 1525
		assert.InDelta(t, 1525.0, stats.AverageOpponentRating, 0.1)
	})

	// Test filtering stats by rated
	t.Run("Filter Stats by Rated", func(t *testing.T) {
		rated := true
		filter := repository.GameFilter{
			Rated: &rated,
		}
		statsArray, err := gameRepo.GetGameStats(context.Background(), user.ID, filter, 0, 100, nil)
		require.NoError(t, err)
		require.Len(t, statsArray, 1, "Expected 1 stats object when not grouping")
		stats := statsArray[0]
		assert.Equal(t, int64(2), stats.TotalCount) // game1, game2

		// Check result counts
		assert.Len(t, stats.ResultCounts, 2) // Mate, Resign
		for _, resultCount := range stats.ResultCounts {
			switch resultCount.Result {
			case models.Mate:
				assert.Equal(t, 1, resultCount.Count) // game1
			case models.Resign:
				assert.Equal(t, 1, resultCount.Count) // game2
			}
		}

		// Check average opponent rating
		// game1: opponent rating 1550
		// game2: opponent rating 1600
		// Average: (1550 + 1600) / 2 = 1575
		assert.InDelta(t, 1575.0, stats.AverageOpponentRating, 0.1)
	})

	// Test filtering stats by time_control and rated
	t.Run("Filter Stats by TimeControl and Rated", func(t *testing.T) {
		timeControl := "10+0"
		rated := true
		filter := repository.GameFilter{
			TimeControl: &timeControl,
			Rated:       &rated,
		}
		statsArray, err := gameRepo.GetGameStats(context.Background(), user.ID, filter, 0, 100, nil)
		require.NoError(t, err)
		require.Len(t, statsArray, 1, "Expected 1 stats object when not grouping")
		stats := statsArray[0]
		assert.Equal(t, int64(1), stats.TotalCount) // Only game1

		// Check user color counts
		assert.Len(t, stats.UserColorCounts, 1) // Only WHITE
		for _, colorCount := range stats.UserColorCounts {
			if colorCount.Color == models.White {
				assert.Equal(t, 1, colorCount.Count)
			}
		}

		// Check average opponent rating
		// game1: opponent rating 1550
		// Average: 1550
		assert.InDelta(t, 1550.0, stats.AverageOpponentRating, 0.1)
	})

	// Test pagination with stats
	t.Run("Paginate Stats", func(t *testing.T) {
		statsArray, err := gameRepo.GetGameStats(context.Background(), user.ID, repository.GameFilter{}, 0, 2, nil)
		require.NoError(t, err)
		require.Len(t, statsArray, 1, "Expected 1 stats object when not grouping")
		stats := statsArray[0]
		// The total count should still be 3 (all games) even with pagination
		assert.Equal(t, int64(3), stats.TotalCount)

		// But the platform counts, user color counts, etc. should only reflect the paginated games
		// This is implementation-dependent, so we don't test specific counts here
	})
}
