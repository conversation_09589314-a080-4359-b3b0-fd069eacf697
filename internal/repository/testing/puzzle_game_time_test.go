package testing

import (
	"context"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/repository/common"
	"github.com/chessticize/chessticize-server/internal/utils"
	"github.com/google/uuid"
	"github.com/lib/pq"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestPuzzleGroupingByGameTime runs the test for the fake repository
func TestPuzzleGroupingByGameTime(t *testing.T) {
	// Test with SQLite by default for faster tests
	provider := GetTestDBProvider(FakeRepository)
	defer provider.Cleanup(t)

	RunPuzzleGroupingByGameTimeTest(t, provider)
}

// TestRealPuzzleGroupingByGameTime runs the test for the real repository
func TestRealPuzzleGroupingByGameTime(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping real database tests")
	}
	provider := GetTestDBProvider(RealRepository)
	defer provider.Cleanup(t)

	RunPuzzleGroupingByGameTimeTest(t, provider)
}

// RunPuzzleGroupingByGameTimeTest tests that puzzles are grouped by Game.GameTime
func RunPuzzleGroupingByGameTimeTest(t *testing.T, provider TestDBProvider) {
	// Create test data with specific game times
	userRepo := provider.GetUserRepository(t)
	gameRepo := provider.GetGameRepository(t)
	puzzleRepo := provider.GetPuzzleRepository(t)

	// Create a test user
	user := CreateTestUser(t, userRepo)

	// Create games with different game times
	games := make([]*models.Game, 3)

	// Create 3 games on different days
	for i := 0; i < 3; i++ {
		// Create a simple PGN
		pgn := "1. e4 e5 2. Nf3 Nc6 3. Bb5 a6"
		compressedPGN, err := utils.CompressPGN(pgn)
		require.NoError(t, err)

		// Create game with specific game time
		gameTime := time.Date(2023, 1, i+1, 12, 0, 0, 0, time.UTC)

		game := &models.Game{
			ID:            uuid.New().String(),
			UserID:        user.ID,
			Platform:      models.ChessDotCom,
			ChessUsername: "testuser",
			UserColor:     models.White,
			GameTime:      gameTime,
			CompressedPGN: compressedPGN,
			TimeControl:   "10+0",
			Rated:         true,
			WhitePlayer:   `{"username":"testuser","rating":1500}`,
			BlackPlayer:   `{"username":"opponent","rating":1600}`,
			Winner:        models.WinnerWhite,
			Result:        models.Mate,
			CreatedAt:     time.Now(), // Use current time for CreatedAt
			UpdatedAt:     time.Now(),
		}
		err = gameRepo.Create(context.Background(), game)
		require.NoError(t, err)
		games[i] = game

		// Create 2 puzzles for each game
		for j := 0; j < 2; j++ {
			puzzle := &models.Puzzle{
				GameID:      game.ID,
				UserID:      user.ID,
				GameMove:    10 + j,
				FEN:         "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
				Moves:       pq.StringArray{"e2e4", "e7e5"},
				PrevCP:      0,
				CP:          100,
				Theme:       models.OpponentBlunderMissed,
				UserColor:   models.White,
				PuzzleColor: models.Black,
				Zugzwang:    false,
				Tags:        pq.StringArray{"opening", "tactical"},
				CreatedAt:   time.Now(), // Use current time for CreatedAt
				UpdatedAt:   time.Now(),
			}
			err = puzzleRepo.Create(context.Background(), puzzle)
			require.NoError(t, err)
		}
	}

	// Create a time range for testing
	startTime := time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)
	endTime := time.Date(2023, 1, 3, 23, 59, 59, 0, time.UTC)

	// Create a filter with the time range
	filter := repository.PuzzleFilter{
		GameStartTime: &startTime,
		GameEndTime:   &endTime,
	}

	// Test grouping by day
	t.Run("Group by Day", func(t *testing.T) {
		grouping := &common.TimeGrouping{
			Unit:   common.TimeGroupingDay,
			Length: 1,
		}

		statsArray, err := puzzleRepo.GetPuzzleStats(context.Background(), user.ID, filter, 0, 100, grouping)
		require.NoError(t, err)

		// With backward calculation, we should have 3 days (Jan 1-3, 2023)
		// No extra empty period since we don't round down startTime
		assert.Equal(t, 3, len(statsArray), "Expected 3 consecutive days of stats")

		// Verify that each day has the correct number of puzzles
		dayMap := make(map[string]*models.PuzzleStats)
		for _, stats := range statsArray {
			assert.NotNil(t, stats.PeriodStart, "PeriodStart should not be nil")
			// Use date string as key to avoid time zone issues
			dateStr := stats.PeriodStart.Format("2006-01-02")
			dayMap[dateStr] = stats
		}

		// Check Jan 1 (should have 2 puzzles from Jan 1 games)
		assert.Contains(t, dayMap, "2023-01-01", "Stats should include Jan 1")
		if stats, ok := dayMap["2023-01-01"]; ok {
			assert.Equal(t, int64(2), stats.TotalCount, "Jan 1 should have 2 puzzles")
		}

		// Check Jan 2 (should have 2 puzzles from Jan 2 games)
		assert.Contains(t, dayMap, "2023-01-02", "Stats should include Jan 2")
		if stats, ok := dayMap["2023-01-02"]; ok {
			assert.Equal(t, int64(2), stats.TotalCount, "Jan 2 should have 2 puzzles")
		}

		// Check Jan 3 (should have 2 puzzles from Jan 3 games)
		assert.Contains(t, dayMap, "2023-01-03", "Stats should include Jan 3")
		if stats, ok := dayMap["2023-01-03"]; ok {
			assert.Equal(t, int64(2), stats.TotalCount, "Jan 3 should have 2 puzzles")
		}
	})
}
