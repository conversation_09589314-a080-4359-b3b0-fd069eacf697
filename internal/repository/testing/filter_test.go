package testing

import (
	"context"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// setupFilterTestData creates test data for filter tests
// Returns user, games (game1, game2, game3), puzzles (puzzle1, puzzle2, puzzle3)
func setupFilterTestData(t *testing.T, provider TestDBProvider) (*models.User, []*models.Game, []*models.Puzzle) {
	userRepo := provider.GetUserRepository(t)
	gameRepo := provider.GetGameRepository(t)
	puzzleRepo := provider.GetPuzzleRepository(t)

	// Create a test user
	user := CreateTestUser(t, userRepo)

	// Create games with different properties
	game1 := CreateTestGame(t, gameRepo, user.ID)
	game1.TimeControl = "10+0"
	game1.Rated = true
	game1.UserColor = models.White
	game1.Result = models.Mate
	game1.Winner = models.WinnerWhite
	err := gameRepo.Update(context.Background(), game1)
	require.NoError(t, err)

	game2 := CreateTestGame(t, gameRepo, user.ID)
	game2.TimeControl = "5+0"
	game2.Rated = true
	game2.UserColor = models.Black
	game2.Result = models.Resign
	game2.Winner = models.WinnerBlack
	err = gameRepo.Update(context.Background(), game2)
	require.NoError(t, err)

	game3 := CreateTestGame(t, gameRepo, user.ID)
	game3.TimeControl = "10+0"
	game3.Rated = false
	game3.UserColor = models.White
	game3.Result = models.Draw
	game3.Winner = models.WinnerNone
	err = gameRepo.Update(context.Background(), game3)
	require.NoError(t, err)

	// Create puzzles associated with these games
	puzzle1 := CreateTestPuzzle(t, puzzleRepo, game1.ID, user.ID)
	puzzle1.Theme = models.OpponentBlunderMissed
	err = puzzleRepo.Update(context.Background(), puzzle1)
	require.NoError(t, err)

	puzzle2 := CreateTestPuzzle(t, puzzleRepo, game2.ID, user.ID)
	puzzle2.Theme = models.OpponentBlunderCaught
	err = puzzleRepo.Update(context.Background(), puzzle2)
	require.NoError(t, err)

	puzzle3 := CreateTestPuzzle(t, puzzleRepo, game3.ID, user.ID)
	puzzle3.Theme = models.OwnMistakePunished
	err = puzzleRepo.Update(context.Background(), puzzle3)
	require.NoError(t, err)

	return user, []*models.Game{game1, game2, game3}, []*models.Puzzle{puzzle1, puzzle2, puzzle3}
}

// TestGameFilters tests filtering games by various criteria
func TestGameFilters(t *testing.T) {
	// Test with SQLite by default for faster tests
	provider := GetTestDBProvider(FakeRepository)
	defer provider.Cleanup(t)

	user, games, _ := setupFilterTestData(t, provider)
	game1, game2, game3 := games[0], games[1], games[2]
	gameRepo := provider.GetGameRepository(t)

	// Test filtering games by time_control
	t.Run("Filter by TimeControl", func(t *testing.T) {
		timeControl := "10+0"
		filter := repository.GameFilter{
			TimeControl: &timeControl,
		}
		games, total, err := gameRepo.ListByUserID(context.Background(), user.ID, filter, 0, 10)
		require.NoError(t, err)
		assert.Equal(t, int64(2), total) // game1 and game3
		assert.Len(t, games, 2)

		// Check if our filtered games are present
		foundIDs := make(map[string]bool)
		for _, g := range games {
			foundIDs[g.ID] = true
		}
		assert.True(t, foundIDs[game1.ID])
		assert.True(t, foundIDs[game3.ID])
	})

	// Test filtering games by rated
	t.Run("Filter by Rated", func(t *testing.T) {
		rated := true
		filter := repository.GameFilter{
			Rated: &rated,
		}
		games, total, err := gameRepo.ListByUserID(context.Background(), user.ID, filter, 0, 10)
		require.NoError(t, err)
		assert.Equal(t, int64(2), total) // game1 and game2
		assert.Len(t, games, 2)

		// Check if our filtered games are present
		foundIDs := make(map[string]bool)
		for _, g := range games {
			foundIDs[g.ID] = true
		}
		assert.True(t, foundIDs[game1.ID])
		assert.True(t, foundIDs[game2.ID])
	})

	// Test filtering games by result
	t.Run("Filter by Result", func(t *testing.T) {
		result := models.Mate
		filter := repository.GameFilter{
			Result: &result,
		}
		games, total, err := gameRepo.ListByUserID(context.Background(), user.ID, filter, 0, 10)
		require.NoError(t, err)
		assert.Equal(t, int64(1), total) // Only game1
		assert.Len(t, games, 1)
		assert.Equal(t, game1.ID, games[0].ID)
	})

	// Test filtering games by multiple criteria
	t.Run("Filter by Multiple Criteria", func(t *testing.T) {
		timeControl := "10+0"
		rated := true
		filter := repository.GameFilter{
			TimeControl: &timeControl,
			Rated:       &rated,
		}
		games, total, err := gameRepo.ListByUserID(context.Background(), user.ID, filter, 0, 10)
		require.NoError(t, err)
		assert.Equal(t, int64(1), total) // Only game1
		assert.Len(t, games, 1)
		assert.Equal(t, game1.ID, games[0].ID)
	})
}

// TestPuzzleFilters tests filtering puzzles by various criteria
func TestPuzzleFilters(t *testing.T) {
	// Test with SQLite by default for faster tests
	provider := GetTestDBProvider(FakeRepository)
	defer provider.Cleanup(t)

	user, _, puzzles := setupFilterTestData(t, provider)
	puzzle1, puzzle2, puzzle3 := puzzles[0], puzzles[1], puzzles[2]
	puzzleRepo := provider.GetPuzzleRepository(t)

	// Test filtering puzzles by time_control
	t.Run("Filter by TimeControl", func(t *testing.T) {
		timeControl := "10+0"
		filter := repository.PuzzleFilter{
			TimeControl: &timeControl,
		}
		puzzles, total, err := puzzleRepo.ListByUserID(context.Background(), user.ID, filter, 0, 10)
		require.NoError(t, err)
		assert.Equal(t, int64(2), total) // puzzle1 and puzzle3
		assert.Len(t, puzzles, 2)

		// Check if our filtered puzzles are present
		foundIDs := make(map[string]bool)
		for _, p := range puzzles {
			foundIDs[p.ID] = true
		}
		assert.True(t, foundIDs[puzzle1.ID])
		assert.True(t, foundIDs[puzzle3.ID])
	})

	// Test filtering puzzles by rated
	t.Run("Filter by Rated", func(t *testing.T) {
		rated := true
		filter := repository.PuzzleFilter{
			Rated: &rated,
		}
		puzzles, total, err := puzzleRepo.ListByUserID(context.Background(), user.ID, filter, 0, 10)
		require.NoError(t, err)
		assert.Equal(t, int64(2), total) // puzzle1 and puzzle2
		assert.Len(t, puzzles, 2)

		// Check if our filtered puzzles are present
		foundIDs := make(map[string]bool)
		for _, p := range puzzles {
			foundIDs[p.ID] = true
		}
		assert.True(t, foundIDs[puzzle1.ID])
		assert.True(t, foundIDs[puzzle2.ID])
	})

	// Test filtering puzzles by theme
	t.Run("Filter by Theme", func(t *testing.T) {
		theme := models.OpponentBlunderMissed
		filter := repository.PuzzleFilter{
			Themes: []models.PuzzleTheme{theme},
		}
		puzzles, total, err := puzzleRepo.ListByUserID(context.Background(), user.ID, filter, 0, 10)
		require.NoError(t, err)
		assert.Equal(t, int64(1), total) // Only puzzle1
		assert.Len(t, puzzles, 1)
		assert.Equal(t, puzzle1.ID, puzzles[0].ID)
	})

	// Test filtering puzzles by multiple criteria
	t.Run("Filter by Multiple Criteria", func(t *testing.T) {
		timeControl := "10+0"
		rated := true
		theme := models.OpponentBlunderMissed
		filter := repository.PuzzleFilter{
			TimeControl: &timeControl,
			Rated:       &rated,
			Themes:      []models.PuzzleTheme{theme},
		}
		puzzles, total, err := puzzleRepo.ListByUserID(context.Background(), user.ID, filter, 0, 10)
		require.NoError(t, err)
		assert.Equal(t, int64(1), total) // Only puzzle1
		assert.Len(t, puzzles, 1)
		assert.Equal(t, puzzle1.ID, puzzles[0].ID)
	})

	// Test filtering puzzles by game time range
	t.Run("Filter by Game Time Range", func(t *testing.T) {
		// Create a separate user and games with specific dates for time filtering tests
		timeTestUser := CreateTestUser(t, provider.GetUserRepository(t))
		gameRepo := provider.GetGameRepository(t)

		// Create games with specific dates
		jan2023 := time.Date(2023, 1, 15, 12, 0, 0, 0, time.UTC)
		feb2023 := time.Date(2023, 2, 15, 12, 0, 0, 0, time.UTC)
		mar2023 := time.Date(2023, 3, 15, 12, 0, 0, 0, time.UTC)

		// Game 1: January 2023
		timeGame1 := CreateTestGame(t, gameRepo, timeTestUser.ID)
		timeGame1.GameTime = jan2023
		err := gameRepo.Update(context.Background(), timeGame1)
		require.NoError(t, err)

		// Game 2: February 2023
		timeGame2 := CreateTestGame(t, gameRepo, timeTestUser.ID)
		timeGame2.GameTime = feb2023
		err = gameRepo.Update(context.Background(), timeGame2)
		require.NoError(t, err)

		// Game 3: March 2023
		timeGame3 := CreateTestGame(t, gameRepo, timeTestUser.ID)
		timeGame3.GameTime = mar2023
		err = gameRepo.Update(context.Background(), timeGame3)
		require.NoError(t, err)

		// Create puzzles for each game
		_ = CreateTestPuzzle(t, puzzleRepo, timeGame1.ID, timeTestUser.ID)
		timePuzzle2 := CreateTestPuzzle(t, puzzleRepo, timeGame2.ID, timeTestUser.ID)
		_ = CreateTestPuzzle(t, puzzleRepo, timeGame3.ID, timeTestUser.ID)

		// Test filtering by GameStartTime - should include games after Feb 1, 2023
		startTime := time.Date(2023, 2, 1, 0, 0, 0, 0, time.UTC)
		filter := repository.PuzzleFilter{
			GameStartTime: &startTime,
		}
		puzzles, total, err := puzzleRepo.ListByUserID(context.Background(), timeTestUser.ID, filter, 0, 10)
		require.NoError(t, err)
		assert.Equal(t, int64(2), total) // timePuzzle2 (Feb) and timePuzzle3 (Mar)
		assert.Len(t, puzzles, 2)

		// Test filtering by GameEndTime - should include games before Mar 1, 2023
		endTime := time.Date(2023, 3, 1, 0, 0, 0, 0, time.UTC)
		filter = repository.PuzzleFilter{
			GameEndTime: &endTime,
		}
		puzzles, total, err = puzzleRepo.ListByUserID(context.Background(), timeTestUser.ID, filter, 0, 10)
		require.NoError(t, err)
		assert.Equal(t, int64(2), total) // timePuzzle1 (Jan) and timePuzzle2 (Feb)
		assert.Len(t, puzzles, 2)

		// Test filtering by both GameStartTime and GameEndTime - should include only Feb games
		startTime = time.Date(2023, 2, 1, 0, 0, 0, 0, time.UTC)
		endTime = time.Date(2023, 2, 28, 23, 59, 59, 0, time.UTC)
		filter = repository.PuzzleFilter{
			GameStartTime: &startTime,
			GameEndTime:   &endTime,
		}
		puzzles, total, err = puzzleRepo.ListByUserID(context.Background(), timeTestUser.ID, filter, 0, 10)
		require.NoError(t, err)
		assert.Equal(t, int64(1), total) // Only timePuzzle2 (Feb)
		assert.Len(t, puzzles, 1)
		assert.Equal(t, timePuzzle2.ID, puzzles[0].ID)

		// Test filtering with no matches
		startTime = time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC)
		endTime = time.Date(2022, 12, 31, 23, 59, 59, 0, time.UTC)
		filter = repository.PuzzleFilter{
			GameStartTime: &startTime,
			GameEndTime:   &endTime,
		}
		puzzles, total, err = puzzleRepo.ListByUserID(context.Background(), timeTestUser.ID, filter, 0, 10)
		require.NoError(t, err)
		assert.Equal(t, int64(0), total)
		assert.Len(t, puzzles, 0)

		// Verify that all puzzles exist without time filter
		filter = repository.PuzzleFilter{}
		puzzles, total, err = puzzleRepo.ListByUserID(context.Background(), timeTestUser.ID, filter, 0, 10)
		require.NoError(t, err)
		assert.Equal(t, int64(3), total) // All 3 puzzles
		assert.Len(t, puzzles, 3)
	})
}
