package testing

import (
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestDailyStatsCreationAndUpdate(t *testing.T) {
	// Use PostgreSQL for this test since it has the triggers
	provider := NewPostgresTestDBProvider()
	defer provider.Cleanup(t)

	userRepo := provider.GetUserRepository(t)
	gameRepo := provider.GetGameRepository(t)
	puzzleRepo := provider.GetPuzzleRepository(t)
	eventRepo := provider.GetEventRepository(t)

	// Create a test user
	user := CreateTestUser(t, userRepo)

	// Create test game and puzzle
	game := CreateTestGame(t, gameRepo, user.ID)
	puzzle := CreateTestPuzzle(t, puzzleRepo, game.ID, user.ID)

	// Create a puzzle event with proper event_sub_type
	CreateTestPuzzleEvent(t, eventRepo, user.ID, puzzle.ID, true, 45, time.Now())

	// Wait a moment for triggers to execute
	time.Sleep(200 * time.Millisecond)

	// Check that daily stats were created
	var dailyStats []models.UserDailyStats
	err := provider.GetDB(t).Where("user_id = ?", user.ID).Find(&dailyStats).Error
	require.NoError(t, err)
	require.Len(t, dailyStats, 1, "Should have one daily stats record")

	todayStats := dailyStats[0]
	assert.Equal(t, 1, todayStats.PuzzleSuccess, "Should have 1 successful puzzle")
	assert.Equal(t, 1, todayStats.PuzzleTotal, "Should have 1 total puzzle")
	assert.Equal(t, 1, todayStats.Streak, "Should have streak of 1")
	assert.Equal(t, 45, todayStats.PuzzleTotalDuration, "Should have 45 seconds total duration")

	// Check that per-puzzle stats were created
	var puzzleStats []models.UserPuzzleStats
	err = provider.GetDB(t).Where("user_id = ?", user.ID).Find(&puzzleStats).Error
	require.NoError(t, err)
	require.Len(t, puzzleStats, 1, "Should have one puzzle stats record")

	puzzleStat := puzzleStats[0]
	assert.Equal(t, puzzle.ID, puzzleStat.PuzzleID, "Should track the correct puzzle")
	assert.Equal(t, 1, puzzleStat.Attempts, "Should have 1 attempt")
	assert.Equal(t, 1, puzzleStat.SuccessCount, "Should have 1 success")
	assert.Equal(t, 45, puzzleStat.TotalTime, "Should have 45 seconds total time")
	assert.Equal(t, 45.0, puzzleStat.AverageTime, "Should have 45 seconds average time")
	assert.True(t, puzzleStat.LastAttemptSuccess, "Last attempt should be successful")
}

func TestDailyStatsMultiplePuzzles(t *testing.T) {
	// Use PostgreSQL for this test since it has the triggers
	provider := NewPostgresTestDBProvider()
	defer provider.Cleanup(t)

	userRepo := provider.GetUserRepository(t)
	gameRepo := provider.GetGameRepository(t)
	puzzleRepo := provider.GetPuzzleRepository(t)
	eventRepo := provider.GetEventRepository(t)

	// Create a test user
	user := CreateTestUser(t, userRepo)

	// Create test game and puzzles
	game := CreateTestGame(t, gameRepo, user.ID)
	puzzle1 := CreateTestPuzzle(t, puzzleRepo, game.ID, user.ID)
	puzzle2 := CreateTestPuzzle(t, puzzleRepo, game.ID, user.ID)
	puzzle3 := CreateTestPuzzle(t, puzzleRepo, game.ID, user.ID)

	// Create multiple puzzle events - some solved, some not
	puzzleEvents := []struct {
		puzzleID  string
		solved    bool
		timeSpent int
	}{
		{puzzle1.ID, true, 30},
		{puzzle2.ID, false, 60},
		{puzzle3.ID, true, 45},
		{puzzle1.ID, true, 25}, // Same puzzle again
	}

	for i, pe := range puzzleEvents {
		CreateTestPuzzleEvent(t, eventRepo, user.ID, pe.puzzleID, pe.solved, pe.timeSpent, time.Now().Add(time.Duration(i)*time.Second))
	}

	// Wait a moment for triggers to execute
	time.Sleep(200 * time.Millisecond)

	// Check daily stats
	var dailyStats []models.UserDailyStats
	err := provider.GetDB(t).Where("user_id = ?", user.ID).Find(&dailyStats).Error
	require.NoError(t, err)
	require.Len(t, dailyStats, 1, "Should have one daily stats record")

	todayStats := dailyStats[0]
	assert.Equal(t, 3, todayStats.PuzzleSuccess, "Should have 3 successful puzzles")
	assert.Equal(t, 4, todayStats.PuzzleTotal, "Should have 4 total puzzle attempts")
	assert.Equal(t, 160, todayStats.PuzzleTotalDuration, "Should have 160 seconds total duration (30+60+45+25)")

	// Check per-puzzle stats
	var puzzleStats []models.UserPuzzleStats
	err = provider.GetDB(t).Where("user_id = ?", user.ID).Find(&puzzleStats).Error
	require.NoError(t, err)
	require.Len(t, puzzleStats, 3, "Should have three puzzle stats records")

	// Find puzzle-1 stats (attempted twice)
	var puzzle1Stats *models.UserPuzzleStats
	for _, ps := range puzzleStats {
		if ps.PuzzleID == puzzle1.ID {
			puzzle1Stats = &ps
			break
		}
	}
	require.NotNil(t, puzzle1Stats, "Should have stats for puzzle-1")
	assert.Equal(t, 2, puzzle1Stats.Attempts, "Puzzle-1 should have 2 attempts")
	assert.Equal(t, 2, puzzle1Stats.SuccessCount, "Puzzle-1 should have 2 successes")
	assert.Equal(t, 55, puzzle1Stats.TotalTime, "Puzzle-1 should have 55 total seconds")
	assert.True(t, puzzle1Stats.LastAttemptSuccess, "Last attempt should be successful")
}

func TestDailyStatsStreak(t *testing.T) {
	// Use PostgreSQL for this test since it has the triggers
	provider := NewPostgresTestDBProvider()
	defer provider.Cleanup(t)

	userRepo := provider.GetUserRepository(t)
	gameRepo := provider.GetGameRepository(t)
	puzzleRepo := provider.GetPuzzleRepository(t)
	eventRepo := provider.GetEventRepository(t)

	// Create a test user
	user := CreateTestUser(t, userRepo)

	// Create test game
	game := CreateTestGame(t, gameRepo, user.ID)

	// Create puzzle events for consecutive days
	now := time.Now()
	for i := 0; i < 3; i++ {
		// Create a new puzzle for each day
		puzzle := CreateTestPuzzle(t, puzzleRepo, game.ID, user.ID)
		CreateTestPuzzleEvent(t, eventRepo, user.ID, puzzle.ID, true, 30, now.AddDate(0, 0, -2+i))
	}

	// Wait a moment for triggers to execute
	time.Sleep(200 * time.Millisecond)

	// Check daily stats
	var dailyStats []models.UserDailyStats
	err := provider.GetDB(t).Where("user_id = ?", user.ID).Order("date ASC").Find(&dailyStats).Error
	require.NoError(t, err)
	require.Len(t, dailyStats, 3, "Should have three daily stats records")

	// Verify streak calculation
	assert.Equal(t, 1, dailyStats[0].Streak, "First day should have streak of 1")
	assert.Equal(t, 2, dailyStats[1].Streak, "Second day should have streak of 2")
	assert.Equal(t, 3, dailyStats[2].Streak, "Third day should have streak of 3")
}
