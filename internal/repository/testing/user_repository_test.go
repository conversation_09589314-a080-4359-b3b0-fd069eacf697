package testing

import (
	"context"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"
)

// TestUserRepository runs all tests for both real and fake user repositories
func TestUserRepository(t *testing.T) {
	// Test with SQLite by default for faster tests
	provider := GetTestDBProvider(FakeRepository)
	defer provider.Cleanup(t)

	RunUserRepositoryTests(t, provider)
}

// TestRealUserRepository runs tests against the real PostgreSQL repository implementation
// This will be skipped if the test database is not available
func TestRealUserRepository(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping real database tests")
	}
	provider := GetTestDBProvider(RealRepository)
	defer provider.Cleanup(t)

	RunUserRepositoryTests(t, provider)
}

// RunUserRepositoryTests runs all tests for a user repository
func RunUserRepositoryTests(t *testing.T, provider TestDBProvider) {
	userRepo := provider.GetUserRepository(t)
	ctx := context.Background()

	t.Run("Create User and GetByID with separate Profiles", func(t *testing.T) {
		// Create user without profiles
		user := &models.User{
			Email:        RandomEmail(),
			PasswordHash: RandomString(32),
		}
		err := userRepo.Create(ctx, user)
		assert.NoError(t, err)
		assert.NotEmpty(t, user.ID, "User ID should be set after creation")
		assert.False(t, user.RegisteredAt.IsZero(), "User RegisteredAt should not be zero")
		assert.False(t, user.UpdatedAt.IsZero(), "User UpdatedAt should not be zero")
		assert.WithinDuration(t, user.RegisteredAt, user.UpdatedAt, 5*time.Second, "User UpdatedAt should be close to RegisteredAt initially")
		assert.WithinDuration(t, time.Now(), user.RegisteredAt, 10*time.Second, "User RegisteredAt should be recent")

		// Add profiles separately
		profile1 := &models.ChessProfile{
			Platform: "chess.com",
			Username: "testuser_cc",
		}
		err = userRepo.AddChessProfile(ctx, user.ID, profile1)
		assert.NoError(t, err)
		assert.NotEmpty(t, profile1.ID, "Profile1 ID should be set after adding")
		assert.False(t, profile1.CreatedAt.IsZero(), "Profile1 CreatedAt should not be zero")
		assert.False(t, profile1.UpdatedAt.IsZero(), "Profile1 UpdatedAt should not be zero")
		assert.WithinDuration(t, profile1.CreatedAt, profile1.UpdatedAt, 5*time.Second, "Profile1 UpdatedAt should be close to CreatedAt initially")
		assert.WithinDuration(t, time.Now(), profile1.CreatedAt, 10*time.Second, "Profile1 CreatedAt should be recent")

		profile2 := &models.ChessProfile{
			Platform: "lichess.org",
			Username: "testuser_li",
		}
		err = userRepo.AddChessProfile(ctx, user.ID, profile2)
		assert.NoError(t, err)
		assert.NotEmpty(t, profile2.ID, "Profile2 ID should be set after adding")
		assert.False(t, profile2.CreatedAt.IsZero(), "Profile2 CreatedAt should not be zero")
		assert.False(t, profile2.UpdatedAt.IsZero(), "Profile2 UpdatedAt should not be zero")
		assert.WithinDuration(t, profile2.CreatedAt, profile2.UpdatedAt, 5*time.Second, "Profile2 UpdatedAt should be close to CreatedAt initially")
		assert.WithinDuration(t, time.Now(), profile2.CreatedAt, 10*time.Second, "Profile2 CreatedAt should be recent")

		// Get user with profiles
		found, err := userRepo.GetByID(ctx, user.ID)
		assert.NoError(t, err)
		assert.NotNil(t, found)
		assert.Equal(t, user.ID, found.ID)
		assert.Equal(t, user.Email, found.Email)
		require.Len(t, found.ChessProfiles, 2)

		// Check profiles (order might not be guaranteed)
		foundPlatforms := make(map[string]string)
		for _, p := range found.ChessProfiles {
			assert.Nil(t, p.LastGameFetchedAt, "LastGameFetchedAt should be nil initially")
			assert.Nil(t, p.LastGamePlayedAt, "LastGamePlayedAt should be nil initially")
			assert.Zero(t, p.GamesFetched, "GamesFetched should be 0 initially")
			foundPlatforms[string(p.Platform)] = p.Username
		}
		assert.Equal(t, "testuser_cc", foundPlatforms["chess.com"])
		assert.Equal(t, "testuser_li", foundPlatforms["lichess.org"])
	})

	t.Run("GetByEmail with separate Profiles", func(t *testing.T) {
		// Create user without profiles
		user := &models.User{
			Email:        RandomEmail(),
			PasswordHash: RandomString(32),
		}
		err := userRepo.Create(ctx, user)
		assert.NoError(t, err)
		assert.NotEmpty(t, user.ID)
		initialRegisteredAt := user.RegisteredAt // Capture initial user times
		initialUpdatedAt := user.UpdatedAt
		assert.False(t, initialRegisteredAt.IsZero(), "Initial User RegisteredAt should not be zero")
		assert.False(t, initialUpdatedAt.IsZero(), "Initial User UpdatedAt should not be zero")

		// Add profile separately
		profile1 := &models.ChessProfile{
			Platform: "chess.com",
			Username: RandomString(10),
		}
		err = userRepo.AddChessProfile(ctx, user.ID, profile1)
		assert.NoError(t, err)
		assert.NotEmpty(t, profile1.ID)
		assert.False(t, profile1.CreatedAt.IsZero(), "Profile CreatedAt should not be zero")
		assert.False(t, profile1.UpdatedAt.IsZero(), "Profile UpdatedAt should not be zero")

		// Fetch by email
		found, err := userRepo.GetByEmail(ctx, user.Email)
		assert.NoError(t, err)
		assert.NotNil(t, found)
		assert.Equal(t, user.ID, found.ID)
		assert.Equal(t, user.Email, found.Email)
		require.Len(t, found.ChessProfiles, 1)
		assert.Equal(t, profile1.Platform, found.ChessProfiles[0].Platform)
		assert.Equal(t, profile1.Username, found.ChessProfiles[0].Username)
		assert.Nil(t, found.ChessProfiles[0].LastGamePlayedAt, "LastGamePlayedAt should be nil initially")
	})

	t.Run("Update User without Profiles (Save)", func(t *testing.T) {
		// Create user without profiles
		user := &models.User{
			Email:        RandomEmail(),
			PasswordHash: RandomString(32),
		}
		err := userRepo.Create(ctx, user)
		assert.NoError(t, err)
		assert.NotEmpty(t, user.ID)
		initialRegisteredAt := user.RegisteredAt // Capture initial user times
		initialUpdatedAt := user.UpdatedAt
		assert.False(t, initialRegisteredAt.IsZero(), "Initial User RegisteredAt should not be zero")
		assert.False(t, initialUpdatedAt.IsZero(), "Initial User UpdatedAt should not be zero")

		// Add a profile separately
		profile1 := &models.ChessProfile{
			Platform: "chess.com",
			Username: "initial_cc",
		}
		err = userRepo.AddChessProfile(ctx, user.ID, profile1)
		assert.NoError(t, err)
		assert.NotEmpty(t, profile1.ID)

		// Get user to update
		foundInitial, err := userRepo.GetByID(ctx, user.ID)
		require.NoError(t, err)
		require.Len(t, foundInitial.ChessProfiles, 1)

		// Update user (only user fields, no profiles)
		userToUpdate := *foundInitial
		userToUpdate.Email = RandomEmail() // Update a user field
		userToUpdate.ChessProfiles = nil   // Profiles must be nil when updating user

		// Wait a bit to ensure UpdatedAt changes noticeably
		time.Sleep(10 * time.Millisecond)

		err = userRepo.Update(ctx, &userToUpdate)
		assert.NoError(t, err)

		// Verify user update but profiles still exist
		foundUpdated, err := userRepo.GetByID(ctx, user.ID)
		assert.NoError(t, err)
		assert.Equal(t, userToUpdate.Email, foundUpdated.Email)
		require.Len(t, foundUpdated.ChessProfiles, 1) // Profile should still exist
		// Check user timestamps after update
		assert.Equal(t, initialRegisteredAt, foundUpdated.RegisteredAt, "User RegisteredAt should not change on update")
		assert.True(t, foundUpdated.UpdatedAt.After(initialUpdatedAt), "User UpdatedAt (%v) should be later than initial UpdatedAt (%v)", foundUpdated.UpdatedAt, initialUpdatedAt)
		assert.WithinDuration(t, time.Now(), foundUpdated.UpdatedAt, 10*time.Second, "User UpdatedAt should be recent after update")

		// Update profile separately
		profileToUpdate, err := userRepo.GetChessProfileByPlatform(ctx, user.ID, "chess.com")
		assert.NoError(t, err)
		profileToUpdate.Username = "updated_cc"
		nowPlayed := time.Now().Add(-2 * time.Hour)
		profileToUpdate.LastGamePlayedAt = &nowPlayed

		err = userRepo.UpdateChessProfile(ctx, profileToUpdate)
		assert.NoError(t, err)

		// Add another profile
		profile2New := &models.ChessProfile{
			Platform: "lichess.org",
			Username: "new_li",
		}
		err = userRepo.AddChessProfile(ctx, user.ID, profile2New)
		assert.NoError(t, err)

		// Verify all updates
		foundFinal, err := userRepo.GetByID(ctx, user.ID)
		assert.NoError(t, err)
		require.Len(t, foundFinal.ChessProfiles, 2)

		// Check profiles
		platforms := make(map[string]models.ChessProfile)
		for _, p := range foundFinal.ChessProfiles {
			platforms[string(p.Platform)] = p
		}
		// Verify updated profile 1
		profile1Updated, ok := platforms["chess.com"]
		assert.True(t, ok)
		assert.Equal(t, "updated_cc", profile1Updated.Username)
		assert.NotNil(t, profile1Updated.LastGamePlayedAt)
		assert.WithinDuration(t, nowPlayed, *profile1Updated.LastGamePlayedAt, time.Second)
		// Verify new profile 2
		profile2Updated, ok := platforms["lichess.org"]
		assert.True(t, ok)
		assert.Equal(t, "new_li", profile2Updated.Username)
		assert.Nil(t, profile2Updated.LastGamePlayedAt) // New profile shouldn't have time set yet
	})

	t.Run("Delete Profiles Separately", func(t *testing.T) {
		// Create user without profiles
		user := &models.User{
			Email:        RandomEmail(),
			PasswordHash: RandomString(32),
		}
		err := userRepo.Create(ctx, user)
		assert.NoError(t, err)

		// Add profiles separately
		profile1 := &models.ChessProfile{
			Platform: "chess.com",
			Username: "cc_todelete",
		}
		err = userRepo.AddChessProfile(ctx, user.ID, profile1)
		assert.NoError(t, err)

		profile2 := &models.ChessProfile{
			Platform: "lichess.org",
			Username: "li_todelete",
		}
		err = userRepo.AddChessProfile(ctx, user.ID, profile2)
		assert.NoError(t, err)

		// Verify initial state
		foundInitial, err := userRepo.GetByID(ctx, user.ID)
		require.NoError(t, err)
		require.Len(t, foundInitial.ChessProfiles, 2)

		// Delete profiles separately
		profiles, err := userRepo.GetChessProfilesByUserID(ctx, user.ID)
		assert.NoError(t, err)
		for _, profile := range profiles {
			err = userRepo.DeleteChessProfile(ctx, user.ID, profile.ID)
			assert.NoError(t, err)
		}

		// Verify profiles are deleted
		foundUpdated, err := userRepo.GetByID(ctx, user.ID)
		assert.NoError(t, err)
		assert.Empty(t, foundUpdated.ChessProfiles) // Should be empty now
	})

	t.Run("GetAll with Profiles", func(t *testing.T) {
		// Create several users, some with profiles
		user1 := &models.User{
			Email:        RandomEmail(),
			PasswordHash: RandomString(32),
		}
		err := userRepo.Create(ctx, user1)
		assert.NoError(t, err)

		profile1 := &models.ChessProfile{
			Platform: "chess.com",
			Username: RandomString(10),
		}
		err = userRepo.AddChessProfile(ctx, user1.ID, profile1)
		assert.NoError(t, err)

		// User without profiles
		user2 := &models.User{
			Email:        RandomEmail(),
			PasswordHash: RandomString(32),
		}
		err = userRepo.Create(ctx, user2)
		assert.NoError(t, err)

		// User with multiple profiles
		user3 := &models.User{
			Email:        RandomEmail(),
			PasswordHash: RandomString(32),
		}
		err = userRepo.Create(ctx, user3)
		assert.NoError(t, err)

		profileA := &models.ChessProfile{
			Platform: "lichess.org",
			Username: RandomString(10),
		}
		err = userRepo.AddChessProfile(ctx, user3.ID, profileA)
		assert.NoError(t, err)

		profileB := &models.ChessProfile{
			Platform: "chess.com",
			Username: RandomString(10),
		}
		err = userRepo.AddChessProfile(ctx, user3.ID, profileB)
		assert.NoError(t, err)

		// Get all users
		users, err := userRepo.GetAll(ctx)
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, len(users), 3)

		// Check profiles are loaded correctly for each user
		foundCount := 0
		for _, u := range users {
			switch u.ID {
			case user1.ID:
				assert.Len(t, u.ChessProfiles, 1)
				assert.Equal(t, models.ChessPlatform("chess.com"), u.ChessProfiles[0].Platform)
				foundCount++
			case user2.ID:
				assert.Empty(t, u.ChessProfiles)
				foundCount++
			case user3.ID:
				assert.Len(t, u.ChessProfiles, 2)
				foundCount++
			}
		}
		assert.Equal(t, 3, foundCount, "Did not find all created users in GetAll result")
	})

	t.Run("Delete User Cascades Profiles", func(t *testing.T) {
		// Create user without profiles
		user := &models.User{
			Email:        RandomEmail(),
			PasswordHash: RandomString(32),
		}
		err := userRepo.Create(ctx, user)
		assert.NoError(t, err)

		// Add profiles separately
		profile1 := &models.ChessProfile{
			Platform: "chess.com",
			Username: "cascade_cc",
		}
		err = userRepo.AddChessProfile(ctx, user.ID, profile1)
		assert.NoError(t, err)

		profile2 := &models.ChessProfile{
			Platform: "lichess.org",
			Username: "cascade_li",
		}
		err = userRepo.AddChessProfile(ctx, user.ID, profile2)
		assert.NoError(t, err)

		// Get profiles to store their IDs
		profiles, err := userRepo.GetChessProfilesByUserID(ctx, user.ID)
		assert.NoError(t, err)
		require.Len(t, profiles, 2)

		profileID1 := profiles[0].ID
		profileID2 := profiles[1].ID
		require.NotZero(t, profileID1)
		require.NotZero(t, profileID2)

		// Delete the user
		err = userRepo.Delete(ctx, user.ID)
		assert.NoError(t, err)

		// Verify user deletion
		_, err = userRepo.GetByID(ctx, user.ID)
		assert.Error(t, err) // User should be gone
		assert.ErrorIs(t, err, gorm.ErrRecordNotFound)

		// Verify profiles are also deleted (using profile-specific getter)
		_, err = userRepo.GetChessProfileByID(ctx, profileID1)
		assert.Error(t, err) // Profile should be gone
		assert.ErrorIs(t, err, gorm.ErrRecordNotFound)

		_, err = userRepo.GetChessProfileByID(ctx, profileID2)
		assert.Error(t, err) // Profile should be gone
		assert.ErrorIs(t, err, gorm.ErrRecordNotFound)
	})

	t.Run("UpdateLastSignIn", func(t *testing.T) {
		// Create user without profiles
		user := &models.User{
			Email:        RandomEmail(),
			PasswordHash: RandomString(32),
		}
		err := userRepo.Create(ctx, user)
		assert.NoError(t, err)

		err = userRepo.UpdateLastSignIn(ctx, user.ID)
		assert.NoError(t, err)
		found, err := userRepo.GetByID(ctx, user.ID)
		assert.NoError(t, err)
		assert.NotNil(t, found.LastSignInAt)
		assert.True(t, time.Since(*found.LastSignInAt) < time.Minute)
	})

	t.Run("GetByID not found", func(t *testing.T) {
		_, err := userRepo.GetByID(ctx, "non-existent-id")
		assert.Error(t, err)
		assert.ErrorIs(t, err, gorm.ErrRecordNotFound)
	})

	t.Run("GetByEmail not found", func(t *testing.T) {
		_, err := userRepo.GetByEmail(ctx, "<EMAIL>")
		assert.Error(t, err)
		assert.ErrorIs(t, err, gorm.ErrRecordNotFound)
	})

	// --- ChessProfile Specific Method Tests ---

	t.Run("AddChessProfile and GetChessProfilesByUserID", func(t *testing.T) {
		// Create user without profiles
		user := &models.User{
			Email:        RandomEmail(),
			PasswordHash: RandomString(32),
		}
		err := userRepo.Create(ctx, user)
		assert.NoError(t, err)
		assert.NotEmpty(t, user.ID)
		assert.False(t, user.RegisteredAt.IsZero(), "User RegisteredAt should not be zero")
		assert.False(t, user.UpdatedAt.IsZero(), "User UpdatedAt should not be zero")
		assert.WithinDuration(t, user.RegisteredAt, user.UpdatedAt, 5*time.Second, "User UpdatedAt should be close to RegisteredAt initially")
		assert.WithinDuration(t, time.Now(), user.RegisteredAt, 10*time.Second, "User RegisteredAt should be recent")

		profile1 := &models.ChessProfile{
			Platform: "chess.com",
			Username: "add_test_cc",
		}
		err = userRepo.AddChessProfile(ctx, user.ID, profile1)
		assert.NoError(t, err)
		assert.NotZero(t, profile1.ID) // ID should be populated
		// Verify initial state after Add
		addedProfile1, err := userRepo.GetChessProfileByID(ctx, profile1.ID)
		assert.NoError(t, err)
		assert.NotNil(t, addedProfile1)
		assert.False(t, addedProfile1.CreatedAt.IsZero(), "Profile CreatedAt should not be zero")
		assert.False(t, addedProfile1.UpdatedAt.IsZero(), "Profile UpdatedAt should not be zero")
		assert.WithinDuration(t, addedProfile1.CreatedAt, addedProfile1.UpdatedAt, 5*time.Second, "Profile UpdatedAt should be close to CreatedAt initially")
		assert.WithinDuration(t, time.Now(), addedProfile1.CreatedAt, 10*time.Second, "Profile CreatedAt should be recent")
		assert.Nil(t, addedProfile1.LastGamePlayedAt, "LastGamePlayedAt should be nil after AddChessProfile")
		assert.Zero(t, addedProfile1.GamesFetched)

		profile2 := &models.ChessProfile{
			Platform: "lichess.org",
			Username: "add_test_li",
		}
		err = userRepo.AddChessProfile(ctx, user.ID, profile2)
		assert.NoError(t, err)
		assert.NotZero(t, profile2.ID)

		// Get profiles for the user
		profiles, err := userRepo.GetChessProfilesByUserID(ctx, user.ID)
		assert.NoError(t, err)
		assert.Len(t, profiles, 2)

		// Verify content (order might vary)
		platforms := make(map[string]string)
		for _, p := range profiles {
			platforms[string(p.Platform)] = p.Username
			assert.Nil(t, p.LastGamePlayedAt) // Check nil state again when fetched via UserID
		}
		assert.Equal(t, "add_test_cc", platforms["chess.com"])
		assert.Equal(t, "add_test_li", platforms["lichess.org"])
	})

	t.Run("GetChessProfileByID", func(t *testing.T) {
		// Create user without profiles
		user := &models.User{
			Email:        RandomEmail(),
			PasswordHash: RandomString(32),
		}
		err := userRepo.Create(ctx, user)
		assert.NoError(t, err)

		profile := &models.ChessProfile{Platform: "chess.com", Username: "getbyid_test"}
		err = userRepo.AddChessProfile(ctx, user.ID, profile)
		assert.NoError(t, err)
		assert.NotZero(t, profile.ID)

		found, err := userRepo.GetChessProfileByID(ctx, profile.ID)
		assert.NoError(t, err)
		assert.NotNil(t, found)
		assert.Equal(t, profile.ID, found.ID)
		assert.Equal(t, user.ID, found.UserID)
		assert.Equal(t, models.ChessPlatform("chess.com"), found.Platform)
		assert.Equal(t, "getbyid_test", found.Username)
		assert.Nil(t, found.LastGamePlayedAt) // Check initial state
	})

	t.Run("GetChessProfileByID not found", func(t *testing.T) {
		_, err := userRepo.GetChessProfileByID(ctx, "non-existent-id")
		assert.Error(t, err)
		assert.ErrorIs(t, err, gorm.ErrRecordNotFound)
	})

	t.Run("GetChessProfileByPlatform", func(t *testing.T) {
		// Create user without profiles
		user := &models.User{
			Email:        RandomEmail(),
			PasswordHash: RandomString(32),
		}
		err := userRepo.Create(ctx, user)
		assert.NoError(t, err)

		profileCC := &models.ChessProfile{Platform: "chess.com", Username: "getbyplat_cc"}
		profileLI := &models.ChessProfile{Platform: "lichess.org", Username: "getbyplat_li"}
		err = userRepo.AddChessProfile(ctx, user.ID, profileCC)
		assert.NoError(t, err)
		err = userRepo.AddChessProfile(ctx, user.ID, profileLI)
		assert.NoError(t, err)

		foundCC, err := userRepo.GetChessProfileByPlatform(ctx, user.ID, "chess.com")
		assert.NoError(t, err)
		assert.NotNil(t, foundCC)
		assert.Equal(t, profileCC.ID, foundCC.ID)
		assert.Equal(t, "getbyplat_cc", foundCC.Username)
		assert.Nil(t, foundCC.LastGamePlayedAt) // Check initial state

		foundLI, err := userRepo.GetChessProfileByPlatform(ctx, user.ID, "lichess.org")
		assert.NoError(t, err)
		assert.NotNil(t, foundLI)
		assert.Equal(t, profileLI.ID, foundLI.ID)
		assert.Equal(t, "getbyplat_li", foundLI.Username)
		assert.Nil(t, foundLI.LastGamePlayedAt) // Check initial state
	})

	t.Run("GetChessProfileByPlatform not found", func(t *testing.T) {
		// Create user without profiles
		user := &models.User{
			Email:        RandomEmail(),
			PasswordHash: RandomString(32),
		}
		err := userRepo.Create(ctx, user)
		assert.NoError(t, err)

		_, err = userRepo.GetChessProfileByPlatform(ctx, user.ID, "nonexistent.com")
		assert.Error(t, err)
		assert.ErrorIs(t, err, gorm.ErrRecordNotFound)
	})

	t.Run("UpdateChessProfile (Save)", func(t *testing.T) {
		// Create user without profiles
		user := &models.User{
			Email:        RandomEmail(),
			PasswordHash: RandomString(32),
		}
		err := userRepo.Create(ctx, user)
		assert.NoError(t, err)
		assert.NotEmpty(t, user.ID)
		initialRegisteredAt := user.RegisteredAt // Capture initial user times
		initialUpdatedAt := user.UpdatedAt
		assert.False(t, initialRegisteredAt.IsZero(), "Initial User RegisteredAt should not be zero")
		assert.False(t, initialUpdatedAt.IsZero(), "Initial User UpdatedAt should not be zero")

		profile := &models.ChessProfile{Platform: "chess.com", Username: "update_before", GamesFetched: 5}
		err = userRepo.AddChessProfile(ctx, user.ID, profile)
		assert.NoError(t, err)
		assert.NotZero(t, profile.ID)

		// Get the profile to update
		profileToUpdate, err := userRepo.GetChessProfileByID(ctx, profile.ID)
		assert.NoError(t, err)
		initialProfileCreatedAt := profileToUpdate.CreatedAt
		initialProfileUpdatedAt := profileToUpdate.UpdatedAt
		assert.False(t, initialProfileCreatedAt.IsZero(), "Initial Profile CreatedAt should not be zero")
		assert.False(t, initialProfileUpdatedAt.IsZero(), "Initial Profile UpdatedAt should not be zero")

		// Modify fields
		profileToUpdate.Username = "update_after"
		profileToUpdate.GamesFetched = 10
		nowFetched := time.Now()
		profileToUpdate.LastGameFetchedAt = &nowFetched
		nowPlayed := time.Now().Add(-time.Hour) // Simulate last played time
		profileToUpdate.LastGamePlayedAt = &nowPlayed

		// Wait a bit to ensure UpdatedAt changes noticeably
		time.Sleep(10 * time.Millisecond)

		err = userRepo.UpdateChessProfile(ctx, profileToUpdate)
		assert.NoError(t, err)

		// Verify
		found, err := userRepo.GetChessProfileByID(ctx, profile.ID)
		assert.NoError(t, err)
		assert.Equal(t, "update_after", found.Username)
		assert.Equal(t, 10, found.GamesFetched)
		assert.NotNil(t, found.LastGameFetchedAt)
		assert.WithinDuration(t, nowFetched, *found.LastGameFetchedAt, time.Second)
		assert.NotNil(t, found.LastGamePlayedAt)
		assert.WithinDuration(t, nowPlayed, *found.LastGamePlayedAt, time.Second)
		// assert.True(t, found.UpdatedAt.After(profile.CreatedAt))
		// Check timestamps after update
		assert.WithinDuration(t, time.Now(), found.CreatedAt, time.Second, "ChessProfile CreatedAt should be recent")
		assert.True(t, found.UpdatedAt.After(initialUpdatedAt), "User UpdatedAt (%v) should be later than initial UpdatedAt (%v)", found.UpdatedAt, initialUpdatedAt)
		assert.WithinDuration(t, time.Now(), found.UpdatedAt, 10*time.Second, "User UpdatedAt should be recent after update")
	})

	t.Run("UpdateChessProfileFields", func(t *testing.T) {
		// Create user without profiles
		user := &models.User{
			Email:        RandomEmail(),
			PasswordHash: RandomString(32),
		}
		err := userRepo.Create(ctx, user)
		assert.NoError(t, err)
		assert.NotEmpty(t, user.ID)
		initialRegisteredAt := user.RegisteredAt // Capture initial user times
		initialUpdatedAt := user.UpdatedAt
		assert.False(t, initialRegisteredAt.IsZero(), "Initial User RegisteredAt should not be zero")
		assert.False(t, initialUpdatedAt.IsZero(), "Initial User UpdatedAt should not be zero")

		profile := &models.ChessProfile{Platform: "lichess.org", Username: "fields_before", GamesFetched: 1}
		err = userRepo.AddChessProfile(ctx, user.ID, profile)
		assert.NoError(t, err)
		assert.NotZero(t, profile.ID)

		// Get initial timestamps
		initialProfile, err := userRepo.GetChessProfileByID(ctx, profile.ID)
		assert.NoError(t, err)
		initialProfileCreatedAt := initialProfile.CreatedAt
		initialProfileUpdatedAt := initialProfile.UpdatedAt
		assert.False(t, initialProfileCreatedAt.IsZero(), "Initial Profile CreatedAt should not be zero")
		assert.False(t, initialProfileUpdatedAt.IsZero(), "Initial Profile UpdatedAt should not be zero")

		nowPlayed := time.Now().Add(-5 * time.Minute)
		updates := map[string]interface{}{
			"username":            "fields_after",
			"games_fetched":       20,
			"last_game_played_at": nowPlayed,
		}
		// Wait a bit to ensure UpdatedAt changes noticeably
		time.Sleep(10 * time.Millisecond)

		err = userRepo.UpdateChessProfileFields(ctx, profile.ID, updates)
		assert.NoError(t, err)

		// Verify
		found, err := userRepo.GetChessProfileByID(ctx, profile.ID)
		assert.NoError(t, err)
		assert.Equal(t, "fields_after", found.Username)
		assert.Equal(t, 20, found.GamesFetched)
		assert.Nil(t, found.LastGameFetchedAt)   // Should not have been updated
		assert.NotNil(t, found.LastGamePlayedAt) // Should have been updated
		assert.WithinDuration(t, nowPlayed, *found.LastGamePlayedAt, time.Second)
		// assert.True(t, found.UpdatedAt.After(profile.CreatedAt))
		// Check timestamps after update
		assert.Equal(t, initialProfileCreatedAt, found.CreatedAt, "Profile CreatedAt should not change on field update")
		assert.True(t, found.UpdatedAt.After(initialProfileUpdatedAt), "Profile UpdatedAt (%v) should be later than initial UpdatedAt (%v) after field update", found.UpdatedAt, initialProfileUpdatedAt)
		assert.WithinDuration(t, time.Now(), found.UpdatedAt, 10*time.Second, "Profile UpdatedAt should be recent after field update")
	})

	t.Run("DeleteChessProfile", func(t *testing.T) {
		// Create user without profiles
		user := &models.User{
			Email:        RandomEmail(),
			PasswordHash: RandomString(32),
		}
		err := userRepo.Create(ctx, user)
		assert.NoError(t, err)

		profile1 := &models.ChessProfile{Platform: "chess.com", Username: "delete_test_1"}
		profile2 := &models.ChessProfile{Platform: "lichess.org", Username: "delete_test_2"}
		err = userRepo.AddChessProfile(ctx, user.ID, profile1)
		assert.NoError(t, err)
		err = userRepo.AddChessProfile(ctx, user.ID, profile2)
		assert.NoError(t, err)
		assert.NotZero(t, profile1.ID)
		assert.NotZero(t, profile2.ID)

		// Delete one profile
		err = userRepo.DeleteChessProfile(ctx, user.ID, profile1.ID)
		assert.NoError(t, err)

		// Verify profile1 is gone
		_, err = userRepo.GetChessProfileByID(ctx, profile1.ID)
		assert.Error(t, err)
		assert.ErrorIs(t, err, gorm.ErrRecordNotFound)

		// Verify profile2 still exists
		found2, err := userRepo.GetChessProfileByID(ctx, profile2.ID)
		assert.NoError(t, err)
		assert.NotNil(t, found2)
		assert.Equal(t, profile2.ID, found2.ID)

		// Verify user still has profile2 (by fetching user)
		foundUser, err := userRepo.GetByID(ctx, user.ID)
		assert.NoError(t, err)
		assert.Len(t, foundUser.ChessProfiles, 1)
		assert.Equal(t, profile2.ID, foundUser.ChessProfiles[0].ID)
	})

	t.Run("AddChessProfile duplicate user/platform fails", func(t *testing.T) {
		// Create user without profiles
		user := &models.User{
			Email:        RandomEmail(),
			PasswordHash: RandomString(32),
		}
		err := userRepo.Create(ctx, user)
		assert.NoError(t, err)

		profile1 := &models.ChessProfile{Platform: "chess.com", Username: "duplicate1"}
		err = userRepo.AddChessProfile(ctx, user.ID, profile1)
		assert.NoError(t, err)

		// Try adding another profile with the same user/platform
		profile2 := &models.ChessProfile{Platform: "chess.com", Username: "duplicate2"}
		err = userRepo.AddChessProfile(ctx, user.ID, profile2)
		assert.Error(t, err) // Should fail due to unique constraint idx_user_platform
		// Note: Fake DB might return gorm.ErrDuplicatedKey, Real DB might return a driver-specific error.
		// Consider checking for specific error types if needed, but assert.Error() is often sufficient.
	})
}

// CreateTestUser helper is now in providers.go
