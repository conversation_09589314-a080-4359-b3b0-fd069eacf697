package testing

import (
	"context"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/repository/fake"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestSprintPuzzleRepository(t *testing.T) {
	providers := []struct {
		name     string
		provider TestDBProvider
	}{
		{"PostgreSQL", NewPostgresTestDBProvider()},
		{"SQLite", NewSQLiteTestDBProvider()},
	}

	for _, p := range providers {
		t.Run(p.name, func(t *testing.T) {
			testSprintPuzzleRepository(t, p.provider, p.name)
		})
	}
}

func testSprintPuzzleRepository(t *testing.T, provider TestDBProvider, providerName string) {
	db := provider.GetDB(t)
	defer provider.Cleanup(t)

	var sprintPuzzleRepo repository.ISprintPuzzleRepository
	if providerName == "SQLite" {
		fakeDB := &fake.DB{DB: db}
		sprintPuzzleRepo = fake.NewSprintPuzzleRepository(fakeDB)
	} else {
		sprintPuzzleRepo = repository.NewSprintPuzzleRepository(db)
	}

	ctx := context.Background()

	// Create test data
	user := &models.User{
		ID:    uuid.New().String(),
		Email: "test-" + uuid.New().String() + "@example.com",
	}
	require.NoError(t, db.Create(user).Error)

	sprint := &models.Sprint{
		ID:               uuid.New().String(),
		UserID:           user.ID,
		EloType:          "overall_intuition",
		Status:           models.SprintStatusActive,
		TargetPuzzles:    20,
		TimeLimitSeconds: 600,
		PuzzlesSolved:    0,
		MistakesMade:     0,
		StartedAt:        time.Now(),
		EloRatingBefore:  1200,
	}
	require.NoError(t, db.Create(sprint).Error)

	// Create some test Lichess puzzles
	lichessPuzzles := []models.LichessPuzzle{
		{
			ID:              "puzzle1",
			FEN:             "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
			Moves:           []string{"e2e4", "e7e5"},
			Rating:          1500,
			RatingDeviation: 75,
			Popularity:      90,
			NbPlays:         1000,
			Themes:          []string{"opening"},
			GameURL:         "https://lichess.org/test1",
			OpeningTags:     []string{},
		},
		{
			ID:              "puzzle2",
			FEN:             "rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2",
			Moves:           []string{"d2d4", "exd4"},
			Rating:          1600,
			RatingDeviation: 80,
			Popularity:      85,
			NbPlays:         800,
			Themes:          []string{"tactics"},
			GameURL:         "https://lichess.org/test2",
			OpeningTags:     []string{},
		},
		{
			ID:              "puzzle3",
			FEN:             "rnbqkbnr/pppp1ppp/8/4p3/3PP3/8/PPP2PPP/RNBQKBNR b KQkq - 0 2",
			Moves:           []string{"exd4", "Qxd4"},
			Rating:          1700,
			RatingDeviation: 85,
			Popularity:      80,
			NbPlays:         600,
			Themes:          []string{"endgame"},
			GameURL:         "https://lichess.org/test3",
			OpeningTags:     []string{},
		},
	}

	for _, puzzle := range lichessPuzzles {
		require.NoError(t, db.Create(&puzzle).Error)
	}

	t.Run("Create and GetByID", func(t *testing.T) {
		sprintPuzzle := &models.SprintPuzzle{
			SprintID:         sprint.ID,
			LichessPuzzleID:  lichessPuzzles[0].ID,
			SequenceInSprint: 1,
		}

		// Create sprint puzzle
		err := sprintPuzzleRepo.Create(ctx, sprintPuzzle)
		require.NoError(t, err)
		assert.NotEmpty(t, sprintPuzzle.ID)
		assert.False(t, sprintPuzzle.CreatedAt.IsZero())
		assert.False(t, sprintPuzzle.UpdatedAt.IsZero())

		// Get sprint puzzle by ID
		retrieved, err := sprintPuzzleRepo.GetByID(ctx, sprintPuzzle.ID)
		require.NoError(t, err)
		assert.Equal(t, sprintPuzzle.ID, retrieved.ID)
		assert.Equal(t, sprintPuzzle.SprintID, retrieved.SprintID)
		assert.Equal(t, sprintPuzzle.LichessPuzzleID, retrieved.LichessPuzzleID)
		assert.Equal(t, sprintPuzzle.SequenceInSprint, retrieved.SequenceInSprint)
	})

	t.Run("CreateBatch", func(t *testing.T) {
		sprintPuzzles := []models.SprintPuzzle{
			{
				SprintID:         sprint.ID,
				LichessPuzzleID:  lichessPuzzles[1].ID,
				SequenceInSprint: 2,
			},
			{
				SprintID:         sprint.ID,
				LichessPuzzleID:  lichessPuzzles[2].ID,
				SequenceInSprint: 3,
			},
		}

		// Create batch of sprint puzzles
		err := sprintPuzzleRepo.CreateBatch(ctx, sprintPuzzles)
		require.NoError(t, err)

		// Verify all puzzles were created with IDs and timestamps
		for _, puzzle := range sprintPuzzles {
			assert.NotEmpty(t, puzzle.ID)
			assert.False(t, puzzle.CreatedAt.IsZero())
			assert.False(t, puzzle.UpdatedAt.IsZero())
		}
	})

	t.Run("GetBySprintID", func(t *testing.T) {
		// Get all sprint puzzles for the sprint
		sprintPuzzles, err := sprintPuzzleRepo.GetBySprintID(ctx, sprint.ID)
		require.NoError(t, err)

		// Should have 3 puzzles (1 from Create test + 2 from CreateBatch test)
		assert.Len(t, sprintPuzzles, 3)

		// Verify they are ordered by sequence
		for i, puzzle := range sprintPuzzles {
			assert.Equal(t, i+1, puzzle.SequenceInSprint)
			assert.Equal(t, sprint.ID, puzzle.SprintID)
		}
	})

	t.Run("CountBySprintID", func(t *testing.T) {
		// Test the new count function
		count, err := sprintPuzzleRepo.CountBySprintID(ctx, sprint.ID)
		require.NoError(t, err)

		// Should have 3 puzzles
		assert.Equal(t, 3, count)

		// Test with non-existent sprint
		nonExistentSprintID := uuid.New().String()
		count, err = sprintPuzzleRepo.CountBySprintID(ctx, nonExistentSprintID)
		require.NoError(t, err)
		assert.Equal(t, 0, count)
	})

	t.Run("CountBySprintID_EmptySprint", func(t *testing.T) {
		// Create another sprint with no puzzles
		emptySprint := &models.Sprint{
			ID:               uuid.New().String(),
			UserID:           user.ID,
			EloType:          "overall_intuition",
			Status:           models.SprintStatusActive,
			TargetPuzzles:    20,
			TimeLimitSeconds: 600,
			PuzzlesSolved:    0,
			MistakesMade:     0,
			StartedAt:        time.Now(),
			EloRatingBefore:  1200,
		}
		require.NoError(t, db.Create(emptySprint).Error)

		// Count should be 0 for empty sprint
		count, err := sprintPuzzleRepo.CountBySprintID(ctx, emptySprint.ID)
		require.NoError(t, err)
		assert.Equal(t, 0, count)
	})

	t.Run("CountBySprintID_Performance", func(t *testing.T) {
		// Create a sprint with many puzzles to test performance
		largeSprint := &models.Sprint{
			ID:               uuid.New().String(),
			UserID:           user.ID,
			EloType:          "overall_intuition",
			Status:           models.SprintStatusActive,
			TargetPuzzles:    100,
			TimeLimitSeconds: 600,
			PuzzlesSolved:    0,
			MistakesMade:     0,
			StartedAt:        time.Now(),
			EloRatingBefore:  1200,
		}
		require.NoError(t, db.Create(largeSprint).Error)

		// Create 50 sprint puzzles
		largeBatch := make([]models.SprintPuzzle, 50)
		for i := 0; i < 50; i++ {
			largeBatch[i] = models.SprintPuzzle{
				SprintID:         largeSprint.ID,
				LichessPuzzleID:  lichessPuzzles[i%3].ID, // Cycle through available puzzles
				SequenceInSprint: i + 1,
			}
		}

		err := sprintPuzzleRepo.CreateBatch(ctx, largeBatch)
		require.NoError(t, err)

		// Test count performance - should be fast even with many records
		start := time.Now()
		count, err := sprintPuzzleRepo.CountBySprintID(ctx, largeSprint.ID)
		duration := time.Since(start)

		require.NoError(t, err)
		assert.Equal(t, 50, count)

		// Count should be very fast (under 100ms even on slow systems)
		assert.Less(t, duration, 100*time.Millisecond, "Count operation should be fast")

		// Compare with GetBySprintID performance
		start = time.Now()
		puzzles, err := sprintPuzzleRepo.GetBySprintID(ctx, largeSprint.ID)
		getDuration := time.Since(start)

		require.NoError(t, err)
		assert.Len(t, puzzles, 50)

		// Count should be significantly faster than fetching all records
		t.Logf("Count duration: %v, GetBySprintID duration: %v", duration, getDuration)
		// Note: We don't assert this in tests as it depends on system performance,
		// but in practice COUNT(*) should be much faster than SELECT *
	})
}
