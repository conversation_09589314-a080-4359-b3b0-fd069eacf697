package testing

import (
	"context"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/google/uuid"
	"github.com/lib/pq"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestCascadeDeletion tests that deleting a chess profile correctly deletes associated games and puzzles
func TestCascadeDeletion(t *testing.T) {
	// Test with SQLite by default for faster tests
	provider := GetTestDBProvider(FakeRepository)
	defer provider.Cleanup(t)

	RunCascadeDeletionTests(t, provider)
}

// TestRealCascadeDeletion runs tests against the real PostgreSQL repository implementation
func TestRealCascadeDeletion(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping real database tests")
	}
	provider := GetTestDBProvider(RealRepository)
	defer provider.Cleanup(t)

	RunCascadeDeletionTests(t, provider)
}

// RunCascadeDeletionTests runs all cascade deletion tests
func RunCascadeDeletionTests(t *testing.T, provider TestDBProvider) {
	userRepo := provider.GetUserRepository(t)
	gameRepo := provider.GetGameRepository(t)
	puzzleRepo := provider.GetPuzzleRepository(t)
	ctx := context.Background()

	t.Run("DeleteChessProfile_CascadeDeletesGames", func(t *testing.T) {
		// Create a user with two chess profiles
		user := CreateTestUser(t, userRepo)

		// Create two chess profiles for the user
		profileToDelete := &models.ChessProfile{
			Platform: models.ChessDotCom,
			Username: "profile_to_delete",
		}
		err := userRepo.AddChessProfile(ctx, user.ID, profileToDelete)
		require.NoError(t, err)

		profileToKeep := &models.ChessProfile{
			Platform: models.LichessOrg,
			Username: "profile_to_keep",
		}
		err = userRepo.AddChessProfile(ctx, user.ID, profileToKeep)
		require.NoError(t, err)

		// Create games for both profiles
		// Games for the profile to delete
		game1 := &models.Game{
			UserID:        user.ID,
			Platform:      profileToDelete.Platform,
			ChessUsername: profileToDelete.Username,
			UserColor:     models.White,
			GameTime:      time.Now(),
			CompressedPGN: []byte("compressed_pgn_1"),
			TimeControl:   "5+0",
			Rated:         true,
			WhitePlayer:   `{"username":"profile_to_delete","rating":1500,"is_ai":false}`,
			BlackPlayer:   `{"username":"opponent1","rating":1600,"is_ai":false}`,
			Winner:        models.Winner("white"),
			Result:        models.Mate,
		}
		err = gameRepo.Create(ctx, game1)
		require.NoError(t, err)

		game2 := &models.Game{
			UserID:        user.ID,
			Platform:      profileToDelete.Platform,
			ChessUsername: profileToDelete.Username,
			UserColor:     models.Black,
			GameTime:      time.Now().Add(-24 * time.Hour),
			CompressedPGN: []byte("compressed_pgn_2"),
			TimeControl:   "10+0",
			Rated:         false,
			WhitePlayer:   `{"username":"opponent2","rating":1700,"is_ai":false}`,
			BlackPlayer:   `{"username":"profile_to_delete","rating":1500,"is_ai":false}`,
			Winner:        models.Winner("black"),
			Result:        models.Resign,
		}
		err = gameRepo.Create(ctx, game2)
		require.NoError(t, err)

		// Games for the profile to keep
		game3 := &models.Game{
			UserID:        user.ID,
			Platform:      profileToKeep.Platform,
			ChessUsername: profileToKeep.Username,
			UserColor:     models.White,
			GameTime:      time.Now(),
			CompressedPGN: []byte("compressed_pgn_3"),
			TimeControl:   "5+0",
			Rated:         true,
			WhitePlayer:   `{"username":"profile_to_keep","rating":1500,"is_ai":false}`,
			BlackPlayer:   `{"username":"opponent3","rating":1600,"is_ai":false}`,
			Winner:        models.Winner("none"),
			Result:        models.Draw,
		}
		err = gameRepo.Create(ctx, game3)
		require.NoError(t, err)

		// Create puzzles for the games
		puzzle1 := &models.Puzzle{
			GameID:      game1.ID,
			UserID:      user.ID,
			GameMove:    10,
			FEN:         "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
			Moves:       pq.StringArray{"e2e4", "e7e5"},
			PrevCP:      100,
			CP:          300,
			Theme:       models.OpponentBlunderCaught,
			UserColor:   models.White,
			PuzzleColor: models.White,
			Zugzwang:    false,
			Tags:        pq.StringArray{"opening", "tactics"},
		}
		err = puzzleRepo.Create(ctx, puzzle1)
		require.NoError(t, err)

		puzzle2 := &models.Puzzle{
			GameID:      game2.ID,
			UserID:      user.ID,
			GameMove:    15,
			FEN:         "rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq - 0 1",
			Moves:       pq.StringArray{"e7e5", "g1f3"},
			PrevCP:      -50,
			CP:          50,
			Theme:       models.OpponentMistakeCaught,
			UserColor:   models.Black,
			PuzzleColor: models.White,
			Zugzwang:    false,
			Tags:        pq.StringArray{"middlegame", "tactics"},
		}
		err = puzzleRepo.Create(ctx, puzzle2)
		require.NoError(t, err)

		puzzle3 := &models.Puzzle{
			GameID:      game3.ID,
			UserID:      user.ID,
			GameMove:    20,
			FEN:         "rnbqkbnr/pppppppp/8/8/4P3/5N2/PPPP1PPP/RNBQKB1R b KQkq - 0 2",
			Moves:       pq.StringArray{"b8c6", "f1b5"},
			PrevCP:      0,
			CP:          200,
			Theme:       models.OpponentMistakeCaught,
			UserColor:   models.White,
			PuzzleColor: models.Black,
			Zugzwang:    false,
			Tags:        pq.StringArray{"endgame", "tactics"},
		}
		err = puzzleRepo.Create(ctx, puzzle3)
		require.NoError(t, err)

		// Verify initial state
		games, totalGames, err := gameRepo.ListByUserID(ctx, user.ID, repository.GameFilter{}, 0, 10)
		require.NoError(t, err)
		assert.Equal(t, int64(3), totalGames)
		assert.Len(t, games, 3)

		puzzles, totalPuzzles, err := puzzleRepo.ListByUserID(ctx, user.ID, repository.PuzzleFilter{}, 0, 10)
		require.NoError(t, err)
		assert.Equal(t, int64(3), totalPuzzles)
		assert.Len(t, puzzles, 3)

		// Delete the first chess profile
		err = userRepo.DeleteChessProfile(ctx, user.ID, profileToDelete.ID)
		require.NoError(t, err)

		// Verify the profile is deleted
		_, err = userRepo.GetChessProfileByID(ctx, profileToDelete.ID)
		assert.Error(t, err)

		// Delete games associated with the profile
		filter := repository.GameFilter{
			Platform:      &profileToDelete.Platform,
			ChessUsername: &profileToDelete.Username,
		}
		deletedCount, err := gameRepo.DeleteByFilter(ctx, user.ID, filter)
		require.NoError(t, err)
		assert.Equal(t, int64(2), deletedCount, "Should have deleted 2 games")

		// Verify games from the deleted profile are gone
		games, totalGames, err = gameRepo.ListByUserID(ctx, user.ID, repository.GameFilter{}, 0, 10)
		require.NoError(t, err)
		assert.Equal(t, int64(1), totalGames)
		assert.Len(t, games, 1)
		assert.Equal(t, game3.ID, games[0].ID)

		// Verify puzzles from the deleted games are gone (cascade deletion)
		puzzles, totalPuzzles, err = puzzleRepo.ListByUserID(ctx, user.ID, repository.PuzzleFilter{}, 0, 10)
		require.NoError(t, err)
		assert.Equal(t, int64(1), totalPuzzles)
		assert.Len(t, puzzles, 1)
		assert.Equal(t, puzzle3.ID, puzzles[0].ID)

		// Verify the other profile still exists
		foundProfile, err := userRepo.GetChessProfileByID(ctx, profileToKeep.ID)
		assert.NoError(t, err)
		assert.Equal(t, profileToKeep.ID, foundProfile.ID)
	})

	t.Run("DeletePuzzle_CascadeDeletesUserPuzzleStats", func(t *testing.T) {
		// Create a test user
		user := CreateTestUser(t, userRepo)

		// Create a test game and puzzle
		game := CreateTestGame(t, gameRepo, user.ID)
		puzzle := CreateTestPuzzle(t, puzzleRepo, game.ID, user.ID)

		// Create UserPuzzleStats directly in the database to test cascade deletion
		// This simulates what would happen when stats are created via triggers
		puzzleStats := &models.UserPuzzleStats{
			ID:                 uuid.New().String(),
			UserID:             user.ID,
			PuzzleID:           puzzle.ID,
			Attempts:           1,
			SuccessCount:       1,
			TotalTime:          45,
			AverageTime:        45.0,
			LastAttemptTime:    time.Now(),
			LastAttemptSuccess: true,
		}
		err := provider.GetDB(t).Create(puzzleStats).Error
		require.NoError(t, err)

		// Verify UserPuzzleStats was created
		var foundStats []models.UserPuzzleStats
		err = provider.GetDB(t).Where("user_id = ? AND puzzle_id = ?", user.ID, puzzle.ID).Find(&foundStats).Error
		require.NoError(t, err)
		require.Len(t, foundStats, 1, "Should have one puzzle stats record")

		// Delete the puzzle
		err = puzzleRepo.Delete(ctx, puzzle.ID)
		require.NoError(t, err)

		// Verify the puzzle is deleted
		_, err = puzzleRepo.GetByID(ctx, puzzle.ID)
		assert.Error(t, err, "Puzzle should be deleted")

		// Verify UserPuzzleStats was cascade deleted
		err = provider.GetDB(t).Where("user_id = ? AND puzzle_id = ?", user.ID, puzzle.ID).Find(&foundStats).Error
		require.NoError(t, err)
		assert.Len(t, foundStats, 0, "UserPuzzleStats should be cascade deleted")
	})
}
