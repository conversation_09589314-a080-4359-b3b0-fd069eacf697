package testing

import (
	"context"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/stretchr/testify/assert"
)

// TestGameRepository runs all tests for both real and fake game repositories
func TestGameRepository(t *testing.T) {
	// Test with SQLite by default for faster tests
	provider := GetTestDBProvider(FakeRepository)
	defer provider.Cleanup(t)

	RunGameRepositoryTests(t, provider)
}

// TestRealGameRepository runs tests against the real PostgreSQL repository implementation
// This will be skipped if the test database is not available
func TestRealGameRepository(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping real database tests")
	}
	provider := GetTestDBProvider(RealRepository)
	defer provider.Cleanup(t)

	RunGameRepositoryTests(t, provider)
}

// RunGameRepositoryTests runs all tests for a game repository
func RunGameRepositoryTests(t *testing.T, provider TestDBProvider) {
	userRepo := provider.GetUserRepository(t)
	gameRepo := provider.GetGameRepository(t)

	t.Run("Create and GetByID", func(t *testing.T) {
		user := CreateTestUser(t, userRepo)
		game := CreateTestGame(t, gameRepo, user.ID)
		found, err := gameRepo.GetByID(context.Background(), game.ID)
		assert.NoError(t, err)
		assert.Equal(t, game.ID, found.ID)
		assert.Equal(t, game.UserID, found.UserID)

		// Check timestamps
		assert.False(t, found.CreatedAt.IsZero(), "CreatedAt should not be zero")
		assert.False(t, found.UpdatedAt.IsZero(), "UpdatedAt should not be zero")
		assert.WithinDuration(t, found.CreatedAt, found.UpdatedAt, 5*time.Second, "UpdatedAt should be close to CreatedAt initially")
		assert.WithinDuration(t, time.Now(), found.CreatedAt, 10*time.Second, "CreatedAt should be recent")
	})

	t.Run("Update", func(t *testing.T) {
		user := CreateTestUser(t, userRepo)
		game := CreateTestGame(t, gameRepo, user.ID)

		// Get initial timestamps
		initialGame, err := gameRepo.GetByID(context.Background(), game.ID)
		assert.NoError(t, err)
		initialCreatedAt := initialGame.CreatedAt
		initialUpdatedAt := initialGame.UpdatedAt
		assert.False(t, initialCreatedAt.IsZero(), "Initial CreatedAt should not be zero")
		assert.False(t, initialUpdatedAt.IsZero(), "Initial UpdatedAt should not be zero")

		// Wait a bit to ensure UpdatedAt changes noticeably
		time.Sleep(10 * time.Millisecond)

		// Update the game with a new time control
		newTimeControl := "10+0"
		game.TimeControl = newTimeControl
		err = gameRepo.Update(context.Background(), game)
		assert.NoError(t, err)

		// Verify the update and timestamps
		found, err := gameRepo.GetByID(context.Background(), game.ID)
		assert.NoError(t, err)
		assert.Equal(t, newTimeControl, found.TimeControl)

		// Check timestamps after update
		assert.Equal(t, initialCreatedAt, found.CreatedAt, "CreatedAt should not change on update")
		assert.True(t, found.UpdatedAt.After(initialUpdatedAt), "UpdatedAt (%v) should be later than initial UpdatedAt (%v)", found.UpdatedAt, initialUpdatedAt)
		assert.WithinDuration(t, time.Now(), found.UpdatedAt, 10*time.Second, "UpdatedAt should be recent after update")
	})

	t.Run("Delete", func(t *testing.T) {
		user := CreateTestUser(t, userRepo)
		game := CreateTestGame(t, gameRepo, user.ID)

		// Delete the game
		err := gameRepo.Delete(context.Background(), game.ID)
		assert.NoError(t, err)

		// Verify the deletion
		_, err = gameRepo.GetByID(context.Background(), game.ID)
		assert.Error(t, err)
	})

	t.Run("GetByIDWithoutPGN", func(t *testing.T) {
		user := CreateTestUser(t, userRepo)
		game := CreateTestGame(t, gameRepo, user.ID)

		// Get the game with PGN
		gameWithPGN, err := gameRepo.GetByID(context.Background(), game.ID)
		assert.NoError(t, err)
		assert.NotNil(t, gameWithPGN.CompressedPGN)
		assert.NotEmpty(t, gameWithPGN.CompressedPGN)

		// Get the game without PGN
		gameWithoutPGN, err := gameRepo.GetByIDWithoutPGN(context.Background(), game.ID)
		assert.NoError(t, err)
		assert.Nil(t, gameWithoutPGN.CompressedPGN)
	})

	t.Run("ListByUserID", func(t *testing.T) {
		user := CreateTestUser(t, userRepo)
		now := time.Now().UTC().Truncate(time.Microsecond) // Ensure consistent precision
		past := now.Add(-2 * time.Hour)
		future := now.Add(2 * time.Hour)

		// Create games with varying properties
		game1 := CreateTestGame(t, gameRepo, user.ID) // Defaults
		game1.Platform = models.LichessOrg
		game1.ChessUsername = "user1"
		game1.GameTime = past
		err := gameRepo.Update(context.Background(), game1)
		assert.NoError(t, err)

		game2 := CreateTestGame(t, gameRepo, user.ID)
		game2.Platform = models.ChessDotCom
		game2.ChessUsername = "user1"
		game2.GameTime = now
		err = gameRepo.Update(context.Background(), game2)
		assert.NoError(t, err)

		game3 := CreateTestGame(t, gameRepo, user.ID)
		game3.Platform = models.LichessOrg
		game3.ChessUsername = "user2"
		game3.GameTime = future
		err = gameRepo.Update(context.Background(), game3)
		assert.NoError(t, err)

		// Test case 1: No filters, get all games for the user, default pagination
		games, total, err := gameRepo.ListByUserID(context.Background(), user.ID, repository.GameFilter{}, 0, 10)
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, total, int64(3)) // Might be other games from previous tests
		assert.GreaterOrEqual(t, len(games), 3)
		// Check if our created games are present (order might vary)
		foundIDs := make(map[string]bool)
		for _, g := range games {
			foundIDs[g.ID] = true
		}
		assert.True(t, foundIDs[game1.ID])
		assert.True(t, foundIDs[game2.ID])
		assert.True(t, foundIDs[game3.ID])

		// Test case 2: Filter by platform
		platformLichess := models.LichessOrg
		games, total, err = gameRepo.ListByUserID(context.Background(), user.ID, repository.GameFilter{Platform: &platformLichess}, 0, 10)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), total) // Only game1 and game3
		assert.Len(t, games, 2)
		foundIDs = make(map[string]bool)
		for _, g := range games {
			foundIDs[g.ID] = true
		}
		assert.True(t, foundIDs[game1.ID])
		assert.True(t, foundIDs[game3.ID])

		// Test case 3: Filter by username
		username := "user1"
		games, total, err = gameRepo.ListByUserID(context.Background(), user.ID, repository.GameFilter{ChessUsername: &username}, 0, 10)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), total) // Only game1 and game2
		assert.Len(t, games, 2)
		foundIDs = make(map[string]bool)
		for _, g := range games {
			foundIDs[g.ID] = true
		}
		assert.True(t, foundIDs[game1.ID])
		assert.True(t, foundIDs[game2.ID])

		// Test case 4: Filter by start time
		startTime := now.Add(-1 * time.Hour) // Should include game2 and game3
		games, total, err = gameRepo.ListByUserID(context.Background(), user.ID, repository.GameFilter{StartTime: &startTime}, 0, 10)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), total)
		assert.Len(t, games, 2)
		foundIDs = make(map[string]bool)
		for _, g := range games {
			foundIDs[g.ID] = true
		}
		assert.True(t, foundIDs[game2.ID])
		assert.True(t, foundIDs[game3.ID])

		// Test case 5: Filter by end time
		endTime := now.Add(1 * time.Hour) // Should include game1 and game2
		games, total, err = gameRepo.ListByUserID(context.Background(), user.ID, repository.GameFilter{EndTime: &endTime}, 0, 10)
		assert.NoError(t, err)
		assert.Equal(t, int64(2), total)
		assert.Len(t, games, 2)
		foundIDs = make(map[string]bool)
		for _, g := range games {
			foundIDs[g.ID] = true
		}
		assert.True(t, foundIDs[game1.ID])
		assert.True(t, foundIDs[game2.ID])

		// Test case 6: Filter by platform and time range
		startTimeOnlyFuture := now.Add(1 * time.Hour)
		filter := repository.GameFilter{
			Platform:  &platformLichess,
			StartTime: &startTimeOnlyFuture,
		}
		games, total, err = gameRepo.ListByUserID(context.Background(), user.ID, filter, 0, 10)
		assert.NoError(t, err)
		assert.Equal(t, int64(1), total) // Only game3
		assert.Len(t, games, 1)
		assert.Equal(t, game3.ID, games[0].ID)

		// Test case 7: Pagination (Limit)
		games, total, err = gameRepo.ListByUserID(context.Background(), user.ID, repository.GameFilter{}, 0, 1)
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, total, int64(3))
		assert.Len(t, games, 1)
		// Game order is DESC by game_time, so game3 should be first
		assert.Equal(t, game3.ID, games[0].ID)

		// Test case 8: Pagination (Offset and Limit)
		games, total, err = gameRepo.ListByUserID(context.Background(), user.ID, repository.GameFilter{}, 1, 1)
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, total, int64(3))
		assert.Len(t, games, 1)
		// Second game should be game2 (now)
		assert.Equal(t, game2.ID, games[0].ID)

		// Test case 9: Filter by user color
		whiteColor := models.White
		filter = repository.GameFilter{
			UserColor: &whiteColor,
		}
		games, total, err = gameRepo.ListByUserID(context.Background(), user.ID, filter, 0, 10)
		assert.NoError(t, err)
		assert.Equal(t, int64(3), total) // All games have white color
		assert.Len(t, games, 3)

		// Test case 10: Filter by user color (black)
		blackColor := models.Black
		// Update game1 to have black color
		game1.UserColor = models.Black
		err = gameRepo.Update(context.Background(), game1)
		assert.NoError(t, err)

		filter = repository.GameFilter{
			UserColor: &blackColor,
		}
		games, total, err = gameRepo.ListByUserID(context.Background(), user.ID, filter, 0, 10)
		assert.NoError(t, err)
		assert.Equal(t, int64(1), total) // Only game1 has black color
		assert.Len(t, games, 1)
		assert.Equal(t, game1.ID, games[0].ID)

		// Test case 11: Combine user color with other filters
		filter = repository.GameFilter{
			Platform:  &platformLichess,
			UserColor: &blackColor,
		}
		games, total, err = gameRepo.ListByUserID(context.Background(), user.ID, filter, 0, 10)
		assert.NoError(t, err)
		assert.Equal(t, int64(1), total) // Only game1 matches both criteria
		assert.Len(t, games, 1)
		assert.Equal(t, game1.ID, games[0].ID)

		// Test case 12: No results found
		platformChessCom := models.ChessDotCom
		filter = repository.GameFilter{
			Platform:  &platformChessCom,
			StartTime: &startTimeOnlyFuture,
		}
		games, total, err = gameRepo.ListByUserID(context.Background(), user.ID, filter, 0, 10)
		assert.NoError(t, err)
		assert.Equal(t, int64(0), total)
		assert.Len(t, games, 0)

		// Test case 13: OmitPGN - get games without PGN
		games, total, err = gameRepo.ListByUserID(context.Background(), user.ID, repository.GameFilter{OmitPGN: true}, 0, 10)
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, total, int64(3))
		assert.GreaterOrEqual(t, len(games), 3)
		// Check that PGN is not loaded
		for _, g := range games {
			assert.Nil(t, g.CompressedPGN, "CompressedPGN should be nil when OmitPGN is true")
		}

		// Test case 14: With PGN - get games with PGN
		games, total, err = gameRepo.ListByUserID(context.Background(), user.ID, repository.GameFilter{OmitPGN: false}, 0, 10)
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, total, int64(3))
		assert.GreaterOrEqual(t, len(games), 3)
		// Check that PGN is loaded
		for _, g := range games {
			assert.NotNil(t, g.CompressedPGN, "CompressedPGN should not be nil when OmitPGN is false")
			assert.NotEmpty(t, g.CompressedPGN, "CompressedPGN should not be empty when OmitPGN is false")
		}
	})
}
