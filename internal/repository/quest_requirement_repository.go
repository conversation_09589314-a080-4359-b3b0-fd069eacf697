package repository

import (
	"context"

	"github.com/chessticize/chessticize-server/internal/models"
	"gorm.io/gorm"
)

// QuestRequirementRepository provides CRUD operations for quest requirements
type QuestRequirementRepository struct {
	db *gorm.DB
}

// NewQuestRequirementRepository creates a new quest requirement repository instance
func NewQuestRequirementRepository(db *gorm.DB) *QuestRequirementRepository {
	return &QuestRequirementRepository{db: db}
}

// CreateQuestRequirement creates a new quest requirement
func (r *QuestRequirementRepository) CreateQuestRequirement(ctx context.Context, req *models.DailyQuestRequirement) error {
	return r.db.WithContext(ctx).Create(req).Error
}

// GetQuestRequirementByID retrieves a quest requirement by ID
func (r *QuestRequirementRepository) GetQuestRequirementByID(ctx context.Context, id string) (*models.DailyQuestRequirement, error) {
	var req models.DailyQuestRequirement
	err := r.db.WithContext(ctx).First(&req, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &req, nil
}

// GetActiveQuestRequirements retrieves all active quest requirements
func (r *QuestRequirementRepository) GetActiveQuestRequirements(ctx context.Context) ([]*models.DailyQuestRequirement, error) {
	var requirements []*models.DailyQuestRequirement
	err := r.db.WithContext(ctx).Where("is_active = ?", true).Find(&requirements).Error
	if err != nil {
		return nil, err
	}
	return requirements, nil
}

// UpdateQuestRequirement updates an existing quest requirement
func (r *QuestRequirementRepository) UpdateQuestRequirement(ctx context.Context, req *models.DailyQuestRequirement) error {
	return r.db.WithContext(ctx).Save(req).Error
}

// DeleteQuestRequirement deletes a quest requirement by ID
func (r *QuestRequirementRepository) DeleteQuestRequirement(ctx context.Context, id string) error {
	return r.db.WithContext(ctx).Delete(&models.DailyQuestRequirement{}, "id = ?", id).Error
}

// ListQuestRequirements retrieves all quest requirements
func (r *QuestRequirementRepository) ListQuestRequirements(ctx context.Context) ([]*models.DailyQuestRequirement, error) {
	var requirements []*models.DailyQuestRequirement
	err := r.db.WithContext(ctx).Find(&requirements).Error
	if err != nil {
		return nil, err
	}
	return requirements, nil
}
