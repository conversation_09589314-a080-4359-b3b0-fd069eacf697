package repository

import (
	"context"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type EloHistoryRepository struct {
	db *gorm.DB
}

func NewEloHistoryRepository(db *gorm.DB) IEloHistoryRepository {
	return &EloHistoryRepository{db: db}
}

// Create creates a new elo history record
func (r *EloHistoryRepository) Create(ctx context.Context, eloHistory *models.EloHistory) error {
	if eloHistory.ID == "" {
		eloHistory.ID = uuid.New().String()
	}
	if eloHistory.CreatedAt.IsZero() {
		eloHistory.CreatedAt = time.Now()
	}
	return r.db.WithContext(ctx).Create(eloHistory).Error
}

// ListByUserID retrieves a paginated list of elo history records for a specific user
func (r *EloHistoryRepository) ListByUserID(ctx context.Context, userID string, eloType *string, offset int, limit int) ([]models.EloHistory, int64, error) {
	query := r.db.WithContext(ctx).Where("user_id = ?", userID)

	// Apply elo type filter if provided
	if eloType != nil {
		query = query.Where("elo_type = ?", *eloType)
	}

	// Get total count
	var totalCount int64
	if err := query.Model(&models.EloHistory{}).Count(&totalCount).Error; err != nil {
		return nil, 0, err
	}

	// Get elo history records with pagination
	var eloHistories []models.EloHistory
	err := query.Order("created_at DESC").Offset(offset).Limit(limit).Find(&eloHistories).Error
	if err != nil {
		return nil, 0, err
	}

	return eloHistories, totalCount, nil
}
