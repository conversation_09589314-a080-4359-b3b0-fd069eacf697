package fake

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestUserPuzzleArrowDuelStatsRepository(t *testing.T) {
	db := NewDB(t)
	repo := NewUserPuzzleArrowDuelStatsRepository(db.DB)
	ctx := context.Background()

	// Test data
	userID := "test-user-123"
	puzzleID := "test-puzzle-456"

	t.Run("GetByUserID_EmptyResult", func(t *testing.T) {
		stats, err := repo.GetByUserID(ctx, userID)
		require.NoError(t, err)
		assert.Empty(t, stats)
	})

	t.Run("GetByUserIDAndPuzzleID_NotFound", func(t *testing.T) {
		// Test getting stats for non-existent puzzle
		stats, err := repo.GetByUserIDAndPuzzleID(ctx, userID, puzzleID)
		assert.Error(t, err) // Should return error when not found
		assert.Nil(t, stats)
	})

	t.Run("GetDislikedPuzzleIDs_EmptyResult", func(t *testing.T) {
		// Test getting disliked puzzle IDs when none exist
		puzzleIDs, err := repo.GetDislikedPuzzleIDs(ctx, userID)
		require.NoError(t, err)
		assert.Empty(t, puzzleIDs)
	})

	// Note: The fake repository calculates stats from events, so we can't test Create methods
	// These tests focus on the available interface methods
}

func TestUserLichessPuzzleArrowDuelStatsRepository(t *testing.T) {
	db := NewDB(t)
	repo := NewUserLichessPuzzleArrowDuelStatsRepository(db.DB)
	ctx := context.Background()

	// Test data
	userID := "test-user-123"
	lichessPuzzleID := "00008"

	t.Run("GetByUserIDAndPuzzleID_NotFound", func(t *testing.T) {
		// Test getting stats for non-existent puzzle
		stats, err := repo.GetByUserIDAndPuzzleID(ctx, userID, lichessPuzzleID)
		assert.Error(t, err) // Should return error when not found
		assert.Nil(t, stats)
	})

	t.Run("GetDislikedPuzzleIDs_EmptyResult", func(t *testing.T) {
		// Test getting disliked puzzle IDs when none exist
		puzzleIDs, err := repo.GetDislikedPuzzleIDs(ctx, userID)
		require.NoError(t, err)
		assert.Empty(t, puzzleIDs)
	})

	// Note: The fake repository calculates stats from events, so we can't test Create methods
	// These tests focus on the available interface methods
}
