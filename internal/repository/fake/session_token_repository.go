package fake

import (
	"context"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"gorm.io/gorm"
)

// FakeSessionTokenRepository is a fake implementation of ISessionTokenRepository using SQLite
type FakeSessionTokenRepository struct {
	db *gorm.DB
}

// NewFakeSessionTokenRepository creates a new fake session token repository
func NewFakeSessionTokenRepository(db *gorm.DB) repository.ISessionTokenRepository {
	return &FakeSessionTokenRepository{db: db}
}

// Create creates a new session token
func (r *FakeSessionTokenRepository) Create(ctx context.Context, token *models.SessionToken) error {
	// Use the real repository implementation since SessionToken model works with SQLite
	realRepo := repository.NewSessionTokenRepository(r.db)
	return realRepo.Create(ctx, token)
}

// GetByToken retrieves a session token by its token string
func (r *FakeSessionTokenRepository) GetByToken(ctx context.Context, token string) (*models.SessionToken, error) {
	realRepo := repository.NewSessionTokenRepository(r.db)
	return realRepo.GetByToken(ctx, token)
}

// GetByID retrieves a session token by its ID
func (r *FakeSessionTokenRepository) GetByID(ctx context.Context, id string) (*models.SessionToken, error) {
	realRepo := repository.NewSessionTokenRepository(r.db)
	return realRepo.GetByID(ctx, id)
}

// ListByUserID retrieves all session tokens for a specific user
func (r *FakeSessionTokenRepository) ListByUserID(ctx context.Context, userID string) ([]models.SessionToken, error) {
	realRepo := repository.NewSessionTokenRepository(r.db)
	return realRepo.ListByUserID(ctx, userID)
}

// Update updates a session token
func (r *FakeSessionTokenRepository) Update(ctx context.Context, token *models.SessionToken) error {
	realRepo := repository.NewSessionTokenRepository(r.db)
	return realRepo.Update(ctx, token)
}

// Delete deletes a session token by ID
func (r *FakeSessionTokenRepository) Delete(ctx context.Context, id string) error {
	realRepo := repository.NewSessionTokenRepository(r.db)
	return realRepo.Delete(ctx, id)
}

// DeleteByUserID deletes all session tokens for a specific user
func (r *FakeSessionTokenRepository) DeleteByUserID(ctx context.Context, userID string) error {
	realRepo := repository.NewSessionTokenRepository(r.db)
	return realRepo.DeleteByUserID(ctx, userID)
}

// DeleteExpired removes session tokens that have expired
func (r *FakeSessionTokenRepository) DeleteExpired(ctx context.Context) (int64, error) {
	realRepo := repository.NewSessionTokenRepository(r.db)
	return realRepo.DeleteExpired(ctx)
}
