package fake

import (
	"context"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/repository/common"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// SprintRepository is a fake implementation of the repository.ISprintRepository interface
type SprintRepository struct {
	db *gorm.DB
}

// NewSprintRepository creates a new fake sprint repository
func NewSprintRepository(db *DB) repository.ISprintRepository {
	return &SprintRepository{
		db: db.DB,
	}
}

// Create creates a new sprint
func (r *SprintRepository) Create(ctx context.Context, sprint *models.Sprint) error {
	if sprint.ID == "" {
		sprint.ID = uuid.New().String()
	}
	now := time.Now()
	sprint.CreatedAt = now
	sprint.UpdatedAt = now
	return r.db.WithContext(ctx).Create(sprint).Error
}

// GetByID retrieves a sprint by ID
func (r *SprintRepository) GetByID(ctx context.Context, id string) (*models.Sprint, error) {
	var sprint models.Sprint
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&sprint).Error
	if err != nil {
		return nil, err
	}
	return &sprint, nil
}

// Update updates an existing sprint
func (r *SprintRepository) Update(ctx context.Context, sprint *models.Sprint) error {
	sprint.UpdatedAt = time.Now()
	return r.db.WithContext(ctx).Save(sprint).Error
}

// ListByUserID retrieves a paginated list of sprints for a specific user
func (r *SprintRepository) ListByUserID(ctx context.Context, userID string, filter repository.SprintFilter, offset int, limit int) ([]models.Sprint, int64, error) {
	query := r.db.WithContext(ctx).Where("user_id = ?", userID)

	// Apply filters
	if filter.StartTime != nil {
		query = query.Where("started_at >= ?", *filter.StartTime)
	}
	if filter.EndTime != nil {
		query = query.Where("started_at <= ?", *filter.EndTime)
	}
	if len(filter.Status) > 0 {
		query = query.Where("status IN ?", filter.Status)
	}
	if filter.EloType != nil {
		query = query.Where("elo_type = ?", *filter.EloType)
	}

	// Get total count
	var totalCount int64
	if err := query.Model(&models.Sprint{}).Count(&totalCount).Error; err != nil {
		return nil, 0, err
	}

	// Get sprints with pagination
	var sprints []models.Sprint
	err := query.Order("started_at DESC").Offset(offset).Limit(limit).Find(&sprints).Error
	if err != nil {
		return nil, 0, err
	}

	return sprints, totalCount, nil
}

// GetSprintStats retrieves aggregated statistics for sprints
func (r *SprintRepository) GetSprintStats(ctx context.Context, userID string, filter repository.SprintFilter, offset int, limit int, grouping *common.TimeGrouping) ([]*models.SprintStats, error) {
	// For now, return a simple implementation - this will be enhanced later
	// when we implement the full stats functionality
	return []*models.SprintStats{}, nil
}

// MarkAbandonedSprints marks sprints as abandoned if they exceed the timeout
func (r *SprintRepository) MarkAbandonedSprints(ctx context.Context, cutoffTime time.Time, limit int) (int64, error) {
	// SQLite doesn't support INTERVAL syntax, so we need to use a different approach
	// Calculate the cutoff timestamp by subtracting time_limit_seconds from cutoffTime

	result := r.db.WithContext(ctx).
		Model(&models.Sprint{}).
		Where("status = ?", models.SprintStatusActive).
		Where("datetime(started_at, '+' || time_limit_seconds || ' seconds') < ?", cutoffTime).
		Limit(limit).
		Update("status", models.SprintStatusAbandoned)

	if result.Error != nil {
		return 0, result.Error
	}

	return result.RowsAffected, nil
}
