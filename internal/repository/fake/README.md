# Fake Repository Package

## Package Overview
This package provides in-memory SQLite implementations of repository interfaces for unit and integration tests. These fake repositories allow tests to be run without requiring a connection to the actual production database, making tests faster and more isolated.

## Features
- Fast testing with in-memory SQLite database
- Matches the real repository interfaces exactly
- Special handling for SQLite limitations, like array types for puzzle tags
- Reuses actual repository code logic where possible
- Uses common utilities from the `internal/repository/common` package for shared functionality

## Usage
```go
import (
	"testing"

	"github.com/chessticize/chessticize-server/internal/repository/fake"
)

func TestYourFunction(t *testing.T) {
	// Create an in-memory database for testing
	db := fake.NewDB(t)

	// Create repositories
	userRepo := fake.NewUserRepository(db)
	gameRepo := fake.NewGameRepository(db)
	puzzleRepo := fake.NewPuzzleRepository(db)

	// Use these repositories in your tests
	// ...
}
```

## Testing
The repository implementations are tested in the `internal/repository/testing` package, which provides a common test suite for both real and fake repositories. The test suite ensures that fake repositories behave identically to real ones.

To run the tests:
```
go test github.com/chessticize/chessticize-server/internal/repository/testing
```

## Benefits
- **Speed**: In-memory SQLite is much faster than a real database
- **Isolation**: Tests don't affect any external systems
- **No External Dependencies**: Tests can run without database connectivity
- **Simplified Setup**: No need to set up and tear down database state
- **Consistent Behavior**: Fake repositories behave just like real repositories

## Implementation Notes
- SQLite doesn't natively support array types like PostgreSQL, so special handling is implemented for storing array-type fields like puzzle tags
- The package handles conversion between PostgreSQL-specific types and SQLite-compatible types transparently
- Foreign key constraints are enforced to ensure database integrity, just like in the real database
- Common functionality like filter handling, time grouping, and statistics calculation is shared with the real repository implementation through the `internal/repository/common` package
- This shared approach ensures consistent behavior and reduces code duplication