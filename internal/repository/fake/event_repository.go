package fake

import (
	"context"
	"encoding/json"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"gorm.io/gorm"
)

// fakeEvent is the GORM model used internally by the fake event repository.
// EventData (json.RawMessage) is stored as a string for SQLite compatibility.
type fakeEvent struct {
	ID           string `gorm:"primaryKey"`
	UserID       string `gorm:"index"`
	EventType    models.EventType
	EventSubType *models.EventSubType
	EventData    string    // Stored as string instead of json.RawMessage
	EventTime    time.Time `gorm:"index"`
	CreatedAt    time.Time `gorm:"index"`
	UpdatedAt    time.Time
}

// TableName specifies the table name for GORM
func (fakeEvent) TableName() string {
	return "events" // Match the table name used in migrations/models
}

// EventRepository implements IEventRepository for testing with SQLite
type EventRepository struct {
	db *gorm.DB
}

// NewEventRepository creates a new fake event repository
func NewEventRepository(db *gorm.DB) repository.IEventRepository {
	return &EventRepository{db: db}
}

// toFakeEvent converts a models.Event to fakeEvent for SQLite storage
func toFakeEvent(event *models.Event) *fakeEvent {
	var eventDataStr string
	if event.EventData != nil {
		eventDataStr = string(event.EventData)
	}

	return &fakeEvent{
		ID:           event.ID,
		UserID:       event.UserID,
		EventType:    event.EventType,
		EventSubType: event.EventSubType,
		EventData:    eventDataStr,
		EventTime:    event.EventTime,
		CreatedAt:    event.CreatedAt,
		UpdatedAt:    event.UpdatedAt,
	}
}

// fromFakeEvent converts a fakeEvent back to models.Event
func fromFakeEvent(fe *fakeEvent) *models.Event {
	var eventData json.RawMessage
	if fe.EventData != "" {
		eventData = json.RawMessage(fe.EventData)
	}

	return &models.Event{
		ID:           fe.ID,
		UserID:       fe.UserID,
		EventType:    fe.EventType,
		EventSubType: fe.EventSubType,
		EventData:    eventData,
		EventTime:    fe.EventTime,
		CreatedAt:    fe.CreatedAt,
		UpdatedAt:    fe.UpdatedAt,
	}
}

// Create creates a new event. The event ID acts as an idempotency key.
// If an event with the same ID already exists, returns an error.
func (r *EventRepository) Create(ctx context.Context, event *models.Event) error {
	// Validate that ID is provided (acts as idempotency key)
	if event.ID == "" {
		return repository.ErrEventIDRequired
	}

	// Check if event already exists (manual idempotency check for SQLite)
	var existing fakeEvent
	err := r.db.WithContext(ctx).Where("id = ?", event.ID).First(&existing).Error
	if err == nil {
		// Event already exists
		return repository.ErrEventAlreadyExists
	}
	if err != gorm.ErrRecordNotFound {
		return err // Some other DB error occurred
	}

	// Set timestamps if not already set
	now := time.Now()
	if event.CreatedAt.IsZero() {
		event.CreatedAt = now
	}
	event.UpdatedAt = now

	// Set event time to now if not provided
	if event.EventTime.IsZero() {
		event.EventTime = now
	}

	fakeE := toFakeEvent(event)
	return r.db.WithContext(ctx).Create(fakeE).Error
}

// GetByID retrieves an event by its ID
func (r *EventRepository) GetByID(ctx context.Context, id string) (*models.Event, error) {
	var fakeE fakeEvent
	err := r.db.WithContext(ctx).First(&fakeE, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return fromFakeEvent(&fakeE), nil
}

// ListByUserID retrieves a paginated list of events for a specific user,
// optionally filtered by event type and time range.
// Returns the list of events and the total count matching the criteria.
func (r *EventRepository) ListByUserID(ctx context.Context, userID string, filter repository.EventFilter, offset int, limit int) ([]models.Event, int64, error) {
	var fakeEvents []fakeEvent
	var totalCount int64

	db := r.db.WithContext(ctx).Model(&fakeEvent{}).Where("user_id = ?", userID)

	// Apply event type filters
	if len(filter.EventTypes) > 0 {
		db = db.Where("event_type IN ?", filter.EventTypes)
	}

	// Apply event sub-type filters
	if len(filter.EventSubTypes) > 0 {
		db = db.Where("event_sub_type IN ?", filter.EventSubTypes)
	}

	// Apply time range filters
	if filter.StartTime != nil {
		db = db.Where("event_time >= ?", *filter.StartTime)
	}
	if filter.EndTime != nil {
		db = db.Where("event_time <= ?", *filter.EndTime)
	}

	// Get total count before applying pagination
	err := db.Count(&totalCount).Error
	if err != nil {
		return nil, 0, err
	}

	// Apply pagination and fetch events
	err = db.Offset(offset).Limit(limit).Order("event_time DESC").Find(&fakeEvents).Error
	if err != nil {
		return nil, 0, err
	}

	// Convert fake events to real events
	events := make([]models.Event, len(fakeEvents))
	for i, fe := range fakeEvents {
		events[i] = *fromFakeEvent(&fe)
	}

	return events, totalCount, nil
}
