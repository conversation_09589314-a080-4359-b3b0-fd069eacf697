package fake

import (
	"context"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Ensure FakeIdempotencyRepository implements IIdempotencyRepository
var _ repository.IIdempotencyRepository = (*FakeIdempotencyRepository)(nil)

const (
	DefaultIdempotencyRecordTTL = repository.DefaultIdempotencyRecordTTL
)

// FakeIdempotencyRepository is a GORM-based implementation of IIdempotencyRepository for testing.
// It now uses models.IdempotencyRecord directly.
type FakeIdempotencyRepository struct {
	db *gorm.DB
}

// NewFakeIdempotencyRepository creates a new instance of FakeIdempotencyRepository.
func NewFakeIdempotencyRepository(db *DB) *FakeIdempotencyRepository {
	return &FakeIdempotencyRepository{
		db: db.DB,
	}
}

// Get retrieves an idempotency record by key, user ID, method, and path from the fake database.
func (f *FakeIdempotencyRepository) Get(ctx context.Context, idempotencyKey string, userID string, requestMethod string, requestPath string) (*models.IdempotencyRecord, error) {
	var record models.IdempotencyRecord // Use models.IdempotencyRecord
	err := f.db.WithContext(ctx).Model(&models.IdempotencyRecord{}).
		Where("idempotency_key = ? AND user_id = ? AND request_method = ? AND request_path = ?",
			idempotencyKey, userID, requestMethod, requestPath).
		First(&record).Error

	if err != nil {
		return nil, err
	}
	return &record, nil // Return the found record directly
}

// Create stores a new idempotency record in the fake database.
func (f *FakeIdempotencyRepository) Create(ctx context.Context, record *models.IdempotencyRecord) error {
	if record.ID == "" {
		record.ID = uuid.New().String()
	}
	if record.CreatedAt.IsZero() {
		now := time.Now()
		record.CreatedAt = now
	}
	// Set ExpiresAt if not provided (e.g., default to 7 days)
	if record.ExpiresAt.IsZero() {
		record.ExpiresAt = record.CreatedAt.Add(DefaultIdempotencyRecordTTL)
	}

	// Create directly using models.IdempotencyRecord
	return f.db.WithContext(ctx).Create(record).Error
}

// DeleteExpired removes expired idempotency records from the fake database.
func (f *FakeIdempotencyRepository) DeleteExpired(ctx context.Context) (int64, error) {
	now := time.Now()
	// Delete using models.IdempotencyRecord
	res := f.db.WithContext(ctx).
		Where("expires_at < ?", now).
		Delete(&models.IdempotencyRecord{})

	return res.RowsAffected, res.Error
}

// --- Test Helpers --- (Optional: Add helpers if needed for tests)

// CreateWithTimestamp allows creating a record with a specific creation/expiry for testing.
func (f *FakeIdempotencyRepository) CreateWithTimestamp(ctx context.Context, record *models.IdempotencyRecord, createdAt, expiresAt time.Time) error {
	if record.ID == "" {
		record.ID = uuid.New().String()
	}
	record.CreatedAt = createdAt
	record.ExpiresAt = expiresAt

	// Create directly using models.IdempotencyRecord
	return f.db.WithContext(ctx).Create(record).Error
}
