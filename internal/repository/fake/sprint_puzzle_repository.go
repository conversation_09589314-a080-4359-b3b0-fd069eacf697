package fake

import (
	"context"
	"strings"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// SprintPuzzleRepository is a fake implementation of the repository.ISprintPuzzleRepository interface
type SprintPuzzleRepository struct {
	db *gorm.DB
}

// NewSprintPuzzleRepository creates a new fake sprint puzzle repository
func NewSprintPuzzleRepository(db *DB) repository.ISprintPuzzleRepository {
	return &SprintPuzzleRepository{
		db: db.DB,
	}
}

// Create creates a new sprint puzzle
func (r *SprintPuzzleRepository) Create(ctx context.Context, sprintPuzzle *models.SprintPuzzle) error {
	if sprintPuzzle.ID == "" {
		sprintPuzzle.ID = uuid.New().String()
	}
	now := time.Now()
	sprintPuzzle.CreatedAt = now
	sprintPuzzle.UpdatedAt = now
	return r.db.WithContext(ctx).Create(sprintPuzzle).Error
}

// <PERSON><PERSON><PERSON><PERSON> creates multiple sprint puzzles in a batch
func (r *SprintPuzzleRepository) CreateBatch(ctx context.Context, sprintPuzzles []models.SprintPuzzle) error {
	if len(sprintPuzzles) == 0 {
		return nil
	}

	now := time.Now()
	for i := range sprintPuzzles {
		if sprintPuzzles[i].ID == "" {
			sprintPuzzles[i].ID = uuid.New().String()
		}
		sprintPuzzles[i].CreatedAt = now
		sprintPuzzles[i].UpdatedAt = now
	}

	return r.db.WithContext(ctx).CreateInBatches(sprintPuzzles, len(sprintPuzzles)).Error
}

// GetBySprintID retrieves all sprint puzzles for a specific sprint
func (r *SprintPuzzleRepository) GetBySprintID(ctx context.Context, sprintID string) ([]models.SprintPuzzle, error) {
	var sprintPuzzles []models.SprintPuzzle
	err := r.db.WithContext(ctx).
		Where("sprint_id = ?", sprintID).
		Order("sequence_in_sprint ASC").
		Find(&sprintPuzzles).Error
	if err != nil {
		return nil, err
	}
	return sprintPuzzles, nil
}

// GetByID retrieves a sprint puzzle by ID
func (r *SprintPuzzleRepository) GetByID(ctx context.Context, id string) (*models.SprintPuzzle, error) {
	var sprintPuzzle models.SprintPuzzle
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&sprintPuzzle).Error
	if err != nil {
		return nil, err
	}
	return &sprintPuzzle, nil
}

// CountBySprintID returns the count of sprint puzzles for a specific sprint
func (r *SprintPuzzleRepository) CountBySprintID(ctx context.Context, sprintID string) (int, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.SprintPuzzle{}).
		Where("sprint_id = ?", sprintID).
		Count(&count).Error
	if err != nil {
		return 0, err
	}
	return int(count), nil
}

// GetSprintPuzzlesWithAttempts retrieves sprint puzzles with attempt information
func (r *SprintPuzzleRepository) GetSprintPuzzlesWithAttempts(ctx context.Context, sprintID string, userID string, filter repository.SprintPuzzleFilter, offset int, limit int) ([]repository.SprintPuzzleWithAttempt, int64, error) {
	// For the fake implementation, we'll use a simpler approach
	// Get all sprint puzzles first
	sprintPuzzles, err := r.GetBySprintID(ctx, sprintID)
	if err != nil {
		return nil, 0, err
	}

	// Get sprint puzzle attempts for this user and sprint
	var attempts []fakeSprintPuzzleAttempt
	err = r.db.WithContext(ctx).
		Where("sprint_id = ? AND user_id = ?", sprintID, userID).
		Find(&attempts).Error
	if err != nil {
		return nil, 0, err
	}

	// Create a map for quick lookup of attempts by puzzle ID
	attemptMap := make(map[string]*fakeSprintPuzzleAttempt)
	for i := range attempts {
		attemptMap[attempts[i].LichessPuzzleID] = &attempts[i]
	}

	// Apply sequence filters
	var filteredPuzzles []models.SprintPuzzle
	for _, sp := range sprintPuzzles {
		include := true
		if filter.SequenceMin != nil && sp.SequenceInSprint < *filter.SequenceMin {
			include = false
		}
		if filter.SequenceMax != nil && sp.SequenceInSprint > *filter.SequenceMax {
			include = false
		}
		if include {
			filteredPuzzles = append(filteredPuzzles, sp)
		}
	}

	totalCount := int64(len(filteredPuzzles))

	// Apply pagination
	start := offset
	end := offset + limit
	if start >= len(filteredPuzzles) {
		return []repository.SprintPuzzleWithAttempt{}, totalCount, nil
	}
	if end > len(filteredPuzzles) {
		end = len(filteredPuzzles)
	}

	paginatedPuzzles := filteredPuzzles[start:end]
	result := make([]repository.SprintPuzzleWithAttempt, 0, len(paginatedPuzzles))

	for _, sp := range paginatedPuzzles {
		// Get the lichess puzzle
		var lichessPuzzle models.LichessPuzzle
		err := r.db.WithContext(ctx).Where("id = ?", sp.LichessPuzzleID).First(&lichessPuzzle).Error
		if err != nil {
			continue // Skip if puzzle not found
		}

		// Check if there's an attempt for this puzzle
		attempt, hasAttempt := attemptMap[sp.LichessPuzzleID]

		// Determine attempt status
		attemptStatus := repository.SprintPuzzleStatusUnattempted
		var userMoves []string
		var wasCorrect *bool
		var timeTakenMs *int
		var attemptedAt *time.Time

		if hasAttempt {
			if attempt.WasCorrect {
				attemptStatus = repository.SprintPuzzleStatusSolved
			} else {
				attemptStatus = repository.SprintPuzzleStatusFailed
			}

			if attempt.UserMoves != "" {
				userMoves = strings.Split(attempt.UserMoves, ",")
			}
			wasCorrect = &attempt.WasCorrect
			timeTakenMs = &attempt.TimeTakenMs
			attemptedAt = &attempt.AttemptedAt
		}

		// Apply status filter
		if filter.Status != nil {
			switch *filter.Status {
			case repository.SprintPuzzleStatusUnattempted:
				if hasAttempt {
					continue
				}
			case repository.SprintPuzzleStatusSolved:
				if !hasAttempt || !attempt.WasCorrect {
					continue
				}
			case repository.SprintPuzzleStatusFailed:
				if !hasAttempt || attempt.WasCorrect {
					continue
				}
			case repository.SprintPuzzleStatusAttempted:
				if !hasAttempt {
					continue
				}
			}
		}

		// Apply attempt type filter
		if filter.AttemptType != nil {
			if !hasAttempt || attempt.AttemptType != *filter.AttemptType {
				continue
			}
		}

		var attemptType *string
		var candidateMoves []string
		var chosenMove *string

		if hasAttempt {
			attemptType = &attempt.AttemptType
			if attempt.CandidateMoves != "" {
				candidateMoves = strings.Split(attempt.CandidateMoves, ",")
			}
			chosenMove = attempt.ChosenMove
		}

		result = append(result, repository.SprintPuzzleWithAttempt{
			SprintPuzzle:   sp,
			LichessPuzzle:  lichessPuzzle,
			AttemptStatus:  attemptStatus,
			UserMoves:      userMoves,
			WasCorrect:     wasCorrect,
			TimeTakenMs:    timeTakenMs,
			AttemptedAt:    attemptedAt,
			AttemptType:    attemptType,
			CandidateMoves: candidateMoves,
			ChosenMove:     chosenMove,
		})
	}

	return result, totalCount, nil
}
