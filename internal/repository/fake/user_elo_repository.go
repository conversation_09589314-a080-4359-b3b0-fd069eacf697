package fake

import (
	"context"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"gorm.io/gorm"
)

// UserEloRepository is a fake implementation of the repository.IUserEloRepository interface
type UserEloRepository struct {
	db *gorm.DB
}

// NewUserEloRepository creates a new fake user elo repository
func NewUserEloRepository(db *DB) repository.IUserEloRepository {
	return &UserEloRepository{
		db: db.DB,
	}
}

// GetByUserIDAndEloType retrieves ELO rating for a specific user and ELO type
func (r *UserEloRepository) GetByUserIDAndEloType(ctx context.Context, userID string, eloType string) (*models.UserElo, error) {
	var userElo models.UserElo
	err := r.db.WithContext(ctx).Where("user_id = ? AND elo_type = ?", userID, eloType).First(&userElo).Error
	if err != nil {
		return nil, err
	}
	return &userElo, nil
}

// GetByUserID retrieves all ELO ratings for a specific user
func (r *UserEloRepository) GetByUserID(ctx context.Context, userID string) ([]models.UserElo, error) {
	var userElos []models.UserElo
	err := r.db.WithContext(ctx).Where("user_id = ?", userID).Find(&userElos).Error
	if err != nil {
		return nil, err
	}
	return userElos, nil
}

// Save creates or updates a user's ELO rating for a specific type
func (r *UserEloRepository) Save(ctx context.Context, userElo *models.UserElo) error {
	now := time.Now()
	if userElo.CreatedAt.IsZero() {
		userElo.CreatedAt = now
	}
	userElo.UpdatedAt = now

	if userElo.LastActiveAt.IsZero() {
		userElo.LastActiveAt = now
	}

	return r.db.WithContext(ctx).Save(userElo).Error
}
