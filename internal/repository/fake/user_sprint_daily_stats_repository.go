package fake

import (
	"context"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"gorm.io/gorm"
)

// UserSprintDailyStatsRepository is a fake implementation of the repository.IUserSprintDailyStatsRepository interface
type UserSprintDailyStatsRepository struct {
	db *gorm.DB
}

// NewUserSprintDailyStatsRepository creates a new fake user sprint daily stats repository
func NewUserSprintDailyStatsRepository(db *DB) repository.IUserSprintDailyStatsRepository {
	return &UserSprintDailyStatsRepository{
		db: db.DB,
	}
}

// ListByUserID retrieves a paginated list of daily sprint stats for a specific user
func (r *UserSprintDailyStatsRepository) ListByUserID(ctx context.Context, userID string, eloType *string, startDate *time.Time, endDate *time.Time, offset int, limit int) ([]models.UserSprintDailyStats, int64, error) {
	query := r.db.WithContext(ctx).Where("user_id = ?", userID)

	// Apply filters
	if eloType != nil {
		query = query.Where("elo_type = ?", *eloType)
	}
	if startDate != nil {
		query = query.Where("date >= ?", *startDate)
	}
	if endDate != nil {
		query = query.Where("date <= ?", *endDate)
	}

	// Get total count
	var totalCount int64
	if err := query.Model(&models.UserSprintDailyStats{}).Count(&totalCount).Error; err != nil {
		return nil, 0, err
	}

	// Get daily stats with pagination
	var dailyStats []models.UserSprintDailyStats
	err := query.Order("date DESC").Offset(offset).Limit(limit).Find(&dailyStats).Error
	if err != nil {
		return nil, 0, err
	}

	return dailyStats, totalCount, nil
}
