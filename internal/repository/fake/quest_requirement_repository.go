package fake

import (
	"context"
	"fmt"
	"sync"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
)

// FakeQuestRequirementRepository is an in-memory implementation of IQuestRequirementRepository for testing
type FakeQuestRequirementRepository struct {
	mu                sync.RWMutex
	questRequirements map[string]*models.DailyQuestRequirement
	nextID            int
}

// NewFakeQuestRequirementRepository creates a new fake quest requirement repository
func NewFakeQuestRequirementRepository() *FakeQuestRequirementRepository {
	return &FakeQuestRequirementRepository{
		questRequirements: make(map[string]*models.DailyQuestRequirement),
		nextID:            1,
	}
}

// CreateQuestRequirement creates a new quest requirement
func (r *FakeQuestRequirementRepository) CreateQuestRequirement(ctx context.Context, req *models.DailyQuestRequirement) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	// Check for type uniqueness
	for _, existing := range r.questRequirements {
		if existing.Type == req.Type {
			return fmt.Errorf("quest requirement with type %s already exists", req.Type)
		}
	}

	// Create a copy to avoid mutations
	copy := *req
	r.questRequirements[req.ID] = &copy
	return nil
}

// GetQuestRequirementByID retrieves a quest requirement by ID
func (r *FakeQuestRequirementRepository) GetQuestRequirementByID(ctx context.Context, id string) (*models.DailyQuestRequirement, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	req, exists := r.questRequirements[id]
	if !exists {
		return nil, repository.ErrNotFound
	}

	// Return a copy to avoid mutations
	copy := *req
	return &copy, nil
}

// GetActiveQuestRequirements retrieves all active quest requirements
func (r *FakeQuestRequirementRepository) GetActiveQuestRequirements(ctx context.Context) ([]*models.DailyQuestRequirement, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	var activeRequirements []*models.DailyQuestRequirement
	for _, req := range r.questRequirements {
		if req.IsActive {
			copy := *req
			activeRequirements = append(activeRequirements, &copy)
		}
	}

	return activeRequirements, nil
}

// UpdateQuestRequirement updates an existing quest requirement
func (r *FakeQuestRequirementRepository) UpdateQuestRequirement(ctx context.Context, req *models.DailyQuestRequirement) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if _, exists := r.questRequirements[req.ID]; !exists {
		return repository.ErrNotFound
	}

	// Create a copy to avoid mutations
	copy := *req
	r.questRequirements[req.ID] = &copy
	return nil
}

// DeleteQuestRequirement deletes a quest requirement by ID
func (r *FakeQuestRequirementRepository) DeleteQuestRequirement(ctx context.Context, id string) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if _, exists := r.questRequirements[id]; !exists {
		return repository.ErrNotFound
	}

	delete(r.questRequirements, id)
	return nil
}

// ListQuestRequirements retrieves all quest requirements
func (r *FakeQuestRequirementRepository) ListQuestRequirements(ctx context.Context) ([]*models.DailyQuestRequirement, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	var requirements []*models.DailyQuestRequirement
	for _, req := range r.questRequirements {
		copy := *req
		requirements = append(requirements, &copy)
	}

	return requirements, nil
}

// Ensure FakeQuestRequirementRepository implements IQuestRequirementRepository
var _ repository.IQuestRequirementRepository = (*FakeQuestRequirementRepository)(nil)
