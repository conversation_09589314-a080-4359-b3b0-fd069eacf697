package fake

import (
	"context"
	"encoding/json"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type UserDailyStatsRepository struct {
	db *gorm.DB
}

func NewUserDailyStatsRepository(db *gorm.DB) repository.IUserDailyStatsRepository {
	return &UserDailyStatsRepository{db: db}
}

// ListByUserID retrieves a paginated list of daily stats for a specific user,
// optionally filtered by date range.
// Since this is a fake implementation, it calculates stats from raw events.
func (r *UserDailyStatsRepository) ListByUserID(ctx context.Context, userID string, startDate *time.Time, endDate *time.Time, offset int, limit int) ([]models.UserDailyStats, int64, error) {
	// Get all puzzle events for the user
	var fakeEvents []fakeEvent
	db := r.db.WithContext(ctx).Model(&fakeEvent{}).Where("user_id = ? AND event_type = ?", userID, models.EventTypePuzzle)

	// Apply date range filters
	if startDate != nil {
		// Use date comparison instead of DATE() function for better SQLite compatibility
		startOfDay := time.Date(startDate.Year(), startDate.Month(), startDate.Day(), 0, 0, 0, 0, startDate.Location())
		db = db.Where("event_time >= ?", startOfDay)
	}
	if endDate != nil {
		// Use date comparison instead of DATE() function for better SQLite compatibility
		endOfDay := time.Date(endDate.Year(), endDate.Month(), endDate.Day(), 23, 59, 59, 999999999, endDate.Location())
		db = db.Where("event_time <= ?", endOfDay)
	}

	err := db.Order("event_time ASC").Find(&fakeEvents).Error
	if err != nil {
		return nil, 0, err
	}

	// Group events by date and calculate daily stats
	dailyStatsMap := make(map[string]*models.UserDailyStats)
	var dates []string

	for _, fe := range fakeEvents {
		eventDate := fe.EventTime.Format("2006-01-02")

		// Parse event data
		var puzzleData models.PuzzleEventData
		if err := json.Unmarshal([]byte(fe.EventData), &puzzleData); err != nil {
			continue // Skip invalid events
		}

		// Get or create daily stats for this date
		if _, exists := dailyStatsMap[eventDate]; !exists {
			parsedDate, _ := time.Parse("2006-01-02", eventDate)
			dailyStatsMap[eventDate] = &models.UserDailyStats{
				ID:                  uuid.New().String(),
				UserID:              userID,
				Date:                parsedDate,
				PuzzleSuccess:       0,
				PuzzleTotal:         0,
				Streak:              0,
				PuzzleTotalDuration: 0,
				CreatedAt:           time.Now(),
				UpdatedAt:           time.Now(),
			}
			dates = append(dates, eventDate)
		}

		stats := dailyStatsMap[eventDate]
		stats.PuzzleTotal++
		if puzzleData.Solved {
			stats.PuzzleSuccess++
		}
		stats.PuzzleTotalDuration += puzzleData.TimeSpent
	}

	// Calculate streaks
	r.calculateStreaks(dates, dailyStatsMap)

	// Convert map to slice and sort by date
	var allStats []models.UserDailyStats
	for _, date := range dates {
		allStats = append(allStats, *dailyStatsMap[date])
	}

	// Sort by date descending
	for i := 0; i < len(allStats)-1; i++ {
		for j := i + 1; j < len(allStats); j++ {
			if allStats[i].Date.Before(allStats[j].Date) {
				allStats[i], allStats[j] = allStats[j], allStats[i]
			}
		}
	}

	totalCount := int64(len(allStats))

	// Apply pagination
	start := offset
	end := offset + limit
	if start > len(allStats) {
		return []models.UserDailyStats{}, totalCount, nil
	}
	if end > len(allStats) {
		end = len(allStats)
	}

	return allStats[start:end], totalCount, nil
}

// calculateStreaks calculates the streak for each day based on consecutive days with activity
func (r *UserDailyStatsRepository) calculateStreaks(dates []string, dailyStatsMap map[string]*models.UserDailyStats) {
	if len(dates) == 0 {
		return
	}

	// Sort dates ascending
	for i := 0; i < len(dates)-1; i++ {
		for j := i + 1; j < len(dates); j++ {
			if dates[i] > dates[j] {
				dates[i], dates[j] = dates[j], dates[i]
			}
		}
	}

	// Calculate streaks
	for i, date := range dates {
		stats := dailyStatsMap[date]
		if i == 0 {
			stats.Streak = 1
		} else {
			prevDate := dates[i-1]
			currentDate, _ := time.Parse("2006-01-02", date)
			previousDate, _ := time.Parse("2006-01-02", prevDate)

			// Check if this date is consecutive to the previous date
			if currentDate.Sub(previousDate) == 24*time.Hour {
				stats.Streak = dailyStatsMap[prevDate].Streak + 1
			} else {
				stats.Streak = 1
			}
		}
	}
}
