package fake

import (
	"context"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/repository/common"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// GameRepository is a fake implementation of the repository.GameRepository interface
type GameRepository struct {
	db *gorm.DB
}

// NewGameRepository creates a new fake game repository
func NewGameRepository(db *DB) *GameRepository {
	return &GameRepository{
		db: db.DB,
	}
}

// Create creates a new game
func (r *GameRepository) Create(ctx context.Context, game *models.Game) error {
	game.ID = uuid.New().String()
	now := time.Now()
	game.CreatedAt = now
	game.UpdatedAt = now
	return r.db.WithContext(ctx).Create(game).Error
}

// GetByID retrieves a game by ID
func (r *GameRepository) GetByID(ctx context.Context, id string) (*models.Game, error) {
	var game models.Game
	err := r.db.WithContext(ctx).First(&game, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &game, nil
}

// GetByIDWithoutPGN retrieves a game by ID, excluding the CompressedPGN field
func (r *GameRepository) GetByIDWithoutPGN(ctx context.Context, id string) (*models.Game, error) {
	var game models.Game
	err := r.db.WithContext(ctx).Omit("CompressedPGN").First(&game, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &game, nil
}

// Update updates a game
func (r *GameRepository) Update(ctx context.Context, game *models.Game) error {
	now := time.Now()
	game.UpdatedAt = now
	return r.db.WithContext(ctx).Save(game).Error
}

// Delete deletes a game by ID
func (r *GameRepository) Delete(ctx context.Context, id string) error {
	return r.db.WithContext(ctx).Delete(&models.Game{}, "id = ?", id).Error
}

// DeleteByFilter deletes games for a specific user that match the given filter.
// Returns the number of games deleted.
func (r *GameRepository) DeleteByFilter(ctx context.Context, userID string, filter repository.GameFilter) (int64, error) {
	db := r.db.WithContext(ctx).Model(&models.Game{}).Where("user_id = ?", userID)

	// Apply filters using common utility
	db = common.ApplyGameFilters(db, common.GameFilter(filter))

	// Execute the delete operation
	result := db.Delete(&models.Game{})
	return result.RowsAffected, result.Error
}

// ListByUserID retrieves a paginated list of games for a specific user,
// optionally filtered by platform, username, and game time range.
// Returns the list of games and the total count matching the criteria.
func (r *GameRepository) ListByUserID(ctx context.Context, userID string, filter repository.GameFilter, offset int, limit int) ([]models.Game, int64, error) {
	var games []models.Game
	var totalCount int64

	db := r.db.WithContext(ctx).Model(&models.Game{}).Where("user_id = ?", userID)

	// Apply filters using common utility
	db = common.ApplyGameFilters(db, common.GameFilter(filter))

	// Get total count before applying pagination
	err := db.Count(&totalCount).Error
	if err != nil {
		return nil, 0, err
	}

	// Apply pagination and fetch games
	query := db.Offset(offset).Limit(limit).Order("game_time DESC")

	// Omit PGN if requested
	if filter.OmitPGN {
		query = query.Omit("CompressedPGN")
	}

	err = query.Find(&games).Error
	if err != nil {
		return nil, 0, err
	}

	return games, totalCount, nil
}

// GetGameStats retrieves statistics about games for a specific user,
// optionally filtered by the same criteria as ListByUserID.
// If offset and limit are provided, stats will be calculated only for the specified games.
// If grouping is provided, stats will be grouped by time periods.
// Returns an array of GameStats structs, one for each time period (or a single-element array if no grouping is requested).
func (r *GameRepository) GetGameStats(ctx context.Context, userID string, filter repository.GameFilter, offset int, limit int, grouping *common.TimeGrouping) ([]*models.GameStats, error) {
	// We don't need PGN for stats
	filter.OmitPGN = true

	// Use ListByUserID to get filtered games
	games, totalCount, err := r.ListByUserID(ctx, userID, filter, offset, limit)
	if err != nil {
		return nil, err
	}

	// If no grouping is requested, calculate stats for all games
	if grouping == nil || !grouping.IsValid() {
		stats := &models.GameStats{
			TotalCount: totalCount,
		}

		// Calculate stats for all games using common utility
		common.CalculateGameStats(games, stats)

		return []*models.GameStats{stats}, nil
	}

	return common.CalculateGroupedGameStats(games, grouping)
}
