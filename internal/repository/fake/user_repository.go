package fake

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// UserRepository is a fake implementation of the repository.UserRepository interface
type UserRepository struct {
	db *gorm.DB
}

// NewUserRepository creates a new fake user repository
func NewUserRepository(db *DB) *UserRepository {
	return &UserRepository{
		db: db.DB,
	}
}

// Create creates a new user along with any associated chess profiles
func (r *UserRepository) Create(ctx context.Context, user *models.User) error {
	user.ID = uuid.New().String()
	now := time.Now()
	user.RegisteredAt = now
	user.UpdatedAt = now
	if len(user.ChessProfiles) != 0 {
		return errors.New("chess profiles must be created separately")
	}
	// Note: GORM's fake DB might not automatically handle associations like Preload/Save does in the real DB.
	// Tests relying on this behavior might need adjustments or mock the behavior more explicitly.
	return r.db.WithContext(ctx).Create(user).Error
}

// GetByID retrieves a user by ID, attempting to preload chess profiles
func (r *UserRepository) GetByID(ctx context.Context, id string) (*models.User, error) {
	var user models.User
	// Fake DB might not support Preload directly, but we call it for interface compliance.
	err := r.db.WithContext(ctx).Preload("ChessProfiles").First(&user, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	// Manually load profiles if Preload didn't work
	if len(user.ChessProfiles) == 0 {
		profiles, _ := r.GetChessProfilesByUserID(ctx, user.ID)
		user.ChessProfiles = profiles
	}
	return &user, nil
}

// GetByEmail retrieves a user by email, attempting to preload chess profiles
func (r *UserRepository) GetByEmail(ctx context.Context, email string) (*models.User, error) {
	var user models.User
	err := r.db.WithContext(ctx).Preload("ChessProfiles").First(&user, "email = ?", email).Error
	if err != nil {
		return nil, err
	}
	// Manually load profiles if Preload didn't work
	if len(user.ChessProfiles) == 0 {
		profiles, _ := r.GetChessProfilesByUserID(ctx, user.ID)
		user.ChessProfiles = profiles
	}
	return &user, nil
}

// GetAll retrieves all users, attempting to preload chess profiles
func (r *UserRepository) GetAll(ctx context.Context) ([]models.User, error) {
	var users []models.User
	err := r.db.WithContext(ctx).Preload("ChessProfiles").Find(&users).Error
	if err != nil {
		return users, err
	}
	// Manually load profiles for each user if Preload didn't work
	for i := range users {
		if len(users[i].ChessProfiles) == 0 {
			profiles, _ := r.GetChessProfilesByUserID(ctx, users[i].ID)
			users[i].ChessProfiles = profiles
		}
	}
	return users, err
}

// Update updates a user and replaces their associated chess profiles.
// Mimics the real repository behavior within transaction, handling potential SQLite limitations.
func (r *UserRepository) Update(ctx context.Context, user *models.User) error {
	user.UpdatedAt = time.Now()
	if len(user.ChessProfiles) != 0 {
		return errors.New("chess profiles must be created separately")
	}

	if err := r.db.WithContext(ctx).Omit("ChessProfiles").Save(user).Error; err != nil {
		return err
	}

	return nil
}

// Delete deletes a user by ID. Fake cascade might not work.
func (r *UserRepository) Delete(ctx context.Context, id string) error {
	// Manually delete profiles first for fake DB
	err := r.db.WithContext(ctx).Where("user_id = ?", id).Delete(&models.ChessProfile{}).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return err // Propagate error only if it's not 'not found'
	}
	// Then delete the user
	return r.db.WithContext(ctx).Delete(&models.User{}, "id = ?", id).Error
}

// UpdateLastSignIn updates the last sign-in time for a user
func (r *UserRepository) UpdateLastSignIn(ctx context.Context, id string) error {
	now := time.Now()
	return r.db.WithContext(ctx).Model(&models.User{}).
		Where("id = ?", id).
		Update("last_sign_in_at", now).
		Error
}

// GetByFirebaseUID retrieves a user by Firebase UID
func (r *UserRepository) GetByFirebaseUID(ctx context.Context, firebaseUID string) (*models.User, error) {
	var user models.User
	err := r.db.WithContext(ctx).Preload("ChessProfiles").First(&user, "firebase_uid = ?", firebaseUID).Error
	if err != nil {
		return nil, err
	}
	// Manually load profiles if Preload didn't work
	if len(user.ChessProfiles) == 0 {
		profiles, _ := r.GetChessProfilesByUserID(ctx, user.ID)
		user.ChessProfiles = profiles
	}
	return &user, nil
}

// GetOrCreateFirebaseUser gets an existing user by Firebase UID or creates a new one
// Returns (user, isNewUser, error)
func (r *UserRepository) GetOrCreateFirebaseUser(ctx context.Context, firebaseUID, email string) (*models.User, bool, error) {
	// First try to find by Firebase UID
	user, err := r.GetByFirebaseUID(ctx, firebaseUID)
	if err == nil {
		// Update email from Firebase token (emails can change in Firebase)
		if user.Email != email {
			user.Email = email
			if err := r.Update(ctx, user); err != nil {
				return nil, false, fmt.Errorf("failed to update email: %w", err)
			}
		}
		return user, false, nil // existing user
	}
	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, false, err
	}

	// Try to find by email for linking
	if email != "" {
		user, err = r.GetByEmail(ctx, email)
		if err == nil {
			// Link Firebase UID to existing user and update email
			user.FirebaseUID = &firebaseUID
			user.Email = email // Ensure email is current
			if err := r.Update(ctx, user); err != nil {
				return nil, false, err
			}
			return user, false, nil // linked existing user
		}
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, false, err
		}
	}

	// Create new user
	newUser := &models.User{
		Email:        email,
		FirebaseUID:  &firebaseUID,
		PasswordHash: "", // Firebase users don't need password hash
	}
	if err := r.Create(ctx, newUser); err != nil {
		return nil, false, err
	}

	return newUser, true, nil // new user created
}

// --- ChessProfile Specific Methods (Fake Implementation) ---

func (r *UserRepository) AddChessProfile(ctx context.Context, userID string, profile *models.ChessProfile) error {
	profile.ID = uuid.New().String()
	profile.UserID = userID
	now := time.Now()
	profile.CreatedAt = now
	profile.UpdatedAt = now
	// Check for duplicates manually if fake db doesn't enforce unique index well
	var existing models.ChessProfile
	err := r.db.WithContext(ctx).Where("user_id = ? AND platform = ?", userID, profile.Platform).First(&existing).Error
	if err == nil {
		// Record exists, return an error similar to a unique constraint violation
		return gorm.ErrDuplicatedKey
	}
	if err != gorm.ErrRecordNotFound {
		return err // Some other DB error occurred
	}
	// No duplicate found, proceed with creation
	return r.db.WithContext(ctx).Create(profile).Error
}

func (r *UserRepository) GetChessProfilesByUserID(ctx context.Context, userID string) ([]models.ChessProfile, error) {
	var profiles []models.ChessProfile
	err := r.db.WithContext(ctx).Where("user_id = ?", userID).Find(&profiles).Error
	return profiles, err
}

func (r *UserRepository) GetChessProfileByID(ctx context.Context, profileID string) (*models.ChessProfile, error) {
	var profile models.ChessProfile
	err := r.db.WithContext(ctx).Where("id = ?", profileID).First(&profile).Error
	if err != nil {
		return nil, err
	}
	return &profile, nil
}

func (r *UserRepository) GetChessProfileByPlatform(ctx context.Context, userID string, platform string) (*models.ChessProfile, error) {
	var profile models.ChessProfile
	err := r.db.WithContext(ctx).Where("user_id = ? AND platform = ?", userID, platform).First(&profile).Error
	if err != nil {
		return nil, err
	}
	return &profile, nil
}

func (r *UserRepository) UpdateChessProfile(ctx context.Context, profile *models.ChessProfile) error {
	profile.UpdatedAt = time.Now()
	return r.db.WithContext(ctx).Save(profile).Error
}

func (r *UserRepository) UpdateChessProfileFields(ctx context.Context, profileID string, updates map[string]interface{}) error {
	updates["updated_at"] = time.Now()
	return r.db.WithContext(ctx).Model(&models.ChessProfile{}).Where("id = ?", profileID).Updates(updates).Error
}

// DeleteChessProfile deletes a chess profile by its ID, ensuring it belongs to the specified user (fake implementation).
func (r *UserRepository) DeleteChessProfile(ctx context.Context, userID string, profileID string) error {
	// Fake check for ownership before deleting
	var profile models.ChessProfile
	if err := r.db.WithContext(ctx).Where("id = ? AND user_id = ?", profileID, userID).First(&profile).Error; err != nil {
		// If record not found, return the specific error so handler can return 404
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return gorm.ErrRecordNotFound
		}
		// Return other potential DB errors
		return err
	}
	// If found and owned, proceed with delete
	return r.db.WithContext(ctx).Where("id = ?", profileID).Delete(&models.ChessProfile{}).Error
}

// CountChessProfiles counts the number of chess profiles for a user (fake implementation).
func (r *UserRepository) CountChessProfiles(ctx context.Context, userID string) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&models.ChessProfile{}).Where("user_id = ?", userID).Count(&count).Error
	return count, err
}
