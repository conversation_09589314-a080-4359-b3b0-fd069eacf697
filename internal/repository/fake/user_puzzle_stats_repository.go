package fake

import (
	"context"
	"encoding/json"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type UserPuzzleStatsRepository struct {
	db *gorm.DB
}

func NewUserPuzzleStatsRepository(db *gorm.DB) repository.IUserPuzzleStatsRepository {
	return &UserPuzzleStatsRepository{db: db}
}

// GetByUserID retrieves all puzzle stats for a specific user
// Since this is a fake implementation, it calculates stats from raw events.
func (r *UserPuzzleStatsRepository) GetByUserID(ctx context.Context, userID string) ([]models.UserPuzzleStats, error) {
	// Get all puzzle events for the user
	var fakeEvents []fakeEvent
	err := r.db.WithContext(ctx).Where("user_id = ? AND event_type = ?", userID, models.EventTypePuzzle).Order("event_time ASC").Find(&fakeEvents).Error
	if err != nil {
		return nil, err
	}

	// Group events by puzzle ID and calculate stats
	puzzleStatsMap := make(map[string]*models.UserPuzzleStats)

	for _, fe := range fakeEvents {
		// Parse event data
		var puzzleData models.PuzzleEventData
		if err := json.Unmarshal([]byte(fe.EventData), &puzzleData); err != nil {
			continue // Skip invalid events
		}

		// Skip events without puzzle ID
		if puzzleData.PuzzleID == "" {
			continue
		}

		// Get or create puzzle stats for this puzzle
		if _, exists := puzzleStatsMap[puzzleData.PuzzleID]; !exists {
			puzzleStatsMap[puzzleData.PuzzleID] = &models.UserPuzzleStats{
				ID:                 uuid.New().String(),
				UserID:             userID,
				PuzzleID:           puzzleData.PuzzleID,
				Attempts:           0,
				SuccessCount:       0,
				TotalTime:          0,
				AverageTime:        0,
				LastAttemptTime:    fe.EventTime,
				LastAttemptSuccess: false,
				CreatedAt:          time.Now(),
				UpdatedAt:          time.Now(),
			}
		}

		stats := puzzleStatsMap[puzzleData.PuzzleID]
		stats.Attempts++
		if puzzleData.Solved {
			stats.SuccessCount++
		}
		stats.TotalTime += puzzleData.TimeSpent
		stats.AverageTime = float64(stats.TotalTime) / float64(stats.Attempts)
		stats.LastAttemptTime = fe.EventTime
		stats.LastAttemptSuccess = puzzleData.Solved
	}

	// Convert map to slice
	var result []models.UserPuzzleStats
	for _, stats := range puzzleStatsMap {
		result = append(result, *stats)
	}

	// Sort by last attempt time descending
	for i := 0; i < len(result)-1; i++ {
		for j := i + 1; j < len(result); j++ {
			if result[i].LastAttemptTime.Before(result[j].LastAttemptTime) {
				result[i], result[j] = result[j], result[i]
			}
		}
	}

	return result, nil
}

// GetByUserIDAndPuzzleID retrieves puzzle stats for a specific user and puzzle
func (r *UserPuzzleStatsRepository) GetByUserIDAndPuzzleID(ctx context.Context, userID string, puzzleID string) (*models.UserPuzzleStats, error) {
	// Get all puzzle events for the user and specific puzzle
	var fakeEvents []fakeEvent
	err := r.db.WithContext(ctx).Where("user_id = ? AND event_type = ?", userID, models.EventTypePuzzle).Order("event_time ASC").Find(&fakeEvents).Error
	if err != nil {
		return nil, err
	}

	var stats *models.UserPuzzleStats

	for _, fe := range fakeEvents {
		// Parse event data
		var puzzleData models.PuzzleEventData
		if err := json.Unmarshal([]byte(fe.EventData), &puzzleData); err != nil {
			continue // Skip invalid events
		}

		// Skip events for different puzzles
		if puzzleData.PuzzleID != puzzleID {
			continue
		}

		// Initialize stats if this is the first event for this puzzle
		if stats == nil {
			stats = &models.UserPuzzleStats{
				ID:                 uuid.New().String(),
				UserID:             userID,
				PuzzleID:           puzzleID,
				Attempts:           0,
				SuccessCount:       0,
				TotalTime:          0,
				AverageTime:        0,
				LastAttemptTime:    fe.EventTime,
				LastAttemptSuccess: false,
				CreatedAt:          time.Now(),
				UpdatedAt:          time.Now(),
			}
		}

		stats.Attempts++
		if puzzleData.Solved {
			stats.SuccessCount++
		}
		stats.TotalTime += puzzleData.TimeSpent
		stats.AverageTime = float64(stats.TotalTime) / float64(stats.Attempts)
		stats.LastAttemptTime = fe.EventTime
		stats.LastAttemptSuccess = puzzleData.Solved
	}

	if stats == nil {
		return nil, gorm.ErrRecordNotFound
	}

	return stats, nil
}

// GetDislikedPuzzleIDs retrieves all disliked puzzle IDs for a specific user
func (r *UserPuzzleStatsRepository) GetDislikedPuzzleIDs(ctx context.Context, userID string) ([]string, error) {
	var puzzleIDs []string

	// Query the database for disliked puzzles (is_disliked = true)
	err := r.db.WithContext(ctx).
		Model(&models.UserPuzzleStats{}).
		Where("user_id = ? AND is_disliked = ?", userID, true).
		Pluck("puzzle_id", &puzzleIDs).Error

	if err != nil {
		return nil, err
	}

	return puzzleIDs, nil
}
