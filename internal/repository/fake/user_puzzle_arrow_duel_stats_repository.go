package fake

import (
	"context"
	"encoding/json"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type UserPuzzleArrowDuelStatsRepository struct {
	db *gorm.DB
}

func NewUserPuzzleArrowDuelStatsRepository(db *gorm.DB) repository.IUserPuzzleArrowDuelStatsRepository {
	return &UserPuzzleArrowDuelStatsRepository{db: db}
}

// GetByUserID retrieves all arrow-duel puzzle stats for a specific user
// Since this is a fake implementation, it calculates stats from raw events.
func (r *UserPuzzleArrowDuelStatsRepository) GetByUserID(ctx context.Context, userID string) ([]models.UserPuzzleArrowDuelStats, error) {
	// Get all arrow-duel puzzle events for the user
	var fakeEvents []fakeEvent
	err := r.db.WithContext(ctx).Where("user_id = ? AND event_type = ?", userID, models.EventTypePuzzle).Order("event_time ASC").Find(&fakeEvents).Error
	if err != nil {
		return nil, err
	}

	// Group events by puzzle ID and calculate stats
	puzzleStatsMap := make(map[string]*models.UserPuzzleArrowDuelStats)

	for _, fe := range fakeEvents {
		// Parse event data
		var puzzleData models.PuzzleEventData
		if err := json.Unmarshal([]byte(fe.EventData), &puzzleData); err != nil {
			continue // Skip invalid events
		}

		// Skip non-arrow-duel events or non-user puzzles
		if puzzleData.PuzzleType != models.PuzzleTypeUser ||
			models.GetAttemptTypeOrDefault(&puzzleData.AttemptType) != models.AttemptTypeArrowDuel {
			continue
		}

		// Get or create puzzle stats for this puzzle
		if _, exists := puzzleStatsMap[puzzleData.PuzzleID]; !exists {
			puzzleStatsMap[puzzleData.PuzzleID] = &models.UserPuzzleArrowDuelStats{
				ID:                 uuid.New().String(),
				UserID:             userID,
				PuzzleID:           puzzleData.PuzzleID,
				Attempts:           0,
				SuccessCount:       0,
				TotalTime:          0,
				AverageTime:        0,
				LastAttemptTime:    fe.EventTime,
				LastAttemptSuccess: false,
				IsDisliked:         false,
				CreatedAt:          fe.EventTime,
				UpdatedAt:          fe.EventTime,
			}
		}

		stats := puzzleStatsMap[puzzleData.PuzzleID]

		// Update stats with this attempt
		stats.Attempts++
		stats.TotalTime += puzzleData.TimeSpent
		stats.LastAttemptTime = fe.EventTime
		stats.LastAttemptSuccess = puzzleData.Solved
		stats.UpdatedAt = fe.EventTime

		if puzzleData.Solved {
			stats.SuccessCount++
		}

		// Handle dislike status
		if puzzleData.IsDisliked != nil && *puzzleData.IsDisliked {
			stats.IsDisliked = true
			stats.DislikedAt = &fe.EventTime
		}
	}

	// Calculate average times and convert to slice
	var allStats []models.UserPuzzleArrowDuelStats
	for _, stats := range puzzleStatsMap {
		if stats.Attempts > 0 {
			stats.AverageTime = float64(stats.TotalTime) / float64(stats.Attempts)
		}
		allStats = append(allStats, *stats)
	}

	return allStats, nil
}

// GetByUserIDAndPuzzleID retrieves arrow-duel stats for a specific user and puzzle
// Since this is a fake implementation, it calculates stats from raw events.
func (r *UserPuzzleArrowDuelStatsRepository) GetByUserIDAndPuzzleID(ctx context.Context, userID, puzzleID string) (*models.UserPuzzleArrowDuelStats, error) {
	// Get all arrow-duel puzzle events for the user and specific puzzle
	var fakeEvents []fakeEvent
	err := r.db.WithContext(ctx).Where("user_id = ? AND event_type = ?", userID, models.EventTypePuzzle).Order("event_time ASC").Find(&fakeEvents).Error
	if err != nil {
		return nil, err
	}

	var stats *models.UserPuzzleArrowDuelStats

	for _, fe := range fakeEvents {
		// Parse event data
		var puzzleData models.PuzzleEventData
		if err := json.Unmarshal([]byte(fe.EventData), &puzzleData); err != nil {
			continue // Skip invalid events
		}

		// Skip non-arrow-duel events or events for different puzzles
		if puzzleData.PuzzleID != puzzleID ||
			puzzleData.PuzzleType != models.PuzzleTypeUser ||
			models.GetAttemptTypeOrDefault(&puzzleData.AttemptType) != models.AttemptTypeArrowDuel {
			continue
		}

		// Initialize stats if this is the first event for this puzzle
		if stats == nil {
			stats = &models.UserPuzzleArrowDuelStats{
				ID:                 uuid.New().String(),
				UserID:             userID,
				PuzzleID:           puzzleID,
				Attempts:           0,
				SuccessCount:       0,
				TotalTime:          0,
				AverageTime:        0,
				LastAttemptTime:    fe.EventTime,
				LastAttemptSuccess: false,
				IsDisliked:         false,
				CreatedAt:          fe.EventTime,
				UpdatedAt:          fe.EventTime,
			}
		}

		// Update stats with this attempt
		stats.Attempts++
		stats.TotalTime += puzzleData.TimeSpent
		stats.LastAttemptTime = fe.EventTime
		stats.LastAttemptSuccess = puzzleData.Solved
		stats.UpdatedAt = fe.EventTime

		if puzzleData.Solved {
			stats.SuccessCount++
		}

		// Handle dislike status
		if puzzleData.IsDisliked != nil && *puzzleData.IsDisliked {
			stats.IsDisliked = true
			stats.DislikedAt = &fe.EventTime
		}
	}

	if stats == nil {
		return nil, gorm.ErrRecordNotFound
	}

	// Calculate average time
	if stats.Attempts > 0 {
		stats.AverageTime = float64(stats.TotalTime) / float64(stats.Attempts)
	}

	return stats, nil
}

// GetDislikedPuzzleIDs retrieves all disliked puzzle IDs for a specific user (arrow-duel mode)
// Since this is a fake implementation, it calculates from raw events.
func (r *UserPuzzleArrowDuelStatsRepository) GetDislikedPuzzleIDs(ctx context.Context, userID string) ([]string, error) {
	// Get all arrow-duel puzzle events for the user
	var fakeEvents []fakeEvent
	err := r.db.WithContext(ctx).Where("user_id = ? AND event_type = ?", userID, models.EventTypePuzzle).Order("event_time ASC").Find(&fakeEvents).Error
	if err != nil {
		return nil, err
	}

	// Track disliked puzzles
	dislikedPuzzles := make(map[string]bool)

	for _, fe := range fakeEvents {
		// Parse event data
		var puzzleData models.PuzzleEventData
		if err := json.Unmarshal([]byte(fe.EventData), &puzzleData); err != nil {
			continue // Skip invalid events
		}

		// Skip non-arrow-duel events or non-user puzzles
		if puzzleData.PuzzleType != models.PuzzleTypeUser ||
			models.GetAttemptTypeOrDefault(&puzzleData.AttemptType) != models.AttemptTypeArrowDuel {
			continue
		}

		// Handle dislike status
		if puzzleData.IsDisliked != nil && *puzzleData.IsDisliked {
			dislikedPuzzles[puzzleData.PuzzleID] = true
		}
	}

	// Convert to slice
	var puzzleIDs []string
	for puzzleID := range dislikedPuzzles {
		puzzleIDs = append(puzzleIDs, puzzleID)
	}

	return puzzleIDs, nil
}
