package fake

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"gorm.io/gorm"
)

// fakePuzzleQueueEntry is the GORM model used internally by the fake puzzle queue repository.
type fakePuzzleQueueEntry struct {
	ID                 string `gorm:"primaryKey"`
	UserID             string `gorm:"index"`
	PuzzleID           string
	PuzzleTheme        string // Store as string for SQLite compatibility
	MistakeBy          string
	DueAt              time.Time `gorm:"index"`
	AttemptsSinceAdded int
	ConsecutiveCorrect int
	CreatedAt          time.Time
	UpdatedAt          time.Time
}

// TableName specifies the table name for GORM
func (fakePuzzleQueueEntry) TableName() string {
	return "puzzle_queue"
}

// toFakePuzzleQueueEntry converts a models.PuzzleQueueEntry to its fake representation.
func toFakePuzzleQueueEntry(entry *models.PuzzleQueueEntry) *fakePuzzleQueueEntry {
	if entry == nil {
		return nil
	}
	return &fakePuzzleQueueEntry{
		ID:                 entry.ID,
		UserID:             entry.UserID,
		PuzzleID:           entry.PuzzleID,
		PuzzleTheme:        string(entry.PuzzleTheme),
		MistakeBy:          entry.MistakeBy,
		DueAt:              entry.DueAt,
		AttemptsSinceAdded: entry.AttemptsSinceAdded,
		ConsecutiveCorrect: entry.ConsecutiveCorrect,
		CreatedAt:          entry.CreatedAt,
		UpdatedAt:          entry.UpdatedAt,
	}
}

// fromFakePuzzleQueueEntry converts a fakePuzzleQueueEntry back to a models.PuzzleQueueEntry.
func fromFakePuzzleQueueEntry(fake *fakePuzzleQueueEntry) *models.PuzzleQueueEntry {
	if fake == nil {
		return nil
	}
	return &models.PuzzleQueueEntry{
		ID:                 fake.ID,
		UserID:             fake.UserID,
		PuzzleID:           fake.PuzzleID,
		PuzzleTheme:        models.PuzzleTheme(fake.PuzzleTheme),
		MistakeBy:          fake.MistakeBy,
		DueAt:              fake.DueAt,
		AttemptsSinceAdded: fake.AttemptsSinceAdded,
		ConsecutiveCorrect: fake.ConsecutiveCorrect,
		CreatedAt:          fake.CreatedAt,
		UpdatedAt:          fake.UpdatedAt,
	}
}

// Ensure FakePuzzleQueueRepository implements IPuzzleQueueRepository
var _ repository.IPuzzleQueueRepository = (*FakePuzzleQueueRepository)(nil)

// FakePuzzleQueueRepository is a GORM-based implementation of IPuzzleQueueRepository for testing.
type FakePuzzleQueueRepository struct {
	db *gorm.DB
}

// NewFakePuzzleQueueRepository creates a new instance of FakePuzzleQueueRepository.
func NewFakePuzzleQueueRepository(db *DB) *FakePuzzleQueueRepository {
	return &FakePuzzleQueueRepository{
		db: db.DB,
	}
}

// AddPuzzlesToQueue adds puzzles to the user's queue
func (f *FakePuzzleQueueRepository) AddPuzzlesToQueue(ctx context.Context, userID string, puzzles []models.PuzzleQueueEntry) (int, error) {
	if len(puzzles) == 0 {
		return 0, nil
	}

	// Convert to fake entries and set common fields
	now := time.Now()
	fakeEntries := make([]fakePuzzleQueueEntry, len(puzzles))
	for i, puzzle := range puzzles {
		puzzle.UserID = userID
		// Set DueAt to now if not already set
		if puzzle.DueAt.IsZero() {
			puzzle.DueAt = now
		}
		puzzle.CreatedAt = now
		puzzle.UpdatedAt = now
		fakeEntries[i] = *toFakePuzzleQueueEntry(&puzzle)
	}

	// Use CreateInBatches to handle potential unique constraint violations gracefully
	result := f.db.WithContext(ctx).CreateInBatches(fakeEntries, 100)
	if result.Error != nil {
		return 0, fmt.Errorf("failed to add puzzles to queue: %w", result.Error)
	}

	return int(result.RowsAffected), nil
}

// GetDuePuzzles retrieves due puzzles for a user, optionally filtered by mistake type
func (f *FakePuzzleQueueRepository) GetDuePuzzles(ctx context.Context, userID string, mistakeBy *string, limit int) ([]models.PuzzleQueueItem, error) {
	var fakeEntries []fakePuzzleQueueEntry

	query := f.db.WithContext(ctx).
		Model(&fakePuzzleQueueEntry{}).
		Where("user_id = ? AND due_at <= ?", userID, time.Now())

	// Apply mistake_by filter if specified
	if mistakeBy != nil && *mistakeBy != "" {
		query = query.Where("mistake_by = ?", *mistakeBy)
	}

	// Apply limit
	if limit <= 0 {
		limit = 10 // Default limit
	}

	err := query.Order("due_at ASC").Limit(limit).Find(&fakeEntries).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get due puzzles: %w", err)
	}

	// Convert to PuzzleQueueItem (without puzzle data for fake implementation)
	items := make([]models.PuzzleQueueItem, len(fakeEntries))
	for i, entry := range fakeEntries {
		items[i] = models.PuzzleQueueItem{
			QueueID:            entry.ID,
			PuzzleID:           entry.PuzzleID,
			PuzzleTheme:        entry.PuzzleTheme,
			MistakeBy:          entry.MistakeBy,
			DueAt:              entry.DueAt,
			AttemptsSinceAdded: entry.AttemptsSinceAdded,
			ConsecutiveCorrect: entry.ConsecutiveCorrect,
			PuzzleData:         nil, // Fake implementation doesn't load puzzle data
		}
	}

	return items, nil
}

// UpdateAfterAttempt updates a puzzle queue entry after an attempt
func (f *FakePuzzleQueueRepository) UpdateAfterAttempt(ctx context.Context, userID, puzzleID string, wasCorrect bool) error {
	return f.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var fakeEntry fakePuzzleQueueEntry
		err := tx.Where("user_id = ? AND puzzle_id = ?", userID, puzzleID).First(&fakeEntry).Error
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				// Puzzle not in queue, nothing to update
				return nil
			}
			return fmt.Errorf("failed to find queue entry: %w", err)
		}

		// Update attempt counters
		fakeEntry.AttemptsSinceAdded++

		// Apply spaced repetition logic
		if wasCorrect {
			fakeEntry.ConsecutiveCorrect++

			// If mastered (5 consecutive correct), remove from queue
			if fakeEntry.ConsecutiveCorrect >= 5 {
				return tx.Delete(&fakeEntry).Error
			}

			// Schedule for future review based on consecutive correct count
			switch fakeEntry.ConsecutiveCorrect {
			case 1:
				fakeEntry.DueAt = time.Now().Add(2 * 24 * time.Hour) // 2 days
			case 2:
				fakeEntry.DueAt = time.Now().Add(4 * 24 * time.Hour) // 4 days
			case 3:
				fakeEntry.DueAt = time.Now().Add(7 * 24 * time.Hour) // 7 days
			case 4:
				fakeEntry.DueAt = time.Now().Add(15 * 24 * time.Hour) // 15 days
			}
		} else {
			// Reset consecutive correct and schedule for tomorrow
			fakeEntry.ConsecutiveCorrect = 0
			fakeEntry.DueAt = time.Now().Add(24 * time.Hour) // 24 hours
		}

		fakeEntry.UpdatedAt = time.Now()

		// Save the updated entry
		return tx.Save(&fakeEntry).Error
	})
}

// GetRecentPuzzlesNotInQueue retrieves recent puzzles not already in the queue
func (f *FakePuzzleQueueRepository) GetRecentPuzzlesNotInQueue(ctx context.Context, userID string, mistakeBy *string, limit int) ([]string, error) {
	// For fake implementation, return mock puzzle IDs
	// In real tests, this would be mocked or use test data
	mockPuzzleIDs := []string{"puzzle-1", "puzzle-2", "puzzle-3", "puzzle-4", "puzzle-5"}

	if limit > 0 && limit < len(mockPuzzleIDs) {
		mockPuzzleIDs = mockPuzzleIDs[:limit]
	}

	return mockPuzzleIDs, nil
}

// RemoveFromQueue removes a puzzle from the queue (when mastered)
func (f *FakePuzzleQueueRepository) RemoveFromQueue(ctx context.Context, userID, puzzleID string) error {
	result := f.db.WithContext(ctx).
		Where("user_id = ? AND puzzle_id = ?", userID, puzzleID).
		Delete(&fakePuzzleQueueEntry{})

	if result.Error != nil {
		return fmt.Errorf("failed to remove puzzle from queue: %w", result.Error)
	}

	return nil
}

// GetQueueStats retrieves queue statistics for a user
func (f *FakePuzzleQueueRepository) GetQueueStats(ctx context.Context, userID string) (*models.PuzzleQueueStats, error) {
	var stats models.PuzzleQueueStats

	// Get total queued
	err := f.db.WithContext(ctx).
		Model(&fakePuzzleQueueEntry{}).
		Where("user_id = ?", userID).
		Count(&stats.TotalQueued).Error
	if err != nil {
		return nil, fmt.Errorf("failed to count total queued: %w", err)
	}

	// Get due today
	today := time.Now().Truncate(24 * time.Hour).Add(24*time.Hour - time.Nanosecond)
	err = f.db.WithContext(ctx).
		Model(&fakePuzzleQueueEntry{}).
		Where("user_id = ? AND due_at <= ?", userID, today).
		Count(&stats.DueToday).Error
	if err != nil {
		return nil, fmt.Errorf("failed to count due today: %w", err)
	}

	// Get opponent mistakes count
	err = f.db.WithContext(ctx).
		Model(&fakePuzzleQueueEntry{}).
		Where("user_id = ? AND mistake_by = ?", userID, "opponent").
		Count(&stats.OpponentMistakes).Error
	if err != nil {
		return nil, fmt.Errorf("failed to count opponent mistakes: %w", err)
	}

	// Get own mistakes count
	err = f.db.WithContext(ctx).
		Model(&fakePuzzleQueueEntry{}).
		Where("user_id = ? AND mistake_by = ?", userID, "own").
		Count(&stats.OwnMistakes).Error
	if err != nil {
		return nil, fmt.Errorf("failed to count own mistakes: %w", err)
	}

	return &stats, nil
}

// GetByUserIDAndPuzzleID retrieves a specific queue entry
func (f *FakePuzzleQueueRepository) GetByUserIDAndPuzzleID(ctx context.Context, userID, puzzleID string) (*models.PuzzleQueueEntry, error) {
	var fakeEntry fakePuzzleQueueEntry
	err := f.db.WithContext(ctx).
		Where("user_id = ? AND puzzle_id = ?", userID, puzzleID).
		First(&fakeEntry).Error
	if err != nil {
		return nil, err
	}
	return fromFakePuzzleQueueEntry(&fakeEntry), nil
}
