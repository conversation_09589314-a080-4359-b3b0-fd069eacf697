package fake

import (
	"context"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"gorm.io/gorm"
)

// UserLichessPuzzleStatsRepository is a fake implementation of the repository.IUserLichessPuzzleStatsRepository interface
type UserLichessPuzzleStatsRepository struct {
	db *gorm.DB
}

// NewUserLichessPuzzleStatsRepository creates a new fake user lichess puzzle stats repository
func NewUserLichessPuzzleStatsRepository(db *DB) repository.IUserLichessPuzzleStatsRepository {
	return &UserLichessPuzzleStatsRepository{
		db: db.DB,
	}
}

// GetByUserIDAndPuzzleID retrieves stats for a specific user and lichess puzzle
func (r *UserLichessPuzzleStatsRepository) GetByUserIDAndPuzzleID(ctx context.Context, userID, lichessPuzzleID string) (*models.UserLichessPuzzleStats, error) {
	var stats models.UserLichessPuzzleStats
	err := r.db.WithContext(ctx).Where("user_id = ? AND lichess_puzzle_id = ?", userID, lichessPuzzleID).First(&stats).Error
	if err != nil {
		return nil, err
	}
	return &stats, nil
}

// GetDislikedPuzzleIDs retrieves all disliked lichess puzzle IDs for a specific user
func (r *UserLichessPuzzleStatsRepository) GetDislikedPuzzleIDs(ctx context.Context, userID string) ([]string, error) {
	var puzzleIDs []string

	// Query the database for disliked lichess puzzles (is_disliked = true)
	err := r.db.WithContext(ctx).
		Model(&models.UserLichessPuzzleStats{}).
		Where("user_id = ? AND is_disliked = ?", userID, true).
		Pluck("puzzle_id", &puzzleIDs).Error

	if err != nil {
		return nil, err
	}

	return puzzleIDs, nil
}
