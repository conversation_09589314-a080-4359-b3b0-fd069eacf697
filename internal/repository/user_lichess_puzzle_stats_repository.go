package repository

import (
	"context"

	"github.com/chessticize/chessticize-server/internal/models"
	"gorm.io/gorm"
)

type IUserLichessPuzzleStatsRepository interface {
	GetByUserIDAndPuzzleID(ctx context.Context, userID, lichessPuzzleID string) (*models.UserLichessPuzzleStats, error)
	GetDislikedPuzzleIDs(ctx context.Context, userID string) ([]string, error)
}

type UserLichessPuzzleStatsRepository struct {
	db *gorm.DB
}

func NewUserLichessPuzzleStatsRepository(db *gorm.DB) IUserLichessPuzzleStatsRepository {
	return &UserLichessPuzzleStatsRepository{db: db}
}

// GetByUserIDAndPuzzleID retrieves stats for a specific user and lichess puzzle
func (r *UserLichessPuzzleStatsRepository) GetByUserIDAndPuzzleID(ctx context.Context, userID, lichessPuzzleID string) (*models.UserLichessPuzzleStats, error) {
	var stats models.UserLichessPuzzleStats
	err := r.db.WithContext(ctx).Where("user_id = ? AND lichess_puzzle_id = ?", userID, lichessPuzzleID).First(&stats).Error
	if err != nil {
		return nil, err
	}
	return &stats, nil
}

// GetDislikedPuzzleIDs retrieves all disliked lichess puzzle IDs for a specific user
func (r *UserLichessPuzzleStatsRepository) GetDislikedPuzzleIDs(ctx context.Context, userID string) ([]string, error) {
	var puzzleIDs []string
	err := r.db.WithContext(ctx).
		Model(&models.UserLichessPuzzleStats{}).
		Where("user_id = ? AND is_disliked = ?", userID, true).
		Pluck("lichess_puzzle_id", &puzzleIDs).Error
	if err != nil {
		return nil, err
	}
	return puzzleIDs, nil
}
