# Repository Package

## Package Overview
This package implements repository interfaces for database operations in the application. The repositories provide an abstraction layer over the database, allowing the application to interact with data storage in a structured and testable way.

## Structure and Guidelines

This package follows specific conventions to ensure consistency and maintainability:

- **`interfaces.go`**: Defines interfaces for all repositories.
    - **Naming Convention**: Interface names **must** start with `I` and end with `Repository` (e.g., `IUserRepository`, `IGameRepository`).

- **`internal/repository/` (This directory)**: Contains the "real" repository implementations.
    - These implementations interact with the primary database (e.g., PostgreSQL using GORM).
    - **Naming Convention**: Implementation files should be named `<entity>_repository.go` (e.g., `user_repository.go`). The struct implementing the interface usually doesn't have a specific suffix.

- **`internal/repository/common/`**: Contains shared utilities and common code.
    - These utilities are used by both the real and fake repository implementations.
    - Includes common filter handling, time grouping, and statistics calculation functions.
    - Helps reduce code duplication and ensure consistent behavior across implementations.

- **`internal/repository/fake/`**: Contains "fake" repository implementations.
    - These implementations typically use an in-memory database (e.g., SQLite) and implement the same interfaces defined in `interfaces.go`.
    - They are used primarily for unit and integration testing, providing isolation from the real database.
    - **Naming Convention**: Fake implementation files should follow the same `<entity>_repository.go` naming convention as their real counterparts.

- **`internal/repository/testing/`**: Contains shared test suites and helpers.
    - **Requirement**: Both the real (`internal/repository/`) and fake (`internal/repository/fake/`) implementations **must** pass the tests defined in this directory. This ensures that both implementations adhere to the interface contracts and exhibit consistent behavior.
    - **Naming Convention**: Test files should follow standard Go conventions, typically named `<entity>_repository_test.go`.

## Testing

The `testing` subdirectory provides a unified framework for testing repository implementations.

To run the repository tests (which use the fake repository implementations by default):
```bash
go test ./internal/repository/testing/...
```

This testing approach guarantees that both real and fake implementations meet the requirements defined by the repository interfaces.

**Required Test Structure**: All repository tests must follow a consistent pattern using the `TestDBProvider` approach:
- Create a main test function (`Test<Entity>Repository`) for fake repository tests
- Create a separate test function (`TestReal<Entity>Repository`) for real repository tests
- Implement a shared test runner function (`Run<Entity>RepositoryTests`) that contains all test cases as subtests
- Do not use test suites or struct methods for tests

## Fake Repositories
For unit and integration testing where a real database connection is not desired, see the `fake` subdirectory. It provides in-memory SQLite implementations of all repository interfaces that can be used as drop-in replacements in tests.
