package repository

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// ErrForbidden is returned when a user tries to perform an action on a resource they don't own.
var ErrForbidden = errors.New("operation forbidden")

// NOTE: The IUserRepository interface definition is in interfaces.go

type UserRepository struct {
	db *gorm.DB
}

// NewUserRepository creates a new user repository returning the interface type
// defined in interfaces.go
func NewUserRepository(db *gorm.DB) IUserRepository {
	return &UserRepository{db: db}
}

// Create creates a new user along with any associated chess profiles
func (r *UserRepository) Create(ctx context.Context, user *models.User) error {
	user.ID = uuid.New().String()
	now := time.Now()
	user.RegisteredAt = now
	user.UpdatedAt = now
	// Only create the user, not the associated profiles
	if len(user.ChessProfiles) != 0 {
		return errors.New("chess profiles must be created separately")
	}
	return r.db.WithContext(ctx).Create(user).Error
}

// GetByID retrieves a user by ID, preloading chess profiles
func (r *UserRepository) GetByID(ctx context.Context, id string) (*models.User, error) {
	var user models.User
	err := r.db.WithContext(ctx).Preload("ChessProfiles").First(&user, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// GetByEmail retrieves a user by email, preloading chess profiles
func (r *UserRepository) GetByEmail(ctx context.Context, email string) (*models.User, error) {
	var user models.User
	err := r.db.WithContext(ctx).Preload("ChessProfiles").First(&user, "email = ?", email).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// GetAll retrieves all users, preloading chess profiles
func (r *UserRepository) GetAll(ctx context.Context) ([]models.User, error) {
	var users []models.User
	err := r.db.WithContext(ctx).Preload("ChessProfiles").Find(&users).Error
	return users, err
}

// Update updates a user and replaces their associated chess profiles.
// It ensures atomicity using a transaction.
func (r *UserRepository) Update(ctx context.Context, user *models.User) error {
	user.UpdatedAt = time.Now()
	if len(user.ChessProfiles) != 0 {
		return errors.New("chess profiles must be updated separately")
	}

	if err := r.db.WithContext(ctx).Omit("ChessProfiles").Save(user).Error; err != nil {
		return err
	}

	return nil
}

// Delete deletes a user by ID. Cascading delete should handle profiles.
func (r *UserRepository) Delete(ctx context.Context, id string) error {
	return r.db.WithContext(ctx).Delete(&models.User{}, "id = ?", id).Error
}

// UpdateLastSignIn updates the last sign-in time for a user
func (r *UserRepository) UpdateLastSignIn(ctx context.Context, id string) error {
	now := time.Now()
	return r.db.WithContext(ctx).Model(&models.User{}).
		Where("id = ?", id).
		Update("last_sign_in_at", now).
		Error
}

// GetByFirebaseUID retrieves a user by Firebase UID
func (r *UserRepository) GetByFirebaseUID(ctx context.Context, firebaseUID string) (*models.User, error) {
	var user models.User
	err := r.db.WithContext(ctx).Preload("ChessProfiles").First(&user, "firebase_uid = ?", firebaseUID).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// GetOrCreateFirebaseUser gets an existing user by Firebase UID or creates a new one
// Returns (user, isNewUser, error)
func (r *UserRepository) GetOrCreateFirebaseUser(ctx context.Context, firebaseUID, email string) (*models.User, bool, error) {
	// First try to find by Firebase UID
	user, err := r.GetByFirebaseUID(ctx, firebaseUID)
	if err == nil {
		// Update email from Firebase token (emails can change in Firebase)
		if user.Email != email {
			user.Email = email
			if err := r.Update(ctx, user); err != nil {
				return nil, false, fmt.Errorf("failed to update email: %w", err)
			}
		}
		return user, false, nil // existing user
	}
	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, false, err
	}

	// Try to find by email for linking
	if email != "" {
		user, err = r.GetByEmail(ctx, email)
		if err == nil {
			// Link Firebase UID to existing user and update email
			user.FirebaseUID = &firebaseUID
			user.Email = email // Ensure email is current
			if err := r.Update(ctx, user); err != nil {
				return nil, false, err
			}
			return user, false, nil // linked existing user
		}
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, false, err
		}
	}

	// Create new user
	newUser := &models.User{
		Email:        email,
		FirebaseUID:  &firebaseUID,
		PasswordHash: "", // Firebase users don't need password hash
	}
	if err := r.Create(ctx, newUser); err != nil {
		return nil, false, err
	}

	return newUser, true, nil // new user created
}

// --- ChessProfile Specific Methods ---

// CountChessProfiles counts the number of chess profiles associated with a user.
func (r *UserRepository) CountChessProfiles(ctx context.Context, userID string) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&models.ChessProfile{}).Where("user_id = ?", userID).Count(&count).Error
	return count, err
}

// AddChessProfile adds a new chess profile for a given user
func (r *UserRepository) AddChessProfile(ctx context.Context, userID string, profile *models.ChessProfile) error {
	profile.ID = uuid.New().String()
	profile.UserID = userID
	now := time.Now()
	profile.CreatedAt = now
	profile.UpdatedAt = now
	return r.db.WithContext(ctx).Create(profile).Error
}

// GetChessProfilesByUserID retrieves all chess profiles for a specific user
func (r *UserRepository) GetChessProfilesByUserID(ctx context.Context, userID string) ([]models.ChessProfile, error) {
	var profiles []models.ChessProfile
	err := r.db.WithContext(ctx).Where("user_id = ?", userID).Find(&profiles).Error
	return profiles, err
}

// GetChessProfileByID retrieves a specific chess profile by its own ID
func (r *UserRepository) GetChessProfileByID(ctx context.Context, profileID string) (*models.ChessProfile, error) {
	var profile models.ChessProfile
	err := r.db.WithContext(ctx).Where("id = ?", profileID).First(&profile).Error
	if err != nil {
		return nil, err
	}
	return &profile, nil
}

// GetChessProfileByPlatform retrieves a specific chess profile by user ID and platform
func (r *UserRepository) GetChessProfileByPlatform(ctx context.Context, userID string, platform string) (*models.ChessProfile, error) {
	var profile models.ChessProfile
	err := r.db.WithContext(ctx).Where("user_id = ? AND platform = ?", userID, platform).First(&profile).Error
	if err != nil {
		return nil, err // Could be gorm.ErrRecordNotFound
	}
	return &profile, nil
}

// UpdateChessProfile updates an existing chess profile
// Note: This updates all fields of the profile based on the provided struct.
// Use Updates if you need to update specific fields.
func (r *UserRepository) UpdateChessProfile(ctx context.Context, profile *models.ChessProfile) error {
	profile.UpdatedAt = time.Now()
	// Save updates the entire object, including zero values.
	return r.db.WithContext(ctx).Save(profile).Error
}

// UpdateChessProfileFields updates specific fields of a chess profile
func (r *UserRepository) UpdateChessProfileFields(ctx context.Context, profileID string, updates map[string]interface{}) error {
	updates["updated_at"] = time.Now()
	return r.db.WithContext(ctx).Model(&models.ChessProfile{}).Where("id = ?", profileID).Updates(updates).Error
}

// DeleteChessProfile deletes a chess profile by its ID, ensuring it belongs to the specified user.
func (r *UserRepository) DeleteChessProfile(ctx context.Context, userID string, profileID string) error {
	result := r.db.WithContext(ctx).Where("id = ? AND user_id = ?", profileID, userID).Delete(&models.ChessProfile{})
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}
	return nil
}
