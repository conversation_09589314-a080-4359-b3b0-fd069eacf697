package repository

import (
	"context"
	"errors"
	"strings"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"gorm.io/gorm"
)

var (
	ErrEventIDRequired    = errors.New("event ID is required")
	ErrEventAlreadyExists = errors.New("event with this ID already exists")
)

type EventRepository struct {
	db *gorm.DB
}

func NewEventRepository(db *gorm.DB) *EventRepository {
	return &EventRepository{db: db}
}

// Create creates a new event. The event ID acts as an idempotency key.
// If an event with the same ID already exists, returns an error.
func (r *EventRepository) Create(ctx context.Context, event *models.Event) error {
	// Validate that ID is provided (acts as idempotency key)
	if event.ID == "" {
		return ErrEventIDRequired
	}

	// Set timestamps if not already set
	now := time.Now()
	if event.CreatedAt.IsZero() {
		event.CreatedAt = now
	}
	event.UpdatedAt = now

	// Set event time to now if not provided
	if event.EventTime.IsZero() {
		event.EventTime = now
	}

	// Attempt to create the event
	err := r.db.WithContext(ctx).Create(event).Error
	if err != nil {
		// Check if it's a duplicate key error (idempotency violation)
		if isDuplicateKeyError(err) {
			return ErrEventAlreadyExists
		}
		return err
	}

	return nil
}

// GetByID retrieves an event by its ID
func (r *EventRepository) GetByID(ctx context.Context, id string) (*models.Event, error) {
	var event models.Event
	err := r.db.WithContext(ctx).First(&event, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &event, nil
}

// ListByUserID retrieves a paginated list of events for a specific user,
// optionally filtered by event type and time range.
// Returns the list of events and the total count matching the criteria.
func (r *EventRepository) ListByUserID(ctx context.Context, userID string, filter EventFilter, offset int, limit int) ([]models.Event, int64, error) {
	var events []models.Event
	var totalCount int64

	db := r.db.WithContext(ctx).Model(&models.Event{}).Where("user_id = ?", userID)

	// Apply event type filters
	if len(filter.EventTypes) > 0 {
		db = db.Where("event_type IN ?", filter.EventTypes)
	}

	// Apply event sub-type filters
	if len(filter.EventSubTypes) > 0 {
		db = db.Where("event_sub_type IN ?", filter.EventSubTypes)
	}

	// Apply time range filters
	if filter.StartTime != nil {
		db = db.Where("event_time >= ?", *filter.StartTime)
	}
	if filter.EndTime != nil {
		db = db.Where("event_time <= ?", *filter.EndTime)
	}

	// Get total count before applying pagination
	err := db.Count(&totalCount).Error
	if err != nil {
		return nil, 0, err
	}

	// Apply pagination and fetch events
	err = db.Offset(offset).Limit(limit).Order("event_time DESC").Find(&events).Error
	if err != nil {
		return nil, 0, err
	}

	return events, totalCount, nil
}

// isDuplicateKeyError checks if the error is a duplicate key constraint violation
func isDuplicateKeyError(err error) bool {
	if err == nil {
		return false
	}

	// For PostgreSQL, check for unique constraint violation
	errStr := strings.ToLower(err.Error())
	return strings.Contains(errStr, "duplicate key") ||
		strings.Contains(errStr, "unique constraint") ||
		strings.Contains(errStr, "violates unique")
}
