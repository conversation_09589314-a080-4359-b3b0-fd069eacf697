package repository

import (
	"context"

	"github.com/chessticize/chessticize-server/internal/models"
	"gorm.io/gorm"
)

type UserPuzzleStatsRepository struct {
	db *gorm.DB
}

func NewUserPuzzleStatsRepository(db *gorm.DB) IUserPuzzleStatsRepository {
	return &UserPuzzleStatsRepository{db: db}
}

// GetByUserID retrieves all puzzle stats for a specific user
func (r *UserPuzzleStatsRepository) GetByUserID(ctx context.Context, userID string) ([]models.UserPuzzleStats, error) {
	var stats []models.UserPuzzleStats
	err := r.db.WithContext(ctx).Where("user_id = ?", userID).Order("last_attempt_time DESC").Find(&stats).Error
	if err != nil {
		return nil, err
	}
	return stats, nil
}

// GetByUserIDAndPuzzleID retrieves puzzle stats for a specific user and puzzle
func (r *UserPuzzleStatsRepository) GetByUserIDAndPuzzleID(ctx context.Context, userID string, puzzleID string) (*models.UserPuzzleStats, error) {
	var stats models.UserPuzzleStats
	err := r.db.WithContext(ctx).Where("user_id = ? AND puzzle_id = ?", userID, puzzleID).First(&stats).Error
	if err != nil {
		return nil, err
	}
	return &stats, nil
}

// GetDislikedPuzzleIDs retrieves all disliked puzzle IDs for a specific user
func (r *UserPuzzleStatsRepository) GetDislikedPuzzleIDs(ctx context.Context, userID string) ([]string, error) {
	var puzzleIDs []string
	err := r.db.WithContext(ctx).
		Model(&models.UserPuzzleStats{}).
		Where("user_id = ? AND is_disliked = ?", userID, true).
		Pluck("puzzle_id", &puzzleIDs).Error
	if err != nil {
		return nil, err
	}
	return puzzleIDs, nil
}
