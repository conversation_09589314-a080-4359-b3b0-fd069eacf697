package middleware

import (
	"net/http"

	"github.com/chessticize/chessticize-server/internal/logger"
	chimiddleware "github.com/go-chi/chi/v5/middleware"
)

// Logger is a middleware that injects a request-scoped logger into the context.
func Logger(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Get request ID from context (set by chi's RequestID middleware)
		requestID := chimiddleware.GetReqID(r.Context())

		// Create a request-scoped logger with the request ID field
		log := logger.GlobalLogger.With().Str("request_id", requestID).Logger()

		// Add logger to context
		ctx := logger.WithLogger(r.Context(), log)

		// Add request ID to response header
		w.Header().Set(chimiddleware.RequestIDHeader, requestID)

		// Call the next handler with the updated context
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}
