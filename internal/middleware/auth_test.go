package middleware

import (
	"log"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/config"
	"github.com/golang-jwt/jwt/v5"
	"github.com/stretchr/testify/assert"
)

func TestAdminOnly(t *testing.T) {
	// Create test JWT config
	jwtConfig := config.JWTConfig{
		Secret:        "test-secret-key",
		ExpiryMinutes: 60,
	}

	// Create a simple test handler
	nextHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		if _, err := w.Write([]byte("success")); err != nil {
			log.Printf("Error writing response: %v", err)
		}
	})

	// Apply the JWTAdminOnly middleware
	adminOnlyHandler := JWTAdminOnly(jwtConfig)(nextHandler)

	t.Run("ValidAdminToken", func(t *testing.T) {
		// Create a valid admin token
		token := createTestToken(t, "user1", "<EMAIL>", true, jwtConfig.Secret)

		// Create a request with the token
		req, _ := http.NewRequest("GET", "/", nil)
		req.Header.Set("Authorization", "Bearer "+token)

		// Execute the request
		rr := httptest.NewRecorder()
		adminOnlyHandler.ServeHTTP(rr, req)

		// Check response
		assert.Equal(t, http.StatusOK, rr.Code)
		assert.Equal(t, "success", rr.Body.String())
	})

	t.Run("ValidNonAdminToken", func(t *testing.T) {
		// Create a valid non-admin token
		token := createTestToken(t, "user2", "<EMAIL>", false, jwtConfig.Secret)

		// Create a request with the token
		req, _ := http.NewRequest("GET", "/", nil)
		req.Header.Set("Authorization", "Bearer "+token)

		// Execute the request
		rr := httptest.NewRecorder()
		adminOnlyHandler.ServeHTTP(rr, req)

		// Check response - should be forbidden
		assert.Equal(t, http.StatusForbidden, rr.Code)
	})

	t.Run("NoToken", func(t *testing.T) {
		// Create a request without a token
		req, _ := http.NewRequest("GET", "/", nil)

		// Execute the request
		rr := httptest.NewRecorder()
		adminOnlyHandler.ServeHTTP(rr, req)

		// Check response - should be unauthorized
		assert.Equal(t, http.StatusUnauthorized, rr.Code)
	})

	t.Run("InvalidToken", func(t *testing.T) {
		// Create a request with an invalid token
		req, _ := http.NewRequest("GET", "/", nil)
		req.Header.Set("Authorization", "Bearer invalid-token")

		// Execute the request
		rr := httptest.NewRecorder()
		adminOnlyHandler.ServeHTTP(rr, req)

		// Check response - should be unauthorized
		assert.Equal(t, http.StatusUnauthorized, rr.Code)
	})
}

// Helper function to create a test token
func createTestToken(t *testing.T, userID, email string, isAdmin bool, secret string) string {
	expirationTime := time.Now().Add(1 * time.Hour)

	claims := &Claims{
		UserID:  userID,
		Email:   email,
		IsAdmin: isAdmin,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(secret))

	if err != nil {
		t.Fatalf("Failed to create test token: %v", err)
	}

	return tokenString
}
