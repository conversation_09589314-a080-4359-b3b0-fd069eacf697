package middleware

import (
	"bytes"
	"errors"
	"net/http"
	"regexp"

	"github.com/chessticize/chessticize-server/internal/logger"
	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/go-chi/chi/v5/middleware"
)

const (
	IdempotencyKeyHeader = "Idempotency-Key"
	MaxIdempotencyKeyLen = 128
)

// Regular expression to validate allowed characters in Idempotency Key.
// Allows alphanumeric characters, underscore, and hyphen.
var validIdempotencyKeyChars = regexp.MustCompile(`^[a-zA-Z0-9-_+]+$`)

// responseWriterInterceptor intercepts WriteHeader and Write calls to capture response data.
type responseWriterInterceptor struct {
	http.ResponseWriter
	statusCode int
	body       *bytes.Buffer
}

func newResponseWriterInterceptor(w http.ResponseWriter) *responseWriterInterceptor {
	return &responseWriterInterceptor{
		ResponseWriter: w,
		statusCode:     http.StatusOK, // Default
		body:           new(bytes.Buffer),
	}
}

// WriteHeader captures the status code.
func (rwi *responseWriterInterceptor) WriteHeader(statusCode int) {
	rwi.statusCode = statusCode
	// Don't write header yet, let the final response writing handle it
	// This prevents WriteHeader from being called multiple times if the handler
	// calls it and then we write the stored response later.
}

// Write captures the body and writes to the original ResponseWriter.
func (rwi *responseWriterInterceptor) Write(b []byte) (int, error) {
	// Write to our buffer first to capture it
	n, err := rwi.body.Write(b)
	if err != nil {
		// This shouldn't generally happen with a bytes.Buffer
		return n, err
	}
	// Don't write to underlying writer here yet. Let the main flow decide
	// whether to write the captured data or stored data.
	return n, nil
}

// flush writes the captured status code and body to the underlying ResponseWriter.
// This should be called ONLY when we are NOT returning a cached response.
func (rwi *responseWriterInterceptor) flush() {
	rwi.ResponseWriter.WriteHeader(rwi.statusCode)
	// Ignore error as ResponseWriter.Write can fail in various ways (e.g., broken pipe)
	// and logging it might be too verbose. The underlying http server handles this.
	_, _ = rwi.ResponseWriter.Write(rwi.body.Bytes())
}

// Idempotency middleware handles idempotency checks for requests.
func Idempotency(repo repository.IIdempotencyRepository) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			log := logger.FromContext(r.Context())
			idempotencyKey := r.Header.Get(IdempotencyKeyHeader)

			// If no key, or not a relevant method, skip the middleware
			if idempotencyKey == "" || (r.Method != http.MethodPost && r.Method != http.MethodPut && r.Method != http.MethodPatch && r.Method != http.MethodDelete) {
				next.ServeHTTP(w, r)
				return
			}

			// Validate the key format and length
			if len(idempotencyKey) == 0 || len(idempotencyKey) > MaxIdempotencyKeyLen || !validIdempotencyKeyChars.MatchString(idempotencyKey) {
				log.Warn().Str("key", idempotencyKey).Msg("Invalid Idempotency-Key format or length")
				http.Error(w, "Invalid Idempotency-Key header format (must be 1-128 chars, alphanumeric, -, _)", http.StatusBadRequest)
				return
			}

			// Get UserID from context (must be set by auth middleware)
			userID := GetUserIDFromContext(r.Context())

			if userID == "" {
				log.Error().Str("key", idempotencyKey).Msg("Idempotency check failed: UserID not found in context or not a string")
				http.Error(w, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
				return
			}

			ctx := r.Context()

			// --- Check for existing record ---
			storedRecord, err := repo.Get(ctx, idempotencyKey, userID, r.Method, r.URL.Path)
			if err == nil && storedRecord != nil {
				log.Info().Str("key", idempotencyKey).Str("user_id", userID).Msg("Idempotency key hit: returning stored response")
				// Found - return the stored response
				w.Header().Set("Content-Type", "application/json") // Assume JSON, might need refinement
				// Add any other stored headers if necessary
				w.WriteHeader(storedRecord.ResponseStatusCode)
				_, _ = w.Write(storedRecord.ResponseBody)
				return
			} else if err != nil && !errors.Is(err, repository.ErrNotFound) {
				// An unexpected error occurred during lookup
				log.Error().Err(err).Str("key", idempotencyKey).Str("user_id", userID).Msg("Failed to check idempotency record")
				http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
				return
			}
			// Not found (or ErrNotFound), proceed to handler

			// --- Execute Handler & Capture Response ---
			// Use chi's response writer wrapper to ensure compatibility with other middleware
			// and capture status/body correctly.
			// Note: Our custom interceptor might be redundant if WrapResponseWriter does everything,
			// but let's keep it for explicit control and potential future needs.
			// Update: Let's use our interceptor directly for now.
			interceptor := newResponseWriterInterceptor(w)
			ww := middleware.NewWrapResponseWriter(interceptor, r.ProtoMajor) // Wrap our interceptor

			next.ServeHTTP(ww, r) // Execute the actual handler using the wrapped writer

			// --- Store Response (if successful and within storable range) ---
			capturedBody := interceptor.body.Bytes() // Get captured body bytes

			// Only store successful (2xx) or specific redirect/conflict responses?
			// Let's store any non-5xx response for now.
			// Critical: Do not store if the handler itself panicked or resulted in a 5xx error.
			// ww.Status() should give the final status code written.
			finalStatusCode := ww.Status()
			if finalStatusCode >= 200 && finalStatusCode < 500 {
				record := &models.IdempotencyRecord{
					UserID:         userID,
					IdempotencyKey: idempotencyKey,
					RequestMethod:  r.Method,   // Store method
					RequestPath:    r.URL.Path, // Store path
					// RequestHash:     calculateHash(bodyBytes), // TODO: Implement hashing if needed
					ResponseStatusCode: finalStatusCode, // Use final status code
					ResponseBody:       capturedBody,    // Use captured body
					// ExpiresAt will be set by the repository layer
				}

				err = repo.Create(ctx, record)
				if err != nil {
					// Handle potential race condition: If create fails because it already exists...
					// This relies on the repository returning a specific error or the DB driver error
					// indicating a unique constraint violation. We assume repo.Create handles this.
					// Let's refine this check later if repo.Create doesn't distinguish errors.
					// For now, assume any error means we might need to fetch again.
					log.Warn().Err(err).Str("key", idempotencyKey).Str("user_id", userID).Msg("Failed to create idempotency record, checking again")
					existingRecord, getErr := repo.Get(ctx, idempotencyKey, userID, r.Method, r.URL.Path)
					if getErr == nil && existingRecord != nil {
						// Race condition occurred, another request finished first. Ignore the record and return current response.
						log.Warn().Str("key", idempotencyKey).Str("user_id", userID).Msg("Idempotency race condition resolved: ignoring newly stored response")
					} else {
						// Failed to create AND failed to get existing. Log the original create error.
						log.Error().Err(err).Str("key", idempotencyKey).Str("user_id", userID).Msg("Failed to create idempotency record")
						// Don't return a 500 here, the client's request might have actually succeeded
						// but we failed to *record* it. Let the original response (captured) go through.
					}
				} else {
					log.Info().Str("key", idempotencyKey).Str("user_id", userID).Int("status", finalStatusCode).Msg("Idempotency record created")
				}
			} else {
				log.Warn().Str("key", idempotencyKey).Str("user_id", userID).Int("status", finalStatusCode).Msg("Skipping idempotency record creation for non-successful status")
			}

			// If we didn't return a cached response earlier, flush the captured response now.
			// This writes the actual response from the handler ONLY IF we didn't hit the cache
			// and didn't resolve a race condition by returning stored data.
			interceptor.flush()
		})
	}
}
