package middleware

import (
	"context"
	"errors"
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/chessticize/chessticize-server/internal/logger"
	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/repository/fake"
	"github.com/google/uuid"
	"github.com/rs/zerolog"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"
)

// mockRaceHandler simulates a handler that completes and inserts the idempotency
// record *before* the middleware gets a chance to, simulating a race condition.
type mockRaceHandler struct {
	called     bool
	body       string
	status     int
	repo       repository.IIdempotencyRepository // Repo to insert the conflicting record
	raceRecord *models.IdempotencyRecord         // The record this handler will insert
}

func (m *mockRaceHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	m.called = true

	// Simulate this handler finishing quickly and creating the record
	// before the middleware does.
	if m.repo != nil && m.raceRecord != nil {
		ctx := r.Context() // Use request context
		err := m.repo.Create(ctx, m.raceRecord)
		// Log error but continue? In a real race, the create might succeed or fail.
		// For the test, we assume it succeeds here.
		if err != nil {
			log := logger.FromContext(ctx)
			log.Warn().Err(err).Msg("[mockRaceHandler] Failed to insert race record")
		}
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(m.status)
	_, _ = w.Write([]byte(m.body))
}

// Mock handler to track execution (standard case)
type mockHandler struct {
	called bool
	body   string
	status int
}

func (m *mockHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	m.called = true
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(m.status)
	_, _ = w.Write([]byte(m.body))
}

// Helper to create a request with context containing userID
func newRequestWithUser(method, url, body string, userID string) *http.Request {
	req := httptest.NewRequest(method, url, strings.NewReader(body))
	ctx := context.WithValue(req.Context(), UserIDKey, userID)
	// Add logger to context
	log := zerolog.New(io.Discard)
	ctx = logger.WithLogger(ctx, log)
	return req.WithContext(ctx)
}

// Helper function to get a record directly from DB for verification
func getRecordDirectly(t *testing.T, db *fake.DB, key, userID, method, path string) *models.IdempotencyRecord {
	var record models.IdempotencyRecord
	err := db.DB.Where("idempotency_key = ? AND user_id = ? AND request_method = ? AND request_path = ?",
		key, userID, method, path).
		First(&record).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil
	}
	require.NoError(t, err)
	return &record
}

func TestIdempotencyMiddleware(t *testing.T) {
	testDB := fake.NewDB(t)
	defer func() { _ = testDB.Close() }()

	fakeRepo := fake.NewFakeIdempotencyRepository(testDB)

	userID := uuid.NewString()
	key := "test-key-123"
	path := "/test/resource"
	method := http.MethodPost
	successBody := `{"message":"success"}`
	successStatus := http.StatusCreated

	t.Run("No Idempotency Key", func(t *testing.T) {
		mockNext := &mockHandler{status: successStatus, body: successBody}
		middleware := Idempotency(fakeRepo)
		handler := middleware(mockNext)

		req := newRequestWithUser(method, path, "", userID)
		rr := httptest.NewRecorder()
		handler.ServeHTTP(rr, req)

		assert.Equal(t, successStatus, rr.Code)
		assert.Equal(t, successBody, rr.Body.String())
		assert.True(t, mockNext.called, "Handler should be called")
		record := getRecordDirectly(t, testDB, key, userID, method, path)
		assert.Nil(t, record, "No record should be created")
	})

	t.Run("Non-Mutating Method (GET)", func(t *testing.T) {
		mockNext := &mockHandler{status: http.StatusOK, body: `{"data":"get response"}`}
		middleware := Idempotency(fakeRepo)
		handler := middleware(mockNext)

		req := newRequestWithUser(http.MethodGet, path, "", userID)
		req.Header.Set(IdempotencyKeyHeader, key)
		rr := httptest.NewRecorder()
		handler.ServeHTTP(rr, req)

		assert.Equal(t, http.StatusOK, rr.Code)
		assert.True(t, mockNext.called, "Handler should be called")
		record := getRecordDirectly(t, testDB, key, userID, http.MethodGet, path)
		assert.Nil(t, record, "No record should be created for GET")
	})

	t.Run("Valid Key - First Request", func(t *testing.T) {
		mockNext := &mockHandler{status: successStatus, body: successBody}
		middleware := Idempotency(fakeRepo)
		handler := middleware(mockNext)

		keyFirst := "first-request-key"
		req := newRequestWithUser(method, path, "", userID)
		req.Header.Set(IdempotencyKeyHeader, keyFirst)
		rr := httptest.NewRecorder()
		handler.ServeHTTP(rr, req)

		assert.Equal(t, successStatus, rr.Code)
		assert.Equal(t, successBody, rr.Body.String())
		assert.True(t, mockNext.called, "Handler should be called")

		// Check if record was stored
		record := getRecordDirectly(t, testDB, keyFirst, userID, method, path)
		require.NotNil(t, record, "Record should be created")
		assert.Equal(t, userID, record.UserID)
		assert.Equal(t, keyFirst, record.IdempotencyKey)
		assert.Equal(t, method, record.RequestMethod)
		assert.Equal(t, path, record.RequestPath)
		assert.Equal(t, successStatus, record.ResponseStatusCode)
		assert.Equal(t, []byte(successBody), record.ResponseBody)
		assert.False(t, record.ExpiresAt.IsZero())
	})

	t.Run("Valid Key - Second Request (Hit)", func(t *testing.T) {
		mockNext := &mockHandler{status: successStatus, body: successBody}
		middleware := Idempotency(fakeRepo)
		handler := middleware(mockNext)
		keySecond := "second-request-key"

		// --- First Request --- (to store the record)
		req1 := newRequestWithUser(method, path, "", userID)
		req1.Header.Set(IdempotencyKeyHeader, keySecond)
		rr1 := httptest.NewRecorder()
		handler.ServeHTTP(rr1, req1)
		require.Equal(t, successStatus, rr1.Code, "First request should succeed")
		require.True(t, mockNext.called, "Handler should be called on first request")
		mockNext.called = false // Reset mock state

		// --- Second Request --- (should hit the cache)
		req2 := newRequestWithUser(method, path, "", userID)
		req2.Header.Set(IdempotencyKeyHeader, keySecond)
		rr2 := httptest.NewRecorder()
		handler.ServeHTTP(rr2, req2)

		assert.Equal(t, successStatus, rr2.Code, "Second request should return stored status")
		assert.Equal(t, successBody, rr2.Body.String(), "Second request should return stored body")
		assert.False(t, mockNext.called, "Handler should NOT be called on second request")
	})

	t.Run("Invalid Key - Format", func(t *testing.T) {
		mockNext := &mockHandler{status: successStatus, body: successBody}
		middleware := Idempotency(fakeRepo)
		handler := middleware(mockNext)

		req := newRequestWithUser(method, path, "", userID)
		req.Header.Set(IdempotencyKeyHeader, "invalid key!$")
		rr := httptest.NewRecorder()
		handler.ServeHTTP(rr, req)

		assert.Equal(t, http.StatusBadRequest, rr.Code)
		assert.Contains(t, rr.Body.String(), "Invalid Idempotency-Key")
		assert.False(t, mockNext.called, "Handler should not be called")
	})

	t.Run("Invalid Key - Too Long", func(t *testing.T) {
		mockNext := &mockHandler{status: successStatus, body: successBody}
		middleware := Idempotency(fakeRepo)
		handler := middleware(mockNext)

		longKey := strings.Repeat("a", MaxIdempotencyKeyLen+1)
		req := newRequestWithUser(method, path, "", userID)
		req.Header.Set(IdempotencyKeyHeader, longKey)
		rr := httptest.NewRecorder()
		handler.ServeHTTP(rr, req)

		assert.Equal(t, http.StatusBadRequest, rr.Code)
		assert.Contains(t, rr.Body.String(), "Invalid Idempotency-Key")
		assert.False(t, mockNext.called, "Handler should not be called")
	})

	t.Run("No UserID in Context", func(t *testing.T) {
		mockNext := &mockHandler{status: successStatus, body: successBody}
		middleware := Idempotency(fakeRepo)
		handler := middleware(mockNext)

		// Create request without user ID in context
		req := httptest.NewRequest(method, path, nil)
		req.Header.Set(IdempotencyKeyHeader, key)

		rr := httptest.NewRecorder()
		handler.ServeHTTP(rr, req)

		assert.Equal(t, http.StatusUnauthorized, rr.Code)
		assert.False(t, mockNext.called, "Handler should not be called")
	})

	t.Run("Repository Create Race Condition", func(t *testing.T) {
		raceKey := "race-condition-key-simulated"
		// This is the response the *handler* would have returned if the middleware didn't
		// intercept due to the race condition.
		handlerOriginalStatus := http.StatusCreated
		handlerOriginalBody := `{"message":"handler original success"}`

		// This is the record that the mock handler will insert, simulating the winning race.
		winningRecord := &models.IdempotencyRecord{
			ID:                 uuid.NewString(), // Need ID for DB create
			UserID:             userID,
			IdempotencyKey:     raceKey,
			RequestMethod:      method,
			RequestPath:        path,
			ResponseStatusCode: http.StatusAccepted, // Different status/body to distinguish
			ResponseBody:       []byte(`{"message":"accepted earlier race winner"}`),
			// CreatedAt/ExpiresAt will be set by Create method
		}

		// Use the mockRaceHandler which will insert the winningRecord
		mockNext := &mockRaceHandler{
			status:     handlerOriginalStatus,
			body:       handlerOriginalBody,
			repo:       fakeRepo, // Give it the repo to use
			raceRecord: winningRecord,
		}

		middleware := Idempotency(fakeRepo)
		handler := middleware(mockNext)

		// Make the request. The initial Get will fail.
		// The mockRaceHandler runs, inserting winningRecord.
		// The middleware tries to Create its record, which fails due to unique constraint.
		// Then it tries Get again, finds winningRecord, and returns it.
		req := newRequestWithUser(method, path, "", userID)
		req.Header.Set(IdempotencyKeyHeader, raceKey)
		rr := httptest.NewRecorder()
		handler.ServeHTTP(rr, req)

		// The middleware should detect the race, ignore the winningRecord, and return the original response.
		assert.Equal(t, handlerOriginalStatus, rr.Code)
		assert.JSONEq(t, handlerOriginalBody, rr.Body.String())

		// Now make another request, it should return the winningRecord
		rr = httptest.NewRecorder()
		handler.ServeHTTP(rr, req)

		assert.Equal(t, winningRecord.ResponseStatusCode, rr.Code)
		assert.JSONEq(t, string(winningRecord.ResponseBody), rr.Body.String())
		// Handler was still called initially because the first Get failed
		assert.True(t, mockNext.called, "mockRaceHandler should have been called initially")

		// Verify only the winning record exists in the DB
		finalRecord := getRecordDirectly(t, testDB, raceKey, userID, method, path)
		require.NotNil(t, finalRecord)
		assert.Equal(t, winningRecord.ID, finalRecord.ID)
		assert.Equal(t, winningRecord.ResponseStatusCode, finalRecord.ResponseStatusCode)
		assert.Equal(t, winningRecord.ResponseBody, finalRecord.ResponseBody)
	})

	t.Run("Handler Returns 5xx Error", func(t *testing.T) {
		mockNext := &mockHandler{status: http.StatusInternalServerError, body: `{"error":"internal server error"}`}
		middleware := Idempotency(fakeRepo)
		handler := middleware(mockNext)
		key5xx := "handler-5xx-key"

		req := newRequestWithUser(method, path, "", userID)
		req.Header.Set(IdempotencyKeyHeader, key5xx)
		rr := httptest.NewRecorder()
		handler.ServeHTTP(rr, req)

		assert.Equal(t, http.StatusInternalServerError, rr.Code)
		assert.Equal(t, `{"error":"internal server error"}`, rr.Body.String())
		assert.True(t, mockNext.called, "Handler should be called")
		// Verify no record was stored for 5xx response
		record := getRecordDirectly(t, testDB, key5xx, userID, method, path)
		assert.Nil(t, record, "No record should be created for 5xx status")
	})
}
