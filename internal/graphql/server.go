package graphql

import (
	"net/http"

	"github.com/99designs/gqlgen/graphql"
	"github.com/99designs/gqlgen/graphql/handler"
	"github.com/99designs/gqlgen/graphql/handler/transport"
	"github.com/chessticize/chessticize-server/internal/config"
	"github.com/chessticize/chessticize-server/internal/graphql/generated"
	"github.com/chessticize/chessticize-server/internal/graphql/resolvers"
	"github.com/chessticize/chessticize-server/internal/middleware"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/go-chi/chi/v5"
	"golang.org/x/time/rate"
)

// This file will not be regenerated automatically.
//
// It serves as dependency injection for your app, add any dependencies you require here.

// RateLimiter is a middleware that limits the number of requests per second
type RateLimiter struct {
	limiter *rate.Limiter
}

// NewRateLimiter creates a new rate limiter
func NewRateLimiter(rps float64, burst int) *RateLimiter {
	return &RateLimiter{
		limiter: rate.NewLimiter(rate.Limit(rps), burst),
	}
}

// Middleware is the rate limiting middleware
func (r *RateLimiter) Middleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, req *http.Request) {
		if !r.limiter.Allow() {
			http.Error(w, "Too many requests", http.StatusTooManyRequests)
			return
		}
		next.ServeHTTP(w, req)
	})
}

// GraphQLHandler returns a handler for the GraphQL API
func GraphQLHandler(userRepo repository.IUserRepository, gameRepo repository.IGameRepository, puzzleRepo repository.IPuzzleRepository, jwtConfig config.JWTConfig) http.Handler {
	// Create a new resolver
	resolver := resolvers.NewResolver(userRepo, gameRepo, puzzleRepo)

	// Create a new GraphQL server
	srv := handler.New(generated.NewExecutableSchema(generated.Config{Resolvers: resolver}))

	// Add error presenter to convert errors to proper GraphQL errors
	srv.SetErrorPresenter(graphql.DefaultErrorPresenter)

	// Enable POST transport
	srv.AddTransport(transport.POST{})

	// Create a new router
	router := chi.NewRouter()

	// Add rate limiting middleware (10 requests per second with burst of 30)
	rateLimiter := NewRateLimiter(10, 30)
	router.Use(rateLimiter.Middleware)

	// Add JWT authentication middleware
	router.Use(middleware.JWTAuth(jwtConfig))

	// Mount the GraphQL server
	router.Handle("/", srv)

	return router
}
