package model

import (
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
)

// Puzzle represents a chess puzzle in the GraphQL API
type Puzzle struct {
	ID          string      `json:"id"`
	UserID      string      `json:"user_id"`
	GameID      string      `json:"game_id"`
	GameMove    int         `json:"game_move"`
	FEN         string      `json:"fen"`
	Moves       []string    `json:"moves"`
	PrevCP      int         `json:"prev_cp"`
	CP          int         `json:"cp"`
	Theme       PuzzleTheme `json:"theme"`
	UserColor   Color       `json:"user_color"`
	PuzzleColor Color       `json:"puzzle_color"`
	Zugzwang    bool        `json:"zugzwang"`
	Tags        []string    `json:"tags"`
	CreatedAt   time.Time   `json:"created_at"`
	UpdatedAt   time.Time   `json:"updated_at"`
}

// ConvertPuzzle converts a models.Puzzle to a model.Puzzle
func ConvertPuzzle(puzzle *models.Puzzle) *Puzzle {
	if puzzle == nil {
		return nil
	}

	// Convert pq.StringArray to []string
	moves := make([]string, len(puzzle.Moves))
	copy(moves, puzzle.Moves)

	tags := make([]string, len(puzzle.Tags))
	copy(tags, puzzle.Tags)

	return &Puzzle{
		ID:          puzzle.ID,
		UserID:      puzzle.UserID,
		GameID:      puzzle.GameID,
		GameMove:    puzzle.GameMove,
		FEN:         puzzle.FEN,
		Moves:       moves,
		PrevCP:      puzzle.PrevCP,
		CP:          puzzle.CP,
		Theme:       PuzzleTheme(puzzle.Theme),
		UserColor:   Color(puzzle.UserColor),
		PuzzleColor: Color(puzzle.PuzzleColor),
		Zugzwang:    puzzle.Zugzwang,
		Tags:        tags,
		CreatedAt:   puzzle.CreatedAt,
		UpdatedAt:   puzzle.UpdatedAt,
	}
}
