// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package model

import (
	"bytes"
	"fmt"
	"io"
	"strconv"
)

// Pagination input for cursor-based pagination
type PaginationInput struct {
	First  *int32  `json:"first,omitempty"`
	After  *string `json:"after,omitempty"`
	Last   *int32  `json:"last,omitempty"`
	Before *string `json:"before,omitempty"`
}

type Query struct {
}

// Sorting options for games
type GameSortField string

const (
	GameSortFieldGameTime  GameSortField = "GAME_TIME"
	GameSortFieldCreatedAt GameSortField = "CREATED_AT"
)

var AllGameSortField = []GameSortField{
	GameSortFieldGameTime,
	GameSortFieldCreatedAt,
}

func (e GameSortField) IsValid() bool {
	switch e {
	case GameSortFieldGameTime, GameSortFieldCreatedAt:
		return true
	}
	return false
}

func (e GameSortField) String() string {
	return string(e)
}

func (e *GameSortField) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = GameSortField(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid GameSortField", str)
	}
	return nil
}

func (e GameSortField) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *GameSortField) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e GameSortField) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}

// Sorting options for puzzles
type PuzzleSortField string

const (
	PuzzleSortFieldCreatedAt PuzzleSortField = "CREATED_AT"
	PuzzleSortFieldCp        PuzzleSortField = "CP"
)

var AllPuzzleSortField = []PuzzleSortField{
	PuzzleSortFieldCreatedAt,
	PuzzleSortFieldCp,
}

func (e PuzzleSortField) IsValid() bool {
	switch e {
	case PuzzleSortFieldCreatedAt, PuzzleSortFieldCp:
		return true
	}
	return false
}

func (e PuzzleSortField) String() string {
	return string(e)
}

func (e *PuzzleSortField) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = PuzzleSortField(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid PuzzleSortField", str)
	}
	return nil
}

func (e PuzzleSortField) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *PuzzleSortField) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e PuzzleSortField) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}
