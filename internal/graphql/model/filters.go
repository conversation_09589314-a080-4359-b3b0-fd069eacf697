package model

import (
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
)

// GameFilter holds criteria for filtering games
type GameFilter struct {
	Platform      *models.ChessPlatform `json:"platform"`
	ChessUsername *string               `json:"chess_username"`
	StartTime     *time.Time            `json:"start_time"`
	EndTime       *time.Time            `json:"end_time"`
	TimeControl   *string               `json:"time_control"`
	Rated         *bool                 `json:"rated"`
	Result        *models.GameResult    `json:"result"`
	UserColor     *models.Color         `json:"user_color"`
}

// PuzzleFilter holds criteria for filtering puzzles
type PuzzleFilter struct {
	Tags          []string             `json:"tags"`
	GameStartTime *time.Time           `json:"game_start_time"`
	GameEndTime   *time.Time           `json:"game_end_time"`
	Themes        []models.PuzzleTheme `json:"themes"`
	UserColor     *models.Color        `json:"user_color"`
	PuzzleColor   *models.Color        `json:"puzzle_color"`
	GameMoveMin   *int                 `json:"game_move_min"`
	GameMoveMax   *int                 `json:"game_move_max"`
	PrevCpMin     *int                 `json:"prev_cp_min"`
	PrevCpMax     *int                 `json:"prev_cp_max"`
	CpChangeMin   *int                 `json:"cp_change_min"`
	CpChangeMax   *int                 `json:"cp_change_max"`
	TimeControl   *string              `json:"time_control"`
	Rated         *bool                `json:"rated"`
}

// OffsetPaginationInput holds pagination parameters
type OffsetPaginationInput struct {
	Offset *int `json:"offset"`
	Limit  *int `json:"limit"`
}

// SortDirection represents the direction of sorting
type SortDirection string

const (
	SortDirectionAsc  SortDirection = "ASC"
	SortDirectionDesc SortDirection = "DESC"
)

// SortInput holds sorting parameters
type SortInput struct {
	Field     string        `json:"field"`
	Direction SortDirection `json:"direction"`
}
