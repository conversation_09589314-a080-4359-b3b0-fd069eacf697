package model

// ChessPlatform represents a chess platform
type ChessPlatform string

// Color represents a chess color
type Color string

// Winner represents the winner of a game
type Winner string

// GameResult represents the result of a game
type GameResult string

// PuzzleTheme represents the theme of a puzzle
type PuzzleTheme string

// Chess platform enum values
const (
	ChessPlatformChessDotCom ChessPlatform = "CHESS_DOT_COM"
	ChessPlatformLichessOrg  ChessPlatform = "LICHESS_ORG"
)

// Color enum values
const (
	ColorWhite Color = "WHITE"
	ColorBlack Color = "BLACK"
)

// Winner enum values
const (
	WinnerWhite Winner = "WHITE"
	WinnerBlack Winner = "BLACK"
	WinnerNone  Winner = "NONE"
)

// GameResult enum values
const (
	GameResultMate   GameResult = "MATE"
	GameResultResign GameResult = "RESIGN"
	GameResultDraw   GameResult = "DRAW"
	GameResultTime   GameResult = "TIME"
)

// PuzzleTheme enum values
const (
	PuzzleThemeOpponentBlunderMissed PuzzleTheme = "OPPONENT_BLUNDER_MISSED"
	PuzzleThemeOpponentBlunderCaught PuzzleTheme = "OPPONENT_BLUNDER_CAUGHT"
	PuzzleThemeOpponentMistakeMissed PuzzleTheme = "OPPONENT_MISTAKE_MISSED"
	PuzzleThemeOpponentMistakeCaught PuzzleTheme = "OPPONENT_MISTAKE_CAUGHT"
	PuzzleThemeUserBlunder           PuzzleTheme = "USER_BLUNDER"
	PuzzleThemeUserMistake           PuzzleTheme = "USER_MISTAKE"
)
