package model

import (
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/utils"
)

// Game represents a chess game in the GraphQL API
type Game struct {
	ID            string        `json:"id"`
	UserID        string        `json:"user_id"`
	Platform      ChessPlatform `json:"platform"`
	ChessUsername string        `json:"chess_username"`
	UserColor     Color         `json:"user_color"`
	GameTime      time.Time     `json:"game_time"`
	PGN           string        `json:"pgn"`
	TimeControl   string        `json:"time_control"`
	Rated         bool          `json:"rated"`
	WhitePlayer   string        `json:"white_player"`
	BlackPlayer   string        `json:"black_player"`
	Winner        Winner        `json:"winner"`
	Result        GameResult    `json:"result"`
	CreatedAt     time.Time     `json:"created_at"`
	UpdatedAt     time.Time     `json:"updated_at"`
}

// ConvertGame converts a models.Game to a model.Game
func ConvertGame(game *models.Game) *Game {
	if game == nil {
		return nil
	}

	pgn, _ := utils.DecompressPGN(game.CompressedPGN)

	return &Game{
		ID:            game.ID,
		UserID:        game.UserID,
		Platform:      ChessPlatform(game.Platform),
		ChessUsername: game.ChessUsername,
		UserColor:     Color(game.UserColor),
		GameTime:      game.GameTime,
		PGN:           pgn,
		TimeControl:   game.TimeControl,
		Rated:         game.Rated,
		WhitePlayer:   game.WhitePlayer,
		BlackPlayer:   game.BlackPlayer,
		Winner:        Winner(game.Winner),
		Result:        GameResult(game.Result),
		CreatedAt:     game.CreatedAt,
		UpdatedAt:     game.UpdatedAt,
	}
}
