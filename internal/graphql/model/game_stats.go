package model

import (
	"github.com/chessticize/chessticize-server/internal/models"
)

// PlatformCount represents a chess platform and its count
type PlatformCount struct {
	Platform ChessPlatform `json:"platform"`
	Count    int           `json:"count"`
}

// ResultCount represents a game result and its count
type ResultCount struct {
	Result GameResult `json:"result"`
	Count  int        `json:"count"`
}

// TimeControlCount represents a time control and its count
type TimeControlCount struct {
	TimeControl string `json:"time_control"`
	Count       int    `json:"count"`
}

// RatedCount represents a rated status and its count
type RatedCount struct {
	Rated bool `json:"rated"`
	Count int  `json:"count"`
}

// GameStats represents aggregated statistics about games
type GameStats struct {
	PlatformCounts        []PlatformCount    `json:"platform_counts"`
	UserColorCounts       []ColorCount       `json:"user_color_counts"`
	ResultCounts          []ResultCount      `json:"result_counts"`
	TimeControlCounts     []TimeControlCount `json:"time_control_counts"`
	RatedCounts           []RatedCount       `json:"rated_counts"`
	AverageOpponentRating float64            `json:"average_opponent_rating"`
	TotalCount            int64              `json:"total_count"`
}

// ConvertGameStats converts a models.GameStats to a model.GameStats
func ConvertGameStats(stats *models.GameStats) *GameStats {
	if stats == nil {
		return nil
	}

	result := &GameStats{
		AverageOpponentRating: stats.AverageOpponentRating,
		TotalCount:            stats.TotalCount,
	}

	// Convert platform counts
	for _, pc := range stats.PlatformCounts {
		result.PlatformCounts = append(result.PlatformCounts, PlatformCount{
			Platform: ChessPlatform(pc.Platform),
			Count:    pc.Count,
		})
	}

	// Convert user color counts
	for _, cc := range stats.UserColorCounts {
		result.UserColorCounts = append(result.UserColorCounts, ColorCount{
			Color: Color(cc.Color),
			Count: cc.Count,
		})
	}

	// Convert result counts
	for _, rc := range stats.ResultCounts {
		result.ResultCounts = append(result.ResultCounts, ResultCount{
			Result: GameResult(rc.Result),
			Count:  rc.Count,
		})
	}

	// Convert time control counts
	for _, tc := range stats.TimeControlCounts {
		result.TimeControlCounts = append(result.TimeControlCounts, TimeControlCount{
			TimeControl: tc.TimeControl,
			Count:       tc.Count,
		})
	}

	// Convert rated counts
	for _, rc := range stats.RatedCounts {
		result.RatedCounts = append(result.RatedCounts, RatedCount{
			Rated: rc.Rated,
			Count: rc.Count,
		})
	}

	return result
}
