package model

import (
	"github.com/chessticize/chessticize-server/internal/models"
)

// TagCount represents a tag and its count
type TagCount struct {
	Tag   string `json:"tag"`
	Count int    `json:"count"`
}

// ThemeCount represents a puzzle theme and its count
type ThemeCount struct {
	Theme PuzzleTheme `json:"theme"`
	Count int         `json:"count"`
}

// ColorCount represents a color and its count
type ColorCount struct {
	Color Color `json:"color"`
	Count int   `json:"count"`
}

// GameMoveBucket represents a range of game moves and the count of puzzles in that range
type GameMoveBucket struct {
	Name    string `json:"name"`
	MinMove int    `json:"min_move"`
	MaxMove int    `json:"max_move"`
	Count   int    `json:"count"`
}

// MoveLengthBucket represents a puzzle move length and its count
type MoveLengthBucket struct {
	Length int `json:"length"`
	Count  int `json:"count"`
}

// PuzzleStats represents aggregated statistics about puzzles
type PuzzleStats struct {
	TagCounts         []TagCount         `json:"tag_counts"`
	ThemeCounts       []ThemeCount       `json:"theme_counts"`
	UserColorCounts   []ColorCount       `json:"user_color_counts"`
	GameMoveBuckets   []GameMoveBucket   `json:"game_move_buckets"`
	MoveLengthCounts  []MoveLengthBucket `json:"move_length_counts"`
	TotalCount        int64              `json:"total_count"`
	UniqueGameCount   int64              `json:"unique_game_count"`
	AverageMoveLength float64            `json:"average_move_length"`
}

// Convert converts a models.PuzzleStats to a model.PuzzleStats
func ConvertPuzzleStats(stats *models.PuzzleStats) *PuzzleStats {
	if stats == nil {
		return nil
	}

	result := &PuzzleStats{
		TotalCount:        stats.TotalCount,
		UniqueGameCount:   stats.UniqueGameCount,
		AverageMoveLength: stats.AverageMoveLength,
	}

	// Convert tag counts
	for _, tc := range stats.TagCounts {
		result.TagCounts = append(result.TagCounts, TagCount{
			Tag:   tc.Tag,
			Count: tc.Count,
		})
	}

	// Convert theme counts
	for _, tc := range stats.ThemeCounts {
		result.ThemeCounts = append(result.ThemeCounts, ThemeCount{
			Theme: PuzzleTheme(tc.Theme),
			Count: tc.Count,
		})
	}

	// Convert user color counts
	for _, cc := range stats.UserColorCounts {
		result.UserColorCounts = append(result.UserColorCounts, ColorCount{
			Color: Color(cc.Color),
			Count: cc.Count,
		})
	}

	// Convert game move buckets
	for _, gmb := range stats.GameMoveBuckets {
		result.GameMoveBuckets = append(result.GameMoveBuckets, GameMoveBucket{
			Name:    gmb.Name,
			MinMove: gmb.MinMove,
			MaxMove: gmb.MaxMove,
			Count:   gmb.Count,
		})
	}

	// Convert move length counts
	for _, mlb := range stats.MoveLengthCounts {
		result.MoveLengthCounts = append(result.MoveLengthCounts, MoveLengthBucket{
			Length: mlb.Length,
			Count:  mlb.Count,
		})
	}

	return result
}
