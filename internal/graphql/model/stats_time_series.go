package model

import (
	"time"
)

// TimeGrouping represents the time unit for grouping statistics
type TimeGrouping string

const (
	// TimeGroupingDay represents daily grouping
	TimeGroupingDay TimeGrouping = "DAY"
	// TimeGroupingWeek represents weekly grouping
	TimeGroupingWeek TimeGrouping = "WEEK"
	// TimeGroupingMonth represents monthly grouping
	TimeGroupingMonth TimeGrouping = "MONTH"
)

// GroupedPuzzleStats represents puzzle statistics for a specific time period
type GroupedPuzzleStats struct {
	StartTime time.Time    `json:"start_time"`
	EndTime   time.Time    `json:"end_time"`
	Stats     *PuzzleStats `json:"stats"`
}

// GroupedPuzzleStatsConnection represents a collection of grouped puzzle statistics
type GroupedPuzzleStatsConnection struct {
	Nodes      []*GroupedPuzzleStats `json:"nodes"`
	TotalCount int                   `json:"total_count"`
}

// GroupedGameStats represents game statistics for a specific time period
type GroupedGameStats struct {
	StartTime time.Time  `json:"start_time"`
	EndTime   time.Time  `json:"end_time"`
	Stats     *GameStats `json:"stats"`
}

// GroupedGameStatsConnection represents a collection of grouped game statistics
type GroupedGameStatsConnection struct {
	Nodes      []*GroupedGameStats `json:"nodes"`
	TotalCount int                 `json:"total_count"`
}
