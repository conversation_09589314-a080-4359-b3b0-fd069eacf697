package model

import (
	"github.com/chessticize/chessticize-server/internal/models"
)

// GameEdge represents a game edge in a connection
type GameEdge struct {
	Node   *Game  `json:"node"`
	Cursor string `json:"cursor"`
}

// GameConnection represents a paginated connection of games
type GameConnection struct {
	Edges      []*GameEdge `json:"edges"`
	PageInfo   *PageInfo   `json:"page_info"`
	TotalCount int32       `json:"total_count"`
}

// PageInfo represents pagination information
type PageInfo struct {
	HasNextPage     bool   `json:"has_next_page"`
	HasPreviousPage bool   `json:"has_previous_page"`
	StartCursor     string `json:"start_cursor"`
	EndCursor       string `json:"end_cursor"`
}

// ConvertGameToEdge converts a models.Game to a GameEdge
func ConvertGameToEdge(game *models.Game) *GameEdge {
	return &GameEdge{
		Node:   ConvertGame(game),
		Cursor: game.ID,
	}
}

// ConvertGamesToConnection converts a slice of models.Game to a GameConnection
func ConvertGamesToConnection(games []models.Game, totalCount int64, hasNextPage bool, hasPrevPage bool) *GameConnection {
	edges := make([]*GameEdge, len(games))
	for i, game := range games {
		gameCopy := game // Create a copy to avoid issues with loop variable capture
		edges[i] = ConvertGameToEdge(&gameCopy)
	}

	return &GameConnection{
		Edges: edges,
		PageInfo: &PageInfo{
			HasNextPage:     hasNextPage,
			HasPreviousPage: hasPrevPage,
			StartCursor:     "",
			EndCursor:       "",
		},
		TotalCount: int32(totalCount),
	}
}
