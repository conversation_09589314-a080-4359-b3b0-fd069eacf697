package model

import (
	"github.com/chessticize/chessticize-server/internal/models"
)

// PuzzleEdge represents a puzzle edge in a connection
type PuzzleEdge struct {
	Node   *models.Puzzle `json:"node"`
	Cursor string         `json:"cursor"`
}

// PuzzleConnection represents a paginated connection of puzzles
type PuzzleConnection struct {
	Edges      []*PuzzleEdge `json:"edges"`
	PageInfo   *PageInfo     `json:"page_info"`
	TotalCount int32         `json:"total_count"`
}

// ConvertPuzzleToEdge converts a models.Puzzle to a PuzzleEdge
func ConvertPuzzleToEdge(puzzle *models.Puzzle) *PuzzleEdge {
	return &PuzzleEdge{
		Node:   puzzle, // Pass the original models.Puzzle directly to preserve preloaded data
		Cursor: puzzle.ID,
	}
}

// ConvertPuzzlesToConnection converts a slice of models.Puzzle to a PuzzleConnection
func ConvertPuzzlesToConnection(puzzles []models.Puzzle, totalCount int64, hasNextPage bool, hasPrevPage bool) *PuzzleConnection {
	edges := make([]*PuzzleEdge, len(puzzles))
	for i, puzzle := range puzzles {
		puzzleCopy := puzzle // Create a copy to avoid issues with loop variable capture
		edges[i] = ConvertPuzzleToEdge(&puzzleCopy)
	}

	return &PuzzleConnection{
		Edges: edges,
		PageInfo: &PageInfo{
			HasNextPage:     hasNextPage,
			HasPreviousPage: hasPrevPage,
			StartCursor:     "",
			EndCursor:       "",
		},
		TotalCount: int32(totalCount),
	}
}
