package model

import (
	"encoding/base64"
	"fmt"
)

// EncodeCursor encodes a cursor for pagination
func EncodeCursor(id string) string {
	return base64.StdEncoding.EncodeToString([]byte(id))
}

// DecodeCursor decodes a cursor for pagination
func DecodeCursor(cursor string) (string, error) {
	bytes, err := base64.StdEncoding.DecodeString(cursor)
	if err != nil {
		return "", fmt.Errorf("invalid cursor: %w", err)
	}
	return string(bytes), nil
}
