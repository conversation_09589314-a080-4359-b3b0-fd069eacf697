package resolvers

import (
	"context"

	"github.com/chessticize/chessticize-server/internal/graphql/generated"
	"github.com/chessticize/chessticize-server/internal/graphql/model"
)

// TimeControlCount returns a generated.TimeControlCountResolver implementation.
func (r *Resolver) TimeControlCount() generated.TimeControlCountResolver {
	return &timeControlCountResolver{r}
}

type timeControlCountResolver struct{ *Resolver }

// Count returns the count as an int32.
func (r *timeControlCountResolver) Count(ctx context.Context, obj *model.TimeControlCount) (int32, error) {
	return int32(obj.Count), nil
}
