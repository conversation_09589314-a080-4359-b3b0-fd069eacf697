package resolvers

import (
	"context"
	"errors"

	"github.com/chessticize/chessticize-server/internal/graphql/generated"
	"github.com/chessticize/chessticize-server/internal/middleware"
	"github.com/chessticize/chessticize-server/internal/models"
)

// Puzzle returns the puzzleResolver implementation
func (r *Resolver) Puzzle() generated.PuzzleResolver {
	return &puzzleResolver{r}
}

// puzzleResolver implements PuzzleResolver
type puzzleResolver struct {
	*Resolver
}

// Game is a resolver for the Puzzle.game field
func (r *puzzleResolver) Game(ctx context.Context, obj *models.Puzzle) (*models.Game, error) {
	// Get user ID from context
	userID := middleware.GetUserIDFromContext(ctx)
	if userID == "" {
		return nil, errors.New("unauthorized")
	}

	// Get the game from the repository using the puzzle's GameID, without loading the PGN
	game, err := r.GameRepo.GetByIDWithoutPGN(ctx, obj.GameID)
	if err != nil {
		return nil, err
	}

	// Check if the game belongs to the user
	if game.UserID != userID {
		return nil, errors.New("forbidden")
	}

	return game, nil
}
