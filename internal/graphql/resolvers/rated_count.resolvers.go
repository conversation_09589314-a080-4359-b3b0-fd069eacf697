package resolvers

import (
	"context"

	"github.com/chessticize/chessticize-server/internal/graphql/generated"
	"github.com/chessticize/chessticize-server/internal/graphql/model"
)

// RatedCount returns a generated.RatedCountResolver implementation.
func (r *Resolver) RatedCount() generated.RatedCountResolver {
	return &ratedCountResolver{r}
}

type ratedCountResolver struct{ *Resolver }

// Count returns the count as an int32.
func (r *ratedCountResolver) Count(ctx context.Context, obj *model.RatedCount) (int32, error) {
	return int32(obj.Count), nil
}
