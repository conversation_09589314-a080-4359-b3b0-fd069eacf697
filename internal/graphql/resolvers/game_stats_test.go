package resolvers

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/graphql/model"
	"github.com/chessticize/chessticize-server/internal/middleware"
	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository/fake"
	"github.com/chessticize/chessticize-server/internal/utils"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// Helper function to add user ID to context for testing
func withUserID(ctx context.Context, userID string) context.Context {
	return context.WithValue(ctx, middleware.UserIDKey, userID)
}

func TestMyGameStats_EmptyResults(t *testing.T) {
	// Create fake DB and repositories
	fakeDB := fake.NewDB(t)
	userRepo := fake.NewUserRepository(fakeDB)
	gameRepo := fake.NewGameRepository(fakeDB)
	puzzleRepo := fake.NewPuzzleRepository(fakeDB)

	// Create resolver
	resolver := NewResolver(userRepo, gameRepo, puzzleRepo)

	// Create a test user with unique email
	uniqueEmail := fmt.Sprintf("<EMAIL>", uuid.New().String())
	user := &models.User{
		ID:           uuid.New().String(),
		Email:        uniqueEmail,
		PasswordHash: "hash",
		RegisteredAt: time.Now(),
		UpdatedAt:    time.Now(),
	}
	err := userRepo.Create(context.Background(), user)
	require.NoError(t, err)

	// Create test games
	for i := 0; i < 10; i++ {
		// Create a simple PGN
		pgn := "1. e4 e5 2. Nf3 Nc6"
		compressedPGN, err := utils.CompressPGN(pgn)
		require.NoError(t, err)

		game := &models.Game{
			ID:            uuid.New().String(),
			UserID:        user.ID,
			Platform:      models.LichessOrg,
			ChessUsername: "testuser",
			UserColor:     models.White,
			GameTime:      time.Now(),
			CompressedPGN: compressedPGN,
			TimeControl:   "10+0",
			Rated:         true,
			WhitePlayer:   `{"username":"testuser","rating":1500}`,
			BlackPlayer:   `{"username":"opponent","rating":1600}`,
			Winner:        models.WinnerWhite,
			Result:        models.Mate,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		}
		err = gameRepo.Create(context.Background(), game)
		require.NoError(t, err)
	}

	// Create context with user ID
	ctx := withUserID(context.Background(), user.ID)

	// Call the resolver
	stats, err := resolver.Query().MyGameStats(ctx, nil, nil)
	require.NoError(t, err)
	require.NotNil(t, stats)

	// Check that we have the correct total count
	assert.Equal(t, int64(10), stats.TotalCount)

	// Check that the arrays are not empty
	assert.NotEmpty(t, stats.PlatformCounts, "PlatformCounts should not be empty")
	assert.NotEmpty(t, stats.UserColorCounts, "UserColorCounts should not be empty")
	assert.NotEmpty(t, stats.ResultCounts, "ResultCounts should not be empty")
	assert.NotEmpty(t, stats.TimeControlCounts, "TimeControlCounts should not be empty")
	assert.NotEmpty(t, stats.RatedCounts, "RatedCounts should not be empty")

	// Check specific values
	assert.Equal(t, 1, len(stats.PlatformCounts))
	assert.Equal(t, "lichess.org", string(stats.PlatformCounts[0].Platform))
	assert.Equal(t, 10, stats.PlatformCounts[0].Count)

	assert.Equal(t, 1, len(stats.UserColorCounts))
	assert.Equal(t, "white", string(stats.UserColorCounts[0].Color))
	assert.Equal(t, 10, stats.UserColorCounts[0].Count)

	assert.Equal(t, 1, len(stats.ResultCounts))
	assert.Equal(t, "mate", string(stats.ResultCounts[0].Result))
	assert.Equal(t, 10, stats.ResultCounts[0].Count)

	assert.Equal(t, 1, len(stats.TimeControlCounts))
	assert.Equal(t, "10+0", stats.TimeControlCounts[0].TimeControl)
	assert.Equal(t, 10, stats.TimeControlCounts[0].Count)

	assert.Equal(t, 1, len(stats.RatedCounts))
	assert.Equal(t, true, stats.RatedCounts[0].Rated)
	assert.Equal(t, 10, stats.RatedCounts[0].Count)

	assert.Equal(t, 1600.0, stats.AverageOpponentRating)
}

func TestMyGameStats_WithPagination(t *testing.T) {
	// Create fake DB and repositories
	fakeDB := fake.NewDB(t)
	userRepo := fake.NewUserRepository(fakeDB)
	gameRepo := fake.NewGameRepository(fakeDB)
	puzzleRepo := fake.NewPuzzleRepository(fakeDB)

	// Create resolver
	resolver := NewResolver(userRepo, gameRepo, puzzleRepo)

	// Create a test user with unique email
	uniqueEmail := fmt.Sprintf("<EMAIL>", uuid.New().String())
	user := &models.User{
		ID:           uuid.New().String(),
		Email:        uniqueEmail,
		PasswordHash: "hash",
		RegisteredAt: time.Now(),
		UpdatedAt:    time.Now(),
	}
	err := userRepo.Create(context.Background(), user)
	require.NoError(t, err)

	// Create 20 test games
	for i := 0; i < 20; i++ {
		// Create a simple PGN
		pgn := "1. e4 e5 2. Nf3 Nc6"
		compressedPGN, err := utils.CompressPGN(pgn)
		require.NoError(t, err)

		game := &models.Game{
			ID:            uuid.New().String(),
			UserID:        user.ID,
			Platform:      models.LichessOrg,
			ChessUsername: "testuser",
			UserColor:     models.White,
			GameTime:      time.Now(),
			CompressedPGN: compressedPGN,
			TimeControl:   "10+0",
			Rated:         true,
			WhitePlayer:   `{"username":"testuser","rating":1500}`,
			BlackPlayer:   `{"username":"opponent","rating":1600}`,
			Winner:        models.WinnerWhite,
			Result:        models.Mate,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		}
		err = gameRepo.Create(context.Background(), game)
		require.NoError(t, err)
	}

	// Create context with user ID
	ctx := withUserID(context.Background(), user.ID)

	// Create pagination to get only the first 10 games
	offset := 0
	limit := 10
	pagination := &model.OffsetPaginationInput{
		Offset: &offset,
		Limit:  &limit,
	}

	// Call the resolver with pagination
	stats, err := resolver.Query().MyGameStats(ctx, nil, pagination)
	require.NoError(t, err)
	require.NotNil(t, stats)

	// Check that we have the correct total count (should be 20, the total number of games)
	assert.Equal(t, int64(20), stats.TotalCount)

	// Check that the arrays are not empty
	assert.NotEmpty(t, stats.PlatformCounts, "PlatformCounts should not be empty")
	assert.NotEmpty(t, stats.UserColorCounts, "UserColorCounts should not be empty")
	assert.NotEmpty(t, stats.ResultCounts, "ResultCounts should not be empty")
	assert.NotEmpty(t, stats.TimeControlCounts, "TimeControlCounts should not be empty")
	assert.NotEmpty(t, stats.RatedCounts, "RatedCounts should not be empty")

	// Check specific values
	assert.Equal(t, 1, len(stats.PlatformCounts))
	assert.Equal(t, "lichess.org", string(stats.PlatformCounts[0].Platform))
	assert.Equal(t, 10, stats.PlatformCounts[0].Count)
}

func TestMyGameStats_WithFilter(t *testing.T) {
	// Create fake DB and repositories
	fakeDB := fake.NewDB(t)
	userRepo := fake.NewUserRepository(fakeDB)
	gameRepo := fake.NewGameRepository(fakeDB)
	puzzleRepo := fake.NewPuzzleRepository(fakeDB)

	// Create resolver
	resolver := NewResolver(userRepo, gameRepo, puzzleRepo)

	// Create a test user with unique email
	uniqueEmail := fmt.Sprintf("<EMAIL>", uuid.New().String())
	user := &models.User{
		ID:           uuid.New().String(),
		Email:        uniqueEmail,
		PasswordHash: "hash",
		RegisteredAt: time.Now(),
		UpdatedAt:    time.Now(),
	}
	err := userRepo.Create(context.Background(), user)
	require.NoError(t, err)

	// Create test games with different time controls
	for i := 0; i < 5; i++ {
		// Create a simple PGN
		pgn := "1. e4 e5 2. Nf3 Nc6"
		compressedPGN, err := utils.CompressPGN(pgn)
		require.NoError(t, err)

		game := &models.Game{
			ID:            uuid.New().String(),
			UserID:        user.ID,
			Platform:      models.LichessOrg,
			ChessUsername: "testuser",
			UserColor:     models.White,
			GameTime:      time.Now(),
			CompressedPGN: compressedPGN,
			TimeControl:   "10+0",
			Rated:         true,
			WhitePlayer:   `{"username":"testuser","rating":1500}`,
			BlackPlayer:   `{"username":"opponent","rating":1600}`,
			Winner:        models.WinnerWhite,
			Result:        models.Mate,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		}
		err = gameRepo.Create(context.Background(), game)
		require.NoError(t, err)
	}

	for i := 0; i < 5; i++ {
		// Create a simple PGN
		pgn := "1. d4 d5 2. c4 e6"
		compressedPGN, err := utils.CompressPGN(pgn)
		require.NoError(t, err)

		game := &models.Game{
			ID:            uuid.New().String(),
			UserID:        user.ID,
			Platform:      models.LichessOrg,
			ChessUsername: "testuser",
			UserColor:     models.White,
			GameTime:      time.Now(),
			CompressedPGN: compressedPGN,
			TimeControl:   "5+0",
			Rated:         true,
			WhitePlayer:   `{"username":"testuser","rating":1500}`,
			BlackPlayer:   `{"username":"opponent","rating":1600}`,
			Winner:        models.WinnerWhite,
			Result:        models.Mate,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		}
		err = gameRepo.Create(context.Background(), game)
		require.NoError(t, err)
	}

	// Create context with user ID
	ctx := withUserID(context.Background(), user.ID)

	// Create filter for 10+0 games
	timeControl := "10+0"
	filter := &model.GameFilter{
		TimeControl: &timeControl,
	}

	// Call the resolver with filter
	stats, err := resolver.Query().MyGameStats(ctx, filter, nil)
	require.NoError(t, err)
	require.NotNil(t, stats)

	// Check that we have the correct total count
	assert.Equal(t, int64(5), stats.TotalCount)

	// Check that the arrays are not empty
	assert.NotEmpty(t, stats.TimeControlCounts, "TimeControlCounts should not be empty")
	assert.Equal(t, 1, len(stats.TimeControlCounts))
	assert.Equal(t, "10+0", stats.TimeControlCounts[0].TimeControl)
	assert.Equal(t, 5, stats.TimeControlCounts[0].Count)
}
