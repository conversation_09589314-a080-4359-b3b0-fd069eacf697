package resolvers

import (
	"context"
	"errors"
	"fmt"

	"github.com/chessticize/chessticize-server/internal/graphql/generated"
	"github.com/chessticize/chessticize-server/internal/graphql/model"
	"github.com/chessticize/chessticize-server/internal/middleware"
	"github.com/chessticize/chessticize-server/internal/repository"
)

// MyGameStats returns statistics about the current user's games
func (r *queryResolver) MyGameStats(ctx context.Context, filter *model.GameFilter, pagination *model.OffsetPaginationInput) (*model.GameStats, error) {
	// Get user ID from context
	userID := middleware.GetUserIDFromContext(ctx)
	if userID == "" {
		return nil, errors.New("unauthorized")
	}

	// Convert filter to repository filter
	repoFilter := repository.GameFilter{}
	if filter != nil {
		if filter.Platform != nil {
			platform := GraphQLToChessPlatform(string(*filter.Platform))
			repoFilter.Platform = &platform
		}
		if filter.ChessUsername != nil {
			repoFilter.ChessUsername = filter.ChessUsername
		}
		if filter.StartTime != nil {
			repoFilter.StartTime = filter.StartTime
		}
		if filter.EndTime != nil {
			repoFilter.EndTime = filter.EndTime
		}
		if filter.UserColor != nil {
			userColor := GraphQLToColor(string(*filter.UserColor))
			repoFilter.UserColor = &userColor
		}
		if filter.TimeControl != nil {
			repoFilter.TimeControl = filter.TimeControl
		}
		if filter.Rated != nil {
			repoFilter.Rated = filter.Rated
		}
		if filter.Result != nil {
			result := GraphQLToGameResult(string(*filter.Result))
			repoFilter.Result = &result
		}
	}

	// Set default pagination if not provided
	offset := 0
	limit := MaxPaginationLimit // Large limit to get all games by default

	// Apply pagination if provided
	if pagination != nil {
		if pagination.Offset != nil {
			offset = int(*pagination.Offset)
		}
		if pagination.Limit != nil {
			limit = int(*pagination.Limit)
		}
	}

	// Set OmitPGN to true - we don't need PGN for stats
	repoFilter.OmitPGN = true

	// Get game stats from repository
	stats, err := r.GameRepo.GetGameStats(ctx, userID, repoFilter, offset, limit, nil)
	if err != nil {
		return nil, err
	}

	// Since we're not grouping, we should have exactly one stats object
	if len(stats) != 1 {
		return nil, fmt.Errorf("expected 1 stats object, got %d", len(stats))
	}

	// Convert to GraphQL model
	return model.ConvertGameStats(stats[0]), nil
}

// GameStats returns a generated.GameStatsResolver implementation.
func (r *Resolver) GameStats() generated.GameStatsResolver {
	return &gameStatsResolver{r}
}

type gameStatsResolver struct{ *Resolver }

// TotalCount returns the total count as an int32.
func (r *gameStatsResolver) TotalCount(ctx context.Context, obj *model.GameStats) (int32, error) {
	return int32(obj.TotalCount), nil
}
