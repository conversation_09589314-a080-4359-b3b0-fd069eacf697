package resolvers

import (
	"context"

	"github.com/chessticize/chessticize-server/internal/graphql/model"
	"github.com/chessticize/chessticize-server/internal/models"
)

// GameConnection represents a connection of games
type GameConnection struct {
	Edges      []*GameEdge `json:"edges"`
	PageInfo   *PageInfo   `json:"page_info"`
	TotalCount int         `json:"total_count"`
}

// GameEdge represents an edge in a connection of games
type GameEdge struct {
	Node   *models.Game `json:"node"`
	Cursor string       `json:"cursor"`
}

// PuzzleConnection represents a connection of puzzles
type PuzzleConnection struct {
	Edges      []*PuzzleEdge `json:"edges"`
	PageInfo   *PageInfo     `json:"page_info"`
	TotalCount int           `json:"total_count"`
}

// PuzzleEdge represents an edge in a connection of puzzles
type PuzzleEdge struct {
	Node   *models.Puzzle `json:"node"`
	Cursor string         `json:"cursor"`
}

// PageInfo represents page information for pagination
type PageInfo struct {
	HasNextPage     bool   `json:"has_next_page"`
	HasPreviousPage bool   `json:"has_previous_page"`
	StartCursor     string `json:"start_cursor"`
	EndCursor       string `json:"end_cursor"`
}

// QueryResolver defines the query resolver interface
type QueryResolver interface {
	Game(ctx context.Context, id string) (*models.Game, error)
	Puzzle(ctx context.Context, id string) (*models.Puzzle, error)
	MyGames(ctx context.Context, filter *model.GameFilter, pagination *model.OffsetPaginationInput, sort *model.SortInput) (*GameConnection, error)
	MyPuzzles(ctx context.Context, filter *model.PuzzleFilter, pagination *model.OffsetPaginationInput, sort *model.SortInput) (*PuzzleConnection, error)
	MyPuzzleStats(ctx context.Context, filter *model.PuzzleFilter, pagination *model.OffsetPaginationInput) (*model.PuzzleStats, error)
	MyGameStats(ctx context.Context, filter *model.GameFilter, pagination *model.OffsetPaginationInput) (*model.GameStats, error)
	MyGroupedPuzzleStats(ctx context.Context, filter *model.PuzzleFilter, groupUnit model.TimeGrouping, groupLength *int32) (*model.GroupedPuzzleStatsConnection, error)
	MyGroupedGameStats(ctx context.Context, filter *model.GameFilter, groupUnit model.TimeGrouping, groupLength *int32) (*model.GroupedGameStatsConnection, error)
}
