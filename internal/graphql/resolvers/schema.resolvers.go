package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.73

import (
	"context"
	"errors"
	"fmt"

	"github.com/chessticize/chessticize-server/internal/graphql/generated"
	"github.com/chessticize/chessticize-server/internal/graphql/model"
	"github.com/chessticize/chessticize-server/internal/middleware"
	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
)

// Pgn resolver is already implemented in game.resolvers.go

// GameMove is the resolver for the game_move field.
func (r *puzzleResolver) GameMove(ctx context.Context, obj *models.Puzzle) (int32, error) {
	return int32(obj.GameMove), nil
}

// Moves is the resolver for the moves field.
func (r *puzzleResolver) Moves(ctx context.Context, obj *models.Puzzle) ([]string, error) {
	return obj.Moves, nil
}

// PrevCp is the resolver for the prev_cp field.
func (r *puzzleResolver) PrevCp(ctx context.Context, obj *models.Puzzle) (int32, error) {
	return int32(obj.PrevCP), nil
}

// Cp is the resolver for the cp field.
func (r *puzzleResolver) Cp(ctx context.Context, obj *models.Puzzle) (int32, error) {
	return int32(obj.CP), nil
}

// Tags is the resolver for the tags field.
func (r *puzzleResolver) Tags(ctx context.Context, obj *models.Puzzle) ([]string, error) {
	return obj.Tags, nil
}

// Game returns the game with the given ID
func (r *queryResolver) Game(ctx context.Context, id string) (*models.Game, error) {
	// Get user ID from context
	userID := middleware.GetUserIDFromContext(ctx)
	if userID == "" {
		return nil, errors.New("unauthorized")
	}

	// Get game from repository
	game, err := r.GameRepo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	// Check if the game belongs to the user
	if game.UserID != userID {
		return nil, errors.New("forbidden")
	}

	return game, nil
}

// Puzzle returns the puzzle with the given ID
func (r *queryResolver) Puzzle(ctx context.Context, id string) (*models.Puzzle, error) {
	// Get user ID from context
	userID := middleware.GetUserIDFromContext(ctx)
	if userID == "" {
		return nil, errors.New("unauthorized")
	}

	// Get puzzle from repository
	puzzle, err := r.PuzzleRepo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	// Check if the puzzle belongs to the user
	if puzzle.UserID != userID {
		return nil, errors.New("forbidden")
	}

	return puzzle, nil
}

// MyGames returns the games of the current user
func (r *queryResolver) MyGames(ctx context.Context, filter *model.GameFilter, pagination *model.OffsetPaginationInput, sort *model.SortInput) (*model.GameConnection, error) {
	// Get user ID from context
	userID := middleware.GetUserIDFromContext(ctx)
	if userID == "" {
		return nil, errors.New("unauthorized")
	}

	// Convert filter to repository filter
	repoFilter := repository.GameFilter{}
	if filter != nil {
		if filter.Platform != nil {
			// The filter.Platform is already a models.ChessPlatform type, but it might contain
			// uppercase GraphQL enum values that need to be converted to lowercase internal values
			platform := GraphQLToChessPlatform(string(*filter.Platform))
			repoFilter.Platform = &platform
		}
		repoFilter.ChessUsername = filter.ChessUsername
		repoFilter.StartTime = filter.StartTime
		repoFilter.EndTime = filter.EndTime
		repoFilter.TimeControl = filter.TimeControl
		repoFilter.Rated = filter.Rated

		// Add userColor filter
		if filter.UserColor != nil {
			// The filter.UserColor is already a models.Color type, but it might contain
			// uppercase GraphQL enum values that need to be converted to lowercase internal values
			userColor := GraphQLToColor(string(*filter.UserColor))
			repoFilter.UserColor = &userColor
		}

		// Add result filter
		if filter.Result != nil {
			// The filter.Result is already a models.GameResult type, but it might contain
			// uppercase GraphQL enum values that need to be converted to lowercase internal values
			result := GraphQLToGameResult(string(*filter.Result))
			repoFilter.Result = &result
		}
	}

	// Set default pagination values
	offset := 0
	limit := 10
	if pagination != nil {
		if pagination.Offset != nil {
			offset = *pagination.Offset
		}
		if pagination.Limit != nil {
			limit = *pagination.Limit
		}
	}

	// The repository already has default sorting
	// No need to apply sorting here

	// Get games from repository
	games, totalCount, err := r.GameRepo.ListByUserID(ctx, userID, repoFilter, offset, limit)
	if err != nil {
		return nil, err
	}

	// Convert to GraphQL types
	edges := make([]*model.GameEdge, len(games))
	for i := range games {
		gameCopy := games[i] // Create a copy to avoid issues with loop variable capture
		edges[i] = &model.GameEdge{
			Node:   model.ConvertGame(&gameCopy),
			Cursor: model.EncodeCursor(games[i].ID),
		}
	}

	// Create page info
	hasNextPage := offset+limit < int(totalCount)
	hasPreviousPage := offset > 0

	var startCursor, endCursor string
	if len(games) > 0 {
		startCursor = model.EncodeCursor(games[0].ID)
		endCursor = model.EncodeCursor(games[len(games)-1].ID)
	}

	// Create connection
	return &model.GameConnection{
		Edges: edges,
		PageInfo: &model.PageInfo{
			HasNextPage:     hasNextPage,
			HasPreviousPage: hasPreviousPage,
			StartCursor:     startCursor,
			EndCursor:       endCursor,
		},
		TotalCount: int32(totalCount),
	}, nil
}

// MyPuzzles returns the puzzles of the current user
func (r *queryResolver) MyPuzzles(ctx context.Context, filter *model.PuzzleFilter, pagination *model.OffsetPaginationInput, sort *model.SortInput) (*model.PuzzleConnection, error) {
	// Get user ID from context
	userID := middleware.GetUserIDFromContext(ctx)
	if userID == "" {
		return nil, errors.New("unauthorized")
	}

	// Convert filter to repository filter
	repoFilter := repository.PuzzleFilter{}
	if filter != nil {
		// Handle tags with OR relation
		repoFilter.Tags = filter.Tags
		repoFilter.GameStartTime = filter.GameStartTime
		repoFilter.GameEndTime = filter.GameEndTime

		// Add theme filters (OR relation)
		if len(filter.Themes) > 0 {
			for _, theme := range filter.Themes {
				// Convert GraphQL enum values to internal values
				convertedTheme := GraphQLToPuzzleTheme(string(theme))
				repoFilter.Themes = append(repoFilter.Themes, convertedTheme)
			}
		}

		// Add color filters
		if filter.UserColor != nil {
			// The filter.UserColor is already a models.Color type, but it might contain
			// uppercase GraphQL enum values that need to be converted to lowercase internal values
			userColor := GraphQLToColor(string(*filter.UserColor))
			repoFilter.UserColor = &userColor
		}

		if filter.PuzzleColor != nil {
			// The filter.PuzzleColor is already a models.Color type, but it might contain
			// uppercase GraphQL enum values that need to be converted to lowercase internal values
			puzzleColor := GraphQLToColor(string(*filter.PuzzleColor))
			repoFilter.PuzzleColor = &puzzleColor
		}

		// Add game move range filters
		repoFilter.GameMoveMin = filter.GameMoveMin
		repoFilter.GameMoveMax = filter.GameMoveMax

		// Add CP range filters
		repoFilter.PrevCpMin = filter.PrevCpMin
		repoFilter.PrevCpMax = filter.PrevCpMax

		// Add CP change range filters
		repoFilter.CpChangeMin = filter.CpChangeMin
		repoFilter.CpChangeMax = filter.CpChangeMax

		// Add game-related filters
		repoFilter.TimeControl = filter.TimeControl
		repoFilter.Rated = filter.Rated
	}

	// Set default pagination values
	offset := 0
	limit := 10
	if pagination != nil {
		if pagination.Offset != nil {
			offset = *pagination.Offset
		}
		if pagination.Limit != nil {
			limit = *pagination.Limit
		}
	}

	// The repository already has default sorting
	// No need to apply sorting here

	// Get puzzles from repository
	puzzles, totalCount, err := r.PuzzleRepo.ListByUserID(ctx, userID, repoFilter, offset, limit)
	if err != nil {
		return nil, err
	}

	// Convert to GraphQL types - use ConvertPuzzleToEdge to preserve preloaded Game data
	edges := make([]*model.PuzzleEdge, len(puzzles))
	for i := range puzzles {
		puzzleCopy := puzzles[i] // Create a copy to avoid issues with loop variable capture
		edges[i] = model.ConvertPuzzleToEdge(&puzzleCopy)
	}

	// Create page info
	hasNextPage := offset+limit < int(totalCount)
	hasPreviousPage := offset > 0

	var startCursor, endCursor string
	if len(puzzles) > 0 {
		startCursor = model.EncodeCursor(puzzles[0].ID)
		endCursor = model.EncodeCursor(puzzles[len(puzzles)-1].ID)
	}

	// Create connection
	return &model.PuzzleConnection{
		Edges: edges,
		PageInfo: &model.PageInfo{
			HasNextPage:     hasNextPage,
			HasPreviousPage: hasPreviousPage,
			StartCursor:     startCursor,
			EndCursor:       endCursor,
		},
		TotalCount: int32(totalCount),
	}, nil
}

// MyPuzzleStats returns statistics about the current user's puzzles
func (r *queryResolver) MyPuzzleStats(ctx context.Context, filter *model.PuzzleFilter, pagination *model.OffsetPaginationInput) (*model.PuzzleStats, error) {
	// Get user ID from context
	userID := middleware.GetUserIDFromContext(ctx)
	if userID == "" {
		return nil, errors.New("unauthorized")
	}

	// Convert filter to repository filter using the same helper function as MyGroupedPuzzleStats
	repoFilter := convertPuzzleFilter(filter)

	// Set default pagination if not provided
	offset := 0
	limit := MaxPaginationLimit // Large limit to get all puzzles by default

	// Apply pagination if provided
	if pagination != nil {
		if pagination.Offset != nil {
			offset = int(*pagination.Offset)
		}
		if pagination.Limit != nil {
			limit = int(*pagination.Limit)
		}
	}

	// Get puzzle stats from repository
	stats, err := r.PuzzleRepo.GetPuzzleStats(ctx, userID, repoFilter, offset, limit, nil)
	if err != nil {
		return nil, err
	}

	// Since we're not grouping, we should have exactly one stats object
	if len(stats) != 1 {
		return nil, fmt.Errorf("expected 1 stats object, got %d", len(stats))
	}

	// Convert to GraphQL model
	return model.ConvertPuzzleStats(stats[0]), nil
}

// Offset is the resolver for the offset field.
func (r *offsetPaginationInputResolver) Offset(ctx context.Context, obj *model.OffsetPaginationInput, data *int32) error {
	if data != nil {
		intVal := int(*data)
		obj.Offset = &intVal
	}
	return nil
}

// Limit is the resolver for the limit field.
func (r *offsetPaginationInputResolver) Limit(ctx context.Context, obj *model.OffsetPaginationInput, data *int32) error {
	if data != nil {
		intVal := int(*data)
		obj.Limit = &intVal
	}
	return nil
}

// GameMoveMin is the resolver for the game_move_min field.
func (r *puzzleFilterResolver) GameMoveMin(ctx context.Context, obj *model.PuzzleFilter, data *int32) error {
	if data != nil {
		intVal := int(*data)
		obj.GameMoveMin = &intVal
	}
	return nil
}

// GameMoveMax is the resolver for the game_move_max field.
func (r *puzzleFilterResolver) GameMoveMax(ctx context.Context, obj *model.PuzzleFilter, data *int32) error {
	if data != nil {
		intVal := int(*data)
		obj.GameMoveMax = &intVal
	}
	return nil
}

// PrevCpMin is the resolver for the prev_cp_min field.
func (r *puzzleFilterResolver) PrevCpMin(ctx context.Context, obj *model.PuzzleFilter, data *int32) error {
	if data != nil {
		intVal := int(*data)
		obj.PrevCpMin = &intVal
	}
	return nil
}

// PrevCpMax is the resolver for the prev_cp_max field.
func (r *puzzleFilterResolver) PrevCpMax(ctx context.Context, obj *model.PuzzleFilter, data *int32) error {
	if data != nil {
		intVal := int(*data)
		obj.PrevCpMax = &intVal
	}
	return nil
}

// CpChangeMin is the resolver for the cp_change_min field.
func (r *puzzleFilterResolver) CpChangeMin(ctx context.Context, obj *model.PuzzleFilter, data *int32) error {
	if data != nil {
		intVal := int(*data)
		obj.CpChangeMin = &intVal
	}
	return nil
}

// CpChangeMax is the resolver for the cp_change_max field.
func (r *puzzleFilterResolver) CpChangeMax(ctx context.Context, obj *model.PuzzleFilter, data *int32) error {
	if data != nil {
		intVal := int(*data)
		obj.CpChangeMax = &intVal
	}
	return nil
}

// Game resolver is already implemented in game.resolvers.go

// Puzzle resolver is already implemented in puzzle.resolvers.go

// Query returns generated.QueryResolver implementation.
func (r *Resolver) Query() generated.QueryResolver { return &queryResolver{r} }

// OffsetPaginationInput returns generated.OffsetPaginationInputResolver implementation.
func (r *Resolver) OffsetPaginationInput() generated.OffsetPaginationInputResolver {
	return &offsetPaginationInputResolver{r}
}

// PuzzleFilter returns generated.PuzzleFilterResolver implementation.
func (r *Resolver) PuzzleFilter() generated.PuzzleFilterResolver { return &puzzleFilterResolver{r} }

// gameResolver is already defined in game.resolvers.go
// puzzleResolver is already defined in puzzle.resolvers.go
type queryResolver struct{ *Resolver }
type offsetPaginationInputResolver struct{ *Resolver }
type puzzleFilterResolver struct{ *Resolver }
