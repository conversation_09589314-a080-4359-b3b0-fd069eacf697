package resolvers

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/graphql/model"
	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/repository/fake"
	"github.com/chessticize/chessticize-server/internal/utils"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// Note: withUserID function is defined in game_stats_test.go

// Helper function to create a test game
func createTestGame(t *testing.T, gameRepo repository.IGameRepository, userID string, gameDate time.Time) {
	// Create a simple PGN
	pgn := "1. e4 e5 2. Nf3 Nc6"
	compressedPGN, err := utils.CompressPGN(pgn)
	require.NoError(t, err)

	game := &models.Game{
		ID:            uuid.New().String(),
		UserID:        userID,
		Platform:      models.LichessOrg,
		ChessUsername: "testuser",
		UserColor:     models.White,
		GameTime:      gameDate,
		CompressedPGN: compressedPGN,
		TimeControl:   "10+0",
		Rated:         true,
		WhitePlayer:   `{"username":"testuser","rating":1500}`,
		BlackPlayer:   `{"username":"opponent","rating":1600}`,
		Winner:        models.WinnerWhite,
		Result:        models.Mate,
		CreatedAt:     gameDate,
		UpdatedAt:     gameDate,
	}
	err = gameRepo.Create(context.Background(), game)
	require.NoError(t, err)
}

func TestMyGroupedPuzzleStats(t *testing.T) {
	// Create fake DB and repositories
	fakeDB := fake.NewDB(t)
	userRepo := fake.NewUserRepository(fakeDB)
	gameRepo := fake.NewGameRepository(fakeDB)
	puzzleRepo := fake.NewPuzzleRepository(fakeDB)

	// Create resolver
	resolver := NewResolver(userRepo, gameRepo, puzzleRepo)

	// Create a test user with unique email
	uniqueEmail := fmt.Sprintf("<EMAIL>", uuid.New().String())
	user := &models.User{
		ID:           uuid.New().String(),
		Email:        uniqueEmail,
		PasswordHash: "hash",
		RegisteredAt: time.Now(),
		UpdatedAt:    time.Now(),
	}
	err := userRepo.Create(context.Background(), user)
	require.NoError(t, err)

	// Create test puzzles with different dates
	startDate := time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)

	// Create 3 puzzles for each of 3 days
	for day := 0; day < 3; day++ {
		puzzleDate := startDate.AddDate(0, 0, day)

		for i := 0; i < 3; i++ {
			puzzle := &models.Puzzle{
				ID:          uuid.New().String(),
				UserID:      user.ID,
				GameID:      uuid.New().String(),
				GameMove:    10 + i,
				FEN:         "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
				Moves:       []string{"e2e4", "e7e5"},
				PrevCP:      0,
				CP:          100,
				Theme:       models.OpponentBlunderMissed,
				UserColor:   models.White,
				PuzzleColor: models.Black,
				Zugzwang:    false,
				Tags:        []string{"opening", "tactical"},
				CreatedAt:   puzzleDate,
				UpdatedAt:   puzzleDate,
			}
			err = puzzleRepo.Create(context.Background(), puzzle)
			require.NoError(t, err)
		}
	}

	// Create context with user ID
	ctx := withUserID(context.Background(), user.ID)

	// Create filter with time range
	endDate := startDate.AddDate(0, 0, 10)
	filter := &model.PuzzleFilter{
		GameStartTime: &startDate,
		GameEndTime:   &endDate,
	}

	// Create pagination
	offset := 0
	limit := 10
	pagination := &model.OffsetPaginationInput{
		Offset: &offset,
		Limit:  &limit,
	}

	// Test with day grouping
	t.Run("Group by Day", func(t *testing.T) {
		// Call resolver
		result, err := resolver.Query().MyGroupedPuzzleStats(ctx, filter, pagination, model.TimeGroupingDay, nil)

		// Verify result
		require.NoError(t, err)
		require.NotNil(t, result)
		assert.GreaterOrEqual(t, result.TotalCount, 0) // Should have some puzzles
		assert.GreaterOrEqual(t, len(result.Nodes), 0) // May have nodes

		// Verify first node
		if len(result.Nodes) > 0 {
			assert.NotNil(t, result.Nodes[0].StartTime)
			assert.NotNil(t, result.Nodes[0].EndTime)
			assert.NotNil(t, result.Nodes[0].Stats)
		}
	})
}

func TestMyGroupedGameStats(t *testing.T) {
	// Create fake DB and repositories
	fakeDB := fake.NewDB(t)
	userRepo := fake.NewUserRepository(fakeDB)
	gameRepo := fake.NewGameRepository(fakeDB)
	puzzleRepo := fake.NewPuzzleRepository(fakeDB)

	// Create resolver
	resolver := NewResolver(userRepo, gameRepo, puzzleRepo)

	// Create a test user with unique email
	uniqueEmail := fmt.Sprintf("<EMAIL>", uuid.New().String())
	user := &models.User{
		ID:           uuid.New().String(),
		Email:        uniqueEmail,
		PasswordHash: "hash",
		RegisteredAt: time.Now(),
		UpdatedAt:    time.Now(),
	}
	err := userRepo.Create(context.Background(), user)
	require.NoError(t, err)

	// Create test games with different dates
	startDate := time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)

	// Create 2 games for each of 2 weeks
	for week := 0; week < 2; week++ {
		gameDate := startDate.AddDate(0, 0, week*7)

		// Create two games for this week
		createTestGame(t, gameRepo, user.ID, gameDate)
		createTestGame(t, gameRepo, user.ID, gameDate)
	}

	// Create context with user ID
	ctx := withUserID(context.Background(), user.ID)

	// Create filter with time range
	endDate := startDate.AddDate(0, 0, 14)
	filter := &model.GameFilter{
		StartTime: &startDate,
		EndTime:   &endDate,
	}

	// Create pagination
	offset := 0
	limit := 10
	pagination := &model.OffsetPaginationInput{
		Offset: &offset,
		Limit:  &limit,
	}

	// Test with week grouping
	t.Run("Group by Week", func(t *testing.T) {
		// Call resolver
		result, err := resolver.Query().MyGroupedGameStats(ctx, filter, pagination, model.TimeGroupingWeek, nil)

		// Verify result
		require.NoError(t, err)
		require.NotNil(t, result)
		assert.GreaterOrEqual(t, result.TotalCount, 0) // Should have some games
		assert.GreaterOrEqual(t, len(result.Nodes), 0) // May have nodes

		// Verify first node
		if len(result.Nodes) > 0 {
			assert.NotNil(t, result.Nodes[0].StartTime)
			assert.NotNil(t, result.Nodes[0].EndTime)
			assert.NotNil(t, result.Nodes[0].Stats)
		}
	})
}
