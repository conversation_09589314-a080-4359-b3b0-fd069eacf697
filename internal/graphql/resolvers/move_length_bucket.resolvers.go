package resolvers

import (
	"context"

	"github.com/chessticize/chessticize-server/internal/graphql/generated"
	"github.com/chessticize/chessticize-server/internal/graphql/model"
)

// MoveLengthBucket returns a generated.MoveLengthBucketResolver implementation.
func (r *Resolver) MoveLengthBucket() generated.MoveLengthBucketResolver {
	return &moveLengthBucketResolver{r}
}

type moveLengthBucketResolver struct{ *Resolver }

// Length returns the length as an int32.
func (r *moveLengthBucketResolver) Length(ctx context.Context, obj *model.MoveLengthBucket) (int32, error) {
	return int32(obj.Length), nil
}

// Count returns the count as an int32.
func (r *moveLengthBucketResolver) Count(ctx context.Context, obj *model.MoveLengthBucket) (int32, error) {
	return int32(obj.Count), nil
}
