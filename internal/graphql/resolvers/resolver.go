package resolvers

import (
	"github.com/chessticize/chessticize-server/internal/graphql/generated"
	"github.com/chessticize/chessticize-server/internal/repository"
)

// This file will not be regenerated automatically.
//
// It serves as dependency injection for your app, add any dependencies you require here.

// Resolver is the resolver root.
type Resolver struct {
	UserRepo   repository.IUserRepository
	GameRepo   repository.IGameRepository
	PuzzleRepo repository.IPuzzleRepository
}

// GroupedGameStatsConnection returns generated.GroupedGameStatsConnectionResolver implementation.
func (r *Resolver) GroupedGameStatsConnection() generated.GroupedGameStatsConnectionResolver {
	return &groupedGameStatsConnectionResolver{r}
}

// GroupedPuzzleStatsConnection returns generated.GroupedPuzzleStatsConnectionResolver implementation.
func (r *Resolver) GroupedPuzzleStatsConnection() generated.GroupedPuzzleStatsConnectionResolver {
	return &groupedPuzzleStatsConnectionResolver{r}
}

// NewResolver creates a new resolver.
func NewResolver(userRepo repository.IUserRepository, gameRepo repository.IGameRepository, puzzleRepo repository.IPuzzleRepository) *Resolver {
	return &Resolver{
		UserRepo:   userRepo,
		GameRepo:   gameRepo,
		PuzzleRepo: puzzleRepo,
	}
}
