package resolvers

import (
	"github.com/chessticize/chessticize-server/internal/models"
)

// These conversion functions are used to convert from GraphQL string representations
// to internal model enum types. They're needed because GraphQL uses uppercase enum values
// while our internal models use lowercase values.

// GraphQLToColor converts a GraphQL Color enum string to the internal Color type
func GraphQLToColor(graphqlColor string) models.Color {
	switch graphqlColor {
	case "WHITE":
		return models.White
	case "BLACK":
		return models.Black
	default:
		// Fallback to direct conversion, though this should not happen with valid input
		return models.Color(graphqlColor)
	}
}

// GraphQLToChessPlatform converts a GraphQL ChessPlatform enum string to the internal ChessPlatform type
func GraphQLToChessPlatform(graphqlPlatform string) models.ChessPlatform {
	switch graphqlPlatform {
	case "CHESS_COM":
		return models.ChessDotCom
	case "LICHESS":
		return models.LichessOrg
	default:
		// Fallback to direct conversion, though this should not happen with valid input
		return models.ChessPlatform(graphqlPlatform)
	}
}

// GraphQLToPuzzleTheme converts a GraphQL PuzzleTheme enum string to the internal PuzzleTheme type
func GraphQLToPuzzleTheme(graphqlTheme string) models.PuzzleTheme {
	switch graphqlTheme {
	case "OPPONENT_MISTAKE_CAUGHT":
		return models.OpponentMistakeCaught
	case "OPPONENT_MISTAKE_MISSED":
		return models.OpponentMistakeMissed
	case "OPPONENT_BLUNDER_CAUGHT":
		return models.OpponentBlunderCaught
	case "OPPONENT_BLUNDER_MISSED":
		return models.OpponentBlunderMissed
	case "OWN_MISTAKE_PUNISHED":
		return models.OwnMistakePunished
	case "OWN_MISTAKE_ESCAPED":
		return models.OwnMistakeEscaped
	case "OWN_BLUNDER_PUNISHED":
		return models.OwnBlunderPunished
	case "OWN_BLUNDER_ESCAPED":
		return models.OwnBlunderEscaped
	default:
		// Fallback to direct conversion, though this should not happen with valid input
		return models.PuzzleTheme(graphqlTheme)
	}
}

// GraphQLToGameResult converts a GraphQL GameResult enum string to the internal GameResult type
func GraphQLToGameResult(graphqlResult string) models.GameResult {
	switch graphqlResult {
	case "MATE":
		return models.Mate
	case "RESIGN":
		return models.Resign
	case "DRAW":
		return models.Draw
	case "ABANDONED":
		return models.Abandoned
	case "OUT_OF_TIME":
		return models.OutOfTime
	default:
		// Fallback to direct conversion, though this should not happen with valid input
		return models.GameResult(graphqlResult)
	}
}

// GraphQLToWinner converts a GraphQL Winner enum string to the internal Winner type
func GraphQLToWinner(graphqlWinner string) models.Winner {
	switch graphqlWinner {
	case "WHITE":
		return models.WinnerWhite
	case "BLACK":
		return models.WinnerBlack
	case "NONE":
		return models.WinnerNone
	default:
		// Fallback to direct conversion, though this should not happen with valid input
		return models.Winner(graphqlWinner)
	}
}
