package resolvers

import (
	"context"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/graphql/model"
	"github.com/chessticize/chessticize-server/internal/middleware"
	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository/fake"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestEnumFiltering(t *testing.T) {
	// Setup fake repositories
	db := fake.NewDB(t)
	userRepo := fake.NewUserRepository(db)
	gameRepo := fake.NewGameRepository(db)
	puzzleRepo := fake.NewPuzzleRepository(db)

	// Create resolver
	resolver := &Resolver{
		UserRepo:   userRepo,
		GameRepo:   gameRepo,
		PuzzleRepo: puzzleRepo,
	}
	queryResolver := resolver.Query()

	// Create a test user
	user := &models.User{
		ID:           uuid.New().String(),
		Email:        "<EMAIL>",
		PasswordHash: "hash",
		RegisteredAt: time.Now(),
		UpdatedAt:    time.Now(),
	}
	err := userRepo.Create(context.Background(), user)
	require.NoError(t, err)

	// Create a test game with WHITE color
	pgn := "1. e4 e5 2. Nf3 Nc6 3. Bb5 a6"
	compressedPGN := []byte(pgn) // In a real scenario, this would be compressed

	game1 := &models.Game{
		ID:            uuid.New().String(),
		UserID:        user.ID,
		Platform:      models.ChessDotCom,
		ChessUsername: "testuser",
		UserColor:     models.White,
		GameTime:      time.Now(),
		CompressedPGN: compressedPGN,
		TimeControl:   "10+0",
		Rated:         true,
		WhitePlayer:   `{"username":"testuser","rating":1500}`,
		BlackPlayer:   `{"username":"opponent","rating":1550}`,
		Winner:        models.WinnerWhite,
		Result:        models.Mate,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}
	err = gameRepo.Create(context.Background(), game1)
	require.NoError(t, err)

	// Create a test game with BLACK color
	pgn2 := "1. d4 d5 2. c4 e6 3. Nc3 Nf6"
	compressedPGN2 := []byte(pgn2) // In a real scenario, this would be compressed

	game2 := &models.Game{
		ID:            uuid.New().String(),
		UserID:        user.ID,
		Platform:      models.ChessDotCom,
		ChessUsername: "testuser",
		UserColor:     models.Black,
		GameTime:      time.Now(),
		CompressedPGN: compressedPGN2,
		TimeControl:   "10+0",
		Rated:         true,
		WhitePlayer:   `{"username":"opponent","rating":1550}`,
		BlackPlayer:   `{"username":"testuser","rating":1500}`,
		Winner:        models.WinnerBlack,
		Result:        models.Mate,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}
	err = gameRepo.Create(context.Background(), game2)
	require.NoError(t, err)

	// Create a test puzzle with OPPONENT_BLUNDER_MISSED theme
	puzzle1 := &models.Puzzle{
		ID:          uuid.New().String(),
		GameID:      game1.ID,
		UserID:      user.ID,
		GameMove:    10,
		FEN:         "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
		Moves:       []string{"e2e4", "e7e5"},
		PrevCP:      0,
		CP:          100,
		Theme:       models.OpponentBlunderMissed,
		UserColor:   models.White,
		PuzzleColor: models.Black,
		Zugzwang:    false,
		Tags:        []string{"opening", "tactical"},
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
	err = puzzleRepo.Create(context.Background(), puzzle1)
	require.NoError(t, err)

	// Create a test puzzle with OPPONENT_BLUNDER_CAUGHT theme
	puzzle2 := &models.Puzzle{
		ID:          uuid.New().String(),
		GameID:      game2.ID,
		UserID:      user.ID,
		GameMove:    15,
		FEN:         "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
		Moves:       []string{"e2e4", "e7e5"},
		PrevCP:      0,
		CP:          300,
		Theme:       models.OpponentBlunderCaught,
		UserColor:   models.Black,
		PuzzleColor: models.White,
		Zugzwang:    false,
		Tags:        []string{"middlegame", "tactical"},
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
	err = puzzleRepo.Create(context.Background(), puzzle2)
	require.NoError(t, err)

	// Create a third test puzzle with OPPONENT_MISTAKE_MISSED theme for multiple theme testing
	puzzle3 := &models.Puzzle{
		ID:          uuid.New().String(),
		GameID:      game1.ID,
		UserID:      user.ID,
		GameMove:    20,
		FEN:         "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
		Moves:       []string{"d2d4", "d7d5", "c2c4"},
		PrevCP:      50,
		CP:          150,
		Theme:       models.OpponentMistakeMissed,
		UserColor:   models.White,
		PuzzleColor: models.Black,
		Zugzwang:    false,
		Tags:        []string{"opening", "positional"},
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
	err = puzzleRepo.Create(context.Background(), puzzle3)
	require.NoError(t, err)

	// Create context with user ID
	ctx := context.WithValue(context.Background(), middleware.UserIDKey, user.ID)

	// Test 1: Filter games by WHITE color
	t.Run("Filter games by WHITE color", func(t *testing.T) {
		// Create WHITE color filter
		whiteColor := "WHITE"
		filter := &model.GameFilter{
			UserColor: (*models.Color)(&whiteColor),
		}

		// Set pagination
		offset := 0
		limit := 10
		pagination := &model.OffsetPaginationInput{
			Offset: &offset,
			Limit:  &limit,
		}

		// Execute query
		result, err := queryResolver.MyGames(ctx, filter, pagination, nil)
		require.NoError(t, err)

		// Verify result
		assert.Equal(t, int32(1), result.TotalCount)
		assert.Len(t, result.Edges, 1)
		assert.Equal(t, game1.ID, result.Edges[0].Node.ID)
	})

	// Test 2: Filter games by BLACK color
	t.Run("Filter games by BLACK color", func(t *testing.T) {
		// Create BLACK color filter
		blackColor := "BLACK"
		filter := &model.GameFilter{
			UserColor: (*models.Color)(&blackColor),
		}

		// Set pagination
		offset := 0
		limit := 10
		pagination := &model.OffsetPaginationInput{
			Offset: &offset,
			Limit:  &limit,
		}

		// Execute query
		result, err := queryResolver.MyGames(ctx, filter, pagination, nil)
		require.NoError(t, err)

		// Verify result
		assert.Equal(t, int32(1), result.TotalCount)
		assert.Len(t, result.Edges, 1)
		assert.Equal(t, game2.ID, result.Edges[0].Node.ID)
	})

	// Test 3: Filter games by CHESS_COM platform
	t.Run("Filter games by CHESS_COM platform", func(t *testing.T) {
		// Create CHESS_COM platform filter
		platform := "CHESS_COM"
		filter := &model.GameFilter{
			Platform: (*models.ChessPlatform)(&platform),
		}

		// Set pagination
		offset := 0
		limit := 10
		pagination := &model.OffsetPaginationInput{
			Offset: &offset,
			Limit:  &limit,
		}

		// Execute query
		result, err := queryResolver.MyGames(ctx, filter, pagination, nil)
		require.NoError(t, err)

		// Verify result
		assert.Equal(t, int32(2), result.TotalCount)
		assert.Len(t, result.Edges, 2)
	})

	// Test 4: Filter puzzles by OPPONENT_BLUNDER_MISSED theme
	t.Run("Filter puzzles by OPPONENT_BLUNDER_MISSED theme", func(t *testing.T) {
		// Create OPPONENT_BLUNDER_MISSED theme filter
		theme := models.PuzzleTheme("OPPONENT_BLUNDER_MISSED")
		filter := &model.PuzzleFilter{
			Themes: []models.PuzzleTheme{theme},
		}

		// Set pagination
		offset := 0
		limit := 10
		pagination := &model.OffsetPaginationInput{
			Offset: &offset,
			Limit:  &limit,
		}

		// Execute query
		result, err := queryResolver.MyPuzzles(ctx, filter, pagination, nil)
		require.NoError(t, err)

		// Verify result
		assert.Equal(t, int32(1), result.TotalCount)
		assert.Len(t, result.Edges, 1)
		assert.Equal(t, puzzle1.ID, result.Edges[0].Node.ID)
	})

	// Test 5: Filter puzzles by OPPONENT_BLUNDER_CAUGHT theme
	t.Run("Filter puzzles by OPPONENT_BLUNDER_CAUGHT theme", func(t *testing.T) {
		// Create OPPONENT_BLUNDER_CAUGHT theme filter
		theme := models.PuzzleTheme("OPPONENT_BLUNDER_CAUGHT")
		filter := &model.PuzzleFilter{
			Themes: []models.PuzzleTheme{theme},
		}

		// Set pagination
		offset := 0
		limit := 10
		pagination := &model.OffsetPaginationInput{
			Offset: &offset,
			Limit:  &limit,
		}

		// Execute query
		result, err := queryResolver.MyPuzzles(ctx, filter, pagination, nil)
		require.NoError(t, err)

		// Verify result
		assert.Equal(t, int32(1), result.TotalCount)
		assert.Len(t, result.Edges, 1)
		assert.Equal(t, puzzle2.ID, result.Edges[0].Node.ID)
	})

	// Test 6: Filter puzzles by WHITE user color
	t.Run("Filter puzzles by WHITE user color", func(t *testing.T) {
		// Create WHITE user color filter
		whiteColor := "WHITE"
		filter := &model.PuzzleFilter{
			UserColor: (*models.Color)(&whiteColor),
		}

		// Set pagination
		offset := 0
		limit := 10
		pagination := &model.OffsetPaginationInput{
			Offset: &offset,
			Limit:  &limit,
		}

		// Execute query
		result, err := queryResolver.MyPuzzles(ctx, filter, pagination, nil)
		require.NoError(t, err)

		// Verify result - should return puzzle1 and puzzle3 (both have WHITE user color)
		assert.Equal(t, int32(2), result.TotalCount)
		assert.Len(t, result.Edges, 2)

		// Collect returned puzzle IDs
		returnedIDs := make([]string, len(result.Edges))
		for i, edge := range result.Edges {
			returnedIDs[i] = edge.Node.ID
		}

		// Verify both WHITE user color puzzles are returned
		assert.Contains(t, returnedIDs, puzzle1.ID) // WHITE user color
		assert.Contains(t, returnedIDs, puzzle3.ID) // WHITE user color
	})

	// Test 7: Filter puzzles by BLACK user color
	t.Run("Filter puzzles by BLACK user color", func(t *testing.T) {
		// Create BLACK user color filter
		blackColor := "BLACK"
		filter := &model.PuzzleFilter{
			UserColor: (*models.Color)(&blackColor),
		}

		// Set pagination
		offset := 0
		limit := 10
		pagination := &model.OffsetPaginationInput{
			Offset: &offset,
			Limit:  &limit,
		}

		// Execute query
		result, err := queryResolver.MyPuzzles(ctx, filter, pagination, nil)
		require.NoError(t, err)

		// Verify result
		assert.Equal(t, int32(1), result.TotalCount)
		assert.Len(t, result.Edges, 1)
		assert.Equal(t, puzzle2.ID, result.Edges[0].Node.ID)
	})

	// Test 8: Filter puzzles by BLACK puzzle color
	t.Run("Filter puzzles by BLACK puzzle color", func(t *testing.T) {
		// Create BLACK puzzle color filter
		blackColor := "BLACK"
		filter := &model.PuzzleFilter{
			PuzzleColor: (*models.Color)(&blackColor),
		}

		// Set pagination
		offset := 0
		limit := 10
		pagination := &model.OffsetPaginationInput{
			Offset: &offset,
			Limit:  &limit,
		}

		// Execute query
		result, err := queryResolver.MyPuzzles(ctx, filter, pagination, nil)
		require.NoError(t, err)

		// Verify result - should return puzzle1 and puzzle3 (both have BLACK puzzle color)
		assert.Equal(t, int32(2), result.TotalCount)
		assert.Len(t, result.Edges, 2)

		// Collect returned puzzle IDs
		returnedIDs := make([]string, len(result.Edges))
		for i, edge := range result.Edges {
			returnedIDs[i] = edge.Node.ID
		}

		// Verify both BLACK puzzle color puzzles are returned
		assert.Contains(t, returnedIDs, puzzle1.ID) // BLACK puzzle color
		assert.Contains(t, returnedIDs, puzzle3.ID) // BLACK puzzle color
	})

	// Test 9: Filter puzzles by multiple themes (OR relation)
	t.Run("Filter puzzles by multiple themes", func(t *testing.T) {
		// Create filter with multiple themes: OPPONENT_BLUNDER_MISSED and OPPONENT_MISTAKE_MISSED
		themes := []models.PuzzleTheme{
			models.PuzzleTheme("OPPONENT_BLUNDER_MISSED"),
			models.PuzzleTheme("OPPONENT_MISTAKE_MISSED"),
		}
		filter := &model.PuzzleFilter{
			Themes: themes,
		}

		// Set pagination
		offset := 0
		limit := 10
		pagination := &model.OffsetPaginationInput{
			Offset: &offset,
			Limit:  &limit,
		}

		// Execute query
		result, err := queryResolver.MyPuzzles(ctx, filter, pagination, nil)
		require.NoError(t, err)

		// Verify result - should return puzzle1 (OPPONENT_BLUNDER_MISSED) and puzzle3 (OPPONENT_MISTAKE_MISSED)
		assert.Equal(t, int32(2), result.TotalCount)
		assert.Len(t, result.Edges, 2)

		// Collect returned puzzle IDs
		returnedIDs := make([]string, len(result.Edges))
		for i, edge := range result.Edges {
			returnedIDs[i] = edge.Node.ID
		}

		// Verify both expected puzzles are returned
		assert.Contains(t, returnedIDs, puzzle1.ID)    // OPPONENT_BLUNDER_MISSED
		assert.Contains(t, returnedIDs, puzzle3.ID)    // OPPONENT_MISTAKE_MISSED
		assert.NotContains(t, returnedIDs, puzzle2.ID) // OPPONENT_BLUNDER_CAUGHT should not be included
	})

	// Test 10: Filter puzzles by all themes (should return all puzzles)
	t.Run("Filter puzzles by all themes", func(t *testing.T) {
		// Create filter with all themes
		themes := []models.PuzzleTheme{
			models.PuzzleTheme("OPPONENT_BLUNDER_MISSED"),
			models.PuzzleTheme("OPPONENT_BLUNDER_CAUGHT"),
			models.PuzzleTheme("OPPONENT_MISTAKE_MISSED"),
		}
		filter := &model.PuzzleFilter{
			Themes: themes,
		}

		// Set pagination
		offset := 0
		limit := 10
		pagination := &model.OffsetPaginationInput{
			Offset: &offset,
			Limit:  &limit,
		}

		// Execute query
		result, err := queryResolver.MyPuzzles(ctx, filter, pagination, nil)
		require.NoError(t, err)

		// Verify result - should return all 3 puzzles
		assert.Equal(t, int32(3), result.TotalCount)
		assert.Len(t, result.Edges, 3)

		// Collect returned puzzle IDs
		returnedIDs := make([]string, len(result.Edges))
		for i, edge := range result.Edges {
			returnedIDs[i] = edge.Node.ID
		}

		// Verify all puzzles are returned
		assert.Contains(t, returnedIDs, puzzle1.ID)
		assert.Contains(t, returnedIDs, puzzle2.ID)
		assert.Contains(t, returnedIDs, puzzle3.ID)
	})

	// Test 11: Filter puzzles excluding "opponent mistakes caught" (demonstrating the use case)
	t.Run("Filter puzzles excluding opponent mistakes caught", func(t *testing.T) {
		// Create filter with themes excluding OPPONENT_BLUNDER_CAUGHT and OPPONENT_MISTAKE_CAUGHT
		// This demonstrates the user's use case of finding themes that are "interesting" for improvement
		themes := []models.PuzzleTheme{
			models.PuzzleTheme("OPPONENT_BLUNDER_MISSED"),
			models.PuzzleTheme("OPPONENT_MISTAKE_MISSED"),
			// Intentionally excluding OPPONENT_BLUNDER_CAUGHT and OPPONENT_MISTAKE_CAUGHT
		}
		filter := &model.PuzzleFilter{
			Themes: themes,
		}

		// Set pagination
		offset := 0
		limit := 10
		pagination := &model.OffsetPaginationInput{
			Offset: &offset,
			Limit:  &limit,
		}

		// Execute query
		result, err := queryResolver.MyPuzzles(ctx, filter, pagination, nil)
		require.NoError(t, err)

		// Verify result - should return only puzzles where the user missed opportunities
		assert.Equal(t, int32(2), result.TotalCount)
		assert.Len(t, result.Edges, 2)

		// Collect returned puzzle IDs
		returnedIDs := make([]string, len(result.Edges))
		for i, edge := range result.Edges {
			returnedIDs[i] = edge.Node.ID
		}

		// Verify only "missed" puzzles are returned (areas for improvement)
		assert.Contains(t, returnedIDs, puzzle1.ID)    // OPPONENT_BLUNDER_MISSED
		assert.Contains(t, returnedIDs, puzzle3.ID)    // OPPONENT_MISTAKE_MISSED
		assert.NotContains(t, returnedIDs, puzzle2.ID) // OPPONENT_BLUNDER_CAUGHT (not interesting for improvement)
	})
}
