package resolvers

import (
	"context"

	"github.com/chessticize/chessticize-server/internal/graphql/generated"
	"github.com/chessticize/chessticize-server/internal/graphql/model"
	"github.com/chessticize/chessticize-server/internal/models"
)

// ResultCount returns a generated.ResultCountResolver implementation.
func (r *Resolver) ResultCount() generated.ResultCountResolver {
	return &resultCountResolver{r}
}

type resultCountResolver struct{ *Resolver }

// Result returns the result.
func (r *resultCountResolver) Result(ctx context.Context, obj *model.ResultCount) (models.GameResult, error) {
	return models.GameResult(obj.Result), nil
}

// Count returns the count as an int32.
func (r *resultCountResolver) Count(ctx context.Context, obj *model.ResultCount) (int32, error) {
	return int32(obj.Count), nil
}
