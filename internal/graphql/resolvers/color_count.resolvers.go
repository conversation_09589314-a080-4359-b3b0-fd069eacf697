package resolvers

import (
	"context"

	"github.com/chessticize/chessticize-server/internal/graphql/generated"
	"github.com/chessticize/chessticize-server/internal/graphql/model"
	"github.com/chessticize/chessticize-server/internal/models"
)

// ColorCount returns a generated.ColorCountResolver implementation.
func (r *Resolver) ColorCount() generated.ColorCountResolver {
	return &colorCountResolver{r}
}

type colorCountResolver struct{ *Resolver }

// Color returns the color.
func (r *colorCountResolver) Color(ctx context.Context, obj *model.ColorCount) (models.Color, error) {
	return models.Color(obj.Color), nil
}

// Count returns the count as an int32.
func (r *colorCountResolver) Count(ctx context.Context, obj *model.ColorCount) (int32, error) {
	return int32(obj.Count), nil
}
