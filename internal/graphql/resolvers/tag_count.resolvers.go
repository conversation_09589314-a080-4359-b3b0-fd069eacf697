package resolvers

import (
	"context"

	"github.com/chessticize/chessticize-server/internal/graphql/generated"
	"github.com/chessticize/chessticize-server/internal/graphql/model"
)

// TagCount returns a generated.TagCountResolver implementation.
func (r *Resolver) TagCount() generated.TagCountResolver {
	return &tagCountResolver{r}
}

type tagCountResolver struct{ *Resolver }

// Count returns the count as an int32.
func (r *tagCountResolver) Count(ctx context.Context, obj *model.TagCount) (int32, error) {
	return int32(obj.Count), nil
}
