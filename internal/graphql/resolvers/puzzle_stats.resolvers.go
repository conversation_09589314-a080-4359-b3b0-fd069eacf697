package resolvers

import (
	"context"

	"github.com/chessticize/chessticize-server/internal/graphql/generated"
	"github.com/chessticize/chessticize-server/internal/graphql/model"
)

// PuzzleStats returns a generated.PuzzleStatsResolver implementation.
func (r *Resolver) PuzzleStats() generated.PuzzleStatsResolver {
	return &puzzleStatsResolver{r}
}

type puzzleStatsResolver struct{ *Resolver }

// TotalCount returns the total count as an int32.
func (r *puzzleStatsResolver) TotalCount(ctx context.Context, obj *model.PuzzleStats) (int32, error) {
	return int32(obj.TotalCount), nil
}

// UniqueGameCount returns the unique game count as an int32.
func (r *puzzleStatsResolver) UniqueGameCount(ctx context.Context, obj *model.PuzzleStats) (int32, error) {
	return int32(obj.UniqueGameCount), nil
}
