package resolvers

import (
	"context"

	"github.com/chessticize/chessticize-server/internal/graphql/generated"
	"github.com/chessticize/chessticize-server/internal/graphql/model"
	"github.com/chessticize/chessticize-server/internal/models"
)

// PlatformCount returns a generated.PlatformCountResolver implementation.
func (r *Resolver) PlatformCount() generated.PlatformCountResolver {
	return &platformCountResolver{r}
}

type platformCountResolver struct{ *Resolver }

// Platform returns the platform.
func (r *platformCountResolver) Platform(ctx context.Context, obj *model.PlatformCount) (models.ChessPlatform, error) {
	return models.ChessPlatform(obj.Platform), nil
}

// Count returns the count as an int32.
func (r *platformCountResolver) Count(ctx context.Context, obj *model.PlatformCount) (int32, error) {
	return int32(obj.Count), nil
}
