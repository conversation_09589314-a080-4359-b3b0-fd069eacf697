package resolvers

import (
	"context"
	"errors"
	"time"

	"github.com/chessticize/chessticize-server/internal/graphql/model"
	"github.com/chessticize/chessticize-server/internal/middleware"
	"github.com/chessticize/chessticize-server/internal/repository/common"
)

// MyGroupedPuzzleStats returns grouped statistics about the current user's puzzles
func (r *queryResolver) MyGroupedPuzzleStats(ctx context.Context, filter *model.PuzzleFilter, pagination *model.OffsetPaginationInput, groupUnit model.TimeGrouping, groupLength *int32) (*model.GroupedPuzzleStatsConnection, error) {
	// Get user ID from context
	userID := middleware.GetUserIDFromContext(ctx)
	if userID == "" {
		return nil, errors.New("unauthorized")
	}

	// Convert filter to repository filter
	repoFilter := convertPuzzleFilter(filter)

	// Set default pagination if not provided
	offset, limit := 0, MaxPaginationLimit
	if pagination != nil {
		if pagination.Offset != nil {
			offset = *pagination.Offset
		}
		if pagination.Limit != nil && *pagination.Limit > 0 {
			limit = *pagination.Limit
		}
	}

	// Create TimeGrouping struct
	grouping := &common.TimeGrouping{
		Unit:   common.TimeGroupingUnit(groupUnit),
		Length: 1,
	}

	// Set group length if provided
	if groupLength != nil {
		grouping.Length = int(*groupLength)
	}

	// Get grouped stats from repository
	repoStats, err := r.PuzzleRepo.GetPuzzleStats(ctx, userID, repoFilter, offset, limit, grouping)
	if err != nil {
		return nil, err
	}

	// Convert to GraphQL model
	nodes := make([]*model.GroupedPuzzleStats, 0, len(repoStats))
	for _, stats := range repoStats {
		// Add to result
		nodes = append(nodes, &model.GroupedPuzzleStats{
			StartTime: *stats.PeriodStart,
			EndTime:   *stats.PeriodEnd,
			Stats:     model.ConvertPuzzleStats(stats),
		})
	}

	return &model.GroupedPuzzleStatsConnection{
		Nodes:      nodes,
		TotalCount: len(nodes),
	}, nil
}

// MyGroupedGameStats returns grouped statistics about the current user's games
func (r *queryResolver) MyGroupedGameStats(ctx context.Context, filter *model.GameFilter, pagination *model.OffsetPaginationInput, groupUnit model.TimeGrouping, groupLength *int32) (*model.GroupedGameStatsConnection, error) {
	// Get user ID from context
	userID := middleware.GetUserIDFromContext(ctx)
	if userID == "" {
		return nil, errors.New("unauthorized")
	}

	// Convert filter to repository filter
	repoFilter := convertGameFilter(filter)

	// Set default pagination if not provided
	offset, limit := 0, MaxPaginationLimit
	if pagination != nil {
		if pagination.Offset != nil {
			offset = *pagination.Offset
		}
		if pagination.Limit != nil && *pagination.Limit > 0 {
			limit = *pagination.Limit
		}
	}

	// Create TimeGrouping struct
	grouping := &common.TimeGrouping{
		Unit:   common.TimeGroupingUnit(groupUnit),
		Length: 1,
	}

	// Set group length if provided
	if groupLength != nil {
		grouping.Length = int(*groupLength)
	}

	// Get grouped stats from repository
	repoStats, err := r.GameRepo.GetGameStats(ctx, userID, repoFilter, offset, limit, grouping)
	if err != nil {
		return nil, err
	}

	// Convert to GraphQL model
	nodes := make([]*model.GroupedGameStats, 0, len(repoStats))
	for _, stats := range repoStats {
		// Add to result
		nodes = append(nodes, &model.GroupedGameStats{
			StartTime: *stats.PeriodStart,
			EndTime:   *stats.PeriodEnd,
			Stats:     model.ConvertGameStats(stats),
		})
	}

	return &model.GroupedGameStatsConnection{
		Nodes:      nodes,
		TotalCount: len(nodes),
	}, nil
}

// TimePeriod represents a time period with start and end times
type TimePeriod struct {
	Start time.Time
	End   time.Time
}

// TimeRange represents a range of time with start and end times
type TimeRange struct {
	Start time.Time
	End   time.Time
}
