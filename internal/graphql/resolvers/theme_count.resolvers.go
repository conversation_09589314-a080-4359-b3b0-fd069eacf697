package resolvers

import (
	"context"

	"github.com/chessticize/chessticize-server/internal/graphql/generated"
	"github.com/chessticize/chessticize-server/internal/graphql/model"
	"github.com/chessticize/chessticize-server/internal/models"
)

// ThemeCount returns a generated.ThemeCountResolver implementation.
func (r *Resolver) ThemeCount() generated.ThemeCountResolver {
	return &themeCountResolver{r}
}

type themeCountResolver struct{ *Resolver }

// Theme returns the theme.
func (r *themeCountResolver) Theme(ctx context.Context, obj *model.ThemeCount) (models.PuzzleTheme, error) {
	return models.PuzzleTheme(obj.Theme), nil
}

// Count returns the count as an int32.
func (r *themeCountResolver) Count(ctx context.Context, obj *model.ThemeCount) (int32, error) {
	return int32(obj.Count), nil
}
