package resolvers

import (
	"context"
	"strings"

	"github.com/chessticize/chessticize-server/internal/models"
)

// These resolvers convert internal model enum values (lowercase) to GraphQL enum values (uppercase)
// for fields in the GraphQL response.

// Theme resolver converts the internal theme value to the GraphQL enum value
func (r *puzzleResolver) Theme(ctx context.Context, obj *models.Puzzle) (string, error) {
	// Convert from internal model enum (lowercase) to GraphQL enum (uppercase)
	switch obj.Theme {
	case models.OpponentMistakeCaught:
		return "OPPONENT_MISTAKE_CAUGHT", nil
	case models.OpponentMistakeMissed:
		return "OPPONENT_MISTAKE_MISSED", nil
	case models.OpponentBlunderCaught:
		return "OPPONENT_BLUNDER_CAUGHT", nil
	case models.OpponentBlunderMissed:
		return "OPPONENT_BLUNDER_MISSED", nil
	case models.OwnMistakePunished:
		return "OWN_MISTAKE_PUNISHED", nil
	case models.OwnMistakeEscaped:
		return "OWN_MISTAKE_ESCAPED", nil
	case models.OwnBlunderPunished:
		return "OWN_BLUNDER_PUNISHED", nil
	case models.OwnBlunderEscaped:
		return "OWN_BLUNDER_ESCAPED", nil
	default:
		// Fallback to uppercase
		return strings.ToUpper(string(obj.Theme)), nil
	}
}

// UserColor resolver converts the internal color value to the GraphQL enum value
func (r *puzzleResolver) UserColor(ctx context.Context, obj *models.Puzzle) (string, error) {
	// Convert from internal model enum (lowercase) to GraphQL enum (uppercase)
	switch obj.UserColor {
	case models.White:
		return "WHITE", nil
	case models.Black:
		return "BLACK", nil
	default:
		// Fallback to uppercase
		return strings.ToUpper(string(obj.UserColor)), nil
	}
}

// PuzzleColor resolver converts the internal color value to the GraphQL enum value
func (r *puzzleResolver) PuzzleColor(ctx context.Context, obj *models.Puzzle) (string, error) {
	// Convert from internal model enum (lowercase) to GraphQL enum (uppercase)
	switch obj.PuzzleColor {
	case models.White:
		return "WHITE", nil
	case models.Black:
		return "BLACK", nil
	default:
		// Fallback to uppercase
		return strings.ToUpper(string(obj.PuzzleColor)), nil
	}
}

// UserColor resolver converts the internal color value to the GraphQL enum value
func (r *gameResolver) UserColor(ctx context.Context, obj *models.Game) (string, error) {
	// Convert from internal model enum (lowercase) to GraphQL enum (uppercase)
	switch obj.UserColor {
	case models.White:
		return "WHITE", nil
	case models.Black:
		return "BLACK", nil
	default:
		// Fallback to uppercase
		return strings.ToUpper(string(obj.UserColor)), nil
	}
}

// Platform resolver converts the internal platform value to the GraphQL enum value
func (r *gameResolver) Platform(ctx context.Context, obj *models.Game) (string, error) {
	// Convert from internal model enum (lowercase) to GraphQL enum (uppercase)
	switch obj.Platform {
	case models.ChessDotCom:
		return "CHESS_COM", nil
	case models.LichessOrg:
		return "LICHESS", nil
	default:
		// Fallback to uppercase
		return strings.ToUpper(string(obj.Platform)), nil
	}
}

// Winner resolver converts the internal winner value to the GraphQL enum value
func (r *gameResolver) Winner(ctx context.Context, obj *models.Game) (string, error) {
	// Convert from internal model enum (lowercase) to GraphQL enum (uppercase)
	switch obj.Winner {
	case models.WinnerWhite:
		return "WHITE", nil
	case models.WinnerBlack:
		return "BLACK", nil
	case models.WinnerNone:
		return "NONE", nil
	default:
		// Fallback to uppercase
		return strings.ToUpper(string(obj.Winner)), nil
	}
}

// Result resolver converts the internal result value to the GraphQL enum value
func (r *gameResolver) Result(ctx context.Context, obj *models.Game) (string, error) {
	// Convert from internal model enum (lowercase) to GraphQL enum (uppercase)
	switch obj.Result {
	case models.Mate:
		return "MATE", nil
	case models.Resign:
		return "RESIGN", nil
	case models.Draw:
		return "DRAW", nil
	case models.Abandoned:
		return "ABANDONED", nil
	case models.OutOfTime:
		return "OUT_OF_TIME", nil
	default:
		// Fallback to uppercase
		return strings.ToUpper(string(obj.Result)), nil
	}
}
