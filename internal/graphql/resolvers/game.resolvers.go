package resolvers

import (
	"context"

	"github.com/chessticize/chessticize-server/internal/graphql/generated"
	"github.com/chessticize/chessticize-server/internal/graphql/model"
	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
)

// Pgn is a custom resolver for the Game.pgn field
func (r *gameResolver) Pgn(ctx context.Context, obj *models.Game) (string, error) {
	// If CompressedPGN is not loaded (e.g., when loaded via GetByIDWithoutPGN),
	// load the full game with PGN
	if len(obj.CompressedPGN) == 0 {
		// Load the game with PGN
		game, err := r.GameRepo.GetByID(ctx, obj.ID)
		if err != nil {
			return "", err
		}
		obj.CompressedPGN = game.CompressedPGN
	}

	// Use the existing GetPGN method to decompress the PGN
	return obj.GetPGN()
}

// Puzzles is a custom resolver for the Game.puzzles field
func (r *gameResolver) Puzzles(ctx context.Context, obj *models.Game, filter *model.PuzzleFilter) ([]*models.Puzzle, error) {
	// Get all puzzles for the game
	puzzles, err := r.PuzzleRepo.GetByGameID(ctx, obj.ID)
	if err != nil {
		return nil, err
	}

	// If no filter, return all puzzles
	if filter == nil {
		// Convert []models.Puzzle to []*models.Puzzle
		result := make([]*models.Puzzle, len(puzzles))
		for i := range puzzles {
			result[i] = &puzzles[i]
		}
		return result, nil
	}

	// Apply filters
	var filteredPuzzles []*models.Puzzle
	for i := range puzzles {
		puzzle := &puzzles[i]

		// Check if puzzle matches the filter
		if matchesPuzzleFilter(puzzle, filter) {
			filteredPuzzles = append(filteredPuzzles, puzzle)
		}
	}

	return filteredPuzzles, nil
}

// matchesPuzzleFilter checks if a puzzle matches the given filter
func matchesPuzzleFilter(puzzle *models.Puzzle, filter *model.PuzzleFilter) bool {
	// Convert filter to repository filter
	repoFilter := repository.PuzzleFilter{}

	// Handle tags with OR relation
	repoFilter.Tags = filter.Tags

	// Add theme filters (OR relation)
	if len(filter.Themes) > 0 {
		for _, theme := range filter.Themes {
			// Convert GraphQL enum values to internal values
			convertedTheme := GraphQLToPuzzleTheme(string(theme))
			repoFilter.Themes = append(repoFilter.Themes, convertedTheme)
		}
	}

	// Add color filters
	if filter.UserColor != nil {
		userColor := GraphQLToColor(string(*filter.UserColor))
		repoFilter.UserColor = &userColor
	}

	if filter.PuzzleColor != nil {
		puzzleColor := GraphQLToColor(string(*filter.PuzzleColor))
		repoFilter.PuzzleColor = &puzzleColor
	}

	// Add game move range filters
	repoFilter.GameMoveMin = filter.GameMoveMin
	repoFilter.GameMoveMax = filter.GameMoveMax

	// Add CP range filters
	repoFilter.PrevCpMin = filter.PrevCpMin
	repoFilter.PrevCpMax = filter.PrevCpMax

	// Add CP change range filters
	repoFilter.CpChangeMin = filter.CpChangeMin
	repoFilter.CpChangeMax = filter.CpChangeMax

	// Check tags (OR relation)
	if len(repoFilter.Tags) > 0 {
		tagMatch := false
		for _, filterTag := range repoFilter.Tags {
			for _, puzzleTag := range puzzle.Tags {
				if filterTag == puzzleTag {
					tagMatch = true
					break
				}
			}
			if tagMatch {
				break
			}
		}
		if !tagMatch {
			return false
		}
	}

	// Check themes (OR relation)
	if len(repoFilter.Themes) > 0 {
		themeMatch := false
		for _, filterTheme := range repoFilter.Themes {
			if puzzle.Theme == filterTheme {
				themeMatch = true
				break
			}
		}
		if !themeMatch {
			return false
		}
	}

	// Check user color
	if repoFilter.UserColor != nil && puzzle.UserColor != *repoFilter.UserColor {
		return false
	}

	// Check puzzle color
	if repoFilter.PuzzleColor != nil && puzzle.PuzzleColor != *repoFilter.PuzzleColor {
		return false
	}

	// Check game move range
	if repoFilter.GameMoveMin != nil && puzzle.GameMove < *repoFilter.GameMoveMin {
		return false
	}
	if repoFilter.GameMoveMax != nil && puzzle.GameMove > *repoFilter.GameMoveMax {
		return false
	}

	// Check prev CP range
	if repoFilter.PrevCpMin != nil && puzzle.PrevCP < *repoFilter.PrevCpMin {
		return false
	}
	if repoFilter.PrevCpMax != nil && puzzle.PrevCP > *repoFilter.PrevCpMax {
		return false
	}

	// Check CP change range
	cpChange := puzzle.CP - puzzle.PrevCP
	if repoFilter.CpChangeMin != nil && cpChange < *repoFilter.CpChangeMin {
		return false
	}
	if repoFilter.CpChangeMax != nil && cpChange > *repoFilter.CpChangeMax {
		return false
	}

	return true
}

// Game returns the gameResolver implementation
func (r *Resolver) Game() generated.GameResolver {
	return &gameResolver{r}
}

// gameResolver implements GameResolver interface
type gameResolver struct {
	*Resolver
}
