package resolvers

import (
	"context"

	"github.com/chessticize/chessticize-server/internal/graphql/model"
)

// Resolver types for grouped stats
type groupedPuzzleStatsConnectionResolver struct{ *Resolver }
type groupedGameStatsConnectionResolver struct{ *Resolver }

// TotalCount returns the total count as an int32.
func (r *groupedPuzzleStatsConnectionResolver) TotalCount(ctx context.Context, obj *model.GroupedPuzzleStatsConnection) (int32, error) {
	return int32(obj.TotalCount), nil
}

// TotalCount returns the total count as an int32.
func (r *groupedGameStatsConnectionResolver) TotalCount(ctx context.Context, obj *model.GroupedGameStatsConnection) (int32, error) {
	return int32(obj.TotalCount), nil
}
