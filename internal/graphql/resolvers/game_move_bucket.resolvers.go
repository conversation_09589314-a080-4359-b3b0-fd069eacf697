package resolvers

import (
	"context"

	"github.com/chessticize/chessticize-server/internal/graphql/generated"
	"github.com/chessticize/chessticize-server/internal/graphql/model"
)

// GameMoveBucket returns a generated.GameMoveBucketResolver implementation.
func (r *Resolver) GameMoveBucket() generated.GameMoveBucketResolver {
	return &gameMoveBucketResolver{r}
}

type gameMoveBucketResolver struct{ *Resolver }

// Min<PERSON>ove returns the min_move as an int32.
func (r *gameMoveBucketResolver) MinMove(ctx context.Context, obj *model.GameMoveBucket) (int32, error) {
	return int32(obj.MinMove), nil
}

// <PERSON><PERSON>ove returns the max_move as an int32.
func (r *gameMoveBucketResolver) MaxMove(ctx context.Context, obj *model.GameMoveBucket) (int32, error) {
	return int32(obj.<PERSON><PERSON>ove), nil
}

// Count returns the count as an int32.
func (r *gameMoveBucketResolver) Count(ctx context.Context, obj *model.GameMoveBucket) (int32, error) {
	return int32(obj.Count), nil
}
