package resolvers

import (
	"github.com/chessticize/chessticize-server/internal/graphql/model"
	"github.com/chessticize/chessticize-server/internal/repository"
)

// convertPuzzleFilter converts a GraphQL puzzle filter to a repository filter
func convertPuzzleFilter(filter *model.PuzzleFilter) repository.PuzzleFilter {
	if filter == nil {
		return repository.PuzzleFilter{}
	}

	repoFilter := repository.PuzzleFilter{
		Tags:          filter.Tags,
		GameStartTime: filter.GameStartTime,
		GameEndTime:   filter.GameEndTime,
	}

	// Add theme filters (OR relation)
	if len(filter.Themes) > 0 {
		for _, theme := range filter.Themes {
			// Convert GraphQL enum values to internal values
			convertedTheme := GraphQLToPuzzleTheme(string(theme))
			repoFilter.Themes = append(repoFilter.Themes, convertedTheme)
		}
	}
	if filter.UserColor != nil {
		userColor := GraphQLToColor(string(*filter.UserColor))
		repoFilter.UserColor = &userColor
	}
	if filter.PuzzleColor != nil {
		puzzleColor := GraphQLToColor(string(*filter.PuzzleColor))
		repoFilter.PuzzleColor = &puzzleColor
	}
	if filter.GameMoveMin != nil {
		repoFilter.GameMoveMin = filter.GameMoveMin
	}
	if filter.GameMoveMax != nil {
		repoFilter.GameMoveMax = filter.GameMoveMax
	}
	if filter.PrevCpMin != nil {
		repoFilter.PrevCpMin = filter.PrevCpMin
	}
	if filter.PrevCpMax != nil {
		repoFilter.PrevCpMax = filter.PrevCpMax
	}
	if filter.CpChangeMin != nil {
		repoFilter.CpChangeMin = filter.CpChangeMin
	}
	if filter.CpChangeMax != nil {
		repoFilter.CpChangeMax = filter.CpChangeMax
	}
	if filter.TimeControl != nil {
		repoFilter.TimeControl = filter.TimeControl
	}
	if filter.Rated != nil {
		repoFilter.Rated = filter.Rated
	}

	return repoFilter
}

// convertGameFilter converts a GraphQL game filter to a repository filter
func convertGameFilter(filter *model.GameFilter) repository.GameFilter {
	if filter == nil {
		return repository.GameFilter{}
	}

	repoFilter := repository.GameFilter{
		StartTime: filter.StartTime,
		EndTime:   filter.EndTime,
		OmitPGN:   true, // We don't need PGN for stats
	}

	if filter.Platform != nil {
		platform := GraphQLToChessPlatform(string(*filter.Platform))
		repoFilter.Platform = &platform
	}
	if filter.ChessUsername != nil {
		repoFilter.ChessUsername = filter.ChessUsername
	}
	if filter.UserColor != nil {
		userColor := GraphQLToColor(string(*filter.UserColor))
		repoFilter.UserColor = &userColor
	}
	if filter.TimeControl != nil {
		repoFilter.TimeControl = filter.TimeControl
	}
	if filter.Rated != nil {
		repoFilter.Rated = filter.Rated
	}
	if filter.Result != nil {
		result := GraphQLToGameResult(string(*filter.Result))
		repoFilter.Result = &result
	}

	return repoFilter
}
