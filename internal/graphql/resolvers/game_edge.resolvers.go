package resolvers

import (
	"context"

	"github.com/chessticize/chessticize-server/internal/graphql/generated"
	"github.com/chessticize/chessticize-server/internal/graphql/model"
	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/utils"
)

// GameEdge returns a generated.GameEdgeResolver implementation.
func (r *Resolver) GameEdge() generated.GameEdgeResolver {
	return &gameEdgeResolver{r}
}

type gameEdgeResolver struct{ *Resolver }

// Node returns the node.
func (r *gameEdgeResolver) Node(ctx context.Context, obj *model.GameEdge) (*models.Game, error) {
	// Convert the model.Game to models.Game
	if obj.Node == nil {
		return nil, nil
	}

	// Create a new models.Game
	game := &models.Game{
		ID:            obj.Node.ID,
		UserID:        obj.Node.UserID,
		Platform:      models.ChessPlatform(obj.Node.Platform),
		ChessUsername: obj.Node.ChessUsername,
		UserColor:     models.Color(obj.Node.UserColor),
		GameTime:      obj.Node.GameTime,
		TimeControl:   obj.Node.TimeControl,
		Rated:         obj.Node.Rated,
		WhitePlayer:   obj.Node.WhitePlayer,
		BlackPlayer:   obj.Node.BlackPlayer,
		Winner:        models.Winner(obj.Node.Winner),
		Result:        models.GameResult(obj.Node.Result),
		CreatedAt:     obj.Node.CreatedAt,
		UpdatedAt:     obj.Node.UpdatedAt,
	}

	// Compress the PGN
	compressedPGN, err := utils.CompressPGN(obj.Node.PGN)
	if err != nil {
		return nil, err
	}
	game.CompressedPGN = compressedPGN

	return game, nil
}
