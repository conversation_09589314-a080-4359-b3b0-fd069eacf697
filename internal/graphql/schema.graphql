"""
Game represents a chess game played on either chess.com or lichess
"""
type Game {
  id: ID!
  user_id: ID!
  platform: ChessPlatform!
  chess_username: String!
  user_color: Color!
  game_time: Time!
  pgn: String!
  time_control: String!
  rated: Boolean!
  url: String
  white_player: String!
  black_player: String!
  winner: Winner!
  result: GameResult!
  created_at: Time!
  updated_at: Time!
  puzzles(filter: PuzzleFilter): [Puzzle!]
}

"""
Puzzle represents a chess puzzle generated from a game position
"""
type Puzzle {
  id: ID!
  game_id: ID!
  user_id: ID!
  game_move: Int!
  fen: String!
  moves: [String!]!
  prev_cp: Int!
  cp: Int!
  theme: PuzzleTheme!
  user_color: Color!
  puzzle_color: Color!
  zugzwang: Boolean!
  tags: [String!]!
  created_at: Time!
  updated_at: Time!
  game: Game
}

"""
GameFilter holds criteria for filtering games
"""
input GameFilter {
  platform: ChessPlatform
  chess_username: String
  start_time: Time
  end_time: Time
  time_control: String
  rated: Boolean
  result: GameResult
  user_color: Color
}

"""
PuzzleFilter holds criteria for filtering puzzles
"""
input PuzzleFilter {
  tags: [String!]
  game_start_time: Time
  game_end_time: Time
  themes: [PuzzleTheme!]
  user_color: Color
  puzzle_color: Color
  game_move_min: Int
  game_move_max: Int
  prev_cp_min: Int
  prev_cp_max: Int
  cp_change_min: Int
  cp_change_max: Int
  time_control: String
  rated: Boolean
}

"""
Pagination input for cursor-based pagination
"""
input PaginationInput {
  first: Int
  after: String
  last: Int
  before: String
}

"""
Pagination input for offset-based pagination
"""
input OffsetPaginationInput {
  offset: Int
  limit: Int
}

"""
Sorting options for games
"""
enum GameSortField {
  GAME_TIME
  CREATED_AT
}

"""
Sorting options for puzzles
"""
enum PuzzleSortField {
  CREATED_AT
  CP
}

"""
Sort direction
"""
enum SortDirection {
  ASC
  DESC
}

"""
Sorting input
"""
input SortInput {
  field: String!
  direction: SortDirection!
}

"""
Game connection for pagination
"""
type GameConnection {
  edges: [GameEdge!]!
  page_info: PageInfo!
  total_count: Int!
}

"""
Game edge for pagination
"""
type GameEdge {
  node: Game!
  cursor: String!
}

"""
Puzzle connection for pagination
"""
type PuzzleConnection {
  edges: [PuzzleEdge!]!
  page_info: PageInfo!
  total_count: Int!
}

"""
TagCount represents a tag and its count
"""
type TagCount {
  tag: String!
  count: Int!
}

"""
ThemeCount represents a puzzle theme and its count
"""
type ThemeCount {
  theme: PuzzleTheme!
  count: Int!
}

"""
ColorCount represents a color and its count
"""
type ColorCount {
  color: Color!
  count: Int!
}

"""
GameMoveBucket represents a range of game moves and the count of puzzles in that range
"""
type GameMoveBucket {
  name: String!
  min_move: Int!
  max_move: Int!
  count: Int!
}

"""
MoveLengthBucket represents a puzzle move length and its count
"""
type MoveLengthBucket {
  length: Int!
  count: Int!
}

"""
PlatformCount represents a chess platform and its count
"""
type PlatformCount {
  platform: ChessPlatform!
  count: Int!
}

"""
ResultCount represents a game result and its count
"""
type ResultCount {
  result: GameResult!
  count: Int!
}

"""
TimeControlCount represents a time control and its count
"""
type TimeControlCount {
  time_control: String!
  count: Int!
}

"""
RatedCount represents a rated status and its count
"""
type RatedCount {
  rated: Boolean!
  count: Int!
}

"""
GameStats represents aggregated statistics about games
"""
type GameStats {
  platform_counts: [PlatformCount!]!
  user_color_counts: [ColorCount!]!
  result_counts: [ResultCount!]!
  time_control_counts: [TimeControlCount!]!
  rated_counts: [RatedCount!]!
  average_opponent_rating: Float!
  total_count: Int!
}

"""
GroupedGameStats represents game statistics for a specific time period
"""
type GroupedGameStats {
  start_time: Time!
  end_time: Time!
  stats: GameStats!
}

"""
GroupedGameStatsConnection represents a collection of grouped game statistics
"""
type GroupedGameStatsConnection {
  nodes: [GroupedGameStats!]!
  total_count: Int!
}

"""
PuzzleStats represents aggregated statistics about puzzles
"""
type PuzzleStats {
  tag_counts: [TagCount!]!
  theme_counts: [ThemeCount!]!
  user_color_counts: [ColorCount!]!
  game_move_buckets: [GameMoveBucket!]!
  move_length_counts: [MoveLengthBucket!]!
  total_count: Int!
  unique_game_count: Int!
  average_move_length: Float!
}

"""
GroupedPuzzleStats represents puzzle statistics for a specific time period
"""
type GroupedPuzzleStats {
  start_time: Time!
  end_time: Time!
  stats: PuzzleStats!
}

"""
GroupedPuzzleStatsConnection represents a collection of grouped puzzle statistics
"""
type GroupedPuzzleStatsConnection {
  nodes: [GroupedPuzzleStats!]!
  total_count: Int!
}

"""
Puzzle edge for pagination
"""
type PuzzleEdge {
  node: Puzzle!
  cursor: String!
}

"""
Page info for pagination
"""
type PageInfo {
  has_next_page: Boolean!
  has_previous_page: Boolean!
  start_cursor: String
  end_cursor: String
}

"""
Chess platform enum
"""
enum ChessPlatform {
  CHESS_COM
  LICHESS
}

"""
Color enum
"""
enum Color {
  WHITE
  BLACK
}

"""
Winner enum
"""
enum Winner {
  WHITE
  BLACK
  NONE
}

"""
TimeGrouping enum for grouping statistics by time periods
"""
enum TimeGrouping {
  DAY
  WEEK
  MONTH
}

"""
Game result enum
"""
enum GameResult {
  MATE
  RESIGN
  DRAW
  ABANDONED
  OUT_OF_TIME
}

"""
Puzzle theme enum
"""
enum PuzzleTheme {
  OPPONENT_MISTAKE_CAUGHT
  OPPONENT_MISTAKE_MISSED
  OPPONENT_BLUNDER_CAUGHT
  OPPONENT_BLUNDER_MISSED
  OWN_MISTAKE_PUNISHED
  OWN_MISTAKE_ESCAPED
  OWN_BLUNDER_PUNISHED
  OWN_BLUNDER_ESCAPED
}

"""
Time scalar for timestamps
"""
scalar Time

type Query {
  """
  Get a game by ID
  """
  game(id: ID!): Game

  """
  Get a puzzle by ID
  """
  puzzle(id: ID!): Puzzle

  """
  Get the current user's games with filtering, pagination, and sorting
  """
  myGames(
    filter: GameFilter
    pagination: OffsetPaginationInput
    sort: SortInput
  ): GameConnection!

  """
  Get the current user's puzzles with filtering, pagination, and sorting
  """
  myPuzzles(
    filter: PuzzleFilter
    pagination: OffsetPaginationInput
    sort: SortInput
  ): PuzzleConnection!

  """
  Get statistics about the current user's puzzles with optional filtering by time range and other criteria.
  If pagination is provided, stats will be calculated only for the specified puzzles.
  """
  myPuzzleStats(
    filter: PuzzleFilter
    pagination: OffsetPaginationInput
  ): PuzzleStats!

  """
  Get statistics about the current user's games with optional filtering by time range and other criteria.
  If pagination is provided, stats will be calculated only for the specified games.
  """
  myGameStats(
    filter: GameFilter
    pagination: OffsetPaginationInput
  ): GameStats!

  """
  Get grouped statistics about the current user's puzzles with optional filtering.
  Returns statistics aggregated by the specified time grouping.
  The group_length parameter specifies how many units to include in each group (e.g., 3 days, 2 weeks).
  If pagination is provided, stats will be calculated only for the specified puzzles.
  """
  myGroupedPuzzleStats(
    filter: PuzzleFilter
    pagination: OffsetPaginationInput
    group_unit: TimeGrouping!
    group_length: Int = 1
  ): GroupedPuzzleStatsConnection!

  """
  Get grouped statistics about the current user's games with optional filtering.
  Returns statistics aggregated by the specified time grouping.
  The group_length parameter specifies how many units to include in each group (e.g., 3 days, 2 weeks).
  If pagination is provided, stats will be calculated only for the specified games.
  """
  myGroupedGameStats(
    filter: GameFilter
    pagination: OffsetPaginationInput
    group_unit: TimeGrouping!
    group_length: Int = 1
  ): GroupedGameStatsConnection!
}

schema {
  query: Query
}
