package graphql

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/repository/fake"
	"github.com/chessticize/chessticize-server/internal/utils"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestPuzzleGameFieldResolution(t *testing.T) {
	// Setup test environment using the same pattern as existing tests
	handler, userRepo, gameRepo, puzzleRepo := setupGraphQLTest(t)
	defer func() {
		// Clean up resources
		fakeDB := fake.NewDB(t)
		_ = fakeDB.Close()
	}()

	// Create test user
	uniqueEmail := fmt.Sprintf("<EMAIL>", uuid.New().String())
	user := &models.User{
		ID:           uuid.New().String(),
		Email:        uniqueEmail,
		PasswordHash: "hashedpassword",
		RegisteredAt: time.Now(),
		UpdatedAt:    time.Now(),
	}
	err := userRepo.Create(context.Background(), user)
	require.NoError(t, err)

	// Create test game
	compressedPGN, err := utils.CompressPGN("1. e4 e5 2. Nf3 Nc6")
	require.NoError(t, err)

	game := &models.Game{
		ID:            uuid.New().String(),
		UserID:        user.ID,
		Platform:      models.ChessDotCom,
		ChessUsername: "testuser",
		UserColor:     models.White,
		GameTime:      time.Now(),
		CompressedPGN: compressedPGN,
		TimeControl:   "10+0",
		Rated:         true,
		WhitePlayer:   `{"username":"testuser","rating":1500}`,
		BlackPlayer:   `{"username":"opponent","rating":1600}`,
		Winner:        models.WinnerWhite,
		Result:        models.Mate,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}
	err = gameRepo.Create(context.Background(), game)
	require.NoError(t, err)

	// Create test puzzle
	puzzle := &models.Puzzle{
		ID:          uuid.New().String(),
		GameID:      game.ID,
		UserID:      user.ID,
		GameMove:    5,
		FEN:         "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
		Moves:       []string{"e2e4", "e7e5"},
		PrevCP:      0,
		CP:          100,
		Theme:       models.OpponentMistakeMissed,
		UserColor:   models.White,
		PuzzleColor: models.Black,
		Zugzwang:    false,
		Tags:        []string{"opening", "fork"},
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
	err = puzzleRepo.Create(context.Background(), puzzle)
	require.NoError(t, err)

	// Create JWT token
	token := createTestToken(t, user.ID, user.Email, "test-secret")

	t.Run("GraphQL Query with Game Field", func(t *testing.T) {
		// Test query that includes the game field
		query := `
		query {
			myPuzzles(pagination: {offset: 0, limit: 10}) {
				edges {
					node {
						id
						theme
						game {
							id
							platform
							chess_username
							game_time
						}
					}
				}
				total_count
			}
		}
		`

		result := executeGraphQLQuery(t, handler, query, token)

		// Check for errors
		errors, hasErrors := result["errors"]
		if hasErrors {
			t.Fatalf("GraphQL query returned errors: %v", errors)
		}

		// Check the data
		data := result["data"].(map[string]interface{})
		myPuzzles := data["myPuzzles"].(map[string]interface{})
		edges := myPuzzles["edges"].([]interface{})

		require.Len(t, edges, 1, "Should return one puzzle")

		edge := edges[0].(map[string]interface{})
		node := edge["node"].(map[string]interface{})

		// Check puzzle data
		assert.Equal(t, puzzle.ID, node["id"])
		assert.Equal(t, string(puzzle.Theme), node["theme"])

		// Check game data - this is where the issue likely occurs
		gameData := node["game"]
		require.NotNil(t, gameData, "Game field should not be null")

		gameMap := gameData.(map[string]interface{})
		assert.Equal(t, game.ID, gameMap["id"])
		assert.Equal(t, string(game.Platform), gameMap["platform"])
		assert.Equal(t, game.ChessUsername, gameMap["chess_username"])
	})

	t.Run("Test Repository GetByIDWithoutPGN", func(t *testing.T) {
		// Test that the repository method works correctly
		ctx := context.Background()

		retrievedGame, err := gameRepo.GetByIDWithoutPGN(ctx, game.ID)
		require.NoError(t, err)
		require.NotNil(t, retrievedGame)

		assert.Equal(t, game.ID, retrievedGame.ID)
		assert.Equal(t, game.UserID, retrievedGame.UserID)
		assert.Equal(t, game.Platform, retrievedGame.Platform)
		assert.Equal(t, game.ChessUsername, retrievedGame.ChessUsername)

		// CompressedPGN should be nil/empty when using GetByIDWithoutPGN
		assert.Nil(t, retrievedGame.CompressedPGN)
	})

	t.Run("Test Puzzle Repository ListByUserID with Game Preload", func(t *testing.T) {
		// Test that the puzzle repository correctly preloads games
		ctx := context.Background()

		puzzles, totalCount, err := puzzleRepo.ListByUserID(ctx, user.ID, repository.PuzzleFilter{}, 0, 10)
		require.NoError(t, err)
		require.Equal(t, int64(1), totalCount)
		require.Len(t, puzzles, 1)

		retrievedPuzzle := puzzles[0]
		assert.Equal(t, puzzle.ID, retrievedPuzzle.ID)

		// Check if the Game field is properly loaded
		assert.Equal(t, game.ID, retrievedPuzzle.Game.ID)
		assert.Equal(t, game.UserID, retrievedPuzzle.Game.UserID)
		assert.Equal(t, game.Platform, retrievedPuzzle.Game.Platform)
	})
}
