// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package generated

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	"github.com/99designs/gqlgen/graphql"
	"github.com/99designs/gqlgen/graphql/introspection"
	"github.com/chessticize/chessticize-server/internal/graphql/model"
	"github.com/chessticize/chessticize-server/internal/models"
	gqlparser "github.com/vektah/gqlparser/v2"
	"github.com/vektah/gqlparser/v2/ast"
)

// region    ************************** generated!.gotpl **************************

// NewExecutableSchema creates an ExecutableSchema from the ResolverRoot interface.
func NewExecutableSchema(cfg Config) graphql.ExecutableSchema {
	return &executableSchema{
		schema:     cfg.Schema,
		resolvers:  cfg.Resolvers,
		directives: cfg.Directives,
		complexity: cfg.Complexity,
	}
}

type Config struct {
	Schema     *ast.Schema
	Resolvers  ResolverRoot
	Directives DirectiveRoot
	Complexity ComplexityRoot
}

type ResolverRoot interface {
	ColorCount() ColorCountResolver
	Game() GameResolver
	GameEdge() GameEdgeResolver
	GameMoveBucket() GameMoveBucketResolver
	GameStats() GameStatsResolver
	GroupedGameStatsConnection() GroupedGameStatsConnectionResolver
	GroupedPuzzleStatsConnection() GroupedPuzzleStatsConnectionResolver
	MoveLengthBucket() MoveLengthBucketResolver
	PlatformCount() PlatformCountResolver
	Puzzle() PuzzleResolver
	PuzzleStats() PuzzleStatsResolver
	Query() QueryResolver
	RatedCount() RatedCountResolver
	ResultCount() ResultCountResolver
	TagCount() TagCountResolver
	ThemeCount() ThemeCountResolver
	TimeControlCount() TimeControlCountResolver
	OffsetPaginationInput() OffsetPaginationInputResolver
	PuzzleFilter() PuzzleFilterResolver
}

type DirectiveRoot struct {
}

type ComplexityRoot struct {
	ColorCount struct {
		Color func(childComplexity int) int
		Count func(childComplexity int) int
	}

	Game struct {
		BlackPlayer   func(childComplexity int) int
		ChessUsername func(childComplexity int) int
		CreatedAt     func(childComplexity int) int
		GameTime      func(childComplexity int) int
		ID            func(childComplexity int) int
		Pgn           func(childComplexity int) int
		Platform      func(childComplexity int) int
		Puzzles       func(childComplexity int, filter *model.PuzzleFilter) int
		Rated         func(childComplexity int) int
		Result        func(childComplexity int) int
		TimeControl   func(childComplexity int) int
		URL           func(childComplexity int) int
		UpdatedAt     func(childComplexity int) int
		UserColor     func(childComplexity int) int
		UserID        func(childComplexity int) int
		WhitePlayer   func(childComplexity int) int
		Winner        func(childComplexity int) int
	}

	GameConnection struct {
		Edges      func(childComplexity int) int
		PageInfo   func(childComplexity int) int
		TotalCount func(childComplexity int) int
	}

	GameEdge struct {
		Cursor func(childComplexity int) int
		Node   func(childComplexity int) int
	}

	GameMoveBucket struct {
		Count   func(childComplexity int) int
		MaxMove func(childComplexity int) int
		MinMove func(childComplexity int) int
		Name    func(childComplexity int) int
	}

	GameStats struct {
		AverageOpponentRating func(childComplexity int) int
		PlatformCounts        func(childComplexity int) int
		RatedCounts           func(childComplexity int) int
		ResultCounts          func(childComplexity int) int
		TimeControlCounts     func(childComplexity int) int
		TotalCount            func(childComplexity int) int
		UserColorCounts       func(childComplexity int) int
	}

	GroupedGameStats struct {
		EndTime   func(childComplexity int) int
		StartTime func(childComplexity int) int
		Stats     func(childComplexity int) int
	}

	GroupedGameStatsConnection struct {
		Nodes      func(childComplexity int) int
		TotalCount func(childComplexity int) int
	}

	GroupedPuzzleStats struct {
		EndTime   func(childComplexity int) int
		StartTime func(childComplexity int) int
		Stats     func(childComplexity int) int
	}

	GroupedPuzzleStatsConnection struct {
		Nodes      func(childComplexity int) int
		TotalCount func(childComplexity int) int
	}

	MoveLengthBucket struct {
		Count  func(childComplexity int) int
		Length func(childComplexity int) int
	}

	PageInfo struct {
		EndCursor       func(childComplexity int) int
		HasNextPage     func(childComplexity int) int
		HasPreviousPage func(childComplexity int) int
		StartCursor     func(childComplexity int) int
	}

	PlatformCount struct {
		Count    func(childComplexity int) int
		Platform func(childComplexity int) int
	}

	Puzzle struct {
		Cp          func(childComplexity int) int
		CreatedAt   func(childComplexity int) int
		FEN         func(childComplexity int) int
		Game        func(childComplexity int) int
		GameID      func(childComplexity int) int
		GameMove    func(childComplexity int) int
		ID          func(childComplexity int) int
		Moves       func(childComplexity int) int
		PrevCp      func(childComplexity int) int
		PuzzleColor func(childComplexity int) int
		Tags        func(childComplexity int) int
		Theme       func(childComplexity int) int
		UpdatedAt   func(childComplexity int) int
		UserColor   func(childComplexity int) int
		UserID      func(childComplexity int) int
		Zugzwang    func(childComplexity int) int
	}

	PuzzleConnection struct {
		Edges      func(childComplexity int) int
		PageInfo   func(childComplexity int) int
		TotalCount func(childComplexity int) int
	}

	PuzzleEdge struct {
		Cursor func(childComplexity int) int
		Node   func(childComplexity int) int
	}

	PuzzleStats struct {
		AverageMoveLength func(childComplexity int) int
		GameMoveBuckets   func(childComplexity int) int
		MoveLengthCounts  func(childComplexity int) int
		TagCounts         func(childComplexity int) int
		ThemeCounts       func(childComplexity int) int
		TotalCount        func(childComplexity int) int
		UniqueGameCount   func(childComplexity int) int
		UserColorCounts   func(childComplexity int) int
	}

	Query struct {
		Game                 func(childComplexity int, id string) int
		MyGameStats          func(childComplexity int, filter *model.GameFilter, pagination *model.OffsetPaginationInput) int
		MyGames              func(childComplexity int, filter *model.GameFilter, pagination *model.OffsetPaginationInput, sort *model.SortInput) int
		MyGroupedGameStats   func(childComplexity int, filter *model.GameFilter, pagination *model.OffsetPaginationInput, groupUnit model.TimeGrouping, groupLength *int32) int
		MyGroupedPuzzleStats func(childComplexity int, filter *model.PuzzleFilter, pagination *model.OffsetPaginationInput, groupUnit model.TimeGrouping, groupLength *int32) int
		MyPuzzleStats        func(childComplexity int, filter *model.PuzzleFilter, pagination *model.OffsetPaginationInput) int
		MyPuzzles            func(childComplexity int, filter *model.PuzzleFilter, pagination *model.OffsetPaginationInput, sort *model.SortInput) int
		Puzzle               func(childComplexity int, id string) int
	}

	RatedCount struct {
		Count func(childComplexity int) int
		Rated func(childComplexity int) int
	}

	ResultCount struct {
		Count  func(childComplexity int) int
		Result func(childComplexity int) int
	}

	TagCount struct {
		Count func(childComplexity int) int
		Tag   func(childComplexity int) int
	}

	ThemeCount struct {
		Count func(childComplexity int) int
		Theme func(childComplexity int) int
	}

	TimeControlCount struct {
		Count       func(childComplexity int) int
		TimeControl func(childComplexity int) int
	}
}

type ColorCountResolver interface {
	Color(ctx context.Context, obj *model.ColorCount) (models.Color, error)
	Count(ctx context.Context, obj *model.ColorCount) (int32, error)
}
type GameResolver interface {
	Pgn(ctx context.Context, obj *models.Game) (string, error)
}
type GameEdgeResolver interface {
	Node(ctx context.Context, obj *model.GameEdge) (*models.Game, error)
}
type GameMoveBucketResolver interface {
	MinMove(ctx context.Context, obj *model.GameMoveBucket) (int32, error)
	MaxMove(ctx context.Context, obj *model.GameMoveBucket) (int32, error)
	Count(ctx context.Context, obj *model.GameMoveBucket) (int32, error)
}
type GameStatsResolver interface {
	TotalCount(ctx context.Context, obj *model.GameStats) (int32, error)
}
type GroupedGameStatsConnectionResolver interface {
	TotalCount(ctx context.Context, obj *model.GroupedGameStatsConnection) (int32, error)
}
type GroupedPuzzleStatsConnectionResolver interface {
	TotalCount(ctx context.Context, obj *model.GroupedPuzzleStatsConnection) (int32, error)
}
type MoveLengthBucketResolver interface {
	Length(ctx context.Context, obj *model.MoveLengthBucket) (int32, error)
	Count(ctx context.Context, obj *model.MoveLengthBucket) (int32, error)
}
type PlatformCountResolver interface {
	Platform(ctx context.Context, obj *model.PlatformCount) (models.ChessPlatform, error)
	Count(ctx context.Context, obj *model.PlatformCount) (int32, error)
}
type PuzzleResolver interface {
	GameMove(ctx context.Context, obj *models.Puzzle) (int32, error)

	Moves(ctx context.Context, obj *models.Puzzle) ([]string, error)
	PrevCp(ctx context.Context, obj *models.Puzzle) (int32, error)
	Cp(ctx context.Context, obj *models.Puzzle) (int32, error)

	Tags(ctx context.Context, obj *models.Puzzle) ([]string, error)
}
type PuzzleStatsResolver interface {
	TotalCount(ctx context.Context, obj *model.PuzzleStats) (int32, error)
	UniqueGameCount(ctx context.Context, obj *model.PuzzleStats) (int32, error)
}
type QueryResolver interface {
	Game(ctx context.Context, id string) (*models.Game, error)
	Puzzle(ctx context.Context, id string) (*models.Puzzle, error)
	MyGames(ctx context.Context, filter *model.GameFilter, pagination *model.OffsetPaginationInput, sort *model.SortInput) (*model.GameConnection, error)
	MyPuzzles(ctx context.Context, filter *model.PuzzleFilter, pagination *model.OffsetPaginationInput, sort *model.SortInput) (*model.PuzzleConnection, error)
	MyPuzzleStats(ctx context.Context, filter *model.PuzzleFilter, pagination *model.OffsetPaginationInput) (*model.PuzzleStats, error)
	MyGameStats(ctx context.Context, filter *model.GameFilter, pagination *model.OffsetPaginationInput) (*model.GameStats, error)
	MyGroupedPuzzleStats(ctx context.Context, filter *model.PuzzleFilter, pagination *model.OffsetPaginationInput, groupUnit model.TimeGrouping, groupLength *int32) (*model.GroupedPuzzleStatsConnection, error)
	MyGroupedGameStats(ctx context.Context, filter *model.GameFilter, pagination *model.OffsetPaginationInput, groupUnit model.TimeGrouping, groupLength *int32) (*model.GroupedGameStatsConnection, error)
}
type RatedCountResolver interface {
	Count(ctx context.Context, obj *model.RatedCount) (int32, error)
}
type ResultCountResolver interface {
	Result(ctx context.Context, obj *model.ResultCount) (models.GameResult, error)
	Count(ctx context.Context, obj *model.ResultCount) (int32, error)
}
type TagCountResolver interface {
	Count(ctx context.Context, obj *model.TagCount) (int32, error)
}
type ThemeCountResolver interface {
	Theme(ctx context.Context, obj *model.ThemeCount) (models.PuzzleTheme, error)
	Count(ctx context.Context, obj *model.ThemeCount) (int32, error)
}
type TimeControlCountResolver interface {
	Count(ctx context.Context, obj *model.TimeControlCount) (int32, error)
}

type OffsetPaginationInputResolver interface {
	Offset(ctx context.Context, obj *model.OffsetPaginationInput, data *int32) error
	Limit(ctx context.Context, obj *model.OffsetPaginationInput, data *int32) error
}
type PuzzleFilterResolver interface {
	GameMoveMin(ctx context.Context, obj *model.PuzzleFilter, data *int32) error
	GameMoveMax(ctx context.Context, obj *model.PuzzleFilter, data *int32) error
	PrevCpMin(ctx context.Context, obj *model.PuzzleFilter, data *int32) error
	PrevCpMax(ctx context.Context, obj *model.PuzzleFilter, data *int32) error
	CpChangeMin(ctx context.Context, obj *model.PuzzleFilter, data *int32) error
	CpChangeMax(ctx context.Context, obj *model.PuzzleFilter, data *int32) error
}

type executableSchema struct {
	schema     *ast.Schema
	resolvers  ResolverRoot
	directives DirectiveRoot
	complexity ComplexityRoot
}

func (e *executableSchema) Schema() *ast.Schema {
	if e.schema != nil {
		return e.schema
	}
	return parsedSchema
}

func (e *executableSchema) Complexity(ctx context.Context, typeName, field string, childComplexity int, rawArgs map[string]any) (int, bool) {
	ec := executionContext{nil, e, 0, 0, nil}
	_ = ec
	switch typeName + "." + field {

	case "ColorCount.color":
		if e.complexity.ColorCount.Color == nil {
			break
		}

		return e.complexity.ColorCount.Color(childComplexity), true

	case "ColorCount.count":
		if e.complexity.ColorCount.Count == nil {
			break
		}

		return e.complexity.ColorCount.Count(childComplexity), true

	case "Game.black_player":
		if e.complexity.Game.BlackPlayer == nil {
			break
		}

		return e.complexity.Game.BlackPlayer(childComplexity), true

	case "Game.chess_username":
		if e.complexity.Game.ChessUsername == nil {
			break
		}

		return e.complexity.Game.ChessUsername(childComplexity), true

	case "Game.created_at":
		if e.complexity.Game.CreatedAt == nil {
			break
		}

		return e.complexity.Game.CreatedAt(childComplexity), true

	case "Game.game_time":
		if e.complexity.Game.GameTime == nil {
			break
		}

		return e.complexity.Game.GameTime(childComplexity), true

	case "Game.id":
		if e.complexity.Game.ID == nil {
			break
		}

		return e.complexity.Game.ID(childComplexity), true

	case "Game.pgn":
		if e.complexity.Game.Pgn == nil {
			break
		}

		return e.complexity.Game.Pgn(childComplexity), true

	case "Game.platform":
		if e.complexity.Game.Platform == nil {
			break
		}

		return e.complexity.Game.Platform(childComplexity), true

	case "Game.puzzles":
		if e.complexity.Game.Puzzles == nil {
			break
		}

		args, err := ec.field_Game_puzzles_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Game.Puzzles(childComplexity, args["filter"].(*model.PuzzleFilter)), true

	case "Game.rated":
		if e.complexity.Game.Rated == nil {
			break
		}

		return e.complexity.Game.Rated(childComplexity), true

	case "Game.result":
		if e.complexity.Game.Result == nil {
			break
		}

		return e.complexity.Game.Result(childComplexity), true

	case "Game.time_control":
		if e.complexity.Game.TimeControl == nil {
			break
		}

		return e.complexity.Game.TimeControl(childComplexity), true

	case "Game.url":
		if e.complexity.Game.URL == nil {
			break
		}

		return e.complexity.Game.URL(childComplexity), true

	case "Game.updated_at":
		if e.complexity.Game.UpdatedAt == nil {
			break
		}

		return e.complexity.Game.UpdatedAt(childComplexity), true

	case "Game.user_color":
		if e.complexity.Game.UserColor == nil {
			break
		}

		return e.complexity.Game.UserColor(childComplexity), true

	case "Game.user_id":
		if e.complexity.Game.UserID == nil {
			break
		}

		return e.complexity.Game.UserID(childComplexity), true

	case "Game.white_player":
		if e.complexity.Game.WhitePlayer == nil {
			break
		}

		return e.complexity.Game.WhitePlayer(childComplexity), true

	case "Game.winner":
		if e.complexity.Game.Winner == nil {
			break
		}

		return e.complexity.Game.Winner(childComplexity), true

	case "GameConnection.edges":
		if e.complexity.GameConnection.Edges == nil {
			break
		}

		return e.complexity.GameConnection.Edges(childComplexity), true

	case "GameConnection.page_info":
		if e.complexity.GameConnection.PageInfo == nil {
			break
		}

		return e.complexity.GameConnection.PageInfo(childComplexity), true

	case "GameConnection.total_count":
		if e.complexity.GameConnection.TotalCount == nil {
			break
		}

		return e.complexity.GameConnection.TotalCount(childComplexity), true

	case "GameEdge.cursor":
		if e.complexity.GameEdge.Cursor == nil {
			break
		}

		return e.complexity.GameEdge.Cursor(childComplexity), true

	case "GameEdge.node":
		if e.complexity.GameEdge.Node == nil {
			break
		}

		return e.complexity.GameEdge.Node(childComplexity), true

	case "GameMoveBucket.count":
		if e.complexity.GameMoveBucket.Count == nil {
			break
		}

		return e.complexity.GameMoveBucket.Count(childComplexity), true

	case "GameMoveBucket.max_move":
		if e.complexity.GameMoveBucket.MaxMove == nil {
			break
		}

		return e.complexity.GameMoveBucket.MaxMove(childComplexity), true

	case "GameMoveBucket.min_move":
		if e.complexity.GameMoveBucket.MinMove == nil {
			break
		}

		return e.complexity.GameMoveBucket.MinMove(childComplexity), true

	case "GameMoveBucket.name":
		if e.complexity.GameMoveBucket.Name == nil {
			break
		}

		return e.complexity.GameMoveBucket.Name(childComplexity), true

	case "GameStats.average_opponent_rating":
		if e.complexity.GameStats.AverageOpponentRating == nil {
			break
		}

		return e.complexity.GameStats.AverageOpponentRating(childComplexity), true

	case "GameStats.platform_counts":
		if e.complexity.GameStats.PlatformCounts == nil {
			break
		}

		return e.complexity.GameStats.PlatformCounts(childComplexity), true

	case "GameStats.rated_counts":
		if e.complexity.GameStats.RatedCounts == nil {
			break
		}

		return e.complexity.GameStats.RatedCounts(childComplexity), true

	case "GameStats.result_counts":
		if e.complexity.GameStats.ResultCounts == nil {
			break
		}

		return e.complexity.GameStats.ResultCounts(childComplexity), true

	case "GameStats.time_control_counts":
		if e.complexity.GameStats.TimeControlCounts == nil {
			break
		}

		return e.complexity.GameStats.TimeControlCounts(childComplexity), true

	case "GameStats.total_count":
		if e.complexity.GameStats.TotalCount == nil {
			break
		}

		return e.complexity.GameStats.TotalCount(childComplexity), true

	case "GameStats.user_color_counts":
		if e.complexity.GameStats.UserColorCounts == nil {
			break
		}

		return e.complexity.GameStats.UserColorCounts(childComplexity), true

	case "GroupedGameStats.end_time":
		if e.complexity.GroupedGameStats.EndTime == nil {
			break
		}

		return e.complexity.GroupedGameStats.EndTime(childComplexity), true

	case "GroupedGameStats.start_time":
		if e.complexity.GroupedGameStats.StartTime == nil {
			break
		}

		return e.complexity.GroupedGameStats.StartTime(childComplexity), true

	case "GroupedGameStats.stats":
		if e.complexity.GroupedGameStats.Stats == nil {
			break
		}

		return e.complexity.GroupedGameStats.Stats(childComplexity), true

	case "GroupedGameStatsConnection.nodes":
		if e.complexity.GroupedGameStatsConnection.Nodes == nil {
			break
		}

		return e.complexity.GroupedGameStatsConnection.Nodes(childComplexity), true

	case "GroupedGameStatsConnection.total_count":
		if e.complexity.GroupedGameStatsConnection.TotalCount == nil {
			break
		}

		return e.complexity.GroupedGameStatsConnection.TotalCount(childComplexity), true

	case "GroupedPuzzleStats.end_time":
		if e.complexity.GroupedPuzzleStats.EndTime == nil {
			break
		}

		return e.complexity.GroupedPuzzleStats.EndTime(childComplexity), true

	case "GroupedPuzzleStats.start_time":
		if e.complexity.GroupedPuzzleStats.StartTime == nil {
			break
		}

		return e.complexity.GroupedPuzzleStats.StartTime(childComplexity), true

	case "GroupedPuzzleStats.stats":
		if e.complexity.GroupedPuzzleStats.Stats == nil {
			break
		}

		return e.complexity.GroupedPuzzleStats.Stats(childComplexity), true

	case "GroupedPuzzleStatsConnection.nodes":
		if e.complexity.GroupedPuzzleStatsConnection.Nodes == nil {
			break
		}

		return e.complexity.GroupedPuzzleStatsConnection.Nodes(childComplexity), true

	case "GroupedPuzzleStatsConnection.total_count":
		if e.complexity.GroupedPuzzleStatsConnection.TotalCount == nil {
			break
		}

		return e.complexity.GroupedPuzzleStatsConnection.TotalCount(childComplexity), true

	case "MoveLengthBucket.count":
		if e.complexity.MoveLengthBucket.Count == nil {
			break
		}

		return e.complexity.MoveLengthBucket.Count(childComplexity), true

	case "MoveLengthBucket.length":
		if e.complexity.MoveLengthBucket.Length == nil {
			break
		}

		return e.complexity.MoveLengthBucket.Length(childComplexity), true

	case "PageInfo.end_cursor":
		if e.complexity.PageInfo.EndCursor == nil {
			break
		}

		return e.complexity.PageInfo.EndCursor(childComplexity), true

	case "PageInfo.has_next_page":
		if e.complexity.PageInfo.HasNextPage == nil {
			break
		}

		return e.complexity.PageInfo.HasNextPage(childComplexity), true

	case "PageInfo.has_previous_page":
		if e.complexity.PageInfo.HasPreviousPage == nil {
			break
		}

		return e.complexity.PageInfo.HasPreviousPage(childComplexity), true

	case "PageInfo.start_cursor":
		if e.complexity.PageInfo.StartCursor == nil {
			break
		}

		return e.complexity.PageInfo.StartCursor(childComplexity), true

	case "PlatformCount.count":
		if e.complexity.PlatformCount.Count == nil {
			break
		}

		return e.complexity.PlatformCount.Count(childComplexity), true

	case "PlatformCount.platform":
		if e.complexity.PlatformCount.Platform == nil {
			break
		}

		return e.complexity.PlatformCount.Platform(childComplexity), true

	case "Puzzle.cp":
		if e.complexity.Puzzle.Cp == nil {
			break
		}

		return e.complexity.Puzzle.Cp(childComplexity), true

	case "Puzzle.created_at":
		if e.complexity.Puzzle.CreatedAt == nil {
			break
		}

		return e.complexity.Puzzle.CreatedAt(childComplexity), true

	case "Puzzle.fen":
		if e.complexity.Puzzle.FEN == nil {
			break
		}

		return e.complexity.Puzzle.FEN(childComplexity), true

	case "Puzzle.game":
		if e.complexity.Puzzle.Game == nil {
			break
		}

		return e.complexity.Puzzle.Game(childComplexity), true

	case "Puzzle.game_id":
		if e.complexity.Puzzle.GameID == nil {
			break
		}

		return e.complexity.Puzzle.GameID(childComplexity), true

	case "Puzzle.game_move":
		if e.complexity.Puzzle.GameMove == nil {
			break
		}

		return e.complexity.Puzzle.GameMove(childComplexity), true

	case "Puzzle.id":
		if e.complexity.Puzzle.ID == nil {
			break
		}

		return e.complexity.Puzzle.ID(childComplexity), true

	case "Puzzle.moves":
		if e.complexity.Puzzle.Moves == nil {
			break
		}

		return e.complexity.Puzzle.Moves(childComplexity), true

	case "Puzzle.prev_cp":
		if e.complexity.Puzzle.PrevCp == nil {
			break
		}

		return e.complexity.Puzzle.PrevCp(childComplexity), true

	case "Puzzle.puzzle_color":
		if e.complexity.Puzzle.PuzzleColor == nil {
			break
		}

		return e.complexity.Puzzle.PuzzleColor(childComplexity), true

	case "Puzzle.tags":
		if e.complexity.Puzzle.Tags == nil {
			break
		}

		return e.complexity.Puzzle.Tags(childComplexity), true

	case "Puzzle.theme":
		if e.complexity.Puzzle.Theme == nil {
			break
		}

		return e.complexity.Puzzle.Theme(childComplexity), true

	case "Puzzle.updated_at":
		if e.complexity.Puzzle.UpdatedAt == nil {
			break
		}

		return e.complexity.Puzzle.UpdatedAt(childComplexity), true

	case "Puzzle.user_color":
		if e.complexity.Puzzle.UserColor == nil {
			break
		}

		return e.complexity.Puzzle.UserColor(childComplexity), true

	case "Puzzle.user_id":
		if e.complexity.Puzzle.UserID == nil {
			break
		}

		return e.complexity.Puzzle.UserID(childComplexity), true

	case "Puzzle.zugzwang":
		if e.complexity.Puzzle.Zugzwang == nil {
			break
		}

		return e.complexity.Puzzle.Zugzwang(childComplexity), true

	case "PuzzleConnection.edges":
		if e.complexity.PuzzleConnection.Edges == nil {
			break
		}

		return e.complexity.PuzzleConnection.Edges(childComplexity), true

	case "PuzzleConnection.page_info":
		if e.complexity.PuzzleConnection.PageInfo == nil {
			break
		}

		return e.complexity.PuzzleConnection.PageInfo(childComplexity), true

	case "PuzzleConnection.total_count":
		if e.complexity.PuzzleConnection.TotalCount == nil {
			break
		}

		return e.complexity.PuzzleConnection.TotalCount(childComplexity), true

	case "PuzzleEdge.cursor":
		if e.complexity.PuzzleEdge.Cursor == nil {
			break
		}

		return e.complexity.PuzzleEdge.Cursor(childComplexity), true

	case "PuzzleEdge.node":
		if e.complexity.PuzzleEdge.Node == nil {
			break
		}

		return e.complexity.PuzzleEdge.Node(childComplexity), true

	case "PuzzleStats.average_move_length":
		if e.complexity.PuzzleStats.AverageMoveLength == nil {
			break
		}

		return e.complexity.PuzzleStats.AverageMoveLength(childComplexity), true

	case "PuzzleStats.game_move_buckets":
		if e.complexity.PuzzleStats.GameMoveBuckets == nil {
			break
		}

		return e.complexity.PuzzleStats.GameMoveBuckets(childComplexity), true

	case "PuzzleStats.move_length_counts":
		if e.complexity.PuzzleStats.MoveLengthCounts == nil {
			break
		}

		return e.complexity.PuzzleStats.MoveLengthCounts(childComplexity), true

	case "PuzzleStats.tag_counts":
		if e.complexity.PuzzleStats.TagCounts == nil {
			break
		}

		return e.complexity.PuzzleStats.TagCounts(childComplexity), true

	case "PuzzleStats.theme_counts":
		if e.complexity.PuzzleStats.ThemeCounts == nil {
			break
		}

		return e.complexity.PuzzleStats.ThemeCounts(childComplexity), true

	case "PuzzleStats.total_count":
		if e.complexity.PuzzleStats.TotalCount == nil {
			break
		}

		return e.complexity.PuzzleStats.TotalCount(childComplexity), true

	case "PuzzleStats.unique_game_count":
		if e.complexity.PuzzleStats.UniqueGameCount == nil {
			break
		}

		return e.complexity.PuzzleStats.UniqueGameCount(childComplexity), true

	case "PuzzleStats.user_color_counts":
		if e.complexity.PuzzleStats.UserColorCounts == nil {
			break
		}

		return e.complexity.PuzzleStats.UserColorCounts(childComplexity), true

	case "Query.game":
		if e.complexity.Query.Game == nil {
			break
		}

		args, err := ec.field_Query_game_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.Game(childComplexity, args["id"].(string)), true

	case "Query.myGameStats":
		if e.complexity.Query.MyGameStats == nil {
			break
		}

		args, err := ec.field_Query_myGameStats_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.MyGameStats(childComplexity, args["filter"].(*model.GameFilter), args["pagination"].(*model.OffsetPaginationInput)), true

	case "Query.myGames":
		if e.complexity.Query.MyGames == nil {
			break
		}

		args, err := ec.field_Query_myGames_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.MyGames(childComplexity, args["filter"].(*model.GameFilter), args["pagination"].(*model.OffsetPaginationInput), args["sort"].(*model.SortInput)), true

	case "Query.myGroupedGameStats":
		if e.complexity.Query.MyGroupedGameStats == nil {
			break
		}

		args, err := ec.field_Query_myGroupedGameStats_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.MyGroupedGameStats(childComplexity, args["filter"].(*model.GameFilter), args["pagination"].(*model.OffsetPaginationInput), args["group_unit"].(model.TimeGrouping), args["group_length"].(*int32)), true

	case "Query.myGroupedPuzzleStats":
		if e.complexity.Query.MyGroupedPuzzleStats == nil {
			break
		}

		args, err := ec.field_Query_myGroupedPuzzleStats_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.MyGroupedPuzzleStats(childComplexity, args["filter"].(*model.PuzzleFilter), args["pagination"].(*model.OffsetPaginationInput), args["group_unit"].(model.TimeGrouping), args["group_length"].(*int32)), true

	case "Query.myPuzzleStats":
		if e.complexity.Query.MyPuzzleStats == nil {
			break
		}

		args, err := ec.field_Query_myPuzzleStats_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.MyPuzzleStats(childComplexity, args["filter"].(*model.PuzzleFilter), args["pagination"].(*model.OffsetPaginationInput)), true

	case "Query.myPuzzles":
		if e.complexity.Query.MyPuzzles == nil {
			break
		}

		args, err := ec.field_Query_myPuzzles_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.MyPuzzles(childComplexity, args["filter"].(*model.PuzzleFilter), args["pagination"].(*model.OffsetPaginationInput), args["sort"].(*model.SortInput)), true

	case "Query.puzzle":
		if e.complexity.Query.Puzzle == nil {
			break
		}

		args, err := ec.field_Query_puzzle_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.Puzzle(childComplexity, args["id"].(string)), true

	case "RatedCount.count":
		if e.complexity.RatedCount.Count == nil {
			break
		}

		return e.complexity.RatedCount.Count(childComplexity), true

	case "RatedCount.rated":
		if e.complexity.RatedCount.Rated == nil {
			break
		}

		return e.complexity.RatedCount.Rated(childComplexity), true

	case "ResultCount.count":
		if e.complexity.ResultCount.Count == nil {
			break
		}

		return e.complexity.ResultCount.Count(childComplexity), true

	case "ResultCount.result":
		if e.complexity.ResultCount.Result == nil {
			break
		}

		return e.complexity.ResultCount.Result(childComplexity), true

	case "TagCount.count":
		if e.complexity.TagCount.Count == nil {
			break
		}

		return e.complexity.TagCount.Count(childComplexity), true

	case "TagCount.tag":
		if e.complexity.TagCount.Tag == nil {
			break
		}

		return e.complexity.TagCount.Tag(childComplexity), true

	case "ThemeCount.count":
		if e.complexity.ThemeCount.Count == nil {
			break
		}

		return e.complexity.ThemeCount.Count(childComplexity), true

	case "ThemeCount.theme":
		if e.complexity.ThemeCount.Theme == nil {
			break
		}

		return e.complexity.ThemeCount.Theme(childComplexity), true

	case "TimeControlCount.count":
		if e.complexity.TimeControlCount.Count == nil {
			break
		}

		return e.complexity.TimeControlCount.Count(childComplexity), true

	case "TimeControlCount.time_control":
		if e.complexity.TimeControlCount.TimeControl == nil {
			break
		}

		return e.complexity.TimeControlCount.TimeControl(childComplexity), true

	}
	return 0, false
}

func (e *executableSchema) Exec(ctx context.Context) graphql.ResponseHandler {
	opCtx := graphql.GetOperationContext(ctx)
	ec := executionContext{opCtx, e, 0, 0, make(chan graphql.DeferredResult)}
	inputUnmarshalMap := graphql.BuildUnmarshalerMap(
		ec.unmarshalInputGameFilter,
		ec.unmarshalInputOffsetPaginationInput,
		ec.unmarshalInputPaginationInput,
		ec.unmarshalInputPuzzleFilter,
		ec.unmarshalInputSortInput,
	)
	first := true

	switch opCtx.Operation.Operation {
	case ast.Query:
		return func(ctx context.Context) *graphql.Response {
			var response graphql.Response
			var data graphql.Marshaler
			if first {
				first = false
				ctx = graphql.WithUnmarshalerMap(ctx, inputUnmarshalMap)
				data = ec._Query(ctx, opCtx.Operation.SelectionSet)
			} else {
				if atomic.LoadInt32(&ec.pendingDeferred) > 0 {
					result := <-ec.deferredResults
					atomic.AddInt32(&ec.pendingDeferred, -1)
					data = result.Result
					response.Path = result.Path
					response.Label = result.Label
					response.Errors = result.Errors
				} else {
					return nil
				}
			}
			var buf bytes.Buffer
			data.MarshalGQL(&buf)
			response.Data = buf.Bytes()
			if atomic.LoadInt32(&ec.deferred) > 0 {
				hasNext := atomic.LoadInt32(&ec.pendingDeferred) > 0
				response.HasNext = &hasNext
			}

			return &response
		}

	default:
		return graphql.OneShot(graphql.ErrorResponse(ctx, "unsupported GraphQL operation"))
	}
}

type executionContext struct {
	*graphql.OperationContext
	*executableSchema
	deferred        int32
	pendingDeferred int32
	deferredResults chan graphql.DeferredResult
}

func (ec *executionContext) processDeferredGroup(dg graphql.DeferredGroup) {
	atomic.AddInt32(&ec.pendingDeferred, 1)
	go func() {
		ctx := graphql.WithFreshResponseContext(dg.Context)
		dg.FieldSet.Dispatch(ctx)
		ds := graphql.DeferredResult{
			Path:   dg.Path,
			Label:  dg.Label,
			Result: dg.FieldSet,
			Errors: graphql.GetErrors(ctx),
		}
		// null fields should bubble up
		if dg.FieldSet.Invalids > 0 {
			ds.Result = graphql.Null
		}
		ec.deferredResults <- ds
	}()
}

func (ec *executionContext) introspectSchema() (*introspection.Schema, error) {
	if ec.DisableIntrospection {
		return nil, errors.New("introspection disabled")
	}
	return introspection.WrapSchema(ec.Schema()), nil
}

func (ec *executionContext) introspectType(name string) (*introspection.Type, error) {
	if ec.DisableIntrospection {
		return nil, errors.New("introspection disabled")
	}
	return introspection.WrapTypeFromDef(ec.Schema(), ec.Schema().Types[name]), nil
}

var sources = []*ast.Source{
	{Name: "../schema.graphql", Input: `"""
Game represents a chess game played on either chess.com or lichess
"""
type Game {
  id: ID!
  user_id: ID!
  platform: ChessPlatform!
  chess_username: String!
  user_color: Color!
  game_time: Time!
  pgn: String!
  time_control: String!
  rated: Boolean!
  url: String
  white_player: String!
  black_player: String!
  winner: Winner!
  result: GameResult!
  created_at: Time!
  updated_at: Time!
  puzzles(filter: PuzzleFilter): [Puzzle!]
}

"""
Puzzle represents a chess puzzle generated from a game position
"""
type Puzzle {
  id: ID!
  game_id: ID!
  user_id: ID!
  game_move: Int!
  fen: String!
  moves: [String!]!
  prev_cp: Int!
  cp: Int!
  theme: PuzzleTheme!
  user_color: Color!
  puzzle_color: Color!
  zugzwang: Boolean!
  tags: [String!]!
  created_at: Time!
  updated_at: Time!
  game: Game
}

"""
GameFilter holds criteria for filtering games
"""
input GameFilter {
  platform: ChessPlatform
  chess_username: String
  start_time: Time
  end_time: Time
  time_control: String
  rated: Boolean
  result: GameResult
  user_color: Color
}

"""
PuzzleFilter holds criteria for filtering puzzles
"""
input PuzzleFilter {
  tags: [String!]
  game_start_time: Time
  game_end_time: Time
  themes: [PuzzleTheme!]
  user_color: Color
  puzzle_color: Color
  game_move_min: Int
  game_move_max: Int
  prev_cp_min: Int
  prev_cp_max: Int
  cp_change_min: Int
  cp_change_max: Int
  time_control: String
  rated: Boolean
}

"""
Pagination input for cursor-based pagination
"""
input PaginationInput {
  first: Int
  after: String
  last: Int
  before: String
}

"""
Pagination input for offset-based pagination
"""
input OffsetPaginationInput {
  offset: Int
  limit: Int
}

"""
Sorting options for games
"""
enum GameSortField {
  GAME_TIME
  CREATED_AT
}

"""
Sorting options for puzzles
"""
enum PuzzleSortField {
  CREATED_AT
  CP
}

"""
Sort direction
"""
enum SortDirection {
  ASC
  DESC
}

"""
Sorting input
"""
input SortInput {
  field: String!
  direction: SortDirection!
}

"""
Game connection for pagination
"""
type GameConnection {
  edges: [GameEdge!]!
  page_info: PageInfo!
  total_count: Int!
}

"""
Game edge for pagination
"""
type GameEdge {
  node: Game!
  cursor: String!
}

"""
Puzzle connection for pagination
"""
type PuzzleConnection {
  edges: [PuzzleEdge!]!
  page_info: PageInfo!
  total_count: Int!
}

"""
TagCount represents a tag and its count
"""
type TagCount {
  tag: String!
  count: Int!
}

"""
ThemeCount represents a puzzle theme and its count
"""
type ThemeCount {
  theme: PuzzleTheme!
  count: Int!
}

"""
ColorCount represents a color and its count
"""
type ColorCount {
  color: Color!
  count: Int!
}

"""
GameMoveBucket represents a range of game moves and the count of puzzles in that range
"""
type GameMoveBucket {
  name: String!
  min_move: Int!
  max_move: Int!
  count: Int!
}

"""
MoveLengthBucket represents a puzzle move length and its count
"""
type MoveLengthBucket {
  length: Int!
  count: Int!
}

"""
PlatformCount represents a chess platform and its count
"""
type PlatformCount {
  platform: ChessPlatform!
  count: Int!
}

"""
ResultCount represents a game result and its count
"""
type ResultCount {
  result: GameResult!
  count: Int!
}

"""
TimeControlCount represents a time control and its count
"""
type TimeControlCount {
  time_control: String!
  count: Int!
}

"""
RatedCount represents a rated status and its count
"""
type RatedCount {
  rated: Boolean!
  count: Int!
}

"""
GameStats represents aggregated statistics about games
"""
type GameStats {
  platform_counts: [PlatformCount!]!
  user_color_counts: [ColorCount!]!
  result_counts: [ResultCount!]!
  time_control_counts: [TimeControlCount!]!
  rated_counts: [RatedCount!]!
  average_opponent_rating: Float!
  total_count: Int!
}

"""
GroupedGameStats represents game statistics for a specific time period
"""
type GroupedGameStats {
  start_time: Time!
  end_time: Time!
  stats: GameStats!
}

"""
GroupedGameStatsConnection represents a collection of grouped game statistics
"""
type GroupedGameStatsConnection {
  nodes: [GroupedGameStats!]!
  total_count: Int!
}

"""
PuzzleStats represents aggregated statistics about puzzles
"""
type PuzzleStats {
  tag_counts: [TagCount!]!
  theme_counts: [ThemeCount!]!
  user_color_counts: [ColorCount!]!
  game_move_buckets: [GameMoveBucket!]!
  move_length_counts: [MoveLengthBucket!]!
  total_count: Int!
  unique_game_count: Int!
  average_move_length: Float!
}

"""
GroupedPuzzleStats represents puzzle statistics for a specific time period
"""
type GroupedPuzzleStats {
  start_time: Time!
  end_time: Time!
  stats: PuzzleStats!
}

"""
GroupedPuzzleStatsConnection represents a collection of grouped puzzle statistics
"""
type GroupedPuzzleStatsConnection {
  nodes: [GroupedPuzzleStats!]!
  total_count: Int!
}

"""
Puzzle edge for pagination
"""
type PuzzleEdge {
  node: Puzzle!
  cursor: String!
}

"""
Page info for pagination
"""
type PageInfo {
  has_next_page: Boolean!
  has_previous_page: Boolean!
  start_cursor: String
  end_cursor: String
}

"""
Chess platform enum
"""
enum ChessPlatform {
  CHESS_COM
  LICHESS
}

"""
Color enum
"""
enum Color {
  WHITE
  BLACK
}

"""
Winner enum
"""
enum Winner {
  WHITE
  BLACK
  NONE
}

"""
TimeGrouping enum for grouping statistics by time periods
"""
enum TimeGrouping {
  DAY
  WEEK
  MONTH
}

"""
Game result enum
"""
enum GameResult {
  MATE
  RESIGN
  DRAW
  ABANDONED
  OUT_OF_TIME
}

"""
Puzzle theme enum
"""
enum PuzzleTheme {
  OPPONENT_MISTAKE_CAUGHT
  OPPONENT_MISTAKE_MISSED
  OPPONENT_BLUNDER_CAUGHT
  OPPONENT_BLUNDER_MISSED
  OWN_MISTAKE_PUNISHED
  OWN_MISTAKE_ESCAPED
  OWN_BLUNDER_PUNISHED
  OWN_BLUNDER_ESCAPED
}

"""
Time scalar for timestamps
"""
scalar Time

type Query {
  """
  Get a game by ID
  """
  game(id: ID!): Game

  """
  Get a puzzle by ID
  """
  puzzle(id: ID!): Puzzle

  """
  Get the current user's games with filtering, pagination, and sorting
  """
  myGames(
    filter: GameFilter
    pagination: OffsetPaginationInput
    sort: SortInput
  ): GameConnection!

  """
  Get the current user's puzzles with filtering, pagination, and sorting
  """
  myPuzzles(
    filter: PuzzleFilter
    pagination: OffsetPaginationInput
    sort: SortInput
  ): PuzzleConnection!

  """
  Get statistics about the current user's puzzles with optional filtering by time range and other criteria.
  If pagination is provided, stats will be calculated only for the specified puzzles.
  """
  myPuzzleStats(
    filter: PuzzleFilter
    pagination: OffsetPaginationInput
  ): PuzzleStats!

  """
  Get statistics about the current user's games with optional filtering by time range and other criteria.
  If pagination is provided, stats will be calculated only for the specified games.
  """
  myGameStats(
    filter: GameFilter
    pagination: OffsetPaginationInput
  ): GameStats!

  """
  Get grouped statistics about the current user's puzzles with optional filtering.
  Returns statistics aggregated by the specified time grouping.
  The group_length parameter specifies how many units to include in each group (e.g., 3 days, 2 weeks).
  If pagination is provided, stats will be calculated only for the specified puzzles.
  """
  myGroupedPuzzleStats(
    filter: PuzzleFilter
    pagination: OffsetPaginationInput
    group_unit: TimeGrouping!
    group_length: Int = 1
  ): GroupedPuzzleStatsConnection!

  """
  Get grouped statistics about the current user's games with optional filtering.
  Returns statistics aggregated by the specified time grouping.
  The group_length parameter specifies how many units to include in each group (e.g., 3 days, 2 weeks).
  If pagination is provided, stats will be calculated only for the specified games.
  """
  myGroupedGameStats(
    filter: GameFilter
    pagination: OffsetPaginationInput
    group_unit: TimeGrouping!
    group_length: Int = 1
  ): GroupedGameStatsConnection!
}

schema {
  query: Query
}
`, BuiltIn: false},
}
var parsedSchema = gqlparser.MustLoadSchema(sources...)

// endregion ************************** generated!.gotpl **************************

// region    ***************************** args.gotpl *****************************

func (ec *executionContext) field_Game_puzzles_args(ctx context.Context, rawArgs map[string]any) (map[string]any, error) {
	var err error
	args := map[string]any{}
	arg0, err := ec.field_Game_puzzles_argsFilter(ctx, rawArgs)
	if err != nil {
		return nil, err
	}
	args["filter"] = arg0
	return args, nil
}
func (ec *executionContext) field_Game_puzzles_argsFilter(
	ctx context.Context,
	rawArgs map[string]any,
) (*model.PuzzleFilter, error) {
	ctx = graphql.WithPathContext(ctx, graphql.NewPathWithField("filter"))
	if tmp, ok := rawArgs["filter"]; ok {
		return ec.unmarshalOPuzzleFilter2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐPuzzleFilter(ctx, tmp)
	}

	var zeroVal *model.PuzzleFilter
	return zeroVal, nil
}

func (ec *executionContext) field_Query___type_args(ctx context.Context, rawArgs map[string]any) (map[string]any, error) {
	var err error
	args := map[string]any{}
	arg0, err := ec.field_Query___type_argsName(ctx, rawArgs)
	if err != nil {
		return nil, err
	}
	args["name"] = arg0
	return args, nil
}
func (ec *executionContext) field_Query___type_argsName(
	ctx context.Context,
	rawArgs map[string]any,
) (string, error) {
	ctx = graphql.WithPathContext(ctx, graphql.NewPathWithField("name"))
	if tmp, ok := rawArgs["name"]; ok {
		return ec.unmarshalNString2string(ctx, tmp)
	}

	var zeroVal string
	return zeroVal, nil
}

func (ec *executionContext) field_Query_game_args(ctx context.Context, rawArgs map[string]any) (map[string]any, error) {
	var err error
	args := map[string]any{}
	arg0, err := ec.field_Query_game_argsID(ctx, rawArgs)
	if err != nil {
		return nil, err
	}
	args["id"] = arg0
	return args, nil
}
func (ec *executionContext) field_Query_game_argsID(
	ctx context.Context,
	rawArgs map[string]any,
) (string, error) {
	ctx = graphql.WithPathContext(ctx, graphql.NewPathWithField("id"))
	if tmp, ok := rawArgs["id"]; ok {
		return ec.unmarshalNID2string(ctx, tmp)
	}

	var zeroVal string
	return zeroVal, nil
}

func (ec *executionContext) field_Query_myGameStats_args(ctx context.Context, rawArgs map[string]any) (map[string]any, error) {
	var err error
	args := map[string]any{}
	arg0, err := ec.field_Query_myGameStats_argsFilter(ctx, rawArgs)
	if err != nil {
		return nil, err
	}
	args["filter"] = arg0
	arg1, err := ec.field_Query_myGameStats_argsPagination(ctx, rawArgs)
	if err != nil {
		return nil, err
	}
	args["pagination"] = arg1
	return args, nil
}
func (ec *executionContext) field_Query_myGameStats_argsFilter(
	ctx context.Context,
	rawArgs map[string]any,
) (*model.GameFilter, error) {
	ctx = graphql.WithPathContext(ctx, graphql.NewPathWithField("filter"))
	if tmp, ok := rawArgs["filter"]; ok {
		return ec.unmarshalOGameFilter2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐGameFilter(ctx, tmp)
	}

	var zeroVal *model.GameFilter
	return zeroVal, nil
}

func (ec *executionContext) field_Query_myGameStats_argsPagination(
	ctx context.Context,
	rawArgs map[string]any,
) (*model.OffsetPaginationInput, error) {
	ctx = graphql.WithPathContext(ctx, graphql.NewPathWithField("pagination"))
	if tmp, ok := rawArgs["pagination"]; ok {
		return ec.unmarshalOOffsetPaginationInput2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐOffsetPaginationInput(ctx, tmp)
	}

	var zeroVal *model.OffsetPaginationInput
	return zeroVal, nil
}

func (ec *executionContext) field_Query_myGames_args(ctx context.Context, rawArgs map[string]any) (map[string]any, error) {
	var err error
	args := map[string]any{}
	arg0, err := ec.field_Query_myGames_argsFilter(ctx, rawArgs)
	if err != nil {
		return nil, err
	}
	args["filter"] = arg0
	arg1, err := ec.field_Query_myGames_argsPagination(ctx, rawArgs)
	if err != nil {
		return nil, err
	}
	args["pagination"] = arg1
	arg2, err := ec.field_Query_myGames_argsSort(ctx, rawArgs)
	if err != nil {
		return nil, err
	}
	args["sort"] = arg2
	return args, nil
}
func (ec *executionContext) field_Query_myGames_argsFilter(
	ctx context.Context,
	rawArgs map[string]any,
) (*model.GameFilter, error) {
	ctx = graphql.WithPathContext(ctx, graphql.NewPathWithField("filter"))
	if tmp, ok := rawArgs["filter"]; ok {
		return ec.unmarshalOGameFilter2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐGameFilter(ctx, tmp)
	}

	var zeroVal *model.GameFilter
	return zeroVal, nil
}

func (ec *executionContext) field_Query_myGames_argsPagination(
	ctx context.Context,
	rawArgs map[string]any,
) (*model.OffsetPaginationInput, error) {
	ctx = graphql.WithPathContext(ctx, graphql.NewPathWithField("pagination"))
	if tmp, ok := rawArgs["pagination"]; ok {
		return ec.unmarshalOOffsetPaginationInput2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐOffsetPaginationInput(ctx, tmp)
	}

	var zeroVal *model.OffsetPaginationInput
	return zeroVal, nil
}

func (ec *executionContext) field_Query_myGames_argsSort(
	ctx context.Context,
	rawArgs map[string]any,
) (*model.SortInput, error) {
	ctx = graphql.WithPathContext(ctx, graphql.NewPathWithField("sort"))
	if tmp, ok := rawArgs["sort"]; ok {
		return ec.unmarshalOSortInput2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐSortInput(ctx, tmp)
	}

	var zeroVal *model.SortInput
	return zeroVal, nil
}

func (ec *executionContext) field_Query_myGroupedGameStats_args(ctx context.Context, rawArgs map[string]any) (map[string]any, error) {
	var err error
	args := map[string]any{}
	arg0, err := ec.field_Query_myGroupedGameStats_argsFilter(ctx, rawArgs)
	if err != nil {
		return nil, err
	}
	args["filter"] = arg0
	arg1, err := ec.field_Query_myGroupedGameStats_argsPagination(ctx, rawArgs)
	if err != nil {
		return nil, err
	}
	args["pagination"] = arg1
	arg2, err := ec.field_Query_myGroupedGameStats_argsGroupUnit(ctx, rawArgs)
	if err != nil {
		return nil, err
	}
	args["group_unit"] = arg2
	arg3, err := ec.field_Query_myGroupedGameStats_argsGroupLength(ctx, rawArgs)
	if err != nil {
		return nil, err
	}
	args["group_length"] = arg3
	return args, nil
}
func (ec *executionContext) field_Query_myGroupedGameStats_argsFilter(
	ctx context.Context,
	rawArgs map[string]any,
) (*model.GameFilter, error) {
	ctx = graphql.WithPathContext(ctx, graphql.NewPathWithField("filter"))
	if tmp, ok := rawArgs["filter"]; ok {
		return ec.unmarshalOGameFilter2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐGameFilter(ctx, tmp)
	}

	var zeroVal *model.GameFilter
	return zeroVal, nil
}

func (ec *executionContext) field_Query_myGroupedGameStats_argsPagination(
	ctx context.Context,
	rawArgs map[string]any,
) (*model.OffsetPaginationInput, error) {
	ctx = graphql.WithPathContext(ctx, graphql.NewPathWithField("pagination"))
	if tmp, ok := rawArgs["pagination"]; ok {
		return ec.unmarshalOOffsetPaginationInput2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐOffsetPaginationInput(ctx, tmp)
	}

	var zeroVal *model.OffsetPaginationInput
	return zeroVal, nil
}

func (ec *executionContext) field_Query_myGroupedGameStats_argsGroupUnit(
	ctx context.Context,
	rawArgs map[string]any,
) (model.TimeGrouping, error) {
	ctx = graphql.WithPathContext(ctx, graphql.NewPathWithField("group_unit"))
	if tmp, ok := rawArgs["group_unit"]; ok {
		return ec.unmarshalNTimeGrouping2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐTimeGrouping(ctx, tmp)
	}

	var zeroVal model.TimeGrouping
	return zeroVal, nil
}

func (ec *executionContext) field_Query_myGroupedGameStats_argsGroupLength(
	ctx context.Context,
	rawArgs map[string]any,
) (*int32, error) {
	ctx = graphql.WithPathContext(ctx, graphql.NewPathWithField("group_length"))
	if tmp, ok := rawArgs["group_length"]; ok {
		return ec.unmarshalOInt2ᚖint32(ctx, tmp)
	}

	var zeroVal *int32
	return zeroVal, nil
}

func (ec *executionContext) field_Query_myGroupedPuzzleStats_args(ctx context.Context, rawArgs map[string]any) (map[string]any, error) {
	var err error
	args := map[string]any{}
	arg0, err := ec.field_Query_myGroupedPuzzleStats_argsFilter(ctx, rawArgs)
	if err != nil {
		return nil, err
	}
	args["filter"] = arg0
	arg1, err := ec.field_Query_myGroupedPuzzleStats_argsPagination(ctx, rawArgs)
	if err != nil {
		return nil, err
	}
	args["pagination"] = arg1
	arg2, err := ec.field_Query_myGroupedPuzzleStats_argsGroupUnit(ctx, rawArgs)
	if err != nil {
		return nil, err
	}
	args["group_unit"] = arg2
	arg3, err := ec.field_Query_myGroupedPuzzleStats_argsGroupLength(ctx, rawArgs)
	if err != nil {
		return nil, err
	}
	args["group_length"] = arg3
	return args, nil
}
func (ec *executionContext) field_Query_myGroupedPuzzleStats_argsFilter(
	ctx context.Context,
	rawArgs map[string]any,
) (*model.PuzzleFilter, error) {
	ctx = graphql.WithPathContext(ctx, graphql.NewPathWithField("filter"))
	if tmp, ok := rawArgs["filter"]; ok {
		return ec.unmarshalOPuzzleFilter2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐPuzzleFilter(ctx, tmp)
	}

	var zeroVal *model.PuzzleFilter
	return zeroVal, nil
}

func (ec *executionContext) field_Query_myGroupedPuzzleStats_argsPagination(
	ctx context.Context,
	rawArgs map[string]any,
) (*model.OffsetPaginationInput, error) {
	ctx = graphql.WithPathContext(ctx, graphql.NewPathWithField("pagination"))
	if tmp, ok := rawArgs["pagination"]; ok {
		return ec.unmarshalOOffsetPaginationInput2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐOffsetPaginationInput(ctx, tmp)
	}

	var zeroVal *model.OffsetPaginationInput
	return zeroVal, nil
}

func (ec *executionContext) field_Query_myGroupedPuzzleStats_argsGroupUnit(
	ctx context.Context,
	rawArgs map[string]any,
) (model.TimeGrouping, error) {
	ctx = graphql.WithPathContext(ctx, graphql.NewPathWithField("group_unit"))
	if tmp, ok := rawArgs["group_unit"]; ok {
		return ec.unmarshalNTimeGrouping2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐTimeGrouping(ctx, tmp)
	}

	var zeroVal model.TimeGrouping
	return zeroVal, nil
}

func (ec *executionContext) field_Query_myGroupedPuzzleStats_argsGroupLength(
	ctx context.Context,
	rawArgs map[string]any,
) (*int32, error) {
	ctx = graphql.WithPathContext(ctx, graphql.NewPathWithField("group_length"))
	if tmp, ok := rawArgs["group_length"]; ok {
		return ec.unmarshalOInt2ᚖint32(ctx, tmp)
	}

	var zeroVal *int32
	return zeroVal, nil
}

func (ec *executionContext) field_Query_myPuzzleStats_args(ctx context.Context, rawArgs map[string]any) (map[string]any, error) {
	var err error
	args := map[string]any{}
	arg0, err := ec.field_Query_myPuzzleStats_argsFilter(ctx, rawArgs)
	if err != nil {
		return nil, err
	}
	args["filter"] = arg0
	arg1, err := ec.field_Query_myPuzzleStats_argsPagination(ctx, rawArgs)
	if err != nil {
		return nil, err
	}
	args["pagination"] = arg1
	return args, nil
}
func (ec *executionContext) field_Query_myPuzzleStats_argsFilter(
	ctx context.Context,
	rawArgs map[string]any,
) (*model.PuzzleFilter, error) {
	ctx = graphql.WithPathContext(ctx, graphql.NewPathWithField("filter"))
	if tmp, ok := rawArgs["filter"]; ok {
		return ec.unmarshalOPuzzleFilter2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐPuzzleFilter(ctx, tmp)
	}

	var zeroVal *model.PuzzleFilter
	return zeroVal, nil
}

func (ec *executionContext) field_Query_myPuzzleStats_argsPagination(
	ctx context.Context,
	rawArgs map[string]any,
) (*model.OffsetPaginationInput, error) {
	ctx = graphql.WithPathContext(ctx, graphql.NewPathWithField("pagination"))
	if tmp, ok := rawArgs["pagination"]; ok {
		return ec.unmarshalOOffsetPaginationInput2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐOffsetPaginationInput(ctx, tmp)
	}

	var zeroVal *model.OffsetPaginationInput
	return zeroVal, nil
}

func (ec *executionContext) field_Query_myPuzzles_args(ctx context.Context, rawArgs map[string]any) (map[string]any, error) {
	var err error
	args := map[string]any{}
	arg0, err := ec.field_Query_myPuzzles_argsFilter(ctx, rawArgs)
	if err != nil {
		return nil, err
	}
	args["filter"] = arg0
	arg1, err := ec.field_Query_myPuzzles_argsPagination(ctx, rawArgs)
	if err != nil {
		return nil, err
	}
	args["pagination"] = arg1
	arg2, err := ec.field_Query_myPuzzles_argsSort(ctx, rawArgs)
	if err != nil {
		return nil, err
	}
	args["sort"] = arg2
	return args, nil
}
func (ec *executionContext) field_Query_myPuzzles_argsFilter(
	ctx context.Context,
	rawArgs map[string]any,
) (*model.PuzzleFilter, error) {
	ctx = graphql.WithPathContext(ctx, graphql.NewPathWithField("filter"))
	if tmp, ok := rawArgs["filter"]; ok {
		return ec.unmarshalOPuzzleFilter2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐPuzzleFilter(ctx, tmp)
	}

	var zeroVal *model.PuzzleFilter
	return zeroVal, nil
}

func (ec *executionContext) field_Query_myPuzzles_argsPagination(
	ctx context.Context,
	rawArgs map[string]any,
) (*model.OffsetPaginationInput, error) {
	ctx = graphql.WithPathContext(ctx, graphql.NewPathWithField("pagination"))
	if tmp, ok := rawArgs["pagination"]; ok {
		return ec.unmarshalOOffsetPaginationInput2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐOffsetPaginationInput(ctx, tmp)
	}

	var zeroVal *model.OffsetPaginationInput
	return zeroVal, nil
}

func (ec *executionContext) field_Query_myPuzzles_argsSort(
	ctx context.Context,
	rawArgs map[string]any,
) (*model.SortInput, error) {
	ctx = graphql.WithPathContext(ctx, graphql.NewPathWithField("sort"))
	if tmp, ok := rawArgs["sort"]; ok {
		return ec.unmarshalOSortInput2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐSortInput(ctx, tmp)
	}

	var zeroVal *model.SortInput
	return zeroVal, nil
}

func (ec *executionContext) field_Query_puzzle_args(ctx context.Context, rawArgs map[string]any) (map[string]any, error) {
	var err error
	args := map[string]any{}
	arg0, err := ec.field_Query_puzzle_argsID(ctx, rawArgs)
	if err != nil {
		return nil, err
	}
	args["id"] = arg0
	return args, nil
}
func (ec *executionContext) field_Query_puzzle_argsID(
	ctx context.Context,
	rawArgs map[string]any,
) (string, error) {
	ctx = graphql.WithPathContext(ctx, graphql.NewPathWithField("id"))
	if tmp, ok := rawArgs["id"]; ok {
		return ec.unmarshalNID2string(ctx, tmp)
	}

	var zeroVal string
	return zeroVal, nil
}

func (ec *executionContext) field___Directive_args_args(ctx context.Context, rawArgs map[string]any) (map[string]any, error) {
	var err error
	args := map[string]any{}
	arg0, err := ec.field___Directive_args_argsIncludeDeprecated(ctx, rawArgs)
	if err != nil {
		return nil, err
	}
	args["includeDeprecated"] = arg0
	return args, nil
}
func (ec *executionContext) field___Directive_args_argsIncludeDeprecated(
	ctx context.Context,
	rawArgs map[string]any,
) (*bool, error) {
	ctx = graphql.WithPathContext(ctx, graphql.NewPathWithField("includeDeprecated"))
	if tmp, ok := rawArgs["includeDeprecated"]; ok {
		return ec.unmarshalOBoolean2ᚖbool(ctx, tmp)
	}

	var zeroVal *bool
	return zeroVal, nil
}

func (ec *executionContext) field___Field_args_args(ctx context.Context, rawArgs map[string]any) (map[string]any, error) {
	var err error
	args := map[string]any{}
	arg0, err := ec.field___Field_args_argsIncludeDeprecated(ctx, rawArgs)
	if err != nil {
		return nil, err
	}
	args["includeDeprecated"] = arg0
	return args, nil
}
func (ec *executionContext) field___Field_args_argsIncludeDeprecated(
	ctx context.Context,
	rawArgs map[string]any,
) (*bool, error) {
	ctx = graphql.WithPathContext(ctx, graphql.NewPathWithField("includeDeprecated"))
	if tmp, ok := rawArgs["includeDeprecated"]; ok {
		return ec.unmarshalOBoolean2ᚖbool(ctx, tmp)
	}

	var zeroVal *bool
	return zeroVal, nil
}

func (ec *executionContext) field___Type_enumValues_args(ctx context.Context, rawArgs map[string]any) (map[string]any, error) {
	var err error
	args := map[string]any{}
	arg0, err := ec.field___Type_enumValues_argsIncludeDeprecated(ctx, rawArgs)
	if err != nil {
		return nil, err
	}
	args["includeDeprecated"] = arg0
	return args, nil
}
func (ec *executionContext) field___Type_enumValues_argsIncludeDeprecated(
	ctx context.Context,
	rawArgs map[string]any,
) (bool, error) {
	ctx = graphql.WithPathContext(ctx, graphql.NewPathWithField("includeDeprecated"))
	if tmp, ok := rawArgs["includeDeprecated"]; ok {
		return ec.unmarshalOBoolean2bool(ctx, tmp)
	}

	var zeroVal bool
	return zeroVal, nil
}

func (ec *executionContext) field___Type_fields_args(ctx context.Context, rawArgs map[string]any) (map[string]any, error) {
	var err error
	args := map[string]any{}
	arg0, err := ec.field___Type_fields_argsIncludeDeprecated(ctx, rawArgs)
	if err != nil {
		return nil, err
	}
	args["includeDeprecated"] = arg0
	return args, nil
}
func (ec *executionContext) field___Type_fields_argsIncludeDeprecated(
	ctx context.Context,
	rawArgs map[string]any,
) (bool, error) {
	ctx = graphql.WithPathContext(ctx, graphql.NewPathWithField("includeDeprecated"))
	if tmp, ok := rawArgs["includeDeprecated"]; ok {
		return ec.unmarshalOBoolean2bool(ctx, tmp)
	}

	var zeroVal bool
	return zeroVal, nil
}

// endregion ***************************** args.gotpl *****************************

// region    ************************** directives.gotpl **************************

// endregion ************************** directives.gotpl **************************

// region    **************************** field.gotpl *****************************

func (ec *executionContext) _ColorCount_color(ctx context.Context, field graphql.CollectedField, obj *model.ColorCount) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ColorCount_color(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.ColorCount().Color(rctx, obj)
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.Color)
	fc.Result = res
	return ec.marshalNColor2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐColor(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ColorCount_color(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ColorCount",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Color does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ColorCount_count(ctx context.Context, field graphql.CollectedField, obj *model.ColorCount) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ColorCount_count(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.ColorCount().Count(rctx, obj)
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int32)
	fc.Result = res
	return ec.marshalNInt2int32(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ColorCount_count(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ColorCount",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Game_id(ctx context.Context, field graphql.CollectedField, obj *models.Game) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Game_id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNID2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Game_id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Game",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Game_user_id(ctx context.Context, field graphql.CollectedField, obj *models.Game) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Game_user_id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNID2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Game_user_id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Game",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Game_platform(ctx context.Context, field graphql.CollectedField, obj *models.Game) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Game_platform(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Platform, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.ChessPlatform)
	fc.Result = res
	return ec.marshalNChessPlatform2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐChessPlatform(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Game_platform(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Game",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ChessPlatform does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Game_chess_username(ctx context.Context, field graphql.CollectedField, obj *models.Game) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Game_chess_username(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ChessUsername, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Game_chess_username(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Game",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Game_user_color(ctx context.Context, field graphql.CollectedField, obj *models.Game) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Game_user_color(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserColor, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.Color)
	fc.Result = res
	return ec.marshalNColor2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐColor(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Game_user_color(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Game",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Color does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Game_game_time(ctx context.Context, field graphql.CollectedField, obj *models.Game) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Game_game_time(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.GameTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Game_game_time(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Game",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Game_pgn(ctx context.Context, field graphql.CollectedField, obj *models.Game) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Game_pgn(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.Game().Pgn(rctx, obj)
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Game_pgn(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Game",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Game_time_control(ctx context.Context, field graphql.CollectedField, obj *models.Game) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Game_time_control(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TimeControl, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Game_time_control(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Game",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Game_rated(ctx context.Context, field graphql.CollectedField, obj *models.Game) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Game_rated(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Rated, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Game_rated(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Game",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Game_url(ctx context.Context, field graphql.CollectedField, obj *models.Game) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Game_url(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.URL, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Game_url(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Game",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Game_white_player(ctx context.Context, field graphql.CollectedField, obj *models.Game) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Game_white_player(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.WhitePlayer, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Game_white_player(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Game",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Game_black_player(ctx context.Context, field graphql.CollectedField, obj *models.Game) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Game_black_player(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.BlackPlayer, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Game_black_player(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Game",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Game_winner(ctx context.Context, field graphql.CollectedField, obj *models.Game) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Game_winner(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Winner, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.Winner)
	fc.Result = res
	return ec.marshalNWinner2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐWinner(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Game_winner(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Game",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Winner does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Game_result(ctx context.Context, field graphql.CollectedField, obj *models.Game) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Game_result(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Result, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.GameResult)
	fc.Result = res
	return ec.marshalNGameResult2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐGameResult(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Game_result(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Game",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type GameResult does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Game_created_at(ctx context.Context, field graphql.CollectedField, obj *models.Game) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Game_created_at(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CreatedAt, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Game_created_at(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Game",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Game_updated_at(ctx context.Context, field graphql.CollectedField, obj *models.Game) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Game_updated_at(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UpdatedAt, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Game_updated_at(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Game",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Game_puzzles(ctx context.Context, field graphql.CollectedField, obj *models.Game) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Game_puzzles(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Puzzles, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]models.Puzzle)
	fc.Result = res
	return ec.marshalOPuzzle2ᚕgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐPuzzleᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Game_puzzles(ctx context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Game",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "id":
				return ec.fieldContext_Puzzle_id(ctx, field)
			case "game_id":
				return ec.fieldContext_Puzzle_game_id(ctx, field)
			case "user_id":
				return ec.fieldContext_Puzzle_user_id(ctx, field)
			case "game_move":
				return ec.fieldContext_Puzzle_game_move(ctx, field)
			case "fen":
				return ec.fieldContext_Puzzle_fen(ctx, field)
			case "moves":
				return ec.fieldContext_Puzzle_moves(ctx, field)
			case "prev_cp":
				return ec.fieldContext_Puzzle_prev_cp(ctx, field)
			case "cp":
				return ec.fieldContext_Puzzle_cp(ctx, field)
			case "theme":
				return ec.fieldContext_Puzzle_theme(ctx, field)
			case "user_color":
				return ec.fieldContext_Puzzle_user_color(ctx, field)
			case "puzzle_color":
				return ec.fieldContext_Puzzle_puzzle_color(ctx, field)
			case "zugzwang":
				return ec.fieldContext_Puzzle_zugzwang(ctx, field)
			case "tags":
				return ec.fieldContext_Puzzle_tags(ctx, field)
			case "created_at":
				return ec.fieldContext_Puzzle_created_at(ctx, field)
			case "updated_at":
				return ec.fieldContext_Puzzle_updated_at(ctx, field)
			case "game":
				return ec.fieldContext_Puzzle_game(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type Puzzle", field.Name)
		},
	}
	defer func() {
		if r := recover(); r != nil {
			err = ec.Recover(ctx, r)
			ec.Error(ctx, err)
		}
	}()
	ctx = graphql.WithFieldContext(ctx, fc)
	if fc.Args, err = ec.field_Game_puzzles_args(ctx, field.ArgumentMap(ec.Variables)); err != nil {
		ec.Error(ctx, err)
		return fc, err
	}
	return fc, nil
}

func (ec *executionContext) _GameConnection_edges(ctx context.Context, field graphql.CollectedField, obj *model.GameConnection) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GameConnection_edges(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Edges, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*model.GameEdge)
	fc.Result = res
	return ec.marshalNGameEdge2ᚕᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐGameEdgeᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GameConnection_edges(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GameConnection",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "node":
				return ec.fieldContext_GameEdge_node(ctx, field)
			case "cursor":
				return ec.fieldContext_GameEdge_cursor(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type GameEdge", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _GameConnection_page_info(ctx context.Context, field graphql.CollectedField, obj *model.GameConnection) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GameConnection_page_info(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PageInfo, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(*model.PageInfo)
	fc.Result = res
	return ec.marshalNPageInfo2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐPageInfo(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GameConnection_page_info(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GameConnection",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "has_next_page":
				return ec.fieldContext_PageInfo_has_next_page(ctx, field)
			case "has_previous_page":
				return ec.fieldContext_PageInfo_has_previous_page(ctx, field)
			case "start_cursor":
				return ec.fieldContext_PageInfo_start_cursor(ctx, field)
			case "end_cursor":
				return ec.fieldContext_PageInfo_end_cursor(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type PageInfo", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _GameConnection_total_count(ctx context.Context, field graphql.CollectedField, obj *model.GameConnection) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GameConnection_total_count(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TotalCount, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int32)
	fc.Result = res
	return ec.marshalNInt2int32(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GameConnection_total_count(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GameConnection",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GameEdge_node(ctx context.Context, field graphql.CollectedField, obj *model.GameEdge) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GameEdge_node(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.GameEdge().Node(rctx, obj)
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(*models.Game)
	fc.Result = res
	return ec.marshalNGame2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐGame(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GameEdge_node(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GameEdge",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "id":
				return ec.fieldContext_Game_id(ctx, field)
			case "user_id":
				return ec.fieldContext_Game_user_id(ctx, field)
			case "platform":
				return ec.fieldContext_Game_platform(ctx, field)
			case "chess_username":
				return ec.fieldContext_Game_chess_username(ctx, field)
			case "user_color":
				return ec.fieldContext_Game_user_color(ctx, field)
			case "game_time":
				return ec.fieldContext_Game_game_time(ctx, field)
			case "pgn":
				return ec.fieldContext_Game_pgn(ctx, field)
			case "time_control":
				return ec.fieldContext_Game_time_control(ctx, field)
			case "rated":
				return ec.fieldContext_Game_rated(ctx, field)
			case "url":
				return ec.fieldContext_Game_url(ctx, field)
			case "white_player":
				return ec.fieldContext_Game_white_player(ctx, field)
			case "black_player":
				return ec.fieldContext_Game_black_player(ctx, field)
			case "winner":
				return ec.fieldContext_Game_winner(ctx, field)
			case "result":
				return ec.fieldContext_Game_result(ctx, field)
			case "created_at":
				return ec.fieldContext_Game_created_at(ctx, field)
			case "updated_at":
				return ec.fieldContext_Game_updated_at(ctx, field)
			case "puzzles":
				return ec.fieldContext_Game_puzzles(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type Game", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _GameEdge_cursor(ctx context.Context, field graphql.CollectedField, obj *model.GameEdge) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GameEdge_cursor(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Cursor, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GameEdge_cursor(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GameEdge",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GameMoveBucket_name(ctx context.Context, field graphql.CollectedField, obj *model.GameMoveBucket) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GameMoveBucket_name(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Name, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GameMoveBucket_name(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GameMoveBucket",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GameMoveBucket_min_move(ctx context.Context, field graphql.CollectedField, obj *model.GameMoveBucket) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GameMoveBucket_min_move(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.GameMoveBucket().MinMove(rctx, obj)
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int32)
	fc.Result = res
	return ec.marshalNInt2int32(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GameMoveBucket_min_move(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GameMoveBucket",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GameMoveBucket_max_move(ctx context.Context, field graphql.CollectedField, obj *model.GameMoveBucket) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GameMoveBucket_max_move(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.GameMoveBucket().MaxMove(rctx, obj)
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int32)
	fc.Result = res
	return ec.marshalNInt2int32(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GameMoveBucket_max_move(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GameMoveBucket",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GameMoveBucket_count(ctx context.Context, field graphql.CollectedField, obj *model.GameMoveBucket) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GameMoveBucket_count(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.GameMoveBucket().Count(rctx, obj)
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int32)
	fc.Result = res
	return ec.marshalNInt2int32(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GameMoveBucket_count(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GameMoveBucket",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GameStats_platform_counts(ctx context.Context, field graphql.CollectedField, obj *model.GameStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GameStats_platform_counts(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PlatformCounts, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]model.PlatformCount)
	fc.Result = res
	return ec.marshalNPlatformCount2ᚕgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐPlatformCountᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GameStats_platform_counts(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GameStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "platform":
				return ec.fieldContext_PlatformCount_platform(ctx, field)
			case "count":
				return ec.fieldContext_PlatformCount_count(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type PlatformCount", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _GameStats_user_color_counts(ctx context.Context, field graphql.CollectedField, obj *model.GameStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GameStats_user_color_counts(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserColorCounts, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]model.ColorCount)
	fc.Result = res
	return ec.marshalNColorCount2ᚕgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐColorCountᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GameStats_user_color_counts(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GameStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "color":
				return ec.fieldContext_ColorCount_color(ctx, field)
			case "count":
				return ec.fieldContext_ColorCount_count(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type ColorCount", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _GameStats_result_counts(ctx context.Context, field graphql.CollectedField, obj *model.GameStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GameStats_result_counts(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ResultCounts, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]model.ResultCount)
	fc.Result = res
	return ec.marshalNResultCount2ᚕgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐResultCountᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GameStats_result_counts(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GameStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "result":
				return ec.fieldContext_ResultCount_result(ctx, field)
			case "count":
				return ec.fieldContext_ResultCount_count(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type ResultCount", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _GameStats_time_control_counts(ctx context.Context, field graphql.CollectedField, obj *model.GameStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GameStats_time_control_counts(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TimeControlCounts, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]model.TimeControlCount)
	fc.Result = res
	return ec.marshalNTimeControlCount2ᚕgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐTimeControlCountᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GameStats_time_control_counts(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GameStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "time_control":
				return ec.fieldContext_TimeControlCount_time_control(ctx, field)
			case "count":
				return ec.fieldContext_TimeControlCount_count(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type TimeControlCount", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _GameStats_rated_counts(ctx context.Context, field graphql.CollectedField, obj *model.GameStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GameStats_rated_counts(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.RatedCounts, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]model.RatedCount)
	fc.Result = res
	return ec.marshalNRatedCount2ᚕgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐRatedCountᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GameStats_rated_counts(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GameStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "rated":
				return ec.fieldContext_RatedCount_rated(ctx, field)
			case "count":
				return ec.fieldContext_RatedCount_count(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type RatedCount", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _GameStats_average_opponent_rating(ctx context.Context, field graphql.CollectedField, obj *model.GameStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GameStats_average_opponent_rating(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.AverageOpponentRating, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(float64)
	fc.Result = res
	return ec.marshalNFloat2float64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GameStats_average_opponent_rating(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GameStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Float does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GameStats_total_count(ctx context.Context, field graphql.CollectedField, obj *model.GameStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GameStats_total_count(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.GameStats().TotalCount(rctx, obj)
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int32)
	fc.Result = res
	return ec.marshalNInt2int32(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GameStats_total_count(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GameStats",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GroupedGameStats_start_time(ctx context.Context, field graphql.CollectedField, obj *model.GroupedGameStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GroupedGameStats_start_time(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.StartTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GroupedGameStats_start_time(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GroupedGameStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GroupedGameStats_end_time(ctx context.Context, field graphql.CollectedField, obj *model.GroupedGameStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GroupedGameStats_end_time(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.EndTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GroupedGameStats_end_time(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GroupedGameStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GroupedGameStats_stats(ctx context.Context, field graphql.CollectedField, obj *model.GroupedGameStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GroupedGameStats_stats(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Stats, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(*model.GameStats)
	fc.Result = res
	return ec.marshalNGameStats2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐGameStats(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GroupedGameStats_stats(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GroupedGameStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "platform_counts":
				return ec.fieldContext_GameStats_platform_counts(ctx, field)
			case "user_color_counts":
				return ec.fieldContext_GameStats_user_color_counts(ctx, field)
			case "result_counts":
				return ec.fieldContext_GameStats_result_counts(ctx, field)
			case "time_control_counts":
				return ec.fieldContext_GameStats_time_control_counts(ctx, field)
			case "rated_counts":
				return ec.fieldContext_GameStats_rated_counts(ctx, field)
			case "average_opponent_rating":
				return ec.fieldContext_GameStats_average_opponent_rating(ctx, field)
			case "total_count":
				return ec.fieldContext_GameStats_total_count(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type GameStats", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _GroupedGameStatsConnection_nodes(ctx context.Context, field graphql.CollectedField, obj *model.GroupedGameStatsConnection) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GroupedGameStatsConnection_nodes(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Nodes, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*model.GroupedGameStats)
	fc.Result = res
	return ec.marshalNGroupedGameStats2ᚕᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐGroupedGameStatsᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GroupedGameStatsConnection_nodes(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GroupedGameStatsConnection",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "start_time":
				return ec.fieldContext_GroupedGameStats_start_time(ctx, field)
			case "end_time":
				return ec.fieldContext_GroupedGameStats_end_time(ctx, field)
			case "stats":
				return ec.fieldContext_GroupedGameStats_stats(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type GroupedGameStats", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _GroupedGameStatsConnection_total_count(ctx context.Context, field graphql.CollectedField, obj *model.GroupedGameStatsConnection) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GroupedGameStatsConnection_total_count(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.GroupedGameStatsConnection().TotalCount(rctx, obj)
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int32)
	fc.Result = res
	return ec.marshalNInt2int32(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GroupedGameStatsConnection_total_count(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GroupedGameStatsConnection",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GroupedPuzzleStats_start_time(ctx context.Context, field graphql.CollectedField, obj *model.GroupedPuzzleStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GroupedPuzzleStats_start_time(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.StartTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GroupedPuzzleStats_start_time(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GroupedPuzzleStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GroupedPuzzleStats_end_time(ctx context.Context, field graphql.CollectedField, obj *model.GroupedPuzzleStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GroupedPuzzleStats_end_time(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.EndTime, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GroupedPuzzleStats_end_time(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GroupedPuzzleStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _GroupedPuzzleStats_stats(ctx context.Context, field graphql.CollectedField, obj *model.GroupedPuzzleStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GroupedPuzzleStats_stats(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Stats, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(*model.PuzzleStats)
	fc.Result = res
	return ec.marshalNPuzzleStats2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐPuzzleStats(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GroupedPuzzleStats_stats(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GroupedPuzzleStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "tag_counts":
				return ec.fieldContext_PuzzleStats_tag_counts(ctx, field)
			case "theme_counts":
				return ec.fieldContext_PuzzleStats_theme_counts(ctx, field)
			case "user_color_counts":
				return ec.fieldContext_PuzzleStats_user_color_counts(ctx, field)
			case "game_move_buckets":
				return ec.fieldContext_PuzzleStats_game_move_buckets(ctx, field)
			case "move_length_counts":
				return ec.fieldContext_PuzzleStats_move_length_counts(ctx, field)
			case "total_count":
				return ec.fieldContext_PuzzleStats_total_count(ctx, field)
			case "unique_game_count":
				return ec.fieldContext_PuzzleStats_unique_game_count(ctx, field)
			case "average_move_length":
				return ec.fieldContext_PuzzleStats_average_move_length(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type PuzzleStats", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _GroupedPuzzleStatsConnection_nodes(ctx context.Context, field graphql.CollectedField, obj *model.GroupedPuzzleStatsConnection) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GroupedPuzzleStatsConnection_nodes(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Nodes, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*model.GroupedPuzzleStats)
	fc.Result = res
	return ec.marshalNGroupedPuzzleStats2ᚕᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐGroupedPuzzleStatsᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GroupedPuzzleStatsConnection_nodes(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GroupedPuzzleStatsConnection",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "start_time":
				return ec.fieldContext_GroupedPuzzleStats_start_time(ctx, field)
			case "end_time":
				return ec.fieldContext_GroupedPuzzleStats_end_time(ctx, field)
			case "stats":
				return ec.fieldContext_GroupedPuzzleStats_stats(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type GroupedPuzzleStats", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _GroupedPuzzleStatsConnection_total_count(ctx context.Context, field graphql.CollectedField, obj *model.GroupedPuzzleStatsConnection) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_GroupedPuzzleStatsConnection_total_count(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.GroupedPuzzleStatsConnection().TotalCount(rctx, obj)
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int32)
	fc.Result = res
	return ec.marshalNInt2int32(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_GroupedPuzzleStatsConnection_total_count(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "GroupedPuzzleStatsConnection",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _MoveLengthBucket_length(ctx context.Context, field graphql.CollectedField, obj *model.MoveLengthBucket) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_MoveLengthBucket_length(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.MoveLengthBucket().Length(rctx, obj)
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int32)
	fc.Result = res
	return ec.marshalNInt2int32(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_MoveLengthBucket_length(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "MoveLengthBucket",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _MoveLengthBucket_count(ctx context.Context, field graphql.CollectedField, obj *model.MoveLengthBucket) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_MoveLengthBucket_count(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.MoveLengthBucket().Count(rctx, obj)
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int32)
	fc.Result = res
	return ec.marshalNInt2int32(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_MoveLengthBucket_count(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "MoveLengthBucket",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PageInfo_has_next_page(ctx context.Context, field graphql.CollectedField, obj *model.PageInfo) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PageInfo_has_next_page(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.HasNextPage, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PageInfo_has_next_page(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PageInfo",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PageInfo_has_previous_page(ctx context.Context, field graphql.CollectedField, obj *model.PageInfo) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PageInfo_has_previous_page(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.HasPreviousPage, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PageInfo_has_previous_page(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PageInfo",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PageInfo_start_cursor(ctx context.Context, field graphql.CollectedField, obj *model.PageInfo) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PageInfo_start_cursor(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.StartCursor, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalOString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PageInfo_start_cursor(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PageInfo",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PageInfo_end_cursor(ctx context.Context, field graphql.CollectedField, obj *model.PageInfo) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PageInfo_end_cursor(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.EndCursor, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalOString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PageInfo_end_cursor(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PageInfo",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PlatformCount_platform(ctx context.Context, field graphql.CollectedField, obj *model.PlatformCount) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PlatformCount_platform(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.PlatformCount().Platform(rctx, obj)
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.ChessPlatform)
	fc.Result = res
	return ec.marshalNChessPlatform2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐChessPlatform(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PlatformCount_platform(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PlatformCount",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ChessPlatform does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PlatformCount_count(ctx context.Context, field graphql.CollectedField, obj *model.PlatformCount) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PlatformCount_count(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.PlatformCount().Count(rctx, obj)
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int32)
	fc.Result = res
	return ec.marshalNInt2int32(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PlatformCount_count(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PlatformCount",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Puzzle_id(ctx context.Context, field graphql.CollectedField, obj *models.Puzzle) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Puzzle_id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNID2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Puzzle_id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Puzzle",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Puzzle_game_id(ctx context.Context, field graphql.CollectedField, obj *models.Puzzle) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Puzzle_game_id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.GameID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNID2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Puzzle_game_id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Puzzle",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Puzzle_user_id(ctx context.Context, field graphql.CollectedField, obj *models.Puzzle) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Puzzle_user_id(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserID, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNID2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Puzzle_user_id(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Puzzle",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type ID does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Puzzle_game_move(ctx context.Context, field graphql.CollectedField, obj *models.Puzzle) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Puzzle_game_move(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.Puzzle().GameMove(rctx, obj)
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int32)
	fc.Result = res
	return ec.marshalNInt2int32(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Puzzle_game_move(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Puzzle",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Puzzle_fen(ctx context.Context, field graphql.CollectedField, obj *models.Puzzle) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Puzzle_fen(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.FEN, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Puzzle_fen(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Puzzle",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Puzzle_moves(ctx context.Context, field graphql.CollectedField, obj *models.Puzzle) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Puzzle_moves(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.Puzzle().Moves(rctx, obj)
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]string)
	fc.Result = res
	return ec.marshalNString2ᚕstringᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Puzzle_moves(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Puzzle",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Puzzle_prev_cp(ctx context.Context, field graphql.CollectedField, obj *models.Puzzle) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Puzzle_prev_cp(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.Puzzle().PrevCp(rctx, obj)
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int32)
	fc.Result = res
	return ec.marshalNInt2int32(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Puzzle_prev_cp(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Puzzle",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Puzzle_cp(ctx context.Context, field graphql.CollectedField, obj *models.Puzzle) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Puzzle_cp(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.Puzzle().Cp(rctx, obj)
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int32)
	fc.Result = res
	return ec.marshalNInt2int32(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Puzzle_cp(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Puzzle",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Puzzle_theme(ctx context.Context, field graphql.CollectedField, obj *models.Puzzle) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Puzzle_theme(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Theme, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.PuzzleTheme)
	fc.Result = res
	return ec.marshalNPuzzleTheme2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐPuzzleTheme(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Puzzle_theme(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Puzzle",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type PuzzleTheme does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Puzzle_user_color(ctx context.Context, field graphql.CollectedField, obj *models.Puzzle) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Puzzle_user_color(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserColor, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.Color)
	fc.Result = res
	return ec.marshalNColor2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐColor(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Puzzle_user_color(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Puzzle",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Color does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Puzzle_puzzle_color(ctx context.Context, field graphql.CollectedField, obj *models.Puzzle) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Puzzle_puzzle_color(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PuzzleColor, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.Color)
	fc.Result = res
	return ec.marshalNColor2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐColor(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Puzzle_puzzle_color(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Puzzle",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Color does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Puzzle_zugzwang(ctx context.Context, field graphql.CollectedField, obj *models.Puzzle) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Puzzle_zugzwang(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Zugzwang, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Puzzle_zugzwang(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Puzzle",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Puzzle_tags(ctx context.Context, field graphql.CollectedField, obj *models.Puzzle) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Puzzle_tags(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.Puzzle().Tags(rctx, obj)
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]string)
	fc.Result = res
	return ec.marshalNString2ᚕstringᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Puzzle_tags(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Puzzle",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Puzzle_created_at(ctx context.Context, field graphql.CollectedField, obj *models.Puzzle) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Puzzle_created_at(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.CreatedAt, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Puzzle_created_at(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Puzzle",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Puzzle_updated_at(ctx context.Context, field graphql.CollectedField, obj *models.Puzzle) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Puzzle_updated_at(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UpdatedAt, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(time.Time)
	fc.Result = res
	return ec.marshalNTime2timeᚐTime(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Puzzle_updated_at(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Puzzle",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Time does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Puzzle_game(ctx context.Context, field graphql.CollectedField, obj *models.Puzzle) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Puzzle_game(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Game, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(models.Game)
	fc.Result = res
	return ec.marshalOGame2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐGame(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Puzzle_game(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Puzzle",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "id":
				return ec.fieldContext_Game_id(ctx, field)
			case "user_id":
				return ec.fieldContext_Game_user_id(ctx, field)
			case "platform":
				return ec.fieldContext_Game_platform(ctx, field)
			case "chess_username":
				return ec.fieldContext_Game_chess_username(ctx, field)
			case "user_color":
				return ec.fieldContext_Game_user_color(ctx, field)
			case "game_time":
				return ec.fieldContext_Game_game_time(ctx, field)
			case "pgn":
				return ec.fieldContext_Game_pgn(ctx, field)
			case "time_control":
				return ec.fieldContext_Game_time_control(ctx, field)
			case "rated":
				return ec.fieldContext_Game_rated(ctx, field)
			case "url":
				return ec.fieldContext_Game_url(ctx, field)
			case "white_player":
				return ec.fieldContext_Game_white_player(ctx, field)
			case "black_player":
				return ec.fieldContext_Game_black_player(ctx, field)
			case "winner":
				return ec.fieldContext_Game_winner(ctx, field)
			case "result":
				return ec.fieldContext_Game_result(ctx, field)
			case "created_at":
				return ec.fieldContext_Game_created_at(ctx, field)
			case "updated_at":
				return ec.fieldContext_Game_updated_at(ctx, field)
			case "puzzles":
				return ec.fieldContext_Game_puzzles(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type Game", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleConnection_edges(ctx context.Context, field graphql.CollectedField, obj *model.PuzzleConnection) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleConnection_edges(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Edges, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]*model.PuzzleEdge)
	fc.Result = res
	return ec.marshalNPuzzleEdge2ᚕᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐPuzzleEdgeᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleConnection_edges(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleConnection",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "node":
				return ec.fieldContext_PuzzleEdge_node(ctx, field)
			case "cursor":
				return ec.fieldContext_PuzzleEdge_cursor(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type PuzzleEdge", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleConnection_page_info(ctx context.Context, field graphql.CollectedField, obj *model.PuzzleConnection) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleConnection_page_info(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PageInfo, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(*model.PageInfo)
	fc.Result = res
	return ec.marshalNPageInfo2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐPageInfo(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleConnection_page_info(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleConnection",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "has_next_page":
				return ec.fieldContext_PageInfo_has_next_page(ctx, field)
			case "has_previous_page":
				return ec.fieldContext_PageInfo_has_previous_page(ctx, field)
			case "start_cursor":
				return ec.fieldContext_PageInfo_start_cursor(ctx, field)
			case "end_cursor":
				return ec.fieldContext_PageInfo_end_cursor(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type PageInfo", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleConnection_total_count(ctx context.Context, field graphql.CollectedField, obj *model.PuzzleConnection) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleConnection_total_count(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TotalCount, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int32)
	fc.Result = res
	return ec.marshalNInt2int32(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleConnection_total_count(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleConnection",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleEdge_node(ctx context.Context, field graphql.CollectedField, obj *model.PuzzleEdge) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleEdge_node(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Node, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(*models.Puzzle)
	fc.Result = res
	return ec.marshalNPuzzle2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐPuzzle(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleEdge_node(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleEdge",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "id":
				return ec.fieldContext_Puzzle_id(ctx, field)
			case "game_id":
				return ec.fieldContext_Puzzle_game_id(ctx, field)
			case "user_id":
				return ec.fieldContext_Puzzle_user_id(ctx, field)
			case "game_move":
				return ec.fieldContext_Puzzle_game_move(ctx, field)
			case "fen":
				return ec.fieldContext_Puzzle_fen(ctx, field)
			case "moves":
				return ec.fieldContext_Puzzle_moves(ctx, field)
			case "prev_cp":
				return ec.fieldContext_Puzzle_prev_cp(ctx, field)
			case "cp":
				return ec.fieldContext_Puzzle_cp(ctx, field)
			case "theme":
				return ec.fieldContext_Puzzle_theme(ctx, field)
			case "user_color":
				return ec.fieldContext_Puzzle_user_color(ctx, field)
			case "puzzle_color":
				return ec.fieldContext_Puzzle_puzzle_color(ctx, field)
			case "zugzwang":
				return ec.fieldContext_Puzzle_zugzwang(ctx, field)
			case "tags":
				return ec.fieldContext_Puzzle_tags(ctx, field)
			case "created_at":
				return ec.fieldContext_Puzzle_created_at(ctx, field)
			case "updated_at":
				return ec.fieldContext_Puzzle_updated_at(ctx, field)
			case "game":
				return ec.fieldContext_Puzzle_game(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type Puzzle", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleEdge_cursor(ctx context.Context, field graphql.CollectedField, obj *model.PuzzleEdge) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleEdge_cursor(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Cursor, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleEdge_cursor(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleEdge",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleStats_tag_counts(ctx context.Context, field graphql.CollectedField, obj *model.PuzzleStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleStats_tag_counts(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TagCounts, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]model.TagCount)
	fc.Result = res
	return ec.marshalNTagCount2ᚕgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐTagCountᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleStats_tag_counts(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "tag":
				return ec.fieldContext_TagCount_tag(ctx, field)
			case "count":
				return ec.fieldContext_TagCount_count(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type TagCount", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleStats_theme_counts(ctx context.Context, field graphql.CollectedField, obj *model.PuzzleStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleStats_theme_counts(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.ThemeCounts, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]model.ThemeCount)
	fc.Result = res
	return ec.marshalNThemeCount2ᚕgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐThemeCountᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleStats_theme_counts(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "theme":
				return ec.fieldContext_ThemeCount_theme(ctx, field)
			case "count":
				return ec.fieldContext_ThemeCount_count(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type ThemeCount", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleStats_user_color_counts(ctx context.Context, field graphql.CollectedField, obj *model.PuzzleStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleStats_user_color_counts(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.UserColorCounts, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]model.ColorCount)
	fc.Result = res
	return ec.marshalNColorCount2ᚕgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐColorCountᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleStats_user_color_counts(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "color":
				return ec.fieldContext_ColorCount_color(ctx, field)
			case "count":
				return ec.fieldContext_ColorCount_count(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type ColorCount", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleStats_game_move_buckets(ctx context.Context, field graphql.CollectedField, obj *model.PuzzleStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleStats_game_move_buckets(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.GameMoveBuckets, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]model.GameMoveBucket)
	fc.Result = res
	return ec.marshalNGameMoveBucket2ᚕgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐGameMoveBucketᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleStats_game_move_buckets(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "name":
				return ec.fieldContext_GameMoveBucket_name(ctx, field)
			case "min_move":
				return ec.fieldContext_GameMoveBucket_min_move(ctx, field)
			case "max_move":
				return ec.fieldContext_GameMoveBucket_max_move(ctx, field)
			case "count":
				return ec.fieldContext_GameMoveBucket_count(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type GameMoveBucket", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleStats_move_length_counts(ctx context.Context, field graphql.CollectedField, obj *model.PuzzleStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleStats_move_length_counts(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.MoveLengthCounts, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]model.MoveLengthBucket)
	fc.Result = res
	return ec.marshalNMoveLengthBucket2ᚕgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐMoveLengthBucketᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleStats_move_length_counts(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "length":
				return ec.fieldContext_MoveLengthBucket_length(ctx, field)
			case "count":
				return ec.fieldContext_MoveLengthBucket_count(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type MoveLengthBucket", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleStats_total_count(ctx context.Context, field graphql.CollectedField, obj *model.PuzzleStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleStats_total_count(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.PuzzleStats().TotalCount(rctx, obj)
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int32)
	fc.Result = res
	return ec.marshalNInt2int32(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleStats_total_count(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleStats",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleStats_unique_game_count(ctx context.Context, field graphql.CollectedField, obj *model.PuzzleStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleStats_unique_game_count(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.PuzzleStats().UniqueGameCount(rctx, obj)
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int32)
	fc.Result = res
	return ec.marshalNInt2int32(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleStats_unique_game_count(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleStats",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _PuzzleStats_average_move_length(ctx context.Context, field graphql.CollectedField, obj *model.PuzzleStats) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_PuzzleStats_average_move_length(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.AverageMoveLength, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(float64)
	fc.Result = res
	return ec.marshalNFloat2float64(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_PuzzleStats_average_move_length(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "PuzzleStats",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Float does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _Query_game(ctx context.Context, field graphql.CollectedField) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Query_game(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.Query().Game(rctx, fc.Args["id"].(string))
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.Game)
	fc.Result = res
	return ec.marshalOGame2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐGame(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Query_game(ctx context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Query",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "id":
				return ec.fieldContext_Game_id(ctx, field)
			case "user_id":
				return ec.fieldContext_Game_user_id(ctx, field)
			case "platform":
				return ec.fieldContext_Game_platform(ctx, field)
			case "chess_username":
				return ec.fieldContext_Game_chess_username(ctx, field)
			case "user_color":
				return ec.fieldContext_Game_user_color(ctx, field)
			case "game_time":
				return ec.fieldContext_Game_game_time(ctx, field)
			case "pgn":
				return ec.fieldContext_Game_pgn(ctx, field)
			case "time_control":
				return ec.fieldContext_Game_time_control(ctx, field)
			case "rated":
				return ec.fieldContext_Game_rated(ctx, field)
			case "url":
				return ec.fieldContext_Game_url(ctx, field)
			case "white_player":
				return ec.fieldContext_Game_white_player(ctx, field)
			case "black_player":
				return ec.fieldContext_Game_black_player(ctx, field)
			case "winner":
				return ec.fieldContext_Game_winner(ctx, field)
			case "result":
				return ec.fieldContext_Game_result(ctx, field)
			case "created_at":
				return ec.fieldContext_Game_created_at(ctx, field)
			case "updated_at":
				return ec.fieldContext_Game_updated_at(ctx, field)
			case "puzzles":
				return ec.fieldContext_Game_puzzles(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type Game", field.Name)
		},
	}
	defer func() {
		if r := recover(); r != nil {
			err = ec.Recover(ctx, r)
			ec.Error(ctx, err)
		}
	}()
	ctx = graphql.WithFieldContext(ctx, fc)
	if fc.Args, err = ec.field_Query_game_args(ctx, field.ArgumentMap(ec.Variables)); err != nil {
		ec.Error(ctx, err)
		return fc, err
	}
	return fc, nil
}

func (ec *executionContext) _Query_puzzle(ctx context.Context, field graphql.CollectedField) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Query_puzzle(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.Query().Puzzle(rctx, fc.Args["id"].(string))
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*models.Puzzle)
	fc.Result = res
	return ec.marshalOPuzzle2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐPuzzle(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Query_puzzle(ctx context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Query",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "id":
				return ec.fieldContext_Puzzle_id(ctx, field)
			case "game_id":
				return ec.fieldContext_Puzzle_game_id(ctx, field)
			case "user_id":
				return ec.fieldContext_Puzzle_user_id(ctx, field)
			case "game_move":
				return ec.fieldContext_Puzzle_game_move(ctx, field)
			case "fen":
				return ec.fieldContext_Puzzle_fen(ctx, field)
			case "moves":
				return ec.fieldContext_Puzzle_moves(ctx, field)
			case "prev_cp":
				return ec.fieldContext_Puzzle_prev_cp(ctx, field)
			case "cp":
				return ec.fieldContext_Puzzle_cp(ctx, field)
			case "theme":
				return ec.fieldContext_Puzzle_theme(ctx, field)
			case "user_color":
				return ec.fieldContext_Puzzle_user_color(ctx, field)
			case "puzzle_color":
				return ec.fieldContext_Puzzle_puzzle_color(ctx, field)
			case "zugzwang":
				return ec.fieldContext_Puzzle_zugzwang(ctx, field)
			case "tags":
				return ec.fieldContext_Puzzle_tags(ctx, field)
			case "created_at":
				return ec.fieldContext_Puzzle_created_at(ctx, field)
			case "updated_at":
				return ec.fieldContext_Puzzle_updated_at(ctx, field)
			case "game":
				return ec.fieldContext_Puzzle_game(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type Puzzle", field.Name)
		},
	}
	defer func() {
		if r := recover(); r != nil {
			err = ec.Recover(ctx, r)
			ec.Error(ctx, err)
		}
	}()
	ctx = graphql.WithFieldContext(ctx, fc)
	if fc.Args, err = ec.field_Query_puzzle_args(ctx, field.ArgumentMap(ec.Variables)); err != nil {
		ec.Error(ctx, err)
		return fc, err
	}
	return fc, nil
}

func (ec *executionContext) _Query_myGames(ctx context.Context, field graphql.CollectedField) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Query_myGames(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.Query().MyGames(rctx, fc.Args["filter"].(*model.GameFilter), fc.Args["pagination"].(*model.OffsetPaginationInput), fc.Args["sort"].(*model.SortInput))
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(*model.GameConnection)
	fc.Result = res
	return ec.marshalNGameConnection2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐGameConnection(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Query_myGames(ctx context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Query",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "edges":
				return ec.fieldContext_GameConnection_edges(ctx, field)
			case "page_info":
				return ec.fieldContext_GameConnection_page_info(ctx, field)
			case "total_count":
				return ec.fieldContext_GameConnection_total_count(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type GameConnection", field.Name)
		},
	}
	defer func() {
		if r := recover(); r != nil {
			err = ec.Recover(ctx, r)
			ec.Error(ctx, err)
		}
	}()
	ctx = graphql.WithFieldContext(ctx, fc)
	if fc.Args, err = ec.field_Query_myGames_args(ctx, field.ArgumentMap(ec.Variables)); err != nil {
		ec.Error(ctx, err)
		return fc, err
	}
	return fc, nil
}

func (ec *executionContext) _Query_myPuzzles(ctx context.Context, field graphql.CollectedField) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Query_myPuzzles(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.Query().MyPuzzles(rctx, fc.Args["filter"].(*model.PuzzleFilter), fc.Args["pagination"].(*model.OffsetPaginationInput), fc.Args["sort"].(*model.SortInput))
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(*model.PuzzleConnection)
	fc.Result = res
	return ec.marshalNPuzzleConnection2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐPuzzleConnection(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Query_myPuzzles(ctx context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Query",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "edges":
				return ec.fieldContext_PuzzleConnection_edges(ctx, field)
			case "page_info":
				return ec.fieldContext_PuzzleConnection_page_info(ctx, field)
			case "total_count":
				return ec.fieldContext_PuzzleConnection_total_count(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type PuzzleConnection", field.Name)
		},
	}
	defer func() {
		if r := recover(); r != nil {
			err = ec.Recover(ctx, r)
			ec.Error(ctx, err)
		}
	}()
	ctx = graphql.WithFieldContext(ctx, fc)
	if fc.Args, err = ec.field_Query_myPuzzles_args(ctx, field.ArgumentMap(ec.Variables)); err != nil {
		ec.Error(ctx, err)
		return fc, err
	}
	return fc, nil
}

func (ec *executionContext) _Query_myPuzzleStats(ctx context.Context, field graphql.CollectedField) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Query_myPuzzleStats(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.Query().MyPuzzleStats(rctx, fc.Args["filter"].(*model.PuzzleFilter), fc.Args["pagination"].(*model.OffsetPaginationInput))
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(*model.PuzzleStats)
	fc.Result = res
	return ec.marshalNPuzzleStats2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐPuzzleStats(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Query_myPuzzleStats(ctx context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Query",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "tag_counts":
				return ec.fieldContext_PuzzleStats_tag_counts(ctx, field)
			case "theme_counts":
				return ec.fieldContext_PuzzleStats_theme_counts(ctx, field)
			case "user_color_counts":
				return ec.fieldContext_PuzzleStats_user_color_counts(ctx, field)
			case "game_move_buckets":
				return ec.fieldContext_PuzzleStats_game_move_buckets(ctx, field)
			case "move_length_counts":
				return ec.fieldContext_PuzzleStats_move_length_counts(ctx, field)
			case "total_count":
				return ec.fieldContext_PuzzleStats_total_count(ctx, field)
			case "unique_game_count":
				return ec.fieldContext_PuzzleStats_unique_game_count(ctx, field)
			case "average_move_length":
				return ec.fieldContext_PuzzleStats_average_move_length(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type PuzzleStats", field.Name)
		},
	}
	defer func() {
		if r := recover(); r != nil {
			err = ec.Recover(ctx, r)
			ec.Error(ctx, err)
		}
	}()
	ctx = graphql.WithFieldContext(ctx, fc)
	if fc.Args, err = ec.field_Query_myPuzzleStats_args(ctx, field.ArgumentMap(ec.Variables)); err != nil {
		ec.Error(ctx, err)
		return fc, err
	}
	return fc, nil
}

func (ec *executionContext) _Query_myGameStats(ctx context.Context, field graphql.CollectedField) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Query_myGameStats(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.Query().MyGameStats(rctx, fc.Args["filter"].(*model.GameFilter), fc.Args["pagination"].(*model.OffsetPaginationInput))
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(*model.GameStats)
	fc.Result = res
	return ec.marshalNGameStats2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐGameStats(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Query_myGameStats(ctx context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Query",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "platform_counts":
				return ec.fieldContext_GameStats_platform_counts(ctx, field)
			case "user_color_counts":
				return ec.fieldContext_GameStats_user_color_counts(ctx, field)
			case "result_counts":
				return ec.fieldContext_GameStats_result_counts(ctx, field)
			case "time_control_counts":
				return ec.fieldContext_GameStats_time_control_counts(ctx, field)
			case "rated_counts":
				return ec.fieldContext_GameStats_rated_counts(ctx, field)
			case "average_opponent_rating":
				return ec.fieldContext_GameStats_average_opponent_rating(ctx, field)
			case "total_count":
				return ec.fieldContext_GameStats_total_count(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type GameStats", field.Name)
		},
	}
	defer func() {
		if r := recover(); r != nil {
			err = ec.Recover(ctx, r)
			ec.Error(ctx, err)
		}
	}()
	ctx = graphql.WithFieldContext(ctx, fc)
	if fc.Args, err = ec.field_Query_myGameStats_args(ctx, field.ArgumentMap(ec.Variables)); err != nil {
		ec.Error(ctx, err)
		return fc, err
	}
	return fc, nil
}

func (ec *executionContext) _Query_myGroupedPuzzleStats(ctx context.Context, field graphql.CollectedField) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Query_myGroupedPuzzleStats(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.Query().MyGroupedPuzzleStats(rctx, fc.Args["filter"].(*model.PuzzleFilter), fc.Args["pagination"].(*model.OffsetPaginationInput), fc.Args["group_unit"].(model.TimeGrouping), fc.Args["group_length"].(*int32))
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(*model.GroupedPuzzleStatsConnection)
	fc.Result = res
	return ec.marshalNGroupedPuzzleStatsConnection2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐGroupedPuzzleStatsConnection(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Query_myGroupedPuzzleStats(ctx context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Query",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "nodes":
				return ec.fieldContext_GroupedPuzzleStatsConnection_nodes(ctx, field)
			case "total_count":
				return ec.fieldContext_GroupedPuzzleStatsConnection_total_count(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type GroupedPuzzleStatsConnection", field.Name)
		},
	}
	defer func() {
		if r := recover(); r != nil {
			err = ec.Recover(ctx, r)
			ec.Error(ctx, err)
		}
	}()
	ctx = graphql.WithFieldContext(ctx, fc)
	if fc.Args, err = ec.field_Query_myGroupedPuzzleStats_args(ctx, field.ArgumentMap(ec.Variables)); err != nil {
		ec.Error(ctx, err)
		return fc, err
	}
	return fc, nil
}

func (ec *executionContext) _Query_myGroupedGameStats(ctx context.Context, field graphql.CollectedField) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Query_myGroupedGameStats(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.Query().MyGroupedGameStats(rctx, fc.Args["filter"].(*model.GameFilter), fc.Args["pagination"].(*model.OffsetPaginationInput), fc.Args["group_unit"].(model.TimeGrouping), fc.Args["group_length"].(*int32))
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(*model.GroupedGameStatsConnection)
	fc.Result = res
	return ec.marshalNGroupedGameStatsConnection2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐGroupedGameStatsConnection(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Query_myGroupedGameStats(ctx context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Query",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "nodes":
				return ec.fieldContext_GroupedGameStatsConnection_nodes(ctx, field)
			case "total_count":
				return ec.fieldContext_GroupedGameStatsConnection_total_count(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type GroupedGameStatsConnection", field.Name)
		},
	}
	defer func() {
		if r := recover(); r != nil {
			err = ec.Recover(ctx, r)
			ec.Error(ctx, err)
		}
	}()
	ctx = graphql.WithFieldContext(ctx, fc)
	if fc.Args, err = ec.field_Query_myGroupedGameStats_args(ctx, field.ArgumentMap(ec.Variables)); err != nil {
		ec.Error(ctx, err)
		return fc, err
	}
	return fc, nil
}

func (ec *executionContext) _Query___type(ctx context.Context, field graphql.CollectedField) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Query___type(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.introspectType(fc.Args["name"].(string))
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*introspection.Type)
	fc.Result = res
	return ec.marshalO__Type2ᚖgithubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐType(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Query___type(ctx context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Query",
		Field:      field,
		IsMethod:   true,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "kind":
				return ec.fieldContext___Type_kind(ctx, field)
			case "name":
				return ec.fieldContext___Type_name(ctx, field)
			case "description":
				return ec.fieldContext___Type_description(ctx, field)
			case "specifiedByURL":
				return ec.fieldContext___Type_specifiedByURL(ctx, field)
			case "fields":
				return ec.fieldContext___Type_fields(ctx, field)
			case "interfaces":
				return ec.fieldContext___Type_interfaces(ctx, field)
			case "possibleTypes":
				return ec.fieldContext___Type_possibleTypes(ctx, field)
			case "enumValues":
				return ec.fieldContext___Type_enumValues(ctx, field)
			case "inputFields":
				return ec.fieldContext___Type_inputFields(ctx, field)
			case "ofType":
				return ec.fieldContext___Type_ofType(ctx, field)
			case "isOneOf":
				return ec.fieldContext___Type_isOneOf(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type __Type", field.Name)
		},
	}
	defer func() {
		if r := recover(); r != nil {
			err = ec.Recover(ctx, r)
			ec.Error(ctx, err)
		}
	}()
	ctx = graphql.WithFieldContext(ctx, fc)
	if fc.Args, err = ec.field_Query___type_args(ctx, field.ArgumentMap(ec.Variables)); err != nil {
		ec.Error(ctx, err)
		return fc, err
	}
	return fc, nil
}

func (ec *executionContext) _Query___schema(ctx context.Context, field graphql.CollectedField) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_Query___schema(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.introspectSchema()
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*introspection.Schema)
	fc.Result = res
	return ec.marshalO__Schema2ᚖgithubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐSchema(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_Query___schema(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "Query",
		Field:      field,
		IsMethod:   true,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "description":
				return ec.fieldContext___Schema_description(ctx, field)
			case "types":
				return ec.fieldContext___Schema_types(ctx, field)
			case "queryType":
				return ec.fieldContext___Schema_queryType(ctx, field)
			case "mutationType":
				return ec.fieldContext___Schema_mutationType(ctx, field)
			case "subscriptionType":
				return ec.fieldContext___Schema_subscriptionType(ctx, field)
			case "directives":
				return ec.fieldContext___Schema_directives(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type __Schema", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) _RatedCount_rated(ctx context.Context, field graphql.CollectedField, obj *model.RatedCount) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_RatedCount_rated(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Rated, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_RatedCount_rated(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "RatedCount",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _RatedCount_count(ctx context.Context, field graphql.CollectedField, obj *model.RatedCount) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_RatedCount_count(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.RatedCount().Count(rctx, obj)
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int32)
	fc.Result = res
	return ec.marshalNInt2int32(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_RatedCount_count(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "RatedCount",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ResultCount_result(ctx context.Context, field graphql.CollectedField, obj *model.ResultCount) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ResultCount_result(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.ResultCount().Result(rctx, obj)
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.GameResult)
	fc.Result = res
	return ec.marshalNGameResult2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐGameResult(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ResultCount_result(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ResultCount",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type GameResult does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ResultCount_count(ctx context.Context, field graphql.CollectedField, obj *model.ResultCount) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ResultCount_count(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.ResultCount().Count(rctx, obj)
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int32)
	fc.Result = res
	return ec.marshalNInt2int32(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ResultCount_count(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ResultCount",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _TagCount_tag(ctx context.Context, field graphql.CollectedField, obj *model.TagCount) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_TagCount_tag(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Tag, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_TagCount_tag(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "TagCount",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _TagCount_count(ctx context.Context, field graphql.CollectedField, obj *model.TagCount) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_TagCount_count(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.TagCount().Count(rctx, obj)
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int32)
	fc.Result = res
	return ec.marshalNInt2int32(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_TagCount_count(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "TagCount",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ThemeCount_theme(ctx context.Context, field graphql.CollectedField, obj *model.ThemeCount) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ThemeCount_theme(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.ThemeCount().Theme(rctx, obj)
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(models.PuzzleTheme)
	fc.Result = res
	return ec.marshalNPuzzleTheme2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐPuzzleTheme(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ThemeCount_theme(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ThemeCount",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type PuzzleTheme does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _ThemeCount_count(ctx context.Context, field graphql.CollectedField, obj *model.ThemeCount) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_ThemeCount_count(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.ThemeCount().Count(rctx, obj)
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int32)
	fc.Result = res
	return ec.marshalNInt2int32(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_ThemeCount_count(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "ThemeCount",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _TimeControlCount_time_control(ctx context.Context, field graphql.CollectedField, obj *model.TimeControlCount) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_TimeControlCount_time_control(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.TimeControl, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_TimeControlCount_time_control(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "TimeControlCount",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) _TimeControlCount_count(ctx context.Context, field graphql.CollectedField, obj *model.TimeControlCount) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext_TimeControlCount_count(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return ec.resolvers.TimeControlCount().Count(rctx, obj)
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(int32)
	fc.Result = res
	return ec.marshalNInt2int32(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext_TimeControlCount_count(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "TimeControlCount",
		Field:      field,
		IsMethod:   true,
		IsResolver: true,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Int does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) ___Directive_name(ctx context.Context, field graphql.CollectedField, obj *introspection.Directive) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___Directive_name(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Name, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___Directive_name(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__Directive",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) ___Directive_description(ctx context.Context, field graphql.CollectedField, obj *introspection.Directive) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___Directive_description(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Description(), nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___Directive_description(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__Directive",
		Field:      field,
		IsMethod:   true,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) ___Directive_isRepeatable(ctx context.Context, field graphql.CollectedField, obj *introspection.Directive) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___Directive_isRepeatable(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.IsRepeatable, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___Directive_isRepeatable(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__Directive",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) ___Directive_locations(ctx context.Context, field graphql.CollectedField, obj *introspection.Directive) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___Directive_locations(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Locations, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]string)
	fc.Result = res
	return ec.marshalN__DirectiveLocation2ᚕstringᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___Directive_locations(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__Directive",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type __DirectiveLocation does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) ___Directive_args(ctx context.Context, field graphql.CollectedField, obj *introspection.Directive) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___Directive_args(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Args, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]introspection.InputValue)
	fc.Result = res
	return ec.marshalN__InputValue2ᚕgithubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐInputValueᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___Directive_args(ctx context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__Directive",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "name":
				return ec.fieldContext___InputValue_name(ctx, field)
			case "description":
				return ec.fieldContext___InputValue_description(ctx, field)
			case "type":
				return ec.fieldContext___InputValue_type(ctx, field)
			case "defaultValue":
				return ec.fieldContext___InputValue_defaultValue(ctx, field)
			case "isDeprecated":
				return ec.fieldContext___InputValue_isDeprecated(ctx, field)
			case "deprecationReason":
				return ec.fieldContext___InputValue_deprecationReason(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type __InputValue", field.Name)
		},
	}
	defer func() {
		if r := recover(); r != nil {
			err = ec.Recover(ctx, r)
			ec.Error(ctx, err)
		}
	}()
	ctx = graphql.WithFieldContext(ctx, fc)
	if fc.Args, err = ec.field___Directive_args_args(ctx, field.ArgumentMap(ec.Variables)); err != nil {
		ec.Error(ctx, err)
		return fc, err
	}
	return fc, nil
}

func (ec *executionContext) ___EnumValue_name(ctx context.Context, field graphql.CollectedField, obj *introspection.EnumValue) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___EnumValue_name(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Name, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___EnumValue_name(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__EnumValue",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) ___EnumValue_description(ctx context.Context, field graphql.CollectedField, obj *introspection.EnumValue) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___EnumValue_description(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Description(), nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___EnumValue_description(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__EnumValue",
		Field:      field,
		IsMethod:   true,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) ___EnumValue_isDeprecated(ctx context.Context, field graphql.CollectedField, obj *introspection.EnumValue) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___EnumValue_isDeprecated(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.IsDeprecated(), nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___EnumValue_isDeprecated(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__EnumValue",
		Field:      field,
		IsMethod:   true,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) ___EnumValue_deprecationReason(ctx context.Context, field graphql.CollectedField, obj *introspection.EnumValue) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___EnumValue_deprecationReason(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.DeprecationReason(), nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___EnumValue_deprecationReason(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__EnumValue",
		Field:      field,
		IsMethod:   true,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) ___Field_name(ctx context.Context, field graphql.CollectedField, obj *introspection.Field) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___Field_name(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Name, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___Field_name(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__Field",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) ___Field_description(ctx context.Context, field graphql.CollectedField, obj *introspection.Field) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___Field_description(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Description(), nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___Field_description(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__Field",
		Field:      field,
		IsMethod:   true,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) ___Field_args(ctx context.Context, field graphql.CollectedField, obj *introspection.Field) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___Field_args(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Args, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]introspection.InputValue)
	fc.Result = res
	return ec.marshalN__InputValue2ᚕgithubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐInputValueᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___Field_args(ctx context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__Field",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "name":
				return ec.fieldContext___InputValue_name(ctx, field)
			case "description":
				return ec.fieldContext___InputValue_description(ctx, field)
			case "type":
				return ec.fieldContext___InputValue_type(ctx, field)
			case "defaultValue":
				return ec.fieldContext___InputValue_defaultValue(ctx, field)
			case "isDeprecated":
				return ec.fieldContext___InputValue_isDeprecated(ctx, field)
			case "deprecationReason":
				return ec.fieldContext___InputValue_deprecationReason(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type __InputValue", field.Name)
		},
	}
	defer func() {
		if r := recover(); r != nil {
			err = ec.Recover(ctx, r)
			ec.Error(ctx, err)
		}
	}()
	ctx = graphql.WithFieldContext(ctx, fc)
	if fc.Args, err = ec.field___Field_args_args(ctx, field.ArgumentMap(ec.Variables)); err != nil {
		ec.Error(ctx, err)
		return fc, err
	}
	return fc, nil
}

func (ec *executionContext) ___Field_type(ctx context.Context, field graphql.CollectedField, obj *introspection.Field) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___Field_type(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Type, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(*introspection.Type)
	fc.Result = res
	return ec.marshalN__Type2ᚖgithubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐType(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___Field_type(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__Field",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "kind":
				return ec.fieldContext___Type_kind(ctx, field)
			case "name":
				return ec.fieldContext___Type_name(ctx, field)
			case "description":
				return ec.fieldContext___Type_description(ctx, field)
			case "specifiedByURL":
				return ec.fieldContext___Type_specifiedByURL(ctx, field)
			case "fields":
				return ec.fieldContext___Type_fields(ctx, field)
			case "interfaces":
				return ec.fieldContext___Type_interfaces(ctx, field)
			case "possibleTypes":
				return ec.fieldContext___Type_possibleTypes(ctx, field)
			case "enumValues":
				return ec.fieldContext___Type_enumValues(ctx, field)
			case "inputFields":
				return ec.fieldContext___Type_inputFields(ctx, field)
			case "ofType":
				return ec.fieldContext___Type_ofType(ctx, field)
			case "isOneOf":
				return ec.fieldContext___Type_isOneOf(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type __Type", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) ___Field_isDeprecated(ctx context.Context, field graphql.CollectedField, obj *introspection.Field) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___Field_isDeprecated(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.IsDeprecated(), nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___Field_isDeprecated(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__Field",
		Field:      field,
		IsMethod:   true,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) ___Field_deprecationReason(ctx context.Context, field graphql.CollectedField, obj *introspection.Field) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___Field_deprecationReason(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.DeprecationReason(), nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___Field_deprecationReason(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__Field",
		Field:      field,
		IsMethod:   true,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) ___InputValue_name(ctx context.Context, field graphql.CollectedField, obj *introspection.InputValue) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___InputValue_name(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Name, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalNString2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___InputValue_name(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__InputValue",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) ___InputValue_description(ctx context.Context, field graphql.CollectedField, obj *introspection.InputValue) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___InputValue_description(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Description(), nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___InputValue_description(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__InputValue",
		Field:      field,
		IsMethod:   true,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) ___InputValue_type(ctx context.Context, field graphql.CollectedField, obj *introspection.InputValue) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___InputValue_type(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Type, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(*introspection.Type)
	fc.Result = res
	return ec.marshalN__Type2ᚖgithubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐType(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___InputValue_type(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__InputValue",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "kind":
				return ec.fieldContext___Type_kind(ctx, field)
			case "name":
				return ec.fieldContext___Type_name(ctx, field)
			case "description":
				return ec.fieldContext___Type_description(ctx, field)
			case "specifiedByURL":
				return ec.fieldContext___Type_specifiedByURL(ctx, field)
			case "fields":
				return ec.fieldContext___Type_fields(ctx, field)
			case "interfaces":
				return ec.fieldContext___Type_interfaces(ctx, field)
			case "possibleTypes":
				return ec.fieldContext___Type_possibleTypes(ctx, field)
			case "enumValues":
				return ec.fieldContext___Type_enumValues(ctx, field)
			case "inputFields":
				return ec.fieldContext___Type_inputFields(ctx, field)
			case "ofType":
				return ec.fieldContext___Type_ofType(ctx, field)
			case "isOneOf":
				return ec.fieldContext___Type_isOneOf(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type __Type", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) ___InputValue_defaultValue(ctx context.Context, field graphql.CollectedField, obj *introspection.InputValue) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___InputValue_defaultValue(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.DefaultValue, nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___InputValue_defaultValue(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__InputValue",
		Field:      field,
		IsMethod:   false,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) ___InputValue_isDeprecated(ctx context.Context, field graphql.CollectedField, obj *introspection.InputValue) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___InputValue_isDeprecated(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.IsDeprecated(), nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalNBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___InputValue_isDeprecated(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__InputValue",
		Field:      field,
		IsMethod:   true,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) ___InputValue_deprecationReason(ctx context.Context, field graphql.CollectedField, obj *introspection.InputValue) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___InputValue_deprecationReason(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.DeprecationReason(), nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___InputValue_deprecationReason(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__InputValue",
		Field:      field,
		IsMethod:   true,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) ___Schema_description(ctx context.Context, field graphql.CollectedField, obj *introspection.Schema) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___Schema_description(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Description(), nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___Schema_description(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__Schema",
		Field:      field,
		IsMethod:   true,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) ___Schema_types(ctx context.Context, field graphql.CollectedField, obj *introspection.Schema) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___Schema_types(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Types(), nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]introspection.Type)
	fc.Result = res
	return ec.marshalN__Type2ᚕgithubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐTypeᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___Schema_types(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__Schema",
		Field:      field,
		IsMethod:   true,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "kind":
				return ec.fieldContext___Type_kind(ctx, field)
			case "name":
				return ec.fieldContext___Type_name(ctx, field)
			case "description":
				return ec.fieldContext___Type_description(ctx, field)
			case "specifiedByURL":
				return ec.fieldContext___Type_specifiedByURL(ctx, field)
			case "fields":
				return ec.fieldContext___Type_fields(ctx, field)
			case "interfaces":
				return ec.fieldContext___Type_interfaces(ctx, field)
			case "possibleTypes":
				return ec.fieldContext___Type_possibleTypes(ctx, field)
			case "enumValues":
				return ec.fieldContext___Type_enumValues(ctx, field)
			case "inputFields":
				return ec.fieldContext___Type_inputFields(ctx, field)
			case "ofType":
				return ec.fieldContext___Type_ofType(ctx, field)
			case "isOneOf":
				return ec.fieldContext___Type_isOneOf(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type __Type", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) ___Schema_queryType(ctx context.Context, field graphql.CollectedField, obj *introspection.Schema) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___Schema_queryType(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.QueryType(), nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(*introspection.Type)
	fc.Result = res
	return ec.marshalN__Type2ᚖgithubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐType(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___Schema_queryType(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__Schema",
		Field:      field,
		IsMethod:   true,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "kind":
				return ec.fieldContext___Type_kind(ctx, field)
			case "name":
				return ec.fieldContext___Type_name(ctx, field)
			case "description":
				return ec.fieldContext___Type_description(ctx, field)
			case "specifiedByURL":
				return ec.fieldContext___Type_specifiedByURL(ctx, field)
			case "fields":
				return ec.fieldContext___Type_fields(ctx, field)
			case "interfaces":
				return ec.fieldContext___Type_interfaces(ctx, field)
			case "possibleTypes":
				return ec.fieldContext___Type_possibleTypes(ctx, field)
			case "enumValues":
				return ec.fieldContext___Type_enumValues(ctx, field)
			case "inputFields":
				return ec.fieldContext___Type_inputFields(ctx, field)
			case "ofType":
				return ec.fieldContext___Type_ofType(ctx, field)
			case "isOneOf":
				return ec.fieldContext___Type_isOneOf(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type __Type", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) ___Schema_mutationType(ctx context.Context, field graphql.CollectedField, obj *introspection.Schema) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___Schema_mutationType(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.MutationType(), nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*introspection.Type)
	fc.Result = res
	return ec.marshalO__Type2ᚖgithubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐType(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___Schema_mutationType(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__Schema",
		Field:      field,
		IsMethod:   true,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "kind":
				return ec.fieldContext___Type_kind(ctx, field)
			case "name":
				return ec.fieldContext___Type_name(ctx, field)
			case "description":
				return ec.fieldContext___Type_description(ctx, field)
			case "specifiedByURL":
				return ec.fieldContext___Type_specifiedByURL(ctx, field)
			case "fields":
				return ec.fieldContext___Type_fields(ctx, field)
			case "interfaces":
				return ec.fieldContext___Type_interfaces(ctx, field)
			case "possibleTypes":
				return ec.fieldContext___Type_possibleTypes(ctx, field)
			case "enumValues":
				return ec.fieldContext___Type_enumValues(ctx, field)
			case "inputFields":
				return ec.fieldContext___Type_inputFields(ctx, field)
			case "ofType":
				return ec.fieldContext___Type_ofType(ctx, field)
			case "isOneOf":
				return ec.fieldContext___Type_isOneOf(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type __Type", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) ___Schema_subscriptionType(ctx context.Context, field graphql.CollectedField, obj *introspection.Schema) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___Schema_subscriptionType(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.SubscriptionType(), nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*introspection.Type)
	fc.Result = res
	return ec.marshalO__Type2ᚖgithubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐType(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___Schema_subscriptionType(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__Schema",
		Field:      field,
		IsMethod:   true,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "kind":
				return ec.fieldContext___Type_kind(ctx, field)
			case "name":
				return ec.fieldContext___Type_name(ctx, field)
			case "description":
				return ec.fieldContext___Type_description(ctx, field)
			case "specifiedByURL":
				return ec.fieldContext___Type_specifiedByURL(ctx, field)
			case "fields":
				return ec.fieldContext___Type_fields(ctx, field)
			case "interfaces":
				return ec.fieldContext___Type_interfaces(ctx, field)
			case "possibleTypes":
				return ec.fieldContext___Type_possibleTypes(ctx, field)
			case "enumValues":
				return ec.fieldContext___Type_enumValues(ctx, field)
			case "inputFields":
				return ec.fieldContext___Type_inputFields(ctx, field)
			case "ofType":
				return ec.fieldContext___Type_ofType(ctx, field)
			case "isOneOf":
				return ec.fieldContext___Type_isOneOf(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type __Type", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) ___Schema_directives(ctx context.Context, field graphql.CollectedField, obj *introspection.Schema) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___Schema_directives(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Directives(), nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.([]introspection.Directive)
	fc.Result = res
	return ec.marshalN__Directive2ᚕgithubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐDirectiveᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___Schema_directives(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__Schema",
		Field:      field,
		IsMethod:   true,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "name":
				return ec.fieldContext___Directive_name(ctx, field)
			case "description":
				return ec.fieldContext___Directive_description(ctx, field)
			case "isRepeatable":
				return ec.fieldContext___Directive_isRepeatable(ctx, field)
			case "locations":
				return ec.fieldContext___Directive_locations(ctx, field)
			case "args":
				return ec.fieldContext___Directive_args(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type __Directive", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) ___Type_kind(ctx context.Context, field graphql.CollectedField, obj *introspection.Type) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___Type_kind(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Kind(), nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		if !graphql.HasFieldError(ctx, fc) {
			ec.Errorf(ctx, "must not be null")
		}
		return graphql.Null
	}
	res := resTmp.(string)
	fc.Result = res
	return ec.marshalN__TypeKind2string(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___Type_kind(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__Type",
		Field:      field,
		IsMethod:   true,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type __TypeKind does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) ___Type_name(ctx context.Context, field graphql.CollectedField, obj *introspection.Type) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___Type_name(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Name(), nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___Type_name(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__Type",
		Field:      field,
		IsMethod:   true,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) ___Type_description(ctx context.Context, field graphql.CollectedField, obj *introspection.Type) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___Type_description(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Description(), nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___Type_description(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__Type",
		Field:      field,
		IsMethod:   true,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) ___Type_specifiedByURL(ctx context.Context, field graphql.CollectedField, obj *introspection.Type) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___Type_specifiedByURL(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.SpecifiedByURL(), nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*string)
	fc.Result = res
	return ec.marshalOString2ᚖstring(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___Type_specifiedByURL(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__Type",
		Field:      field,
		IsMethod:   true,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type String does not have child fields")
		},
	}
	return fc, nil
}

func (ec *executionContext) ___Type_fields(ctx context.Context, field graphql.CollectedField, obj *introspection.Type) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___Type_fields(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Fields(fc.Args["includeDeprecated"].(bool)), nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]introspection.Field)
	fc.Result = res
	return ec.marshalO__Field2ᚕgithubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐFieldᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___Type_fields(ctx context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__Type",
		Field:      field,
		IsMethod:   true,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "name":
				return ec.fieldContext___Field_name(ctx, field)
			case "description":
				return ec.fieldContext___Field_description(ctx, field)
			case "args":
				return ec.fieldContext___Field_args(ctx, field)
			case "type":
				return ec.fieldContext___Field_type(ctx, field)
			case "isDeprecated":
				return ec.fieldContext___Field_isDeprecated(ctx, field)
			case "deprecationReason":
				return ec.fieldContext___Field_deprecationReason(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type __Field", field.Name)
		},
	}
	defer func() {
		if r := recover(); r != nil {
			err = ec.Recover(ctx, r)
			ec.Error(ctx, err)
		}
	}()
	ctx = graphql.WithFieldContext(ctx, fc)
	if fc.Args, err = ec.field___Type_fields_args(ctx, field.ArgumentMap(ec.Variables)); err != nil {
		ec.Error(ctx, err)
		return fc, err
	}
	return fc, nil
}

func (ec *executionContext) ___Type_interfaces(ctx context.Context, field graphql.CollectedField, obj *introspection.Type) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___Type_interfaces(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.Interfaces(), nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]introspection.Type)
	fc.Result = res
	return ec.marshalO__Type2ᚕgithubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐTypeᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___Type_interfaces(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__Type",
		Field:      field,
		IsMethod:   true,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "kind":
				return ec.fieldContext___Type_kind(ctx, field)
			case "name":
				return ec.fieldContext___Type_name(ctx, field)
			case "description":
				return ec.fieldContext___Type_description(ctx, field)
			case "specifiedByURL":
				return ec.fieldContext___Type_specifiedByURL(ctx, field)
			case "fields":
				return ec.fieldContext___Type_fields(ctx, field)
			case "interfaces":
				return ec.fieldContext___Type_interfaces(ctx, field)
			case "possibleTypes":
				return ec.fieldContext___Type_possibleTypes(ctx, field)
			case "enumValues":
				return ec.fieldContext___Type_enumValues(ctx, field)
			case "inputFields":
				return ec.fieldContext___Type_inputFields(ctx, field)
			case "ofType":
				return ec.fieldContext___Type_ofType(ctx, field)
			case "isOneOf":
				return ec.fieldContext___Type_isOneOf(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type __Type", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) ___Type_possibleTypes(ctx context.Context, field graphql.CollectedField, obj *introspection.Type) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___Type_possibleTypes(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.PossibleTypes(), nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]introspection.Type)
	fc.Result = res
	return ec.marshalO__Type2ᚕgithubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐTypeᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___Type_possibleTypes(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__Type",
		Field:      field,
		IsMethod:   true,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "kind":
				return ec.fieldContext___Type_kind(ctx, field)
			case "name":
				return ec.fieldContext___Type_name(ctx, field)
			case "description":
				return ec.fieldContext___Type_description(ctx, field)
			case "specifiedByURL":
				return ec.fieldContext___Type_specifiedByURL(ctx, field)
			case "fields":
				return ec.fieldContext___Type_fields(ctx, field)
			case "interfaces":
				return ec.fieldContext___Type_interfaces(ctx, field)
			case "possibleTypes":
				return ec.fieldContext___Type_possibleTypes(ctx, field)
			case "enumValues":
				return ec.fieldContext___Type_enumValues(ctx, field)
			case "inputFields":
				return ec.fieldContext___Type_inputFields(ctx, field)
			case "ofType":
				return ec.fieldContext___Type_ofType(ctx, field)
			case "isOneOf":
				return ec.fieldContext___Type_isOneOf(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type __Type", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) ___Type_enumValues(ctx context.Context, field graphql.CollectedField, obj *introspection.Type) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___Type_enumValues(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.EnumValues(fc.Args["includeDeprecated"].(bool)), nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]introspection.EnumValue)
	fc.Result = res
	return ec.marshalO__EnumValue2ᚕgithubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐEnumValueᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___Type_enumValues(ctx context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__Type",
		Field:      field,
		IsMethod:   true,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "name":
				return ec.fieldContext___EnumValue_name(ctx, field)
			case "description":
				return ec.fieldContext___EnumValue_description(ctx, field)
			case "isDeprecated":
				return ec.fieldContext___EnumValue_isDeprecated(ctx, field)
			case "deprecationReason":
				return ec.fieldContext___EnumValue_deprecationReason(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type __EnumValue", field.Name)
		},
	}
	defer func() {
		if r := recover(); r != nil {
			err = ec.Recover(ctx, r)
			ec.Error(ctx, err)
		}
	}()
	ctx = graphql.WithFieldContext(ctx, fc)
	if fc.Args, err = ec.field___Type_enumValues_args(ctx, field.ArgumentMap(ec.Variables)); err != nil {
		ec.Error(ctx, err)
		return fc, err
	}
	return fc, nil
}

func (ec *executionContext) ___Type_inputFields(ctx context.Context, field graphql.CollectedField, obj *introspection.Type) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___Type_inputFields(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.InputFields(), nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.([]introspection.InputValue)
	fc.Result = res
	return ec.marshalO__InputValue2ᚕgithubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐInputValueᚄ(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___Type_inputFields(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__Type",
		Field:      field,
		IsMethod:   true,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "name":
				return ec.fieldContext___InputValue_name(ctx, field)
			case "description":
				return ec.fieldContext___InputValue_description(ctx, field)
			case "type":
				return ec.fieldContext___InputValue_type(ctx, field)
			case "defaultValue":
				return ec.fieldContext___InputValue_defaultValue(ctx, field)
			case "isDeprecated":
				return ec.fieldContext___InputValue_isDeprecated(ctx, field)
			case "deprecationReason":
				return ec.fieldContext___InputValue_deprecationReason(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type __InputValue", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) ___Type_ofType(ctx context.Context, field graphql.CollectedField, obj *introspection.Type) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___Type_ofType(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.OfType(), nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(*introspection.Type)
	fc.Result = res
	return ec.marshalO__Type2ᚖgithubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐType(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___Type_ofType(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__Type",
		Field:      field,
		IsMethod:   true,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			switch field.Name {
			case "kind":
				return ec.fieldContext___Type_kind(ctx, field)
			case "name":
				return ec.fieldContext___Type_name(ctx, field)
			case "description":
				return ec.fieldContext___Type_description(ctx, field)
			case "specifiedByURL":
				return ec.fieldContext___Type_specifiedByURL(ctx, field)
			case "fields":
				return ec.fieldContext___Type_fields(ctx, field)
			case "interfaces":
				return ec.fieldContext___Type_interfaces(ctx, field)
			case "possibleTypes":
				return ec.fieldContext___Type_possibleTypes(ctx, field)
			case "enumValues":
				return ec.fieldContext___Type_enumValues(ctx, field)
			case "inputFields":
				return ec.fieldContext___Type_inputFields(ctx, field)
			case "ofType":
				return ec.fieldContext___Type_ofType(ctx, field)
			case "isOneOf":
				return ec.fieldContext___Type_isOneOf(ctx, field)
			}
			return nil, fmt.Errorf("no field named %q was found under type __Type", field.Name)
		},
	}
	return fc, nil
}

func (ec *executionContext) ___Type_isOneOf(ctx context.Context, field graphql.CollectedField, obj *introspection.Type) (ret graphql.Marshaler) {
	fc, err := ec.fieldContext___Type_isOneOf(ctx, field)
	if err != nil {
		return graphql.Null
	}
	ctx = graphql.WithFieldContext(ctx, fc)
	defer func() {
		if r := recover(); r != nil {
			ec.Error(ctx, ec.Recover(ctx, r))
			ret = graphql.Null
		}
	}()
	resTmp, err := ec.ResolverMiddleware(ctx, func(rctx context.Context) (any, error) {
		ctx = rctx // use context from middleware stack in children
		return obj.IsOneOf(), nil
	})
	if err != nil {
		ec.Error(ctx, err)
		return graphql.Null
	}
	if resTmp == nil {
		return graphql.Null
	}
	res := resTmp.(bool)
	fc.Result = res
	return ec.marshalOBoolean2bool(ctx, field.Selections, res)
}

func (ec *executionContext) fieldContext___Type_isOneOf(_ context.Context, field graphql.CollectedField) (fc *graphql.FieldContext, err error) {
	fc = &graphql.FieldContext{
		Object:     "__Type",
		Field:      field,
		IsMethod:   true,
		IsResolver: false,
		Child: func(ctx context.Context, field graphql.CollectedField) (*graphql.FieldContext, error) {
			return nil, errors.New("field of type Boolean does not have child fields")
		},
	}
	return fc, nil
}

// endregion **************************** field.gotpl *****************************

// region    **************************** input.gotpl *****************************

func (ec *executionContext) unmarshalInputGameFilter(ctx context.Context, obj any) (model.GameFilter, error) {
	var it model.GameFilter
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"platform", "chess_username", "start_time", "end_time", "time_control", "rated", "result", "user_color"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "platform":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("platform"))
			data, err := ec.unmarshalOChessPlatform2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐChessPlatform(ctx, v)
			if err != nil {
				return it, err
			}
			it.Platform = data
		case "chess_username":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("chess_username"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.ChessUsername = data
		case "start_time":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("start_time"))
			data, err := ec.unmarshalOTime2ᚖtimeᚐTime(ctx, v)
			if err != nil {
				return it, err
			}
			it.StartTime = data
		case "end_time":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("end_time"))
			data, err := ec.unmarshalOTime2ᚖtimeᚐTime(ctx, v)
			if err != nil {
				return it, err
			}
			it.EndTime = data
		case "time_control":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("time_control"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.TimeControl = data
		case "rated":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("rated"))
			data, err := ec.unmarshalOBoolean2ᚖbool(ctx, v)
			if err != nil {
				return it, err
			}
			it.Rated = data
		case "result":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("result"))
			data, err := ec.unmarshalOGameResult2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐGameResult(ctx, v)
			if err != nil {
				return it, err
			}
			it.Result = data
		case "user_color":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("user_color"))
			data, err := ec.unmarshalOColor2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐColor(ctx, v)
			if err != nil {
				return it, err
			}
			it.UserColor = data
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputOffsetPaginationInput(ctx context.Context, obj any) (model.OffsetPaginationInput, error) {
	var it model.OffsetPaginationInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"offset", "limit"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "offset":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("offset"))
			data, err := ec.unmarshalOInt2ᚖint32(ctx, v)
			if err != nil {
				return it, err
			}
			if err = ec.resolvers.OffsetPaginationInput().Offset(ctx, &it, data); err != nil {
				return it, err
			}
		case "limit":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("limit"))
			data, err := ec.unmarshalOInt2ᚖint32(ctx, v)
			if err != nil {
				return it, err
			}
			if err = ec.resolvers.OffsetPaginationInput().Limit(ctx, &it, data); err != nil {
				return it, err
			}
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputPaginationInput(ctx context.Context, obj any) (model.PaginationInput, error) {
	var it model.PaginationInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"first", "after", "last", "before"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "first":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("first"))
			data, err := ec.unmarshalOInt2ᚖint32(ctx, v)
			if err != nil {
				return it, err
			}
			it.First = data
		case "after":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("after"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.After = data
		case "last":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("last"))
			data, err := ec.unmarshalOInt2ᚖint32(ctx, v)
			if err != nil {
				return it, err
			}
			it.Last = data
		case "before":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("before"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.Before = data
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputPuzzleFilter(ctx context.Context, obj any) (model.PuzzleFilter, error) {
	var it model.PuzzleFilter
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"tags", "game_start_time", "game_end_time", "themes", "user_color", "puzzle_color", "game_move_min", "game_move_max", "prev_cp_min", "prev_cp_max", "cp_change_min", "cp_change_max", "time_control", "rated"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "tags":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("tags"))
			data, err := ec.unmarshalOString2ᚕstringᚄ(ctx, v)
			if err != nil {
				return it, err
			}
			it.Tags = data
		case "game_start_time":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("game_start_time"))
			data, err := ec.unmarshalOTime2ᚖtimeᚐTime(ctx, v)
			if err != nil {
				return it, err
			}
			it.GameStartTime = data
		case "game_end_time":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("game_end_time"))
			data, err := ec.unmarshalOTime2ᚖtimeᚐTime(ctx, v)
			if err != nil {
				return it, err
			}
			it.GameEndTime = data
		case "themes":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("themes"))
			data, err := ec.unmarshalOPuzzleTheme2ᚕgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐPuzzleThemeᚄ(ctx, v)
			if err != nil {
				return it, err
			}
			it.Themes = data
		case "user_color":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("user_color"))
			data, err := ec.unmarshalOColor2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐColor(ctx, v)
			if err != nil {
				return it, err
			}
			it.UserColor = data
		case "puzzle_color":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("puzzle_color"))
			data, err := ec.unmarshalOColor2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐColor(ctx, v)
			if err != nil {
				return it, err
			}
			it.PuzzleColor = data
		case "game_move_min":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("game_move_min"))
			data, err := ec.unmarshalOInt2ᚖint32(ctx, v)
			if err != nil {
				return it, err
			}
			if err = ec.resolvers.PuzzleFilter().GameMoveMin(ctx, &it, data); err != nil {
				return it, err
			}
		case "game_move_max":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("game_move_max"))
			data, err := ec.unmarshalOInt2ᚖint32(ctx, v)
			if err != nil {
				return it, err
			}
			if err = ec.resolvers.PuzzleFilter().GameMoveMax(ctx, &it, data); err != nil {
				return it, err
			}
		case "prev_cp_min":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("prev_cp_min"))
			data, err := ec.unmarshalOInt2ᚖint32(ctx, v)
			if err != nil {
				return it, err
			}
			if err = ec.resolvers.PuzzleFilter().PrevCpMin(ctx, &it, data); err != nil {
				return it, err
			}
		case "prev_cp_max":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("prev_cp_max"))
			data, err := ec.unmarshalOInt2ᚖint32(ctx, v)
			if err != nil {
				return it, err
			}
			if err = ec.resolvers.PuzzleFilter().PrevCpMax(ctx, &it, data); err != nil {
				return it, err
			}
		case "cp_change_min":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("cp_change_min"))
			data, err := ec.unmarshalOInt2ᚖint32(ctx, v)
			if err != nil {
				return it, err
			}
			if err = ec.resolvers.PuzzleFilter().CpChangeMin(ctx, &it, data); err != nil {
				return it, err
			}
		case "cp_change_max":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("cp_change_max"))
			data, err := ec.unmarshalOInt2ᚖint32(ctx, v)
			if err != nil {
				return it, err
			}
			if err = ec.resolvers.PuzzleFilter().CpChangeMax(ctx, &it, data); err != nil {
				return it, err
			}
		case "time_control":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("time_control"))
			data, err := ec.unmarshalOString2ᚖstring(ctx, v)
			if err != nil {
				return it, err
			}
			it.TimeControl = data
		case "rated":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("rated"))
			data, err := ec.unmarshalOBoolean2ᚖbool(ctx, v)
			if err != nil {
				return it, err
			}
			it.Rated = data
		}
	}

	return it, nil
}

func (ec *executionContext) unmarshalInputSortInput(ctx context.Context, obj any) (model.SortInput, error) {
	var it model.SortInput
	asMap := map[string]any{}
	for k, v := range obj.(map[string]any) {
		asMap[k] = v
	}

	fieldsInOrder := [...]string{"field", "direction"}
	for _, k := range fieldsInOrder {
		v, ok := asMap[k]
		if !ok {
			continue
		}
		switch k {
		case "field":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("field"))
			data, err := ec.unmarshalNString2string(ctx, v)
			if err != nil {
				return it, err
			}
			it.Field = data
		case "direction":
			ctx := graphql.WithPathContext(ctx, graphql.NewPathWithField("direction"))
			data, err := ec.unmarshalNSortDirection2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐSortDirection(ctx, v)
			if err != nil {
				return it, err
			}
			it.Direction = data
		}
	}

	return it, nil
}

// endregion **************************** input.gotpl *****************************

// region    ************************** interface.gotpl ***************************

// endregion ************************** interface.gotpl ***************************

// region    **************************** object.gotpl ****************************

var colorCountImplementors = []string{"ColorCount"}

func (ec *executionContext) _ColorCount(ctx context.Context, sel ast.SelectionSet, obj *model.ColorCount) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, colorCountImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("ColorCount")
		case "color":
			field := field

			innerFunc := func(ctx context.Context, fs *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._ColorCount_color(ctx, field, obj)
				if res == graphql.Null {
					atomic.AddUint32(&fs.Invalids, 1)
				}
				return res
			}

			if field.Deferrable != nil {
				dfs, ok := deferred[field.Deferrable.Label]
				di := 0
				if ok {
					dfs.AddField(field)
					di = len(dfs.Values) - 1
				} else {
					dfs = graphql.NewFieldSet([]graphql.CollectedField{field})
					deferred[field.Deferrable.Label] = dfs
				}
				dfs.Concurrently(di, func(ctx context.Context) graphql.Marshaler {
					return innerFunc(ctx, dfs)
				})

				// don't run the out.Concurrently() call below
				out.Values[i] = graphql.Null
				continue
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
		case "count":
			field := field

			innerFunc := func(ctx context.Context, fs *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._ColorCount_count(ctx, field, obj)
				if res == graphql.Null {
					atomic.AddUint32(&fs.Invalids, 1)
				}
				return res
			}

			if field.Deferrable != nil {
				dfs, ok := deferred[field.Deferrable.Label]
				di := 0
				if ok {
					dfs.AddField(field)
					di = len(dfs.Values) - 1
				} else {
					dfs = graphql.NewFieldSet([]graphql.CollectedField{field})
					deferred[field.Deferrable.Label] = dfs
				}
				dfs.Concurrently(di, func(ctx context.Context) graphql.Marshaler {
					return innerFunc(ctx, dfs)
				})

				// don't run the out.Concurrently() call below
				out.Values[i] = graphql.Null
				continue
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var gameImplementors = []string{"Game"}

func (ec *executionContext) _Game(ctx context.Context, sel ast.SelectionSet, obj *models.Game) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, gameImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("Game")
		case "id":
			out.Values[i] = ec._Game_id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "user_id":
			out.Values[i] = ec._Game_user_id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "platform":
			out.Values[i] = ec._Game_platform(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "chess_username":
			out.Values[i] = ec._Game_chess_username(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "user_color":
			out.Values[i] = ec._Game_user_color(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "game_time":
			out.Values[i] = ec._Game_game_time(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "pgn":
			field := field

			innerFunc := func(ctx context.Context, fs *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._Game_pgn(ctx, field, obj)
				if res == graphql.Null {
					atomic.AddUint32(&fs.Invalids, 1)
				}
				return res
			}

			if field.Deferrable != nil {
				dfs, ok := deferred[field.Deferrable.Label]
				di := 0
				if ok {
					dfs.AddField(field)
					di = len(dfs.Values) - 1
				} else {
					dfs = graphql.NewFieldSet([]graphql.CollectedField{field})
					deferred[field.Deferrable.Label] = dfs
				}
				dfs.Concurrently(di, func(ctx context.Context) graphql.Marshaler {
					return innerFunc(ctx, dfs)
				})

				// don't run the out.Concurrently() call below
				out.Values[i] = graphql.Null
				continue
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
		case "time_control":
			out.Values[i] = ec._Game_time_control(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "rated":
			out.Values[i] = ec._Game_rated(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "url":
			out.Values[i] = ec._Game_url(ctx, field, obj)
		case "white_player":
			out.Values[i] = ec._Game_white_player(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "black_player":
			out.Values[i] = ec._Game_black_player(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "winner":
			out.Values[i] = ec._Game_winner(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "result":
			out.Values[i] = ec._Game_result(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "created_at":
			out.Values[i] = ec._Game_created_at(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "updated_at":
			out.Values[i] = ec._Game_updated_at(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "puzzles":
			out.Values[i] = ec._Game_puzzles(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var gameConnectionImplementors = []string{"GameConnection"}

func (ec *executionContext) _GameConnection(ctx context.Context, sel ast.SelectionSet, obj *model.GameConnection) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, gameConnectionImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("GameConnection")
		case "edges":
			out.Values[i] = ec._GameConnection_edges(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "page_info":
			out.Values[i] = ec._GameConnection_page_info(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "total_count":
			out.Values[i] = ec._GameConnection_total_count(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var gameEdgeImplementors = []string{"GameEdge"}

func (ec *executionContext) _GameEdge(ctx context.Context, sel ast.SelectionSet, obj *model.GameEdge) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, gameEdgeImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("GameEdge")
		case "node":
			field := field

			innerFunc := func(ctx context.Context, fs *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._GameEdge_node(ctx, field, obj)
				if res == graphql.Null {
					atomic.AddUint32(&fs.Invalids, 1)
				}
				return res
			}

			if field.Deferrable != nil {
				dfs, ok := deferred[field.Deferrable.Label]
				di := 0
				if ok {
					dfs.AddField(field)
					di = len(dfs.Values) - 1
				} else {
					dfs = graphql.NewFieldSet([]graphql.CollectedField{field})
					deferred[field.Deferrable.Label] = dfs
				}
				dfs.Concurrently(di, func(ctx context.Context) graphql.Marshaler {
					return innerFunc(ctx, dfs)
				})

				// don't run the out.Concurrently() call below
				out.Values[i] = graphql.Null
				continue
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
		case "cursor":
			out.Values[i] = ec._GameEdge_cursor(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var gameMoveBucketImplementors = []string{"GameMoveBucket"}

func (ec *executionContext) _GameMoveBucket(ctx context.Context, sel ast.SelectionSet, obj *model.GameMoveBucket) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, gameMoveBucketImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("GameMoveBucket")
		case "name":
			out.Values[i] = ec._GameMoveBucket_name(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "min_move":
			field := field

			innerFunc := func(ctx context.Context, fs *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._GameMoveBucket_min_move(ctx, field, obj)
				if res == graphql.Null {
					atomic.AddUint32(&fs.Invalids, 1)
				}
				return res
			}

			if field.Deferrable != nil {
				dfs, ok := deferred[field.Deferrable.Label]
				di := 0
				if ok {
					dfs.AddField(field)
					di = len(dfs.Values) - 1
				} else {
					dfs = graphql.NewFieldSet([]graphql.CollectedField{field})
					deferred[field.Deferrable.Label] = dfs
				}
				dfs.Concurrently(di, func(ctx context.Context) graphql.Marshaler {
					return innerFunc(ctx, dfs)
				})

				// don't run the out.Concurrently() call below
				out.Values[i] = graphql.Null
				continue
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
		case "max_move":
			field := field

			innerFunc := func(ctx context.Context, fs *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._GameMoveBucket_max_move(ctx, field, obj)
				if res == graphql.Null {
					atomic.AddUint32(&fs.Invalids, 1)
				}
				return res
			}

			if field.Deferrable != nil {
				dfs, ok := deferred[field.Deferrable.Label]
				di := 0
				if ok {
					dfs.AddField(field)
					di = len(dfs.Values) - 1
				} else {
					dfs = graphql.NewFieldSet([]graphql.CollectedField{field})
					deferred[field.Deferrable.Label] = dfs
				}
				dfs.Concurrently(di, func(ctx context.Context) graphql.Marshaler {
					return innerFunc(ctx, dfs)
				})

				// don't run the out.Concurrently() call below
				out.Values[i] = graphql.Null
				continue
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
		case "count":
			field := field

			innerFunc := func(ctx context.Context, fs *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._GameMoveBucket_count(ctx, field, obj)
				if res == graphql.Null {
					atomic.AddUint32(&fs.Invalids, 1)
				}
				return res
			}

			if field.Deferrable != nil {
				dfs, ok := deferred[field.Deferrable.Label]
				di := 0
				if ok {
					dfs.AddField(field)
					di = len(dfs.Values) - 1
				} else {
					dfs = graphql.NewFieldSet([]graphql.CollectedField{field})
					deferred[field.Deferrable.Label] = dfs
				}
				dfs.Concurrently(di, func(ctx context.Context) graphql.Marshaler {
					return innerFunc(ctx, dfs)
				})

				// don't run the out.Concurrently() call below
				out.Values[i] = graphql.Null
				continue
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var gameStatsImplementors = []string{"GameStats"}

func (ec *executionContext) _GameStats(ctx context.Context, sel ast.SelectionSet, obj *model.GameStats) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, gameStatsImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("GameStats")
		case "platform_counts":
			out.Values[i] = ec._GameStats_platform_counts(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "user_color_counts":
			out.Values[i] = ec._GameStats_user_color_counts(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "result_counts":
			out.Values[i] = ec._GameStats_result_counts(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "time_control_counts":
			out.Values[i] = ec._GameStats_time_control_counts(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "rated_counts":
			out.Values[i] = ec._GameStats_rated_counts(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "average_opponent_rating":
			out.Values[i] = ec._GameStats_average_opponent_rating(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "total_count":
			field := field

			innerFunc := func(ctx context.Context, fs *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._GameStats_total_count(ctx, field, obj)
				if res == graphql.Null {
					atomic.AddUint32(&fs.Invalids, 1)
				}
				return res
			}

			if field.Deferrable != nil {
				dfs, ok := deferred[field.Deferrable.Label]
				di := 0
				if ok {
					dfs.AddField(field)
					di = len(dfs.Values) - 1
				} else {
					dfs = graphql.NewFieldSet([]graphql.CollectedField{field})
					deferred[field.Deferrable.Label] = dfs
				}
				dfs.Concurrently(di, func(ctx context.Context) graphql.Marshaler {
					return innerFunc(ctx, dfs)
				})

				// don't run the out.Concurrently() call below
				out.Values[i] = graphql.Null
				continue
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var groupedGameStatsImplementors = []string{"GroupedGameStats"}

func (ec *executionContext) _GroupedGameStats(ctx context.Context, sel ast.SelectionSet, obj *model.GroupedGameStats) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, groupedGameStatsImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("GroupedGameStats")
		case "start_time":
			out.Values[i] = ec._GroupedGameStats_start_time(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "end_time":
			out.Values[i] = ec._GroupedGameStats_end_time(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "stats":
			out.Values[i] = ec._GroupedGameStats_stats(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var groupedGameStatsConnectionImplementors = []string{"GroupedGameStatsConnection"}

func (ec *executionContext) _GroupedGameStatsConnection(ctx context.Context, sel ast.SelectionSet, obj *model.GroupedGameStatsConnection) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, groupedGameStatsConnectionImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("GroupedGameStatsConnection")
		case "nodes":
			out.Values[i] = ec._GroupedGameStatsConnection_nodes(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "total_count":
			field := field

			innerFunc := func(ctx context.Context, fs *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._GroupedGameStatsConnection_total_count(ctx, field, obj)
				if res == graphql.Null {
					atomic.AddUint32(&fs.Invalids, 1)
				}
				return res
			}

			if field.Deferrable != nil {
				dfs, ok := deferred[field.Deferrable.Label]
				di := 0
				if ok {
					dfs.AddField(field)
					di = len(dfs.Values) - 1
				} else {
					dfs = graphql.NewFieldSet([]graphql.CollectedField{field})
					deferred[field.Deferrable.Label] = dfs
				}
				dfs.Concurrently(di, func(ctx context.Context) graphql.Marshaler {
					return innerFunc(ctx, dfs)
				})

				// don't run the out.Concurrently() call below
				out.Values[i] = graphql.Null
				continue
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var groupedPuzzleStatsImplementors = []string{"GroupedPuzzleStats"}

func (ec *executionContext) _GroupedPuzzleStats(ctx context.Context, sel ast.SelectionSet, obj *model.GroupedPuzzleStats) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, groupedPuzzleStatsImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("GroupedPuzzleStats")
		case "start_time":
			out.Values[i] = ec._GroupedPuzzleStats_start_time(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "end_time":
			out.Values[i] = ec._GroupedPuzzleStats_end_time(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "stats":
			out.Values[i] = ec._GroupedPuzzleStats_stats(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var groupedPuzzleStatsConnectionImplementors = []string{"GroupedPuzzleStatsConnection"}

func (ec *executionContext) _GroupedPuzzleStatsConnection(ctx context.Context, sel ast.SelectionSet, obj *model.GroupedPuzzleStatsConnection) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, groupedPuzzleStatsConnectionImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("GroupedPuzzleStatsConnection")
		case "nodes":
			out.Values[i] = ec._GroupedPuzzleStatsConnection_nodes(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "total_count":
			field := field

			innerFunc := func(ctx context.Context, fs *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._GroupedPuzzleStatsConnection_total_count(ctx, field, obj)
				if res == graphql.Null {
					atomic.AddUint32(&fs.Invalids, 1)
				}
				return res
			}

			if field.Deferrable != nil {
				dfs, ok := deferred[field.Deferrable.Label]
				di := 0
				if ok {
					dfs.AddField(field)
					di = len(dfs.Values) - 1
				} else {
					dfs = graphql.NewFieldSet([]graphql.CollectedField{field})
					deferred[field.Deferrable.Label] = dfs
				}
				dfs.Concurrently(di, func(ctx context.Context) graphql.Marshaler {
					return innerFunc(ctx, dfs)
				})

				// don't run the out.Concurrently() call below
				out.Values[i] = graphql.Null
				continue
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var moveLengthBucketImplementors = []string{"MoveLengthBucket"}

func (ec *executionContext) _MoveLengthBucket(ctx context.Context, sel ast.SelectionSet, obj *model.MoveLengthBucket) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, moveLengthBucketImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("MoveLengthBucket")
		case "length":
			field := field

			innerFunc := func(ctx context.Context, fs *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._MoveLengthBucket_length(ctx, field, obj)
				if res == graphql.Null {
					atomic.AddUint32(&fs.Invalids, 1)
				}
				return res
			}

			if field.Deferrable != nil {
				dfs, ok := deferred[field.Deferrable.Label]
				di := 0
				if ok {
					dfs.AddField(field)
					di = len(dfs.Values) - 1
				} else {
					dfs = graphql.NewFieldSet([]graphql.CollectedField{field})
					deferred[field.Deferrable.Label] = dfs
				}
				dfs.Concurrently(di, func(ctx context.Context) graphql.Marshaler {
					return innerFunc(ctx, dfs)
				})

				// don't run the out.Concurrently() call below
				out.Values[i] = graphql.Null
				continue
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
		case "count":
			field := field

			innerFunc := func(ctx context.Context, fs *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._MoveLengthBucket_count(ctx, field, obj)
				if res == graphql.Null {
					atomic.AddUint32(&fs.Invalids, 1)
				}
				return res
			}

			if field.Deferrable != nil {
				dfs, ok := deferred[field.Deferrable.Label]
				di := 0
				if ok {
					dfs.AddField(field)
					di = len(dfs.Values) - 1
				} else {
					dfs = graphql.NewFieldSet([]graphql.CollectedField{field})
					deferred[field.Deferrable.Label] = dfs
				}
				dfs.Concurrently(di, func(ctx context.Context) graphql.Marshaler {
					return innerFunc(ctx, dfs)
				})

				// don't run the out.Concurrently() call below
				out.Values[i] = graphql.Null
				continue
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var pageInfoImplementors = []string{"PageInfo"}

func (ec *executionContext) _PageInfo(ctx context.Context, sel ast.SelectionSet, obj *model.PageInfo) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, pageInfoImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("PageInfo")
		case "has_next_page":
			out.Values[i] = ec._PageInfo_has_next_page(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "has_previous_page":
			out.Values[i] = ec._PageInfo_has_previous_page(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "start_cursor":
			out.Values[i] = ec._PageInfo_start_cursor(ctx, field, obj)
		case "end_cursor":
			out.Values[i] = ec._PageInfo_end_cursor(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var platformCountImplementors = []string{"PlatformCount"}

func (ec *executionContext) _PlatformCount(ctx context.Context, sel ast.SelectionSet, obj *model.PlatformCount) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, platformCountImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("PlatformCount")
		case "platform":
			field := field

			innerFunc := func(ctx context.Context, fs *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._PlatformCount_platform(ctx, field, obj)
				if res == graphql.Null {
					atomic.AddUint32(&fs.Invalids, 1)
				}
				return res
			}

			if field.Deferrable != nil {
				dfs, ok := deferred[field.Deferrable.Label]
				di := 0
				if ok {
					dfs.AddField(field)
					di = len(dfs.Values) - 1
				} else {
					dfs = graphql.NewFieldSet([]graphql.CollectedField{field})
					deferred[field.Deferrable.Label] = dfs
				}
				dfs.Concurrently(di, func(ctx context.Context) graphql.Marshaler {
					return innerFunc(ctx, dfs)
				})

				// don't run the out.Concurrently() call below
				out.Values[i] = graphql.Null
				continue
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
		case "count":
			field := field

			innerFunc := func(ctx context.Context, fs *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._PlatformCount_count(ctx, field, obj)
				if res == graphql.Null {
					atomic.AddUint32(&fs.Invalids, 1)
				}
				return res
			}

			if field.Deferrable != nil {
				dfs, ok := deferred[field.Deferrable.Label]
				di := 0
				if ok {
					dfs.AddField(field)
					di = len(dfs.Values) - 1
				} else {
					dfs = graphql.NewFieldSet([]graphql.CollectedField{field})
					deferred[field.Deferrable.Label] = dfs
				}
				dfs.Concurrently(di, func(ctx context.Context) graphql.Marshaler {
					return innerFunc(ctx, dfs)
				})

				// don't run the out.Concurrently() call below
				out.Values[i] = graphql.Null
				continue
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var puzzleImplementors = []string{"Puzzle"}

func (ec *executionContext) _Puzzle(ctx context.Context, sel ast.SelectionSet, obj *models.Puzzle) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, puzzleImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("Puzzle")
		case "id":
			out.Values[i] = ec._Puzzle_id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "game_id":
			out.Values[i] = ec._Puzzle_game_id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "user_id":
			out.Values[i] = ec._Puzzle_user_id(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "game_move":
			field := field

			innerFunc := func(ctx context.Context, fs *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._Puzzle_game_move(ctx, field, obj)
				if res == graphql.Null {
					atomic.AddUint32(&fs.Invalids, 1)
				}
				return res
			}

			if field.Deferrable != nil {
				dfs, ok := deferred[field.Deferrable.Label]
				di := 0
				if ok {
					dfs.AddField(field)
					di = len(dfs.Values) - 1
				} else {
					dfs = graphql.NewFieldSet([]graphql.CollectedField{field})
					deferred[field.Deferrable.Label] = dfs
				}
				dfs.Concurrently(di, func(ctx context.Context) graphql.Marshaler {
					return innerFunc(ctx, dfs)
				})

				// don't run the out.Concurrently() call below
				out.Values[i] = graphql.Null
				continue
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
		case "fen":
			out.Values[i] = ec._Puzzle_fen(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "moves":
			field := field

			innerFunc := func(ctx context.Context, fs *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._Puzzle_moves(ctx, field, obj)
				if res == graphql.Null {
					atomic.AddUint32(&fs.Invalids, 1)
				}
				return res
			}

			if field.Deferrable != nil {
				dfs, ok := deferred[field.Deferrable.Label]
				di := 0
				if ok {
					dfs.AddField(field)
					di = len(dfs.Values) - 1
				} else {
					dfs = graphql.NewFieldSet([]graphql.CollectedField{field})
					deferred[field.Deferrable.Label] = dfs
				}
				dfs.Concurrently(di, func(ctx context.Context) graphql.Marshaler {
					return innerFunc(ctx, dfs)
				})

				// don't run the out.Concurrently() call below
				out.Values[i] = graphql.Null
				continue
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
		case "prev_cp":
			field := field

			innerFunc := func(ctx context.Context, fs *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._Puzzle_prev_cp(ctx, field, obj)
				if res == graphql.Null {
					atomic.AddUint32(&fs.Invalids, 1)
				}
				return res
			}

			if field.Deferrable != nil {
				dfs, ok := deferred[field.Deferrable.Label]
				di := 0
				if ok {
					dfs.AddField(field)
					di = len(dfs.Values) - 1
				} else {
					dfs = graphql.NewFieldSet([]graphql.CollectedField{field})
					deferred[field.Deferrable.Label] = dfs
				}
				dfs.Concurrently(di, func(ctx context.Context) graphql.Marshaler {
					return innerFunc(ctx, dfs)
				})

				// don't run the out.Concurrently() call below
				out.Values[i] = graphql.Null
				continue
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
		case "cp":
			field := field

			innerFunc := func(ctx context.Context, fs *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._Puzzle_cp(ctx, field, obj)
				if res == graphql.Null {
					atomic.AddUint32(&fs.Invalids, 1)
				}
				return res
			}

			if field.Deferrable != nil {
				dfs, ok := deferred[field.Deferrable.Label]
				di := 0
				if ok {
					dfs.AddField(field)
					di = len(dfs.Values) - 1
				} else {
					dfs = graphql.NewFieldSet([]graphql.CollectedField{field})
					deferred[field.Deferrable.Label] = dfs
				}
				dfs.Concurrently(di, func(ctx context.Context) graphql.Marshaler {
					return innerFunc(ctx, dfs)
				})

				// don't run the out.Concurrently() call below
				out.Values[i] = graphql.Null
				continue
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
		case "theme":
			out.Values[i] = ec._Puzzle_theme(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "user_color":
			out.Values[i] = ec._Puzzle_user_color(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "puzzle_color":
			out.Values[i] = ec._Puzzle_puzzle_color(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "zugzwang":
			out.Values[i] = ec._Puzzle_zugzwang(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "tags":
			field := field

			innerFunc := func(ctx context.Context, fs *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._Puzzle_tags(ctx, field, obj)
				if res == graphql.Null {
					atomic.AddUint32(&fs.Invalids, 1)
				}
				return res
			}

			if field.Deferrable != nil {
				dfs, ok := deferred[field.Deferrable.Label]
				di := 0
				if ok {
					dfs.AddField(field)
					di = len(dfs.Values) - 1
				} else {
					dfs = graphql.NewFieldSet([]graphql.CollectedField{field})
					deferred[field.Deferrable.Label] = dfs
				}
				dfs.Concurrently(di, func(ctx context.Context) graphql.Marshaler {
					return innerFunc(ctx, dfs)
				})

				// don't run the out.Concurrently() call below
				out.Values[i] = graphql.Null
				continue
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
		case "created_at":
			out.Values[i] = ec._Puzzle_created_at(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "updated_at":
			out.Values[i] = ec._Puzzle_updated_at(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "game":
			out.Values[i] = ec._Puzzle_game(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var puzzleConnectionImplementors = []string{"PuzzleConnection"}

func (ec *executionContext) _PuzzleConnection(ctx context.Context, sel ast.SelectionSet, obj *model.PuzzleConnection) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, puzzleConnectionImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("PuzzleConnection")
		case "edges":
			out.Values[i] = ec._PuzzleConnection_edges(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "page_info":
			out.Values[i] = ec._PuzzleConnection_page_info(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "total_count":
			out.Values[i] = ec._PuzzleConnection_total_count(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var puzzleEdgeImplementors = []string{"PuzzleEdge"}

func (ec *executionContext) _PuzzleEdge(ctx context.Context, sel ast.SelectionSet, obj *model.PuzzleEdge) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, puzzleEdgeImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("PuzzleEdge")
		case "node":
			out.Values[i] = ec._PuzzleEdge_node(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "cursor":
			out.Values[i] = ec._PuzzleEdge_cursor(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var puzzleStatsImplementors = []string{"PuzzleStats"}

func (ec *executionContext) _PuzzleStats(ctx context.Context, sel ast.SelectionSet, obj *model.PuzzleStats) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, puzzleStatsImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("PuzzleStats")
		case "tag_counts":
			out.Values[i] = ec._PuzzleStats_tag_counts(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "theme_counts":
			out.Values[i] = ec._PuzzleStats_theme_counts(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "user_color_counts":
			out.Values[i] = ec._PuzzleStats_user_color_counts(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "game_move_buckets":
			out.Values[i] = ec._PuzzleStats_game_move_buckets(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "move_length_counts":
			out.Values[i] = ec._PuzzleStats_move_length_counts(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "total_count":
			field := field

			innerFunc := func(ctx context.Context, fs *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._PuzzleStats_total_count(ctx, field, obj)
				if res == graphql.Null {
					atomic.AddUint32(&fs.Invalids, 1)
				}
				return res
			}

			if field.Deferrable != nil {
				dfs, ok := deferred[field.Deferrable.Label]
				di := 0
				if ok {
					dfs.AddField(field)
					di = len(dfs.Values) - 1
				} else {
					dfs = graphql.NewFieldSet([]graphql.CollectedField{field})
					deferred[field.Deferrable.Label] = dfs
				}
				dfs.Concurrently(di, func(ctx context.Context) graphql.Marshaler {
					return innerFunc(ctx, dfs)
				})

				// don't run the out.Concurrently() call below
				out.Values[i] = graphql.Null
				continue
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
		case "unique_game_count":
			field := field

			innerFunc := func(ctx context.Context, fs *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._PuzzleStats_unique_game_count(ctx, field, obj)
				if res == graphql.Null {
					atomic.AddUint32(&fs.Invalids, 1)
				}
				return res
			}

			if field.Deferrable != nil {
				dfs, ok := deferred[field.Deferrable.Label]
				di := 0
				if ok {
					dfs.AddField(field)
					di = len(dfs.Values) - 1
				} else {
					dfs = graphql.NewFieldSet([]graphql.CollectedField{field})
					deferred[field.Deferrable.Label] = dfs
				}
				dfs.Concurrently(di, func(ctx context.Context) graphql.Marshaler {
					return innerFunc(ctx, dfs)
				})

				// don't run the out.Concurrently() call below
				out.Values[i] = graphql.Null
				continue
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
		case "average_move_length":
			out.Values[i] = ec._PuzzleStats_average_move_length(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var queryImplementors = []string{"Query"}

func (ec *executionContext) _Query(ctx context.Context, sel ast.SelectionSet) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, queryImplementors)
	ctx = graphql.WithFieldContext(ctx, &graphql.FieldContext{
		Object: "Query",
	})

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		innerCtx := graphql.WithRootFieldContext(ctx, &graphql.RootFieldContext{
			Object: field.Name,
			Field:  field,
		})

		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("Query")
		case "game":
			field := field

			innerFunc := func(ctx context.Context, _ *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._Query_game(ctx, field)
				return res
			}

			rrm := func(ctx context.Context) graphql.Marshaler {
				return ec.OperationContext.RootResolverMiddleware(ctx,
					func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return rrm(innerCtx) })
		case "puzzle":
			field := field

			innerFunc := func(ctx context.Context, _ *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._Query_puzzle(ctx, field)
				return res
			}

			rrm := func(ctx context.Context) graphql.Marshaler {
				return ec.OperationContext.RootResolverMiddleware(ctx,
					func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return rrm(innerCtx) })
		case "myGames":
			field := field

			innerFunc := func(ctx context.Context, fs *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._Query_myGames(ctx, field)
				if res == graphql.Null {
					atomic.AddUint32(&fs.Invalids, 1)
				}
				return res
			}

			rrm := func(ctx context.Context) graphql.Marshaler {
				return ec.OperationContext.RootResolverMiddleware(ctx,
					func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return rrm(innerCtx) })
		case "myPuzzles":
			field := field

			innerFunc := func(ctx context.Context, fs *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._Query_myPuzzles(ctx, field)
				if res == graphql.Null {
					atomic.AddUint32(&fs.Invalids, 1)
				}
				return res
			}

			rrm := func(ctx context.Context) graphql.Marshaler {
				return ec.OperationContext.RootResolverMiddleware(ctx,
					func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return rrm(innerCtx) })
		case "myPuzzleStats":
			field := field

			innerFunc := func(ctx context.Context, fs *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._Query_myPuzzleStats(ctx, field)
				if res == graphql.Null {
					atomic.AddUint32(&fs.Invalids, 1)
				}
				return res
			}

			rrm := func(ctx context.Context) graphql.Marshaler {
				return ec.OperationContext.RootResolverMiddleware(ctx,
					func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return rrm(innerCtx) })
		case "myGameStats":
			field := field

			innerFunc := func(ctx context.Context, fs *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._Query_myGameStats(ctx, field)
				if res == graphql.Null {
					atomic.AddUint32(&fs.Invalids, 1)
				}
				return res
			}

			rrm := func(ctx context.Context) graphql.Marshaler {
				return ec.OperationContext.RootResolverMiddleware(ctx,
					func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return rrm(innerCtx) })
		case "myGroupedPuzzleStats":
			field := field

			innerFunc := func(ctx context.Context, fs *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._Query_myGroupedPuzzleStats(ctx, field)
				if res == graphql.Null {
					atomic.AddUint32(&fs.Invalids, 1)
				}
				return res
			}

			rrm := func(ctx context.Context) graphql.Marshaler {
				return ec.OperationContext.RootResolverMiddleware(ctx,
					func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return rrm(innerCtx) })
		case "myGroupedGameStats":
			field := field

			innerFunc := func(ctx context.Context, fs *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._Query_myGroupedGameStats(ctx, field)
				if res == graphql.Null {
					atomic.AddUint32(&fs.Invalids, 1)
				}
				return res
			}

			rrm := func(ctx context.Context) graphql.Marshaler {
				return ec.OperationContext.RootResolverMiddleware(ctx,
					func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return rrm(innerCtx) })
		case "__type":
			out.Values[i] = ec.OperationContext.RootResolverMiddleware(innerCtx, func(ctx context.Context) (res graphql.Marshaler) {
				return ec._Query___type(ctx, field)
			})
		case "__schema":
			out.Values[i] = ec.OperationContext.RootResolverMiddleware(innerCtx, func(ctx context.Context) (res graphql.Marshaler) {
				return ec._Query___schema(ctx, field)
			})
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var ratedCountImplementors = []string{"RatedCount"}

func (ec *executionContext) _RatedCount(ctx context.Context, sel ast.SelectionSet, obj *model.RatedCount) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, ratedCountImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("RatedCount")
		case "rated":
			out.Values[i] = ec._RatedCount_rated(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "count":
			field := field

			innerFunc := func(ctx context.Context, fs *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._RatedCount_count(ctx, field, obj)
				if res == graphql.Null {
					atomic.AddUint32(&fs.Invalids, 1)
				}
				return res
			}

			if field.Deferrable != nil {
				dfs, ok := deferred[field.Deferrable.Label]
				di := 0
				if ok {
					dfs.AddField(field)
					di = len(dfs.Values) - 1
				} else {
					dfs = graphql.NewFieldSet([]graphql.CollectedField{field})
					deferred[field.Deferrable.Label] = dfs
				}
				dfs.Concurrently(di, func(ctx context.Context) graphql.Marshaler {
					return innerFunc(ctx, dfs)
				})

				// don't run the out.Concurrently() call below
				out.Values[i] = graphql.Null
				continue
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var resultCountImplementors = []string{"ResultCount"}

func (ec *executionContext) _ResultCount(ctx context.Context, sel ast.SelectionSet, obj *model.ResultCount) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, resultCountImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("ResultCount")
		case "result":
			field := field

			innerFunc := func(ctx context.Context, fs *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._ResultCount_result(ctx, field, obj)
				if res == graphql.Null {
					atomic.AddUint32(&fs.Invalids, 1)
				}
				return res
			}

			if field.Deferrable != nil {
				dfs, ok := deferred[field.Deferrable.Label]
				di := 0
				if ok {
					dfs.AddField(field)
					di = len(dfs.Values) - 1
				} else {
					dfs = graphql.NewFieldSet([]graphql.CollectedField{field})
					deferred[field.Deferrable.Label] = dfs
				}
				dfs.Concurrently(di, func(ctx context.Context) graphql.Marshaler {
					return innerFunc(ctx, dfs)
				})

				// don't run the out.Concurrently() call below
				out.Values[i] = graphql.Null
				continue
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
		case "count":
			field := field

			innerFunc := func(ctx context.Context, fs *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._ResultCount_count(ctx, field, obj)
				if res == graphql.Null {
					atomic.AddUint32(&fs.Invalids, 1)
				}
				return res
			}

			if field.Deferrable != nil {
				dfs, ok := deferred[field.Deferrable.Label]
				di := 0
				if ok {
					dfs.AddField(field)
					di = len(dfs.Values) - 1
				} else {
					dfs = graphql.NewFieldSet([]graphql.CollectedField{field})
					deferred[field.Deferrable.Label] = dfs
				}
				dfs.Concurrently(di, func(ctx context.Context) graphql.Marshaler {
					return innerFunc(ctx, dfs)
				})

				// don't run the out.Concurrently() call below
				out.Values[i] = graphql.Null
				continue
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var tagCountImplementors = []string{"TagCount"}

func (ec *executionContext) _TagCount(ctx context.Context, sel ast.SelectionSet, obj *model.TagCount) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, tagCountImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("TagCount")
		case "tag":
			out.Values[i] = ec._TagCount_tag(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "count":
			field := field

			innerFunc := func(ctx context.Context, fs *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._TagCount_count(ctx, field, obj)
				if res == graphql.Null {
					atomic.AddUint32(&fs.Invalids, 1)
				}
				return res
			}

			if field.Deferrable != nil {
				dfs, ok := deferred[field.Deferrable.Label]
				di := 0
				if ok {
					dfs.AddField(field)
					di = len(dfs.Values) - 1
				} else {
					dfs = graphql.NewFieldSet([]graphql.CollectedField{field})
					deferred[field.Deferrable.Label] = dfs
				}
				dfs.Concurrently(di, func(ctx context.Context) graphql.Marshaler {
					return innerFunc(ctx, dfs)
				})

				// don't run the out.Concurrently() call below
				out.Values[i] = graphql.Null
				continue
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var themeCountImplementors = []string{"ThemeCount"}

func (ec *executionContext) _ThemeCount(ctx context.Context, sel ast.SelectionSet, obj *model.ThemeCount) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, themeCountImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("ThemeCount")
		case "theme":
			field := field

			innerFunc := func(ctx context.Context, fs *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._ThemeCount_theme(ctx, field, obj)
				if res == graphql.Null {
					atomic.AddUint32(&fs.Invalids, 1)
				}
				return res
			}

			if field.Deferrable != nil {
				dfs, ok := deferred[field.Deferrable.Label]
				di := 0
				if ok {
					dfs.AddField(field)
					di = len(dfs.Values) - 1
				} else {
					dfs = graphql.NewFieldSet([]graphql.CollectedField{field})
					deferred[field.Deferrable.Label] = dfs
				}
				dfs.Concurrently(di, func(ctx context.Context) graphql.Marshaler {
					return innerFunc(ctx, dfs)
				})

				// don't run the out.Concurrently() call below
				out.Values[i] = graphql.Null
				continue
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
		case "count":
			field := field

			innerFunc := func(ctx context.Context, fs *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._ThemeCount_count(ctx, field, obj)
				if res == graphql.Null {
					atomic.AddUint32(&fs.Invalids, 1)
				}
				return res
			}

			if field.Deferrable != nil {
				dfs, ok := deferred[field.Deferrable.Label]
				di := 0
				if ok {
					dfs.AddField(field)
					di = len(dfs.Values) - 1
				} else {
					dfs = graphql.NewFieldSet([]graphql.CollectedField{field})
					deferred[field.Deferrable.Label] = dfs
				}
				dfs.Concurrently(di, func(ctx context.Context) graphql.Marshaler {
					return innerFunc(ctx, dfs)
				})

				// don't run the out.Concurrently() call below
				out.Values[i] = graphql.Null
				continue
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var timeControlCountImplementors = []string{"TimeControlCount"}

func (ec *executionContext) _TimeControlCount(ctx context.Context, sel ast.SelectionSet, obj *model.TimeControlCount) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, timeControlCountImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("TimeControlCount")
		case "time_control":
			out.Values[i] = ec._TimeControlCount_time_control(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				atomic.AddUint32(&out.Invalids, 1)
			}
		case "count":
			field := field

			innerFunc := func(ctx context.Context, fs *graphql.FieldSet) (res graphql.Marshaler) {
				defer func() {
					if r := recover(); r != nil {
						ec.Error(ctx, ec.Recover(ctx, r))
					}
				}()
				res = ec._TimeControlCount_count(ctx, field, obj)
				if res == graphql.Null {
					atomic.AddUint32(&fs.Invalids, 1)
				}
				return res
			}

			if field.Deferrable != nil {
				dfs, ok := deferred[field.Deferrable.Label]
				di := 0
				if ok {
					dfs.AddField(field)
					di = len(dfs.Values) - 1
				} else {
					dfs = graphql.NewFieldSet([]graphql.CollectedField{field})
					deferred[field.Deferrable.Label] = dfs
				}
				dfs.Concurrently(di, func(ctx context.Context) graphql.Marshaler {
					return innerFunc(ctx, dfs)
				})

				// don't run the out.Concurrently() call below
				out.Values[i] = graphql.Null
				continue
			}

			out.Concurrently(i, func(ctx context.Context) graphql.Marshaler { return innerFunc(ctx, out) })
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var __DirectiveImplementors = []string{"__Directive"}

func (ec *executionContext) ___Directive(ctx context.Context, sel ast.SelectionSet, obj *introspection.Directive) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, __DirectiveImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("__Directive")
		case "name":
			out.Values[i] = ec.___Directive_name(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "description":
			out.Values[i] = ec.___Directive_description(ctx, field, obj)
		case "isRepeatable":
			out.Values[i] = ec.___Directive_isRepeatable(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "locations":
			out.Values[i] = ec.___Directive_locations(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "args":
			out.Values[i] = ec.___Directive_args(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var __EnumValueImplementors = []string{"__EnumValue"}

func (ec *executionContext) ___EnumValue(ctx context.Context, sel ast.SelectionSet, obj *introspection.EnumValue) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, __EnumValueImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("__EnumValue")
		case "name":
			out.Values[i] = ec.___EnumValue_name(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "description":
			out.Values[i] = ec.___EnumValue_description(ctx, field, obj)
		case "isDeprecated":
			out.Values[i] = ec.___EnumValue_isDeprecated(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "deprecationReason":
			out.Values[i] = ec.___EnumValue_deprecationReason(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var __FieldImplementors = []string{"__Field"}

func (ec *executionContext) ___Field(ctx context.Context, sel ast.SelectionSet, obj *introspection.Field) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, __FieldImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("__Field")
		case "name":
			out.Values[i] = ec.___Field_name(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "description":
			out.Values[i] = ec.___Field_description(ctx, field, obj)
		case "args":
			out.Values[i] = ec.___Field_args(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "type":
			out.Values[i] = ec.___Field_type(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "isDeprecated":
			out.Values[i] = ec.___Field_isDeprecated(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "deprecationReason":
			out.Values[i] = ec.___Field_deprecationReason(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var __InputValueImplementors = []string{"__InputValue"}

func (ec *executionContext) ___InputValue(ctx context.Context, sel ast.SelectionSet, obj *introspection.InputValue) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, __InputValueImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("__InputValue")
		case "name":
			out.Values[i] = ec.___InputValue_name(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "description":
			out.Values[i] = ec.___InputValue_description(ctx, field, obj)
		case "type":
			out.Values[i] = ec.___InputValue_type(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "defaultValue":
			out.Values[i] = ec.___InputValue_defaultValue(ctx, field, obj)
		case "isDeprecated":
			out.Values[i] = ec.___InputValue_isDeprecated(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "deprecationReason":
			out.Values[i] = ec.___InputValue_deprecationReason(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var __SchemaImplementors = []string{"__Schema"}

func (ec *executionContext) ___Schema(ctx context.Context, sel ast.SelectionSet, obj *introspection.Schema) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, __SchemaImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("__Schema")
		case "description":
			out.Values[i] = ec.___Schema_description(ctx, field, obj)
		case "types":
			out.Values[i] = ec.___Schema_types(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "queryType":
			out.Values[i] = ec.___Schema_queryType(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "mutationType":
			out.Values[i] = ec.___Schema_mutationType(ctx, field, obj)
		case "subscriptionType":
			out.Values[i] = ec.___Schema_subscriptionType(ctx, field, obj)
		case "directives":
			out.Values[i] = ec.___Schema_directives(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

var __TypeImplementors = []string{"__Type"}

func (ec *executionContext) ___Type(ctx context.Context, sel ast.SelectionSet, obj *introspection.Type) graphql.Marshaler {
	fields := graphql.CollectFields(ec.OperationContext, sel, __TypeImplementors)

	out := graphql.NewFieldSet(fields)
	deferred := make(map[string]*graphql.FieldSet)
	for i, field := range fields {
		switch field.Name {
		case "__typename":
			out.Values[i] = graphql.MarshalString("__Type")
		case "kind":
			out.Values[i] = ec.___Type_kind(ctx, field, obj)
			if out.Values[i] == graphql.Null {
				out.Invalids++
			}
		case "name":
			out.Values[i] = ec.___Type_name(ctx, field, obj)
		case "description":
			out.Values[i] = ec.___Type_description(ctx, field, obj)
		case "specifiedByURL":
			out.Values[i] = ec.___Type_specifiedByURL(ctx, field, obj)
		case "fields":
			out.Values[i] = ec.___Type_fields(ctx, field, obj)
		case "interfaces":
			out.Values[i] = ec.___Type_interfaces(ctx, field, obj)
		case "possibleTypes":
			out.Values[i] = ec.___Type_possibleTypes(ctx, field, obj)
		case "enumValues":
			out.Values[i] = ec.___Type_enumValues(ctx, field, obj)
		case "inputFields":
			out.Values[i] = ec.___Type_inputFields(ctx, field, obj)
		case "ofType":
			out.Values[i] = ec.___Type_ofType(ctx, field, obj)
		case "isOneOf":
			out.Values[i] = ec.___Type_isOneOf(ctx, field, obj)
		default:
			panic("unknown field " + strconv.Quote(field.Name))
		}
	}
	out.Dispatch(ctx)
	if out.Invalids > 0 {
		return graphql.Null
	}

	atomic.AddInt32(&ec.deferred, int32(len(deferred)))

	for label, dfs := range deferred {
		ec.processDeferredGroup(graphql.DeferredGroup{
			Label:    label,
			Path:     graphql.GetPath(ctx),
			FieldSet: dfs,
			Context:  ctx,
		})
	}

	return out
}

// endregion **************************** object.gotpl ****************************

// region    ***************************** type.gotpl *****************************

func (ec *executionContext) unmarshalNBoolean2bool(ctx context.Context, v any) (bool, error) {
	res, err := graphql.UnmarshalBoolean(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNBoolean2bool(ctx context.Context, sel ast.SelectionSet, v bool) graphql.Marshaler {
	_ = sel
	res := graphql.MarshalBoolean(v)
	if res == graphql.Null {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
	}
	return res
}

func (ec *executionContext) unmarshalNChessPlatform2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐChessPlatform(ctx context.Context, v any) (models.ChessPlatform, error) {
	tmp, err := graphql.UnmarshalString(v)
	res := models.ChessPlatform(tmp)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNChessPlatform2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐChessPlatform(ctx context.Context, sel ast.SelectionSet, v models.ChessPlatform) graphql.Marshaler {
	_ = sel
	res := graphql.MarshalString(string(v))
	if res == graphql.Null {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
	}
	return res
}

func (ec *executionContext) unmarshalNColor2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐColor(ctx context.Context, v any) (models.Color, error) {
	tmp, err := graphql.UnmarshalString(v)
	res := models.Color(tmp)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNColor2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐColor(ctx context.Context, sel ast.SelectionSet, v models.Color) graphql.Marshaler {
	_ = sel
	res := graphql.MarshalString(string(v))
	if res == graphql.Null {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
	}
	return res
}

func (ec *executionContext) marshalNColorCount2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐColorCount(ctx context.Context, sel ast.SelectionSet, v model.ColorCount) graphql.Marshaler {
	return ec._ColorCount(ctx, sel, &v)
}

func (ec *executionContext) marshalNColorCount2ᚕgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐColorCountᚄ(ctx context.Context, sel ast.SelectionSet, v []model.ColorCount) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNColorCount2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐColorCount(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) unmarshalNFloat2float64(ctx context.Context, v any) (float64, error) {
	res, err := graphql.UnmarshalFloatContext(ctx, v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNFloat2float64(ctx context.Context, sel ast.SelectionSet, v float64) graphql.Marshaler {
	_ = sel
	res := graphql.MarshalFloatContext(v)
	if res == graphql.Null {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
	}
	return graphql.WrapContextMarshaler(ctx, res)
}

func (ec *executionContext) marshalNGame2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐGame(ctx context.Context, sel ast.SelectionSet, v models.Game) graphql.Marshaler {
	return ec._Game(ctx, sel, &v)
}

func (ec *executionContext) marshalNGame2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐGame(ctx context.Context, sel ast.SelectionSet, v *models.Game) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._Game(ctx, sel, v)
}

func (ec *executionContext) marshalNGameConnection2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐGameConnection(ctx context.Context, sel ast.SelectionSet, v model.GameConnection) graphql.Marshaler {
	return ec._GameConnection(ctx, sel, &v)
}

func (ec *executionContext) marshalNGameConnection2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐGameConnection(ctx context.Context, sel ast.SelectionSet, v *model.GameConnection) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._GameConnection(ctx, sel, v)
}

func (ec *executionContext) marshalNGameEdge2ᚕᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐGameEdgeᚄ(ctx context.Context, sel ast.SelectionSet, v []*model.GameEdge) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNGameEdge2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐGameEdge(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNGameEdge2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐGameEdge(ctx context.Context, sel ast.SelectionSet, v *model.GameEdge) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._GameEdge(ctx, sel, v)
}

func (ec *executionContext) marshalNGameMoveBucket2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐGameMoveBucket(ctx context.Context, sel ast.SelectionSet, v model.GameMoveBucket) graphql.Marshaler {
	return ec._GameMoveBucket(ctx, sel, &v)
}

func (ec *executionContext) marshalNGameMoveBucket2ᚕgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐGameMoveBucketᚄ(ctx context.Context, sel ast.SelectionSet, v []model.GameMoveBucket) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNGameMoveBucket2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐGameMoveBucket(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) unmarshalNGameResult2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐGameResult(ctx context.Context, v any) (models.GameResult, error) {
	tmp, err := graphql.UnmarshalString(v)
	res := models.GameResult(tmp)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNGameResult2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐGameResult(ctx context.Context, sel ast.SelectionSet, v models.GameResult) graphql.Marshaler {
	_ = sel
	res := graphql.MarshalString(string(v))
	if res == graphql.Null {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
	}
	return res
}

func (ec *executionContext) marshalNGameStats2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐGameStats(ctx context.Context, sel ast.SelectionSet, v model.GameStats) graphql.Marshaler {
	return ec._GameStats(ctx, sel, &v)
}

func (ec *executionContext) marshalNGameStats2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐGameStats(ctx context.Context, sel ast.SelectionSet, v *model.GameStats) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._GameStats(ctx, sel, v)
}

func (ec *executionContext) marshalNGroupedGameStats2ᚕᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐGroupedGameStatsᚄ(ctx context.Context, sel ast.SelectionSet, v []*model.GroupedGameStats) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNGroupedGameStats2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐGroupedGameStats(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNGroupedGameStats2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐGroupedGameStats(ctx context.Context, sel ast.SelectionSet, v *model.GroupedGameStats) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._GroupedGameStats(ctx, sel, v)
}

func (ec *executionContext) marshalNGroupedGameStatsConnection2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐGroupedGameStatsConnection(ctx context.Context, sel ast.SelectionSet, v model.GroupedGameStatsConnection) graphql.Marshaler {
	return ec._GroupedGameStatsConnection(ctx, sel, &v)
}

func (ec *executionContext) marshalNGroupedGameStatsConnection2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐGroupedGameStatsConnection(ctx context.Context, sel ast.SelectionSet, v *model.GroupedGameStatsConnection) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._GroupedGameStatsConnection(ctx, sel, v)
}

func (ec *executionContext) marshalNGroupedPuzzleStats2ᚕᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐGroupedPuzzleStatsᚄ(ctx context.Context, sel ast.SelectionSet, v []*model.GroupedPuzzleStats) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNGroupedPuzzleStats2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐGroupedPuzzleStats(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNGroupedPuzzleStats2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐGroupedPuzzleStats(ctx context.Context, sel ast.SelectionSet, v *model.GroupedPuzzleStats) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._GroupedPuzzleStats(ctx, sel, v)
}

func (ec *executionContext) marshalNGroupedPuzzleStatsConnection2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐGroupedPuzzleStatsConnection(ctx context.Context, sel ast.SelectionSet, v model.GroupedPuzzleStatsConnection) graphql.Marshaler {
	return ec._GroupedPuzzleStatsConnection(ctx, sel, &v)
}

func (ec *executionContext) marshalNGroupedPuzzleStatsConnection2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐGroupedPuzzleStatsConnection(ctx context.Context, sel ast.SelectionSet, v *model.GroupedPuzzleStatsConnection) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._GroupedPuzzleStatsConnection(ctx, sel, v)
}

func (ec *executionContext) unmarshalNID2string(ctx context.Context, v any) (string, error) {
	res, err := graphql.UnmarshalID(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNID2string(ctx context.Context, sel ast.SelectionSet, v string) graphql.Marshaler {
	_ = sel
	res := graphql.MarshalID(v)
	if res == graphql.Null {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
	}
	return res
}

func (ec *executionContext) unmarshalNInt2int32(ctx context.Context, v any) (int32, error) {
	res, err := graphql.UnmarshalInt32(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNInt2int32(ctx context.Context, sel ast.SelectionSet, v int32) graphql.Marshaler {
	_ = sel
	res := graphql.MarshalInt32(v)
	if res == graphql.Null {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
	}
	return res
}

func (ec *executionContext) marshalNMoveLengthBucket2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐMoveLengthBucket(ctx context.Context, sel ast.SelectionSet, v model.MoveLengthBucket) graphql.Marshaler {
	return ec._MoveLengthBucket(ctx, sel, &v)
}

func (ec *executionContext) marshalNMoveLengthBucket2ᚕgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐMoveLengthBucketᚄ(ctx context.Context, sel ast.SelectionSet, v []model.MoveLengthBucket) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNMoveLengthBucket2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐMoveLengthBucket(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNPageInfo2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐPageInfo(ctx context.Context, sel ast.SelectionSet, v *model.PageInfo) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._PageInfo(ctx, sel, v)
}

func (ec *executionContext) marshalNPlatformCount2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐPlatformCount(ctx context.Context, sel ast.SelectionSet, v model.PlatformCount) graphql.Marshaler {
	return ec._PlatformCount(ctx, sel, &v)
}

func (ec *executionContext) marshalNPlatformCount2ᚕgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐPlatformCountᚄ(ctx context.Context, sel ast.SelectionSet, v []model.PlatformCount) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNPlatformCount2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐPlatformCount(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNPuzzle2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐPuzzle(ctx context.Context, sel ast.SelectionSet, v models.Puzzle) graphql.Marshaler {
	return ec._Puzzle(ctx, sel, &v)
}

func (ec *executionContext) marshalNPuzzle2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐPuzzle(ctx context.Context, sel ast.SelectionSet, v *models.Puzzle) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._Puzzle(ctx, sel, v)
}

func (ec *executionContext) marshalNPuzzleConnection2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐPuzzleConnection(ctx context.Context, sel ast.SelectionSet, v model.PuzzleConnection) graphql.Marshaler {
	return ec._PuzzleConnection(ctx, sel, &v)
}

func (ec *executionContext) marshalNPuzzleConnection2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐPuzzleConnection(ctx context.Context, sel ast.SelectionSet, v *model.PuzzleConnection) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._PuzzleConnection(ctx, sel, v)
}

func (ec *executionContext) marshalNPuzzleEdge2ᚕᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐPuzzleEdgeᚄ(ctx context.Context, sel ast.SelectionSet, v []*model.PuzzleEdge) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNPuzzleEdge2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐPuzzleEdge(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNPuzzleEdge2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐPuzzleEdge(ctx context.Context, sel ast.SelectionSet, v *model.PuzzleEdge) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._PuzzleEdge(ctx, sel, v)
}

func (ec *executionContext) marshalNPuzzleStats2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐPuzzleStats(ctx context.Context, sel ast.SelectionSet, v model.PuzzleStats) graphql.Marshaler {
	return ec._PuzzleStats(ctx, sel, &v)
}

func (ec *executionContext) marshalNPuzzleStats2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐPuzzleStats(ctx context.Context, sel ast.SelectionSet, v *model.PuzzleStats) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec._PuzzleStats(ctx, sel, v)
}

func (ec *executionContext) unmarshalNPuzzleTheme2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐPuzzleTheme(ctx context.Context, v any) (models.PuzzleTheme, error) {
	tmp, err := graphql.UnmarshalString(v)
	res := models.PuzzleTheme(tmp)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNPuzzleTheme2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐPuzzleTheme(ctx context.Context, sel ast.SelectionSet, v models.PuzzleTheme) graphql.Marshaler {
	_ = sel
	res := graphql.MarshalString(string(v))
	if res == graphql.Null {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
	}
	return res
}

func (ec *executionContext) marshalNRatedCount2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐRatedCount(ctx context.Context, sel ast.SelectionSet, v model.RatedCount) graphql.Marshaler {
	return ec._RatedCount(ctx, sel, &v)
}

func (ec *executionContext) marshalNRatedCount2ᚕgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐRatedCountᚄ(ctx context.Context, sel ast.SelectionSet, v []model.RatedCount) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNRatedCount2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐRatedCount(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNResultCount2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐResultCount(ctx context.Context, sel ast.SelectionSet, v model.ResultCount) graphql.Marshaler {
	return ec._ResultCount(ctx, sel, &v)
}

func (ec *executionContext) marshalNResultCount2ᚕgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐResultCountᚄ(ctx context.Context, sel ast.SelectionSet, v []model.ResultCount) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNResultCount2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐResultCount(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) unmarshalNSortDirection2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐSortDirection(ctx context.Context, v any) (model.SortDirection, error) {
	tmp, err := graphql.UnmarshalString(v)
	res := model.SortDirection(tmp)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNSortDirection2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐSortDirection(ctx context.Context, sel ast.SelectionSet, v model.SortDirection) graphql.Marshaler {
	_ = sel
	res := graphql.MarshalString(string(v))
	if res == graphql.Null {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
	}
	return res
}

func (ec *executionContext) unmarshalNString2string(ctx context.Context, v any) (string, error) {
	res, err := graphql.UnmarshalString(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNString2string(ctx context.Context, sel ast.SelectionSet, v string) graphql.Marshaler {
	_ = sel
	res := graphql.MarshalString(v)
	if res == graphql.Null {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
	}
	return res
}

func (ec *executionContext) unmarshalNString2ᚕstringᚄ(ctx context.Context, v any) ([]string, error) {
	var vSlice []any
	vSlice = graphql.CoerceList(v)
	var err error
	res := make([]string, len(vSlice))
	for i := range vSlice {
		ctx := graphql.WithPathContext(ctx, graphql.NewPathWithIndex(i))
		res[i], err = ec.unmarshalNString2string(ctx, vSlice[i])
		if err != nil {
			return nil, err
		}
	}
	return res, nil
}

func (ec *executionContext) marshalNString2ᚕstringᚄ(ctx context.Context, sel ast.SelectionSet, v []string) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	for i := range v {
		ret[i] = ec.marshalNString2string(ctx, sel, v[i])
	}

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNTagCount2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐTagCount(ctx context.Context, sel ast.SelectionSet, v model.TagCount) graphql.Marshaler {
	return ec._TagCount(ctx, sel, &v)
}

func (ec *executionContext) marshalNTagCount2ᚕgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐTagCountᚄ(ctx context.Context, sel ast.SelectionSet, v []model.TagCount) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNTagCount2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐTagCount(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalNThemeCount2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐThemeCount(ctx context.Context, sel ast.SelectionSet, v model.ThemeCount) graphql.Marshaler {
	return ec._ThemeCount(ctx, sel, &v)
}

func (ec *executionContext) marshalNThemeCount2ᚕgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐThemeCountᚄ(ctx context.Context, sel ast.SelectionSet, v []model.ThemeCount) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNThemeCount2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐThemeCount(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) unmarshalNTime2timeᚐTime(ctx context.Context, v any) (time.Time, error) {
	res, err := graphql.UnmarshalTime(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNTime2timeᚐTime(ctx context.Context, sel ast.SelectionSet, v time.Time) graphql.Marshaler {
	_ = sel
	res := graphql.MarshalTime(v)
	if res == graphql.Null {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
	}
	return res
}

func (ec *executionContext) marshalNTimeControlCount2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐTimeControlCount(ctx context.Context, sel ast.SelectionSet, v model.TimeControlCount) graphql.Marshaler {
	return ec._TimeControlCount(ctx, sel, &v)
}

func (ec *executionContext) marshalNTimeControlCount2ᚕgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐTimeControlCountᚄ(ctx context.Context, sel ast.SelectionSet, v []model.TimeControlCount) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNTimeControlCount2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐTimeControlCount(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) unmarshalNTimeGrouping2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐTimeGrouping(ctx context.Context, v any) (model.TimeGrouping, error) {
	tmp, err := graphql.UnmarshalString(v)
	res := model.TimeGrouping(tmp)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNTimeGrouping2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐTimeGrouping(ctx context.Context, sel ast.SelectionSet, v model.TimeGrouping) graphql.Marshaler {
	_ = sel
	res := graphql.MarshalString(string(v))
	if res == graphql.Null {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
	}
	return res
}

func (ec *executionContext) unmarshalNWinner2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐWinner(ctx context.Context, v any) (models.Winner, error) {
	tmp, err := graphql.UnmarshalString(v)
	res := models.Winner(tmp)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalNWinner2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐWinner(ctx context.Context, sel ast.SelectionSet, v models.Winner) graphql.Marshaler {
	_ = sel
	res := graphql.MarshalString(string(v))
	if res == graphql.Null {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
	}
	return res
}

func (ec *executionContext) marshalN__Directive2githubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐDirective(ctx context.Context, sel ast.SelectionSet, v introspection.Directive) graphql.Marshaler {
	return ec.___Directive(ctx, sel, &v)
}

func (ec *executionContext) marshalN__Directive2ᚕgithubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐDirectiveᚄ(ctx context.Context, sel ast.SelectionSet, v []introspection.Directive) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalN__Directive2githubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐDirective(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) unmarshalN__DirectiveLocation2string(ctx context.Context, v any) (string, error) {
	res, err := graphql.UnmarshalString(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalN__DirectiveLocation2string(ctx context.Context, sel ast.SelectionSet, v string) graphql.Marshaler {
	_ = sel
	res := graphql.MarshalString(v)
	if res == graphql.Null {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
	}
	return res
}

func (ec *executionContext) unmarshalN__DirectiveLocation2ᚕstringᚄ(ctx context.Context, v any) ([]string, error) {
	var vSlice []any
	vSlice = graphql.CoerceList(v)
	var err error
	res := make([]string, len(vSlice))
	for i := range vSlice {
		ctx := graphql.WithPathContext(ctx, graphql.NewPathWithIndex(i))
		res[i], err = ec.unmarshalN__DirectiveLocation2string(ctx, vSlice[i])
		if err != nil {
			return nil, err
		}
	}
	return res, nil
}

func (ec *executionContext) marshalN__DirectiveLocation2ᚕstringᚄ(ctx context.Context, sel ast.SelectionSet, v []string) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalN__DirectiveLocation2string(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalN__EnumValue2githubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐEnumValue(ctx context.Context, sel ast.SelectionSet, v introspection.EnumValue) graphql.Marshaler {
	return ec.___EnumValue(ctx, sel, &v)
}

func (ec *executionContext) marshalN__Field2githubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐField(ctx context.Context, sel ast.SelectionSet, v introspection.Field) graphql.Marshaler {
	return ec.___Field(ctx, sel, &v)
}

func (ec *executionContext) marshalN__InputValue2githubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐInputValue(ctx context.Context, sel ast.SelectionSet, v introspection.InputValue) graphql.Marshaler {
	return ec.___InputValue(ctx, sel, &v)
}

func (ec *executionContext) marshalN__InputValue2ᚕgithubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐInputValueᚄ(ctx context.Context, sel ast.SelectionSet, v []introspection.InputValue) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalN__InputValue2githubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐInputValue(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalN__Type2githubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐType(ctx context.Context, sel ast.SelectionSet, v introspection.Type) graphql.Marshaler {
	return ec.___Type(ctx, sel, &v)
}

func (ec *executionContext) marshalN__Type2ᚕgithubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐTypeᚄ(ctx context.Context, sel ast.SelectionSet, v []introspection.Type) graphql.Marshaler {
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalN__Type2githubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐType(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalN__Type2ᚖgithubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐType(ctx context.Context, sel ast.SelectionSet, v *introspection.Type) graphql.Marshaler {
	if v == nil {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
		return graphql.Null
	}
	return ec.___Type(ctx, sel, v)
}

func (ec *executionContext) unmarshalN__TypeKind2string(ctx context.Context, v any) (string, error) {
	res, err := graphql.UnmarshalString(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalN__TypeKind2string(ctx context.Context, sel ast.SelectionSet, v string) graphql.Marshaler {
	_ = sel
	res := graphql.MarshalString(v)
	if res == graphql.Null {
		if !graphql.HasFieldError(ctx, graphql.GetFieldContext(ctx)) {
			ec.Errorf(ctx, "the requested element is null which the schema does not allow")
		}
	}
	return res
}

func (ec *executionContext) unmarshalOBoolean2bool(ctx context.Context, v any) (bool, error) {
	res, err := graphql.UnmarshalBoolean(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOBoolean2bool(ctx context.Context, sel ast.SelectionSet, v bool) graphql.Marshaler {
	_ = sel
	_ = ctx
	res := graphql.MarshalBoolean(v)
	return res
}

func (ec *executionContext) unmarshalOBoolean2ᚖbool(ctx context.Context, v any) (*bool, error) {
	if v == nil {
		return nil, nil
	}
	res, err := graphql.UnmarshalBoolean(v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOBoolean2ᚖbool(ctx context.Context, sel ast.SelectionSet, v *bool) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	_ = sel
	_ = ctx
	res := graphql.MarshalBoolean(*v)
	return res
}

func (ec *executionContext) unmarshalOChessPlatform2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐChessPlatform(ctx context.Context, v any) (*models.ChessPlatform, error) {
	if v == nil {
		return nil, nil
	}
	tmp, err := graphql.UnmarshalString(v)
	res := models.ChessPlatform(tmp)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOChessPlatform2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐChessPlatform(ctx context.Context, sel ast.SelectionSet, v *models.ChessPlatform) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	_ = sel
	_ = ctx
	res := graphql.MarshalString(string(*v))
	return res
}

func (ec *executionContext) unmarshalOColor2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐColor(ctx context.Context, v any) (*models.Color, error) {
	if v == nil {
		return nil, nil
	}
	tmp, err := graphql.UnmarshalString(v)
	res := models.Color(tmp)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOColor2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐColor(ctx context.Context, sel ast.SelectionSet, v *models.Color) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	_ = sel
	_ = ctx
	res := graphql.MarshalString(string(*v))
	return res
}

func (ec *executionContext) marshalOGame2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐGame(ctx context.Context, sel ast.SelectionSet, v models.Game) graphql.Marshaler {
	return ec._Game(ctx, sel, &v)
}

func (ec *executionContext) marshalOGame2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐGame(ctx context.Context, sel ast.SelectionSet, v *models.Game) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._Game(ctx, sel, v)
}

func (ec *executionContext) unmarshalOGameFilter2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐGameFilter(ctx context.Context, v any) (*model.GameFilter, error) {
	if v == nil {
		return nil, nil
	}
	res, err := ec.unmarshalInputGameFilter(ctx, v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) unmarshalOGameResult2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐGameResult(ctx context.Context, v any) (*models.GameResult, error) {
	if v == nil {
		return nil, nil
	}
	tmp, err := graphql.UnmarshalString(v)
	res := models.GameResult(tmp)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOGameResult2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐGameResult(ctx context.Context, sel ast.SelectionSet, v *models.GameResult) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	_ = sel
	_ = ctx
	res := graphql.MarshalString(string(*v))
	return res
}

func (ec *executionContext) unmarshalOInt2ᚖint32(ctx context.Context, v any) (*int32, error) {
	if v == nil {
		return nil, nil
	}
	res, err := graphql.UnmarshalInt32(v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOInt2ᚖint32(ctx context.Context, sel ast.SelectionSet, v *int32) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	_ = sel
	_ = ctx
	res := graphql.MarshalInt32(*v)
	return res
}

func (ec *executionContext) unmarshalOOffsetPaginationInput2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐOffsetPaginationInput(ctx context.Context, v any) (*model.OffsetPaginationInput, error) {
	if v == nil {
		return nil, nil
	}
	res, err := ec.unmarshalInputOffsetPaginationInput(ctx, v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOPuzzle2ᚕgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐPuzzleᚄ(ctx context.Context, sel ast.SelectionSet, v []models.Puzzle) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNPuzzle2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐPuzzle(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalOPuzzle2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐPuzzle(ctx context.Context, sel ast.SelectionSet, v *models.Puzzle) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec._Puzzle(ctx, sel, v)
}

func (ec *executionContext) unmarshalOPuzzleFilter2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐPuzzleFilter(ctx context.Context, v any) (*model.PuzzleFilter, error) {
	if v == nil {
		return nil, nil
	}
	res, err := ec.unmarshalInputPuzzleFilter(ctx, v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) unmarshalOPuzzleTheme2ᚕgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐPuzzleThemeᚄ(ctx context.Context, v any) ([]models.PuzzleTheme, error) {
	if v == nil {
		return nil, nil
	}
	var vSlice []any
	vSlice = graphql.CoerceList(v)
	var err error
	res := make([]models.PuzzleTheme, len(vSlice))
	for i := range vSlice {
		ctx := graphql.WithPathContext(ctx, graphql.NewPathWithIndex(i))
		res[i], err = ec.unmarshalNPuzzleTheme2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐPuzzleTheme(ctx, vSlice[i])
		if err != nil {
			return nil, err
		}
	}
	return res, nil
}

func (ec *executionContext) marshalOPuzzleTheme2ᚕgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐPuzzleThemeᚄ(ctx context.Context, sel ast.SelectionSet, v []models.PuzzleTheme) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalNPuzzleTheme2githubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋmodelsᚐPuzzleTheme(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) unmarshalOSortInput2ᚖgithubᚗcomᚋchessticizeᚋchessticizeᚑserverᚋinternalᚋgraphqlᚋmodelᚐSortInput(ctx context.Context, v any) (*model.SortInput, error) {
	if v == nil {
		return nil, nil
	}
	res, err := ec.unmarshalInputSortInput(ctx, v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) unmarshalOString2string(ctx context.Context, v any) (string, error) {
	res, err := graphql.UnmarshalString(v)
	return res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOString2string(ctx context.Context, sel ast.SelectionSet, v string) graphql.Marshaler {
	_ = sel
	_ = ctx
	res := graphql.MarshalString(v)
	return res
}

func (ec *executionContext) unmarshalOString2ᚕstringᚄ(ctx context.Context, v any) ([]string, error) {
	if v == nil {
		return nil, nil
	}
	var vSlice []any
	vSlice = graphql.CoerceList(v)
	var err error
	res := make([]string, len(vSlice))
	for i := range vSlice {
		ctx := graphql.WithPathContext(ctx, graphql.NewPathWithIndex(i))
		res[i], err = ec.unmarshalNString2string(ctx, vSlice[i])
		if err != nil {
			return nil, err
		}
	}
	return res, nil
}

func (ec *executionContext) marshalOString2ᚕstringᚄ(ctx context.Context, sel ast.SelectionSet, v []string) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	ret := make(graphql.Array, len(v))
	for i := range v {
		ret[i] = ec.marshalNString2string(ctx, sel, v[i])
	}

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) unmarshalOString2ᚖstring(ctx context.Context, v any) (*string, error) {
	if v == nil {
		return nil, nil
	}
	res, err := graphql.UnmarshalString(v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOString2ᚖstring(ctx context.Context, sel ast.SelectionSet, v *string) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	_ = sel
	_ = ctx
	res := graphql.MarshalString(*v)
	return res
}

func (ec *executionContext) unmarshalOTime2ᚖtimeᚐTime(ctx context.Context, v any) (*time.Time, error) {
	if v == nil {
		return nil, nil
	}
	res, err := graphql.UnmarshalTime(v)
	return &res, graphql.ErrorOnPath(ctx, err)
}

func (ec *executionContext) marshalOTime2ᚖtimeᚐTime(ctx context.Context, sel ast.SelectionSet, v *time.Time) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	_ = sel
	_ = ctx
	res := graphql.MarshalTime(*v)
	return res
}

func (ec *executionContext) marshalO__EnumValue2ᚕgithubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐEnumValueᚄ(ctx context.Context, sel ast.SelectionSet, v []introspection.EnumValue) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalN__EnumValue2githubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐEnumValue(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalO__Field2ᚕgithubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐFieldᚄ(ctx context.Context, sel ast.SelectionSet, v []introspection.Field) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalN__Field2githubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐField(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalO__InputValue2ᚕgithubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐInputValueᚄ(ctx context.Context, sel ast.SelectionSet, v []introspection.InputValue) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalN__InputValue2githubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐInputValue(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalO__Schema2ᚖgithubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐSchema(ctx context.Context, sel ast.SelectionSet, v *introspection.Schema) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec.___Schema(ctx, sel, v)
}

func (ec *executionContext) marshalO__Type2ᚕgithubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐTypeᚄ(ctx context.Context, sel ast.SelectionSet, v []introspection.Type) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	ret := make(graphql.Array, len(v))
	var wg sync.WaitGroup
	isLen1 := len(v) == 1
	if !isLen1 {
		wg.Add(len(v))
	}
	for i := range v {
		i := i
		fc := &graphql.FieldContext{
			Index:  &i,
			Result: &v[i],
		}
		ctx := graphql.WithFieldContext(ctx, fc)
		f := func(i int) {
			defer func() {
				if r := recover(); r != nil {
					ec.Error(ctx, ec.Recover(ctx, r))
					ret = nil
				}
			}()
			if !isLen1 {
				defer wg.Done()
			}
			ret[i] = ec.marshalN__Type2githubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐType(ctx, sel, v[i])
		}
		if isLen1 {
			f(i)
		} else {
			go f(i)
		}

	}
	wg.Wait()

	for _, e := range ret {
		if e == graphql.Null {
			return graphql.Null
		}
	}

	return ret
}

func (ec *executionContext) marshalO__Type2ᚖgithubᚗcomᚋ99designsᚋgqlgenᚋgraphqlᚋintrospectionᚐType(ctx context.Context, sel ast.SelectionSet, v *introspection.Type) graphql.Marshaler {
	if v == nil {
		return graphql.Null
	}
	return ec.___Type(ctx, sel, v)
}

// endregion ***************************** type.gotpl *****************************
