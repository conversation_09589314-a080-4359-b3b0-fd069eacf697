package testing

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"math/big"
	"net/http"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// FirebaseEmulator provides a simple Firebase Authentication emulator for testing
type FirebaseEmulator struct {
	privateKey *rsa.PrivateKey
	publicKey  *rsa.PublicKey
	keyID      string
	projectID  string
}

// NewFirebaseEmulator creates a new Firebase emulator instance
func NewFirebaseEmulator(projectID string) (*FirebaseEmulator, error) {
	// Generate RSA key pair for signing tokens
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		return nil, fmt.Errorf("failed to generate RSA key: %w", err)
	}

	return &FirebaseEmulator{
		privateKey: privateKey,
		publicKey:  &privateKey.PublicKey,
		keyID:      "test-key-id",
		projectID:  projectID,
	}, nil
}

// JWKSResponse represents the JWKS response format
type JWKSResponse struct {
	Keys []JWK `json:"keys"`
}

// JW<PERSON> represents a JSON Web Key
type JWK struct {
	Kty string `json:"kty"`
	Use string `json:"use"`
	Kid string `json:"kid"`
	N   string `json:"n"`
	E   string `json:"e"`
	Alg string `json:"alg"`
}

// ServeJWKS serves the JWKS endpoint
func (e *FirebaseEmulator) ServeJWKS(w http.ResponseWriter, r *http.Request) {
	// Convert RSA public key to JWK format
	jwk := JWK{
		Kty: "RSA",
		Use: "sig",
		Kid: e.keyID,
		N:   base64.RawURLEncoding.EncodeToString(e.publicKey.N.Bytes()),
		E:   base64.RawURLEncoding.EncodeToString(big.NewInt(int64(e.publicKey.E)).Bytes()),
		Alg: "RS256",
	}

	response := JWKSResponse{
		Keys: []JWK{jwk},
	}

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(response); err != nil {
		http.Error(w, "Failed to encode response", http.StatusInternalServerError)
	}
}

// GenerateTestToken generates a test Firebase ID token
func (e *FirebaseEmulator) GenerateTestToken(userID, email string) (string, error) {
	now := time.Now()

	claims := jwt.MapClaims{
		"iss":            fmt.Sprintf("https://securetoken.google.com/%s", e.projectID),
		"aud":            e.projectID,
		"auth_time":      now.Unix(),
		"user_id":        userID,
		"sub":            userID,
		"iat":            now.Unix(),
		"exp":            now.Add(time.Hour).Unix(),
		"email":          email,
		"email_verified": true,
		"firebase": map[string]interface{}{
			"identities": map[string]interface{}{
				"email": []string{email},
			},
			"sign_in_provider": "password",
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodRS256, claims)
	token.Header["kid"] = e.keyID

	return token.SignedString(e.privateKey)
}

// StartServer starts the Firebase emulator server
func (e *FirebaseEmulator) StartServer(port string) *http.Server {
	mux := http.NewServeMux()
	mux.HandleFunc("/.well-known/jwks.json", e.ServeJWKS)

	server := &http.Server{
		Addr:    ":" + port,
		Handler: mux,
	}

	go func() {
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			fmt.Printf("Firebase emulator server error: %v\n", err)
		}
	}()

	return server
}

// GetPrivateKeyPEM returns the private key in PEM format for testing
func (e *FirebaseEmulator) GetPrivateKeyPEM() (string, error) {
	privateKeyBytes, err := x509.MarshalPKCS8PrivateKey(e.privateKey)
	if err != nil {
		return "", err
	}

	privateKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "PRIVATE KEY",
		Bytes: privateKeyBytes,
	})

	return string(privateKeyPEM), nil
}

// GetPublicKeyPEM returns the public key in PEM format for testing
func (e *FirebaseEmulator) GetPublicKeyPEM() (string, error) {
	publicKeyBytes, err := x509.MarshalPKIXPublicKey(e.publicKey)
	if err != nil {
		return "", err
	}

	publicKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "PUBLIC KEY",
		Bytes: publicKeyBytes,
	})

	return string(publicKeyPEM), nil
}
