package api

import (
	"encoding/json"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/go-chi/chi/v5"
	"github.com/go-chi/render"
)

// TaskHandler handles HTTP requests for tasks
type TaskHandler struct {
	taskRepo repository.ITaskRepository
}

// NewTaskHandler creates a new TaskHandler instance
func NewTaskHandler(taskRepo repository.ITaskRepository) *TaskHandler {
	return &TaskHandler{
		taskRepo: taskRepo,
	}
}

func TaskRoutes(repo repository.ITaskRepository) http.Handler {
	h := NewTaskHandler(repo)
	r := chi.NewRouter()

	// Admin task routes
	r.Post("/", h.CreateTask)                     // Create a new task
	r.Get("/", h.ListTasks)                       // List all tasks with pagination
	r.Get("/{id}", h.GetTask)                     // Get a specific task by ID
	r.Post("/claim", h.ClaimNextTask)             // Claim the next available pending task
	r.Put("/{id}", h.UpdateTask)                  // Update a task's status/data
	r.Post("/cleanup", h.CleanupOldTasks)         // Clean up old tasks based on criteria
	r.Post("/reset-hanging", h.ResetHangingTasks) // Reset tasks stuck in progress

	return r
}

// CreateTaskRequest represents the request body for creating a task
type CreateTaskRequest struct {
	UserID      string          `json:"user_id"`
	TaskType    models.TaskType `json:"task_type"`
	TaskData    json.RawMessage `json:"task_data"`
	ScheduledAt *time.Time      `json:"scheduled_at,omitempty"` // Optional: Time when the task should be picked up
}

// CreateTask handles the creation of a new task
func (h *TaskHandler) CreateTask(w http.ResponseWriter, r *http.Request) {
	var req CreateTaskRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		apiError(w, r, http.StatusBadRequest, err, "Invalid request body")
		return
	}

	// Validate request
	if req.UserID == "" || req.TaskType == "" {
		apiError(w, r, http.StatusBadRequest, nil, "user_id and task_type are required")
		return
	}

	// Validate TaskType
	validTaskType := false
	for _, tt := range []models.TaskType{
		models.FetchChessGamesTask,
		models.EvaluateChessGameTask,
		models.GenerateChessPuzzlesTask,
	} {
		if req.TaskType == tt {
			validTaskType = true
			break
		}
	}

	if !validTaskType {
		apiError(w, r, http.StatusBadRequest, nil, "Invalid task_type")
		return
	}

	// Create task
	task := &models.Task{
		UserID:   req.UserID,
		TaskType: req.TaskType,
		TaskData: req.TaskData,
		Status:   models.TaskStatusPending,
		Attempts: 0,
	}

	// Set scheduled_at if provided in the request
	if req.ScheduledAt != nil {
		task.ScheduledAt = *req.ScheduledAt
	}

	if err := h.taskRepo.Create(r.Context(), task); err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to create task")
		return
	}

	render.Status(r, http.StatusCreated)
	render.JSON(w, r, task)
}

// ListTasks handles listing tasks with pagination and filtering
func (h *TaskHandler) ListTasks(w http.ResponseWriter, r *http.Request) {
	// Parse pagination parameters
	offsetStr := r.URL.Query().Get("offset")
	limitStr := r.URL.Query().Get("limit")
	statusStr := r.URL.Query().Get("status")
	userIDStr := r.URL.Query().Get("user_id") // Get user_id query param

	offset := 0
	if offsetStr != "" {
		var err error
		offset, err = strconv.Atoi(offsetStr)
		if err != nil || offset < 0 {
			apiError(w, r, http.StatusBadRequest, nil, "Invalid offset parameter")
			return
		}
	}

	limit := 50 // Default limit
	if limitStr != "" {
		var err error
		limit, err = strconv.Atoi(limitStr)
		if err != nil || limit <= 0 || limit > 100 {
			apiError(w, r, http.StatusBadRequest, nil, "Invalid limit parameter (must be 1-100)")
			return
		}
	}

	// Parse status filter if provided
	var statuses []models.TaskStatus
	if statusStr != "" {
		statusValues := []models.TaskStatus{
			models.TaskStatusPending,
			models.TaskStatusInProgress,
			models.TaskStatusCompleted,
			models.TaskStatusFailed,
		}

		for _, s := range statusValues {
			if string(s) == statusStr {
				statuses = append(statuses, s)
				break
			}
		}

		if len(statuses) == 0 {
			apiError(w, r, http.StatusBadRequest, nil, "Invalid status parameter")
			return
		}
	}

	// --- Parse user_id (optional) ---
	var userID *string
	if userIDStr != "" {
		// Basic validation could be added here (e.g., UUID format) if desired
		userID = &userIDStr
	}
	// --------------------------------

	// Get tasks with pagination and filtering
	tasks, totalCount, err := h.taskRepo.ListAll(r.Context(), userID, statuses, offset, limit) // Pass userID
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to list tasks")
		return
	}

	// Build response with pagination metadata
	response := map[string]interface{}{
		"tasks":       tasks,
		"total_count": totalCount,
		"offset":      offset,
		"limit":       limit,
	}

	render.JSON(w, r, response)
}

// GetTask retrieves a specific task by ID
func (h *TaskHandler) GetTask(w http.ResponseWriter, r *http.Request) {
	id := chi.URLParam(r, "id")
	task, err := h.taskRepo.GetByID(r.Context(), id)
	if err != nil {
		if err == repository.ErrNotFound {
			apiError(w, r, http.StatusNotFound, nil, "Task not found")
			return
		}
		apiError(w, r, http.StatusInternalServerError, err, "Failed to get task")
		return
	}

	render.JSON(w, r, task)
}

// ClaimTaskRequest represents the request body for claiming a task
type ClaimTaskRequest struct {
	WorkerID string `json:"worker_id"`
}

// ClaimNextTask handles claiming the oldest pending task for processing
func (h *TaskHandler) ClaimNextTask(w http.ResponseWriter, r *http.Request) {
	var req ClaimTaskRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		apiError(w, r, http.StatusBadRequest, err, "Invalid request body")
		return
	}

	if req.WorkerID == "" {
		apiError(w, r, http.StatusBadRequest, nil, "worker_id is required")
		return
	}

	// Claim the next pending task
	task, err := h.taskRepo.ClaimNextPending(r.Context(), req.WorkerID)
	if err != nil {
		// Check for specific errors if needed, e.g., transaction issues
		apiError(w, r, http.StatusInternalServerError, err, "Failed to claim task")
		return
	}

	if task == nil {
		// No pending tasks available
		apiError(w, r, http.StatusNotFound, nil, "No pending tasks available")
		return
	}

	render.JSON(w, r, task)
}

// UpdateTaskRequest represents the request body for updating a task
type UpdateTaskRequest struct {
	Status models.TaskStatus `json:"status,omitempty"`
	Error  *string           `json:"error,omitempty"`
}

// UpdateTask handles updating a task's status or other fields
func (h *TaskHandler) UpdateTask(w http.ResponseWriter, r *http.Request) {
	id := chi.URLParam(r, "id")

	var req UpdateTaskRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		apiError(w, r, http.StatusBadRequest, err, "Invalid request body")
		return
	}

	// Get the task first to check if it exists
	task, err := h.taskRepo.GetByID(r.Context(), id)
	if err != nil {
		if err == repository.ErrNotFound {
			apiError(w, r, http.StatusNotFound, nil, "Task not found")
			return
		}
		apiError(w, r, http.StatusInternalServerError, err, "Failed to get task")
		return
	}

	// Store the current updated_at for optimistic locking
	expectedUpdatedAt := task.UpdatedAt

	// Update fields based on request
	updateMade := false
	if req.Status != "" {
		// Validate status transition
		validStatus := false
		for _, s := range []models.TaskStatus{
			models.TaskStatusPending,
			models.TaskStatusInProgress,
			models.TaskStatusCompleted,
			models.TaskStatusFailed,
		} {
			if req.Status == s {
				validStatus = true
				break
			}
		}

		if !validStatus {
			apiError(w, r, http.StatusBadRequest, nil, "Invalid status value")
			return
		}

		// Enforce state transition rules
		if task.Status == models.TaskStatusCompleted && req.Status != models.TaskStatusCompleted {
			apiError(w, r, http.StatusBadRequest, nil, "Completed tasks cannot change status")
			return
		}

		task.Status = req.Status
		updateMade = true

		// Clear worker info if setting back to pending
		if req.Status == models.TaskStatusPending {
			task.WorkerID.Valid = false
			task.PickedUpAt.Valid = false
		}
	}

	// Update error field if provided
	if req.Error != nil {
		if *req.Error == "" {
			task.Error.Valid = false
		} else {
			task.Error.String = *req.Error
			task.Error.Valid = true
		}
		updateMade = true
	}

	if !updateMade {
		apiError(w, r, http.StatusBadRequest, nil, "No updates provided")
		return
	}

	// Apply update with optimistic locking
	if err := h.taskRepo.Update(r.Context(), task, expectedUpdatedAt); err != nil {
		if err == repository.ErrTaskUpdateConflict {
			apiError(w, r, http.StatusConflict, err, "Task was modified by another process")
			return
		}
		apiError(w, r, http.StatusInternalServerError, err, "Failed to update task")
		return
	}

	render.JSON(w, r, task)
}

// CleanupTasksRequest defines query parameters for cleaning up old tasks.
type CleanupTasksRequest struct {
	Cutoff   time.Time           `json:"-"` // Parsed from query param
	Statuses []models.TaskStatus `json:"-"` // Parsed from query param
	Limit    int                 `json:"-"` // Parsed from query param
}

// CleanupOldTasks handles deleting old tasks based on cutoff time and status.
// Query parameters:
// - cutoff: RFC3339 timestamp (e.g., 2023-01-01T00:00:00Z) - Required.
// - statuses: Comma-separated list of statuses (e.g., completed,failed). Defaults to completed,failed.
// - limit: Maximum number of tasks to delete (e.g., 1000). Defaults to 0 (no limit, but repo might have safety).
func (h *TaskHandler) CleanupOldTasks(w http.ResponseWriter, r *http.Request) {
	var req CleanupTasksRequest
	var err error

	// Parse cutoff time (Required)
	cutoffStr := r.URL.Query().Get("cutoff")
	if cutoffStr == "" {
		apiError(w, r, http.StatusBadRequest, nil, "cutoff query parameter is required (RFC3339 format)")
		return
	}
	req.Cutoff, err = time.Parse(time.RFC3339, cutoffStr)
	if err != nil {
		apiError(w, r, http.StatusBadRequest, nil, "Invalid cutoff query parameter format (must be RFC3339)")
		return
	}

	// Parse statuses (Optional, defaults to completed,failed)
	statusesStr := r.URL.Query().Get("statuses")
	if statusesStr != "" {
		validStatuses := map[models.TaskStatus]bool{
			models.TaskStatusPending:    true,
			models.TaskStatusInProgress: true,
			models.TaskStatusCompleted:  true,
			models.TaskStatusFailed:     true,
		}
		parts := strings.Split(statusesStr, ",")
		for _, part := range parts {
			status := models.TaskStatus(strings.TrimSpace(part))
			if _, ok := validStatuses[status]; ok {
				req.Statuses = append(req.Statuses, status)
			} else {
				apiError(w, r, http.StatusBadRequest, nil, "Invalid status value in statuses parameter: "+part)
				return
			}
		}
		if len(req.Statuses) == 0 {
			// Should not happen if parsing logic is correct, but safeguard
			apiError(w, r, http.StatusBadRequest, nil, "No valid statuses provided in statuses parameter")
			return
		}
	}
	// Default to completed and failed if not specified
	if len(req.Statuses) == 0 {
		req.Statuses = []models.TaskStatus{models.TaskStatusCompleted, models.TaskStatusFailed}
	}

	// Parse limit (Optional)
	limitStr := r.URL.Query().Get("limit")
	req.Limit = 0 // Default to 0 (no limit on handler side)
	if limitStr != "" {
		req.Limit, err = strconv.Atoi(limitStr)
		if err != nil || req.Limit < 0 {
			apiError(w, r, http.StatusBadRequest, nil, "Invalid limit query parameter (must be non-negative integer)")
			return
		}
	}

	// Perform deletion
	deletedCount, err := h.taskRepo.DeleteOldTasks(r.Context(), req.Cutoff, req.Statuses, req.Limit)
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to delete old tasks")
		return
	}

	render.JSON(w, r, map[string]interface{}{"deleted_count": deletedCount})
}

// ResetHangingTasksRequest defines query parameters for resetting hanging tasks.
type ResetHangingTasksRequest struct {
	Timeout time.Duration `json:"-"` // Parsed from query param
}

// ResetHangingTasks handles resetting tasks stuck in 'in_progress'.
// Query parameters:
// - timeout: Duration string (e.g., "1h", "30m") - Required. Tasks in progress longer than this will be reset.
func (h *TaskHandler) ResetHangingTasks(w http.ResponseWriter, r *http.Request) {
	var req ResetHangingTasksRequest
	var err error

	// Parse timeout duration (Required)
	timeoutStr := r.URL.Query().Get("timeout")
	if timeoutStr == "" {
		apiError(w, r, http.StatusBadRequest, nil, "timeout query parameter is required (e.g., '1h', '30m')")
		return
	}
	req.Timeout, err = time.ParseDuration(timeoutStr)
	if err != nil {
		apiError(w, r, http.StatusBadRequest, nil, "Invalid timeout query parameter format")
		return
	}
	if req.Timeout <= 0 {
		apiError(w, r, http.StatusBadRequest, nil, "timeout duration must be positive")
		return
	}

	// Perform reset
	resetCount, err := h.taskRepo.ResetHanging(r.Context(), req.Timeout)
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to reset hanging tasks")
		return
	}

	render.JSON(w, r, map[string]interface{}{"reset_count": resetCount})
}
