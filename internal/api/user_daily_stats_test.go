package api

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/api/testutils"
	"github.com/chessticize/chessticize-server/internal/config"
	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/repository/fake"
	"github.com/chessticize/chessticize-server/internal/service"
	verificationFake "github.com/chessticize/chessticize-server/internal/verification/fake"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func setupUserDailyStatsTest(t *testing.T) (http.Handler, string, string) {
	// Create fake repositories
	fakeDB := testutils.GetFakeDB(t)
	userRepo := fake.NewUserRepository(fakeDB)
	gameRepo := fake.NewGameRepository(fakeDB)
	puzzleRepo := fake.NewPuzzleRepository(fakeDB)
	taskRepo := fake.NewFakeTaskRepository(fakeDB)
	eventRepo := fake.NewEventRepository(fakeDB.DB)
	userDailyStatsRepo := fake.NewUserDailyStatsRepository(fakeDB.DB)
	userEloRepo := repository.NewUserEloRepository(fakeDB.DB)
	userSprintDailyStatsRepo := repository.NewUserSprintDailyStatsRepository(fakeDB.DB)
	idempotencyRepo := fake.NewFakeIdempotencyRepository(fakeDB)

	// Create services
	jwtConfig := testutils.GetTestJWTConfig()
	authService := service.NewAuthService(jwtConfig)
	eventService := service.NewEventService(eventRepo)
	chessVerifier := verificationFake.NewFakeChessProfileVerifier()

	// Create test config
	testConfig := &config.Config{
		JWT: jwtConfig,
	}

	// Create quest requirement repo
	questRequirementRepo := fake.NewFakeQuestRequirementRepository()

	// Create dependencies
	deps := &RouterDependencies{
		UserRepo:                 userRepo,
		GameRepo:                 gameRepo,
		PuzzleRepo:               puzzleRepo,
		TaskRepo:                 taskRepo,
		IdempotencyRepo:          idempotencyRepo,
		EventRepo:                eventRepo,
		UserDailyStatsRepo:       userDailyStatsRepo,
		UserEloRepo:              userEloRepo,
		UserSprintDailyStatsRepo: userSprintDailyStatsRepo,
		QuestRequirementRepo:     questRequirementRepo,
		AuthService:              authService,
		EventService:             eventService,
		ChessVerifier:            chessVerifier,
		Config:                   testConfig,
	}

	// Create router
	router := setupRouter(deps)

	// Create test user
	testUser := testutils.CreateTestUser(t, userRepo)

	// Create some test daily stats data
	today := time.Now().UTC()
	yesterday := today.AddDate(0, 0, -1)
	twoDaysAgo := today.AddDate(0, 0, -2)

	// Create puzzle events to generate daily stats
	puzzleEvents := []struct {
		date   time.Time
		solved bool
		time   int
	}{
		{today, true, 30},
		{today, false, 45},
		{yesterday, true, 60},
		{yesterday, true, 25},
		{twoDaysAgo, false, 90},
	}

	for i, pe := range puzzleEvents {
		puzzleEventData := models.PuzzleEventData{
			PuzzleID:    "puzzle-" + pe.date.Format("2006-01-02"),
			PuzzleType:  models.PuzzleTypeUser,
			Solved:      pe.solved,
			TimeSpent:   pe.time,
			MovesPlayed: []string{"e4", "e5", "Nf3"},
		}

		// Create event with specific date
		eventDataJSON, err := json.Marshal(puzzleEventData)
		require.NoError(t, err)

		event := &models.Event{
			ID:        fmt.Sprintf("event-%d-%s", i, testutils.RandomString(8)),
			UserID:    testUser.ID,
			EventType: models.EventTypePuzzle,
			EventData: eventDataJSON,
			EventTime: pe.date,
		}

		err = eventRepo.Create(context.TODO(), event)
		require.NoError(t, err)
	}

	return router, testUser.ID, testUser.Email
}

func TestGetMeWithDailyStats(t *testing.T) {
	router, userID, userEmail := setupUserDailyStatsTest(t)

	t.Run("GetMe_DefaultLast7Days", func(t *testing.T) {
		// Generate token for the test user
		token := testutils.GenerateUserToken(t, userID, userEmail)

		req := testutils.MakeRequest(t, http.MethodGet, "/api/v1/users/me", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)

		var response UserResponse
		testutils.ParseResponseBody(t, resp, &response)

		// Verify user data
		assert.Equal(t, userID, response.ID)
		assert.Equal(t, userEmail, response.Email)

		// Verify daily stats are included
		assert.NotNil(t, response.DailyStats)
		// Should have stats for the days we created events (up to 7 days)
		assert.LessOrEqual(t, len(response.DailyStats), 7)

		// Verify stats are sorted by date descending (most recent first)
		if len(response.DailyStats) > 1 {
			for i := 0; i < len(response.DailyStats)-1; i++ {
				assert.True(t, response.DailyStats[i].Date.After(response.DailyStats[i+1].Date) ||
					response.DailyStats[i].Date.Equal(response.DailyStats[i+1].Date))
			}
		}
	})

	t.Run("GetMe_CustomDaysParameter", func(t *testing.T) {
		// Generate token for the test user
		token := testutils.GenerateUserToken(t, userID, userEmail)

		req := testutils.MakeRequest(t, http.MethodGet, "/api/v1/users/me?days=2", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)

		var response UserResponse
		testutils.ParseResponseBody(t, resp, &response)

		// Verify daily stats are limited to 2 days
		assert.NotNil(t, response.DailyStats)
		assert.LessOrEqual(t, len(response.DailyStats), 2)
	})

	t.Run("GetMe_CustomDateRange", func(t *testing.T) {
		// Generate token for the test user
		token := testutils.GenerateUserToken(t, userID, userEmail)

		// Test specific date range
		today := time.Now().UTC()
		yesterday := today.AddDate(0, 0, -1)

		startDate := yesterday.Format("2006-01-02")
		endDate := today.Format("2006-01-02")

		req := testutils.MakeRequest(t, http.MethodGet, "/api/v1/users/me?start_date="+startDate+"&end_date="+endDate, nil)
		req.Header.Set("Authorization", "Bearer "+token)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)

		var response UserResponse
		testutils.ParseResponseBody(t, resp, &response)

		// Verify daily stats are within the specified range
		assert.NotNil(t, response.DailyStats)
		for _, stat := range response.DailyStats {
			assert.True(t, stat.Date.After(yesterday.AddDate(0, 0, -1)) || stat.Date.Equal(yesterday))
			assert.True(t, stat.Date.Before(today.AddDate(0, 0, 1)) || stat.Date.Equal(today))
		}
	})

	t.Run("GetMe_InvalidDaysParameter", func(t *testing.T) {
		// Generate token for the test user
		token := testutils.GenerateUserToken(t, userID, userEmail)

		req := testutils.MakeRequest(t, http.MethodGet, "/api/v1/users/me?days=500", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response - should return bad request
		testutils.CheckResponseCode(t, http.StatusBadRequest, resp.Code)
	})

	t.Run("GetMe_InvalidDateFormat", func(t *testing.T) {
		// Generate token for the test user
		token := testutils.GenerateUserToken(t, userID, userEmail)

		req := testutils.MakeRequest(t, http.MethodGet, "/api/v1/users/me?start_date=invalid&end_date=2024-01-01", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response - should return bad request
		testutils.CheckResponseCode(t, http.StatusBadRequest, resp.Code)
	})

	t.Run("GetMe_Unauthorized", func(t *testing.T) {
		req := testutils.MakeRequest(t, http.MethodGet, "/api/v1/users/me", nil)
		// Don't set Authorization header
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusUnauthorized, resp.Code)
	})
}
