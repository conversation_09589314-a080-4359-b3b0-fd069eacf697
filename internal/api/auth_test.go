package api

import (
	"net/http"
	"strings"
	"testing"

	"github.com/chessticize/chessticize-server/internal/api/testutils"
	"github.com/chessticize/chessticize-server/internal/config"
	"github.com/chessticize/chessticize-server/internal/db"
	"github.com/stretchr/testify/assert"
)

// ApiTestCase defines a single API endpoint test case
type ApiTestCase struct {
	Name     string
	Endpoint string
	Method   string
	Type     string // "admin" or "user"
}

// ApiTestCases contains all API endpoints to be tested
var ApiTestCases = func() []ApiTestCase {
	// Placeholders will be replaced during tests
	userIdPlaceholder := "{userId}"
	profileIdPlaceholder := "{profileID}"
	taskIdPlaceholder := "{taskId}" // Added task ID placeholder

	return []ApiTestCase{
		// Admin routes - Users
		{"Register", "/api/v1/auth/admin/register", http.MethodPost, "admin"},
		{"ListUsers", "/api/v1/admin/users", http.MethodGet, "admin"},
		{"CreateUser", "/api/v1/admin/users", http.MethodPost, "admin"},
		{"GetUser", "/api/v1/admin/users/" + userIdPlaceholder, http.MethodGet, "admin"},
		{"UpdateUser", "/api/v1/admin/users/" + userIdPlaceholder, http.MethodPut, "admin"},
		// Admin routes - Games & Puzzles
		{"ListGames", "/api/v1/admin/games", http.MethodGet, "admin"},
		{"ListPuzzles", "/api/v1/admin/puzzles", http.MethodGet, "admin"},
		// Admin routes - Chess Profiles
		{"ListUserChessProfiles", "/api/v1/admin/users/" + userIdPlaceholder + "/chess-profiles", http.MethodGet, "admin"},
		{"RefreshUserChessProfile", "/api/v1/admin/users/" + userIdPlaceholder + "/chess-profiles/" + profileIdPlaceholder + "/refresh", http.MethodPut, "admin"},
		// Admin routes - Tasks
		{"ListTasks", "/api/v1/admin/tasks", http.MethodGet, "admin"},
		{"CreateTask", "/api/v1/admin/tasks", http.MethodPost, "admin"},
		{"GetTask", "/api/v1/admin/tasks/" + taskIdPlaceholder, http.MethodGet, "admin"},
		{"ClaimNextTask", "/api/v1/admin/tasks/claim", http.MethodPost, "admin"},
		{"UpdateTask", "/api/v1/admin/tasks/" + taskIdPlaceholder, http.MethodPut, "admin"},
		{"CleanupOldTasks", "/api/v1/admin/tasks/cleanup", http.MethodPost, "admin"},
		{"ResetHangingTasks", "/api/v1/admin/tasks/reset-hanging", http.MethodPost, "admin"},

		// User routes - Profile
		{"GetMyProfile", "/api/v1/users/me", http.MethodGet, "user"},
		// User routes - Chess Profiles
		{"ListMyChessProfiles", "/api/v1/users/me/chess-profiles", http.MethodGet, "user"},
		{"CreateMyChessProfile", "/api/v1/users/me/chess-profiles", http.MethodPost, "user"},
		{"DeleteMyChessProfile", "/api/v1/users/me/chess-profiles/" + profileIdPlaceholder, http.MethodDelete, "user"},
		// User routes - Puzzles
		{"ListMyPuzzles", "/api/v1/users/me/puzzles", http.MethodGet, "user"},
		// User routes - Games
		{"ListMyGames", "/api/v1/users/me/games", http.MethodGet, "user"},
	}
}()

// Common setup for all auth tests
func SetupAuthTest(t *testing.T) (http.Handler, string, string, string) {
	// Set up common test dependencies
	userRepo, _, _ := testutils.GetFakeRepositories(t)
	defer testutils.CleanupTestDB(t)

	// Create test user for tokens
	testUser := testutils.CreateTestUser(t, userRepo)

	// Generate tokens for testing
	adminToken := testutils.GenerateAdminToken(t, testUser.ID, testUser.Email)
	userToken := testutils.GenerateUserToken(t, testUser.ID, testUser.Email)

	// Create database mock with repositories
	database := &db.Database{
		DB: testutils.TestDB(t),
	}

	// Get JWT config and create actual app configuration
	jwtConfig := testutils.GetTestJWTConfig()
	cfg := &config.Config{
		JWT: jwtConfig,
	}

	// Use the actual router implementation
	router := NewRouter(database, cfg)

	return router, testUser.ID, adminToken, userToken
}

// TestAdminRoutesNoToken checks that admin routes reject requests with no token
func TestAdminRoutesNoToken(t *testing.T) {
	router, userID, _, _ := SetupAuthTest(t)

	for _, tc := range ApiTestCases {
		if tc.Type != "admin" {
			continue
		}

		endpoint := replacePlaceholders(tc.Endpoint, userID, "dummy-profile-id", "dummy-task-id")

		t.Run(tc.Name+"_NoToken", func(t *testing.T) {
			req := testutils.MakeRequest(t, tc.Method, endpoint, nil)
			resp := testutils.ExecuteRequest(t, router, req)
			assert.Equal(t, http.StatusUnauthorized, resp.Code,
				"Expected status %d for %s %s, got %d. Response: %s",
				http.StatusUnauthorized, tc.Method, endpoint, resp.Code, resp.Body.String())
		})
	}
}

// TestAdminRoutesUserToken checks that admin routes reject requests with regular user token
func TestAdminRoutesUserToken(t *testing.T) {
	router, userID, _, userToken := SetupAuthTest(t)

	for _, tc := range ApiTestCases {
		if tc.Type != "admin" {
			continue
		}

		endpoint := replacePlaceholders(tc.Endpoint, userID, "dummy-profile-id", "dummy-task-id")

		t.Run(tc.Name+"_UserToken", func(t *testing.T) {
			req := testutils.MakeRequest(t, tc.Method, endpoint, nil)
			req.Header.Set("Authorization", "Bearer "+userToken)
			resp := testutils.ExecuteRequest(t, router, req)
			assert.Equal(t, http.StatusForbidden, resp.Code,
				"Expected status %d for %s %s, got %d. Response: %s",
				http.StatusForbidden, tc.Method, endpoint, resp.Code, resp.Body.String())
		})
	}
}

// TestUserRoutesNoToken checks that user routes reject requests with no token
func TestUserRoutesNoToken(t *testing.T) {
	router, userID, _, _ := SetupAuthTest(t)

	for _, tc := range ApiTestCases {
		if tc.Type != "user" {
			continue
		}

		endpoint := replacePlaceholders(tc.Endpoint, userID, "dummy-profile-id", "dummy-task-id")

		t.Run(tc.Name+"_NoToken", func(t *testing.T) {
			req := testutils.MakeRequest(t, tc.Method, endpoint, nil)
			resp := testutils.ExecuteRequest(t, router, req)
			assert.Equal(t, http.StatusUnauthorized, resp.Code,
				"Expected status %d for %s %s, got %d. Response: %s",
				http.StatusUnauthorized, tc.Method, endpoint, resp.Code, resp.Body.String())
		})
	}
}

// TestInvalidToken checks that routes reject requests with invalid tokens
func TestInvalidToken(t *testing.T) {
	router, userID, _, _ := SetupAuthTest(t)

	for _, tc := range ApiTestCases {
		endpoint := replacePlaceholders(tc.Endpoint, userID, "dummy-profile-id", "dummy-task-id")

		t.Run(tc.Name+"_InvalidToken", func(t *testing.T) {
			req := testutils.MakeRequest(t, tc.Method, endpoint, nil)
			req.Header.Set("Authorization", "Bearer invalid-token-string")
			resp := testutils.ExecuteRequest(t, router, req)
			assert.Equal(t, http.StatusUnauthorized, resp.Code,
				"Expected status %d for %s %s, got %d. Response: %s",
				http.StatusUnauthorized, tc.Method, endpoint, resp.Code, resp.Body.String())
		})
	}
}

// Helper function to replace placeholders with actual IDs or dummy values
func replacePlaceholders(endpoint string, userID, profileID, taskID string) string {
	const userIdPlaceholder = "{userId}"
	const profileIdPlaceholder = "{profileID}"
	const taskIdPlaceholder = "{taskId}"

	result := endpoint
	if userID != "" {
		result = strings.ReplaceAll(result, userIdPlaceholder, userID)
	}
	if profileID != "" {
		result = strings.ReplaceAll(result, profileIdPlaceholder, profileID)
	}
	if taskID != "" {
		result = strings.ReplaceAll(result, taskIdPlaceholder, taskID)
	}

	return result
}
