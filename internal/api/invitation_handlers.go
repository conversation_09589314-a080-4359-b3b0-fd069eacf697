package api

import (
	"crypto/rand"
	"encoding/base64"
	"errors"
	"net/http"
	"strconv"
	"time"

	"github.com/chessticize/chessticize-server/internal/logger"
	"github.com/chessticize/chessticize-server/internal/middleware"
	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/go-chi/render"
	"gorm.io/gorm"
)

// RegisterWithInvitation handles user registration with an invitation code
func (h *AuthHandler) RegisterWithInvitation(w http.ResponseWriter, r *http.Request) {
	var req RegisterWithInvitationRequest
	if err := render.DecodeJSON(r.Body, &req); err != nil {
		apiError(w, r, http.StatusBadRequest, err, "Invalid request body")
		return
	}

	// Validate invitation code
	if req.InvitationCode == "" {
		apiError(w, r, http.StatusBadRequest, nil, "Invitation code is required")
		return
	}

	// Verify invitation code
	invitationCode, err := h.invitationCodeRepo.GetByCode(r.Context(), req.InvitationCode)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			apiError(w, r, http.StatusBadRequest, nil, "Invalid invitation code")
		} else {
			apiError(w, r, http.StatusInternalServerError, err, "Error verifying invitation code")
		}
		return
	}

	// Check if invitation code is valid
	if !invitationCode.IsValid() {
		apiError(w, r, http.StatusBadRequest, nil, "Invitation code is expired or already used")
		return
	}

	// Create user using common logic
	params := req.toCreateUserParams()
	userAgent := r.Header.Get("User-Agent")
	result := h.createUser(r.Context(), params, userAgent)

	// Handle error if any
	if result.Error != nil {
		apiError(w, r, result.Error.Status, result.Error.Err, result.Error.Message)
		return
	}

	// Mark invitation code as used
	invitationCode.MarkAsUsed(result.User.ID)
	if err := h.invitationCodeRepo.Update(r.Context(), invitationCode); err != nil {
		// Log the error but continue - user is already created
		logger.FromContext(r.Context()).Error().Err(err).Str("invitation_code_id", invitationCode.ID).Msg("Failed to mark invitation code as used")
	}

	// Indicate 201 status
	render.Status(r, http.StatusCreated)

	// Return register response with user info and tokens for backward compatibility
	render.JSON(w, r, RegisterResponse{
		ID:           result.User.ID,
		Email:        result.User.Email,
		Token:        result.Token,
		SessionToken: result.SessionToken,
	})
}

// CreateInvitationCode creates a new invitation code (admin only)
func (h *AuthHandler) CreateInvitationCode(w http.ResponseWriter, r *http.Request) {
	var req CreateInvitationCodeRequest
	if err := render.DecodeJSON(r.Body, &req); err != nil {
		apiError(w, r, http.StatusBadRequest, err, "Invalid request body")
		return
	}

	// Get admin user ID from context
	adminUserID := middleware.GetUserIDFromContext(r.Context())
	if adminUserID == "" {
		apiError(w, r, http.StatusUnauthorized, nil, "User ID not found in context")
		return
	}

	// Generate a random code (32 bytes -> base64 encoded)
	codeBytes := make([]byte, 32)
	if _, err := rand.Read(codeBytes); err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to generate random code")
		return
	}
	codeStr := base64.URLEncoding.EncodeToString(codeBytes)

	// Create invitation code model
	invitationCode := &models.InvitationCode{
		Code:            codeStr,
		CreatedByUserID: adminUserID,
	}

	// Set expiration time if provided
	if req.ExpiresInHours != nil && *req.ExpiresInHours > 0 {
		expiresAt := time.Now().Add(time.Duration(*req.ExpiresInHours) * time.Hour)
		invitationCode.ExpiresAt = &expiresAt
	}

	// Save invitation code to database
	if err := h.invitationCodeRepo.Create(r.Context(), invitationCode); err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to create invitation code")
		return
	}

	// Return the invitation code
	response := InvitationCodeResponse{
		ID:        invitationCode.ID,
		Code:      invitationCode.Code,
		CreatedAt: invitationCode.CreatedAt,
		ExpiresAt: invitationCode.ExpiresAt,
		Used:      false,
	}

	render.Status(r, http.StatusCreated)
	render.JSON(w, r, response)
}

// ListInvitationCodes lists all invitation codes (admin only)
func (h *AuthHandler) ListInvitationCodes(w http.ResponseWriter, r *http.Request) {
	// Parse pagination parameters
	offset, limit := 0, 50
	if offsetStr := r.URL.Query().Get("offset"); offsetStr != "" {
		if val, err := strconv.Atoi(offsetStr); err == nil && val >= 0 {
			offset = val
		}
	}
	if limitStr := r.URL.Query().Get("limit"); limitStr != "" {
		if val, err := strconv.Atoi(limitStr); err == nil && val > 0 && val <= 100 {
			limit = val
		}
	}

	// Get only valid codes if specified
	onlyValid := false
	if validStr := r.URL.Query().Get("valid"); validStr == "true" {
		onlyValid = true
	}

	var codes []models.InvitationCode
	var count int64
	var err error

	if onlyValid {
		codes, count, err = h.invitationCodeRepo.ListValid(r.Context(), offset, limit)
	} else {
		codes, count, err = h.invitationCodeRepo.ListAll(r.Context(), offset, limit)
	}

	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to list invitation codes")
		return
	}

	// Convert to response format
	response := struct {
		Codes      []InvitationCodeResponse `json:"codes"`
		TotalCount int64                    `json:"total_count"`
	}{
		Codes:      make([]InvitationCodeResponse, len(codes)),
		TotalCount: count,
	}

	for i, code := range codes {
		response.Codes[i] = InvitationCodeResponse{
			ID:        code.ID,
			Code:      code.Code,
			CreatedAt: code.CreatedAt,
			ExpiresAt: code.ExpiresAt,
			Used:      code.UsedAt != nil,
		}
	}

	render.JSON(w, r, response)
}
