package api

import (
	"net/http"
	"strconv"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/go-chi/chi/v5"
	"github.com/go-chi/render"
	"github.com/google/uuid"
)

// PuzzleQueueHandler handles puzzle queue related HTTP requests
type PuzzleQueueHandler struct {
	puzzleQueueRepo repository.IPuzzleQueueRepository
	puzzleRepo      repository.IPuzzleRepository
}

// NewPuzzleQueueHandler creates a new PuzzleQueueHandler
func NewPuzzleQueueHandler(puzzleQueueRepo repository.IPuzzleQueueRepository, puzzleRepo repository.IPuzzleRepository) *PuzzleQueueHandler {
	return &PuzzleQueueHandler{
		puzzleQueueRepo: puzzleQueueRepo,
		puzzleRepo:      puzzleRepo,
	}
}

// PuzzleQueueRoutes creates a router for puzzle queue endpoints
func PuzzleQueueRoutes(puzzleQueueRepo repository.IPuzzleQueueRepository, puzzleRepo repository.IPuzzleRepository) http.Handler {
	h := NewPuzzleQueueHandler(puzzleQueueRepo, puzzleRepo)
	r := chi.NewRouter()

	r.Post("/add", h.AddPuzzlesToQueue)
	r.Get("/due", h.GetDuePuzzles)
	r.Get("/stats", h.GetQueueStats)

	return r
}

// AddPuzzlesToQueueRequest defines the request body for adding puzzles to queue
type AddPuzzlesToQueueRequest struct {
	Count     int     `json:"count"`                // Number of puzzles to add (max 200)
	MistakeBy *string `json:"mistake_by,omitempty"` // Optional: "opponent" or "own"
}

// AddPuzzlesToQueueResponse defines the response for adding puzzles to queue
type AddPuzzlesToQueueResponse struct {
	AddedCount   int    `json:"added_count"`
	SkippedCount int    `json:"skipped_count"`
	Message      string `json:"message"`
}

// AddPuzzlesToQueue handles POST /api/puzzle-queue/add
func (h *PuzzleQueueHandler) AddPuzzlesToQueue(w http.ResponseWriter, r *http.Request) {
	userID, err := getUserIDFromContext(r)
	if err != nil {
		apiError(w, r, http.StatusUnauthorized, err, "Unauthorized: Invalid user context")
		return
	}

	// Parse request body
	var req AddPuzzlesToQueueRequest
	if err := render.DecodeJSON(r.Body, &req); err != nil {
		apiError(w, r, http.StatusBadRequest, err, "Invalid request body")
		return
	}

	// Validate request
	if req.Count <= 0 {
		apiError(w, r, http.StatusBadRequest, nil, "count must be greater than 0")
		return
	}
	if req.Count > 200 {
		apiError(w, r, http.StatusBadRequest, nil, "count cannot exceed 200")
		return
	}
	if req.MistakeBy != nil && *req.MistakeBy != "" && *req.MistakeBy != "opponent" && *req.MistakeBy != "own" {
		apiError(w, r, http.StatusBadRequest, nil, "mistake_by must be 'opponent' or 'own'")
		return
	}

	// Get recent puzzles not in queue
	puzzleIDs, err := h.puzzleQueueRepo.GetRecentPuzzlesNotInQueue(r.Context(), userID, req.MistakeBy, req.Count)
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to get recent puzzles")
		return
	}

	if len(puzzleIDs) == 0 {
		response := AddPuzzlesToQueueResponse{
			AddedCount:   0,
			SkippedCount: 0,
			Message:      "No eligible puzzles found to add to queue",
		}
		render.JSON(w, r, response)
		return
	}

	// Get puzzle details to determine themes and mistake_by
	puzzles := make([]models.PuzzleQueueEntry, 0, len(puzzleIDs))
	for _, puzzleID := range puzzleIDs {
		puzzle, err := h.puzzleRepo.GetByID(r.Context(), puzzleID)
		if err != nil {
			// Skip puzzles that can't be found
			continue
		}

		// Check if theme is queueable
		if !models.IsQueueableTheme(puzzle.Theme) {
			continue
		}

		// Create queue entry
		queueEntry := models.PuzzleQueueEntry{
			ID:          uuid.New().String(),
			UserID:      userID,
			PuzzleID:    puzzleID,
			PuzzleTheme: puzzle.Theme,
			MistakeBy:   models.GetMistakeByFromTheme(puzzle.Theme),
		}

		puzzles = append(puzzles, queueEntry)
	}

	// Add puzzles to queue
	addedCount, err := h.puzzleQueueRepo.AddPuzzlesToQueue(r.Context(), userID, puzzles)
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to add puzzles to queue")
		return
	}

	skippedCount := len(puzzleIDs) - addedCount
	response := AddPuzzlesToQueueResponse{
		AddedCount:   addedCount,
		SkippedCount: skippedCount,
		Message:      "Puzzles added to queue successfully",
	}

	render.JSON(w, r, response)
}

// GetDuePuzzles handles GET /api/puzzle-queue/due
func (h *PuzzleQueueHandler) GetDuePuzzles(w http.ResponseWriter, r *http.Request) {
	userID, err := getUserIDFromContext(r)
	if err != nil {
		apiError(w, r, http.StatusUnauthorized, err, "Unauthorized: Invalid user context")
		return
	}

	// Parse query parameters
	limitStr := r.URL.Query().Get("limit")
	limit := 10 // Default limit
	if limitStr != "" {
		if parsedLimit, err := strconv.Atoi(limitStr); err == nil && parsedLimit > 0 {
			if parsedLimit > 50 {
				limit = 50 // Max limit
			} else {
				limit = parsedLimit
			}
		}
	}

	mistakeBy := r.URL.Query().Get("mistake_by")
	var mistakeByPtr *string
	if mistakeBy != "" {
		if mistakeBy != "opponent" && mistakeBy != "own" {
			apiError(w, r, http.StatusBadRequest, nil, "mistake_by must be 'opponent' or 'own'")
			return
		}
		mistakeByPtr = &mistakeBy
	}

	// Get due puzzles
	puzzles, err := h.puzzleQueueRepo.GetDuePuzzles(r.Context(), userID, mistakeByPtr, limit)
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to get due puzzles")
		return
	}

	// Get total due count for response
	stats, err := h.puzzleQueueRepo.GetQueueStats(r.Context(), userID)
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to get queue stats")
		return
	}

	response := map[string]interface{}{
		"puzzles":   puzzles,
		"total_due": stats.DueToday,
	}

	render.JSON(w, r, response)
}

// GetQueueStats handles GET /api/puzzle-queue/stats
func (h *PuzzleQueueHandler) GetQueueStats(w http.ResponseWriter, r *http.Request) {
	userID, err := getUserIDFromContext(r)
	if err != nil {
		apiError(w, r, http.StatusUnauthorized, err, "Unauthorized: Invalid user context")
		return
	}

	// Get queue statistics
	stats, err := h.puzzleQueueRepo.GetQueueStats(r.Context(), userID)
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to get queue stats")
		return
	}

	render.JSON(w, r, stats)
}
