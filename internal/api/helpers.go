package api

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/chessticize/chessticize-server/internal/logger"
	"github.com/go-chi/render"
)

// apiError logs the error (if not nil) and sends a JSON error response.
func apiError(w http.ResponseWriter, r *http.Request, status int, err error, message string) {
	log := logger.FromContext(r.Context())
	if err != nil {
		log.Error().Err(err).Msg(message) // Log the actual error
	} else {
		log.Warn().Msg(message) // Log validation/request errors without a Go error object
	}

	render.Status(r, status)
	render.JSON(w, r, map[string]string{"error": message}) // Send a user-friendly message
}

// Helper function to parse pagination parameters
func parsePaginationParams(r *http.Request) (offset int, limit int, err error) {
	offsetStr := r.URL.Query().Get("offset")
	limitStr := r.URL.Query().Get("limit")

	offset = 0 // Default offset
	if offsetStr != "" {
		offset, err = strconv.Atoi(offsetStr)
		if err != nil || offset < 0 {
			return 0, 0, fmt.Errorf("invalid offset parameter")
		}
	}

	limit = 50 // Default limit
	if limitStr != "" {
		limit, err = strconv.Atoi(limitStr)
		if err != nil || limit <= 0 || limit > 100 {
			return 0, 0, fmt.Errorf("invalid limit parameter (must be 1-100)")
		}
	}
	return offset, limit, nil
}

// Helper function to parse time parameter
func parseTimeParam(r *http.Request, paramName string) (*time.Time, error) {
	timeStr := r.URL.Query().Get(paramName)
	if timeStr == "" {
		return nil, nil // Not provided, which is valid
	}
	t, err := time.Parse(time.RFC3339, timeStr)
	if err != nil {
		return nil, fmt.Errorf("invalid %s parameter format (must be RFC3339): %w", paramName, err)
	}
	return &t, nil
}
