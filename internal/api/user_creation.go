package api

import (
	"context"
	"errors"
	"net/http"

	"github.com/chessticize/chessticize-server/internal/logger"
	"github.com/chessticize/chessticize-server/internal/models"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// createUserParams contains the parameters needed to create a new user
type createUserParams struct {
	Email    string
	Password string
}

// createUserResult contains the result of a user creation operation
type createUserResult struct {
	User         *models.User
	Token        string
	SessionToken string
	Error        *apiErrorInfo
}

// apiErrorInfo contains information about an API error
type apiErrorInfo struct {
	Status  int
	Err     error
	Message string
}

// validateUserParams validates the user creation parameters
func validateUserParams(params createUserParams) *apiErrorInfo {
	if params.Email == "" {
		return &apiErrorInfo{
			Status:  http.StatusBadRequest,
			Message: "Email is required",
		}
	}
	if len(params.Password) < 6 || len(params.Password) > 40 {
		return &apiErrorInfo{
			Status:  http.StatusBadRequest,
			Message: "Password must be between 6 and 40 characters",
		}
	}
	return nil
}

// createUser handles the common logic for creating a user
func (h *AuthHandler) createUser(ctx context.Context, params createUserParams, userAgent string) createUserResult {
	// Validate parameters
	if errInfo := validateUserParams(params); errInfo != nil {
		return createUserResult{Error: errInfo}
	}

	// Check if user already exists
	_, err := h.userRepo.GetByEmail(ctx, params.Email)
	if err == nil {
		// User found, return conflict
		return createUserResult{
			Error: &apiErrorInfo{
				Status:  http.StatusConflict,
				Message: "Email already registered",
			},
		}
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		// Unexpected database error
		return createUserResult{
			Error: &apiErrorInfo{
				Status:  http.StatusInternalServerError,
				Err:     err,
				Message: "Database error while checking email existence",
			},
		}
	}

	// Hash the password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(params.Password), bcrypt.DefaultCost)
	if err != nil {
		return createUserResult{
			Error: &apiErrorInfo{
				Status:  http.StatusInternalServerError,
				Err:     err,
				Message: "Failed to process password",
			},
		}
	}

	// Create new user model
	newUser := &models.User{
		Email:        params.Email,
		PasswordHash: string(hashedPassword),
		// ID, RegisteredAt, UpdatedAt will be set by repository.Create
	}

	// Save user to database
	if err := h.userRepo.Create(ctx, newUser); err != nil {
		return createUserResult{
			Error: &apiErrorInfo{
				Status:  http.StatusInternalServerError,
				Err:     err,
				Message: "Failed to register user",
			},
		}
	}

	// Generate token for the new user
	token, err := h.authService.GenerateToken(newUser.ID, newUser.Email, false)
	if err != nil {
		// Log the error but continue - user is already created
		logger.FromContext(ctx).Error().Err(err).Str("user_id", newUser.ID).Msg("Failed to generate token for new user")
	}

	// Generate session token for the new user
	var sessionTokenString string
	sessionToken, err := h.authService.GenerateSessionToken(ctx, newUser.ID, userAgent)
	if err != nil {
		// Log the error but continue - user is already created
		logger.FromContext(ctx).Error().Err(err).Str("user_id", newUser.ID).Msg("Failed to generate session token for new user")
	} else if sessionToken != nil {
		sessionTokenString = sessionToken.Token
	}

	// Create sign-in success event for registration (registration is essentially a successful password login)
	if err := h.eventService.CreateSignInSuccessEvent(ctx, newUser.ID, "password", userAgent); err != nil {
		// Log the error but continue - user is already created and tokens generated
		logger.FromContext(ctx).Error().Err(err).Str("user_id", newUser.ID).Msg("Failed to create sign-in success event for registration")
	}

	return createUserResult{
		User:         newUser,
		Token:        token,
		SessionToken: sessionTokenString,
	}
}
