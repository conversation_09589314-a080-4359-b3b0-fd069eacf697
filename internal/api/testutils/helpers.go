package testutils

import (
	"bytes"
	"context"
	"encoding/json"
	"math/rand"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/config"
	"github.com/chessticize/chessticize-server/internal/middleware"
	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/repository/fake"
	"github.com/chessticize/chessticize-server/internal/service"
	"github.com/chessticize/chessticize-server/internal/utils"
	"github.com/google/uuid"
	"github.com/lib/pq"
	"github.com/stretchr/testify/require"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

var (
	testDB *fake.DB
)

// TestDB returns a fake DB instance for testing
func TestDB(t *testing.T) *gorm.DB {
	if testDB != nil {
		return testDB.DB
	}

	testDB = fake.NewDB(t)
	return testDB.DB
}

// GetFakeDB returns the fake DB wrapper instance for testing
func GetFakeDB(t *testing.T) *fake.DB {
	if testDB == nil {
		testDB = fake.NewDB(t)
	}
	return testDB
}

// CreateRepositories creates repository instances for testing
func CreateRepositories(t *testing.T) (repository.IUserRepository, repository.IGameRepository, repository.IPuzzleRepository) {
	// Get or create test DB
	db := TestDB(t)

	// Create repositories
	userRepo := repository.NewUserRepository(db)
	gameRepo := repository.NewGameRepository(db)
	puzzleRepo := repository.NewPuzzleRepository(db)

	return userRepo, gameRepo, puzzleRepo
}

// GetFakeRepositories returns fake repository implementations for testing
func GetFakeRepositories(t *testing.T) (repository.IUserRepository, repository.IGameRepository, repository.IPuzzleRepository) {
	// Get or create fake DB
	if testDB == nil {
		testDB = fake.NewDB(t)
	}

	// Create fake repositories
	userRepo := fake.NewUserRepository(testDB)
	gameRepo := fake.NewGameRepository(testDB)
	puzzleRepo := fake.NewPuzzleRepository(testDB)

	return userRepo, gameRepo, puzzleRepo
}

// CleanupTestDB cleans up the test database
func CleanupTestDB(t *testing.T) {
	if testDB != nil {
		err := testDB.Close()
		if err != nil {
			t.Logf("Error closing test DB: %v", err)
		}
		testDB = nil
	}
}

// RandomString generates a random string of specified length
func RandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	result := make([]byte, length)
	for i := range result {
		result[i] = charset[rand.Intn(len(charset))]
	}
	return string(result)
}

// RandomEmail generates a random email
func RandomEmail() string {
	return RandomString(10) + "@example.com"
}

// CreateTestUser creates a test user
func CreateTestUser(t *testing.T, userRepo repository.IUserRepository) *models.User {
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte("password"), bcrypt.DefaultCost)
	require.NoError(t, err)

	user := &models.User{
		Email:         RandomEmail(),
		PasswordHash:  string(hashedPassword),
		ChessProfiles: nil,
	}

	err = userRepo.Create(context.Background(), user)
	require.NoError(t, err)

	createdUser, err := userRepo.GetByEmail(context.Background(), user.Email)
	require.NoError(t, err)
	require.NotNil(t, createdUser)

	return createdUser
}

// CreateTestGame creates a test game with valid compressed PGN.
func CreateTestGame(t *testing.T, gameRepo repository.IGameRepository, userID string) *models.Game {
	whitePlayer, _ := json.Marshal(models.PlayerInfo{
		Username: RandomString(10),
		Rating:   nil,
		IsAI:     false,
	})

	blackPlayer, _ := json.Marshal(models.PlayerInfo{
		Username: RandomString(10),
		Rating:   nil,
		IsAI:     false,
	})

	// Use a minimal valid PGN for default test games
	defaultPGN := `[Event "?"]\n[Site "?"]\n[Date "????.??.??"]\n[Round "?"]\n[White "?"]\n[Black "?"]\n[Result "*"]\n\n*`
	compressedPGN, err := utils.CompressPGN(defaultPGN)
	require.NoError(t, err, "Failed to compress default PGN for test game")

	game := &models.Game{
		UserID:        userID,
		Platform:      models.ChessDotCom,
		ChessUsername: RandomString(10),
		UserColor:     models.White,
		GameTime:      time.Now().UTC(),
		CompressedPGN: compressedPGN, // Use valid compressed data
		TimeControl:   "5+0",
		Rated:         rand.Intn(2) == 1,
		URL:           nil,
		WhitePlayer:   string(whitePlayer),
		BlackPlayer:   string(blackPlayer),
		Winner:        models.WinnerWhite,
		Result:        models.Mate,
	}

	err = gameRepo.Create(context.Background(), game)
	require.NoError(t, err)

	// Fetch the created game to ensure it has an ID
	createdGame, err := gameRepo.GetByID(context.Background(), game.ID)
	require.NoError(t, err)
	require.NotNil(t, createdGame)

	return createdGame
}

// CreateTestGameWithPGN creates a test game with specified PGN data.
// This function is similar to CreateTestGame but compresses the provided PGN.
func CreateTestGameWithPGN(t *testing.T, gameRepo repository.IGameRepository, userID string, pgnData string) *models.Game {
	whitePlayer, _ := json.Marshal(models.PlayerInfo{
		Username: RandomString(10),
		Rating:   nil,
		IsAI:     false,
	})

	blackPlayer, _ := json.Marshal(models.PlayerInfo{
		Username: RandomString(10),
		Rating:   nil,
		IsAI:     false,
	})

	// Compress the provided PGN data
	compressedPGN, err := utils.CompressPGN(pgnData) // Use utils package
	require.NoError(t, err, "Failed to compress PGN data for test game")

	game := &models.Game{
		UserID:        userID,
		Platform:      models.ChessDotCom,
		ChessUsername: RandomString(10),
		UserColor:     models.White,
		GameTime:      time.Now().UTC(),
		CompressedPGN: compressedPGN, // Use the compressed data
		TimeControl:   "5+0",
		Rated:         rand.Intn(2) == 1,
		URL:           nil,
		WhitePlayer:   string(whitePlayer),
		BlackPlayer:   string(blackPlayer),
		Winner:        models.WinnerWhite,
		Result:        models.Mate,
	}

	err = gameRepo.Create(context.Background(), game)
	require.NoError(t, err)

	// Fetch the created game to ensure it has an ID
	createdGame, err := gameRepo.GetByID(context.Background(), game.ID)
	require.NoError(t, err)
	require.NotNil(t, createdGame)

	return createdGame
}

// CreateTestPuzzle creates a test puzzle with deterministic data
func CreateTestPuzzle(t *testing.T, puzzleRepo repository.IPuzzleRepository, gameID, userID string) *models.Puzzle {
	puzzle := &models.Puzzle{
		GameID:      gameID,
		UserID:      userID,
		GameMove:    15,                                                         // Deterministic value
		FEN:         "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1", // Standard starting position
		Moves:       pq.StringArray{"e2e4", "e7e5"},                             // Standard opening moves
		PrevCP:      0,                                                          // Deterministic value
		CP:          100,                                                        // CP change = 100, outside common test filter ranges
		Theme:       models.OpponentMistakeCaught,
		UserColor:   models.White,
		PuzzleColor: models.Black,
		Zugzwang:    false,                                   // Deterministic value
		Tags:        pq.StringArray{"test", "deterministic"}, // Deterministic tags
	}

	err := puzzleRepo.Create(context.Background(), puzzle)
	require.NoError(t, err)

	return puzzle
}

// MakeRequest creates an HTTP request with JSON body
func MakeRequest(t *testing.T, method, url string, body interface{}) *http.Request {
	var reqBody []byte
	var err error

	if body != nil {
		reqBody, err = json.Marshal(body)
		require.NoError(t, err)
	}

	req, err := http.NewRequest(method, url, bytes.NewBuffer(reqBody))
	require.NoError(t, err)

	req.Header.Set("Content-Type", "application/json")
	return req
}

// MakeAuthenticatedRequest creates an HTTP request with JSON body and adds Authorization header
func MakeAuthenticatedRequest(t *testing.T, method, url, userID, userEmail string, body interface{}) *http.Request {
	req := MakeRequest(t, method, url, body)

	// Generate a token for the user
	token := GenerateUserToken(t, userID, userEmail) // Use helper to get non-admin token

	// Add Authorization header
	req.Header.Set("Authorization", "Bearer "+token)

	// NOTE: We no longer need to manually add the UserID to the context,
	// as the real JWTAuth middleware should handle extracting it from the token.
	return req
}

func MakeAdminRequest(t *testing.T, method, url string, body interface{}) *http.Request {
	req := MakeRequest(t, method, url, body)
	req.Header.Set("Authorization", "Bearer "+GenerateAdminToken(t, "test-admin-id", "<EMAIL>"))
	return req
}

// ExecuteRequest executes a request and returns a response recorder
func ExecuteRequest(t *testing.T, handler http.Handler, req *http.Request) *httptest.ResponseRecorder {
	rr := httptest.NewRecorder()
	handler.ServeHTTP(rr, req)
	return rr
}

// CheckResponseCode checks the HTTP status code
func CheckResponseCode(t *testing.T, expected, actual int) {
	require.Equal(t, expected, actual, "Expected response code %d, got %d", expected, actual)
}

// ParseResponseBody parses the JSON response body
func ParseResponseBody(t *testing.T, rr *httptest.ResponseRecorder, v interface{}) {
	err := json.Unmarshal(rr.Body.Bytes(), v)
	require.NoError(t, err)
}

// GetTestJWTConfig returns a test JWT configuration
func GetTestJWTConfig() config.JWTConfig {
	return config.JWTConfig{
		Secret:        "test-secret-key-for-testing-purposes-only",
		ExpiryMinutes: 60,
	}
}

// GenerateAdminToken generates an admin JWT token for testing
func GenerateAdminToken(t *testing.T, userID, email string) string {
	jwtConfig := GetTestJWTConfig()
	authService := service.NewAuthService(jwtConfig)

	token, err := authService.GenerateToken(userID, email, true)
	require.NoError(t, err)

	return token
}

// GenerateUserToken generates a non-admin JWT token for testing
func GenerateUserToken(t *testing.T, userID, email string) string {
	jwtConfig := GetTestJWTConfig()
	authService := service.NewAuthService(jwtConfig)

	// Generate token with isAdmin = false
	token, err := authService.GenerateToken(userID, email, false)
	require.NoError(t, err)

	return token
}

// HashPassword hashes a password for testing
func HashPassword(password string) (string, error) {
	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return string(hashedBytes), nil
}

// CreateUserWithPassword creates a test user with a specified password
func CreateUserWithPassword(t *testing.T, userRepo repository.IUserRepository, email, passwordHash string) *models.User {
	user := &models.User{
		Email:         email,
		PasswordHash:  passwordHash,
		ChessProfiles: nil,
	}

	err := userRepo.Create(context.Background(), user)
	require.NoError(t, err)

	createdUser, err := userRepo.GetByEmail(context.Background(), user.Email)
	require.NoError(t, err)
	require.NotNil(t, createdUser)

	return createdUser
}

// CreateTestAuthService creates an auth service for testing
func CreateTestAuthService(jwtConfig config.JWTConfig) *service.AuthService {
	return service.NewAuthService(jwtConfig)
}

// GenerateIdempotencyKey creates a new UUID string for use as an idempotency key.
func GenerateIdempotencyKey() string {
	return uuid.NewString()
}

// AddIdempotencyHeader adds the Idempotency-Key header to an HTTP request.
func AddIdempotencyHeader(req *http.Request, key string) {
	req.Header.Set(middleware.IdempotencyKeyHeader, key)
}
