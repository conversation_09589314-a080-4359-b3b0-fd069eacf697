package api

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"testing"

	"github.com/chessticize/chessticize-server/internal/api/testutils"
	"github.com/chessticize/chessticize-server/internal/config"
	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/repository/fake"
	"github.com/chessticize/chessticize-server/internal/service"
	"github.com/lib/pq"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// setupPuzzleTest sets up the router with fake dependencies for puzzle API tests.
func setupPuzzleTest(t *testing.T) (http.Handler, repository.IPuzzleRepository, repository.IGameRepository, repository.IUserRepository) {
	// Setup fake DB and Repos
	fakeDB := testutils.GetFakeDB(t)
	userRepo, gameRepo, puzzleRepo := testutils.GetFakeRepositories(t)
	taskRepo := fake.NewFakeTaskRepository(fakeDB)
	idempotencyRepo := fake.NewFakeIdempotencyRepository(fakeDB)

	cfg := &config.Config{
		JWT: testutils.GetTestJWTConfig(),
	}
	authService := service.NewAuthService(cfg.JWT)

	deps := &RouterDependencies{
		UserRepo:        userRepo,
		GameRepo:        gameRepo,
		PuzzleRepo:      puzzleRepo,
		TaskRepo:        taskRepo,
		IdempotencyRepo: idempotencyRepo,
		AuthService:     authService,
		Config:          cfg,
	}
	router := setupRouter(deps)

	return router, puzzleRepo, gameRepo, userRepo
}

func TestPuzzleAPI(t *testing.T) {
	router, puzzleRepo, gameRepo, userRepo := setupPuzzleTest(t)
	defer testutils.CleanupTestDB(t)

	// Helper to make admin requests
	makeAdminRequest := func(t *testing.T, method, path string, body interface{}) *http.Request {
		// Ensure the path starts with the correct admin prefix
		fullPath := "/api/v1/admin/puzzles" + path
		return testutils.MakeAdminRequest(t, method, fullPath, body)
	}

	// Create a test user and game
	user := testutils.CreateTestUser(t, userRepo)
	game := testutils.CreateTestGame(t, gameRepo, user.ID)

	t.Run("CreatePuzzle", func(t *testing.T) {
		// Create test puzzle request
		createPuzzleReq := CreatePuzzleRequest{
			GameID:      game.ID,
			UserID:      user.ID,
			GameMove:    20,
			FEN:         "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
			Moves:       pq.StringArray{"e2e4", "e7e5"},
			PrevCP:      -50,
			CP:          150,
			Theme:       models.OpponentMistakeCaught,
			UserColor:   models.White,
			PuzzleColor: models.Black,
			Zugzwang:    false,
			Tags:        []string{"opening", "tactics"},
		}

		// Make ADMIN request
		req := makeAdminRequest(t, http.MethodPost, "/", createPuzzleReq)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusCreated, resp.Code)

		// Parse response
		var puzzle models.Puzzle
		testutils.ParseResponseBody(t, resp, &puzzle)

		// Verify puzzle was created
		assert.NotEmpty(t, puzzle.ID)
		assert.Equal(t, game.ID, puzzle.GameID)
		assert.Equal(t, user.ID, puzzle.UserID)
		assert.Equal(t, 20, puzzle.GameMove)
		assert.Equal(t, "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1", puzzle.FEN)
		assert.Equal(t, pq.StringArray{"e2e4", "e7e5"}, puzzle.Moves)
		assert.Equal(t, -50, puzzle.PrevCP)
		assert.Equal(t, 150, puzzle.CP)
		assert.Equal(t, models.OpponentMistakeCaught, puzzle.Theme)
		assert.Equal(t, models.White, puzzle.UserColor)
		assert.Equal(t, models.Black, puzzle.PuzzleColor)
		assert.Equal(t, false, puzzle.Zugzwang)
		assert.Len(t, puzzle.Tags, 2)
		assert.Contains(t, puzzle.Tags, "opening")
		assert.Contains(t, puzzle.Tags, "tactics")
	})

	t.Run("CreatePuzzle_Idempotency", func(t *testing.T) {
		idempotencyKey := testutils.GenerateIdempotencyKey()
		userID := user.ID // Use the user/game from the outer scope
		gameID := game.ID

		// Get initial puzzle count for this user (using ListByUserID)
		_, initialPuzzleCount, err := puzzleRepo.ListByUserID(context.Background(), userID, repository.PuzzleFilter{}, 0, 1)
		require.NoError(t, err)

		// Create test puzzle request
		createReq := CreatePuzzleRequest{
			GameID:      gameID,
			UserID:      userID,
			GameMove:    25,
			FEN:         "rnbq1bnr/pp1ppppp/8/2p5/4P3/5N2/PPPP1PPP/RNBQKB1R w KQkq - 1 2", // Sicilian
			Moves:       pq.StringArray{"g1f3", "b8c6"},
			PrevCP:      10,
			CP:          -200,
			Theme:       models.OpponentMistakeCaught,
			UserColor:   models.Black,
			PuzzleColor: models.White,
			Zugzwang:    false,
			Tags:        []string{"sicilian", "idem-create"},
		}

		// --- First Request ---
		req1 := makeAdminRequest(t, http.MethodPost, "/", createReq)
		testutils.AddIdempotencyHeader(req1, idempotencyKey)
		resp1 := testutils.ExecuteRequest(t, router, req1)

		testutils.CheckResponseCode(t, http.StatusCreated, resp1.Code)
		var createdPuzzle1 models.Puzzle
		testutils.ParseResponseBody(t, resp1, &createdPuzzle1)
		require.NotEmpty(t, createdPuzzle1.ID)
		assert.Equal(t, userID, createdPuzzle1.UserID)
		assert.Equal(t, gameID, createdPuzzle1.GameID)

		// Verify DB state (count increased by 1)
		_, countAfterFirst, err := puzzleRepo.ListByUserID(context.Background(), userID, repository.PuzzleFilter{}, 0, 1)
		require.NoError(t, err)
		assert.Equal(t, initialPuzzleCount+1, countAfterFirst)

		// --- Second Request (Same Key, Same Body) ---
		req2 := makeAdminRequest(t, http.MethodPost, "/", createReq)
		testutils.AddIdempotencyHeader(req2, idempotencyKey)
		resp2 := testutils.ExecuteRequest(t, router, req2)

		testutils.CheckResponseCode(t, http.StatusCreated, resp2.Code)
		var createdPuzzle2 models.Puzzle
		testutils.ParseResponseBody(t, resp2, &createdPuzzle2)

		// Verify response bodies are identical (compare relevant fields)
		assert.Equal(t, createdPuzzle1.ID, createdPuzzle2.ID)
		assert.Equal(t, createdPuzzle1.UserID, createdPuzzle2.UserID)
		assert.Equal(t, createdPuzzle1.GameID, createdPuzzle2.GameID)
		assert.Equal(t, createdPuzzle1.FEN, createdPuzzle2.FEN)
		assert.Equal(t, createdPuzzle1.Moves, createdPuzzle2.Moves)
		assert.Equal(t, createdPuzzle1.Tags, createdPuzzle2.Tags)

		// Verify DB state hasn't changed (count is still +1)
		_, countAfterSecond, err := puzzleRepo.ListByUserID(context.Background(), userID, repository.PuzzleFilter{}, 0, 1)
		require.NoError(t, err)
		assert.Equal(t, countAfterFirst, countAfterSecond, "Puzzle count should not change on second idempotent call")
	})

	t.Run("ListPuzzlesByUser", func(t *testing.T) {
		newUser := testutils.CreateTestUser(t, userRepo)
		// Create some test puzzles for the specific user
		p1 := testutils.CreateTestPuzzle(t, puzzleRepo, game.ID, newUser.ID)
		p2 := testutils.CreateTestPuzzle(t, puzzleRepo, game.ID, newUser.ID)

		// Create puzzle for another user (should be excluded)
		otherUser := testutils.CreateTestUser(t, userRepo)
		otherGame := testutils.CreateTestGame(t, gameRepo, otherUser.ID)
		testutils.CreateTestPuzzle(t, puzzleRepo, otherGame.ID, otherUser.ID)

		// Make ADMIN request to list puzzles for the specific user
		req := makeAdminRequest(t, http.MethodGet, "/?user_id="+newUser.ID, nil)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)

		// Parse response (expecting map structure now)
		var respBody map[string]interface{}
		testutils.ParseResponseBody(t, resp, &respBody)

		// Extract puzzles from the response map
		puzzlesData, ok := respBody["puzzles"].([]interface{})
		require.True(t, ok, "Response should contain a 'puzzles' array")

		// Manually convert []interface{} to []models.Puzzle if needed for assertions
		var puzzles []models.Puzzle
		for _, item := range puzzlesData {
			itemBytes, err := json.Marshal(item)
			require.NoError(t, err)
			var puzzle models.Puzzle
			err = json.Unmarshal(itemBytes, &puzzle)
			require.NoError(t, err)
			puzzles = append(puzzles, puzzle)
		}

		// Verify only the correct user's puzzles were returned
		assert.Len(t, puzzles, 2, "Expected exactly 2 puzzles for the specified user")
		foundP1, foundP2 := false, false
		for _, puzzle := range puzzles {
			assert.Equal(t, newUser.ID, puzzle.UserID)
			if puzzle.ID == p1.ID {
				foundP1 = true
			}
			if puzzle.ID == p2.ID {
				foundP2 = true
			}
		}
		assert.True(t, foundP1, "Puzzle P1 not found in response")
		assert.True(t, foundP2, "Puzzle P2 not found in response")
		assert.EqualValues(t, 2, respBody["total_count"].(float64), "Total count should match filtered puzzles")
	})

	t.Run("ListPuzzlesByUser_TagFilter", func(t *testing.T) {
		// Create a puzzle with specific tag for the user
		puzzleWithTag := testutils.CreateTestPuzzle(t, puzzleRepo, game.ID, user.ID)
		customTags := []string{"endgame", "checkmate_filter"} // Use a unique tag
		puzzleWithTag.Tags = pq.StringArray(customTags)
		err := puzzleRepo.Update(context.Background(), puzzleWithTag)
		require.NoError(t, err)

		// Create another puzzle for the same user without the tag
		testutils.CreateTestPuzzle(t, puzzleRepo, game.ID, user.ID)

		// Make ADMIN request with user and tag filter
		url := fmt.Sprintf("/?user_id=%s&tags=%s", user.ID, "checkmate_filter")
		req := makeAdminRequest(t, http.MethodGet, url, nil)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)

		// Parse response (expecting map structure now)
		var respBody map[string]interface{}
		testutils.ParseResponseBody(t, resp, &respBody)

		// Extract puzzles from the response map
		puzzlesData, ok := respBody["puzzles"].([]interface{})
		require.True(t, ok, "Response should contain a 'puzzles' array")

		// Manually convert []interface{} to []models.Puzzle
		var puzzles []models.Puzzle
		for _, item := range puzzlesData {
			itemBytes, err := json.Marshal(item)
			require.NoError(t, err)
			var p models.Puzzle
			err = json.Unmarshal(itemBytes, &p)
			require.NoError(t, err)
			puzzles = append(puzzles, p)
		}

		// Verify only the filtered puzzle was returned
		require.Len(t, puzzles, 1, "Expected exactly one puzzle for the tag filter")
		assert.Equal(t, puzzleWithTag.ID, puzzles[0].ID)
		assert.Contains(t, puzzles[0].Tags, "checkmate_filter")
		assert.EqualValues(t, 1, respBody["total_count"].(float64), "Total count should be 1 for filtered puzzles")
	})

	t.Run("DeletePuzzle", func(t *testing.T) {
		// Create a puzzle to delete
		puzzle := testutils.CreateTestPuzzle(t, puzzleRepo, game.ID, user.ID)

		// Make ADMIN delete request
		req := makeAdminRequest(t, http.MethodDelete, "/"+puzzle.ID, nil)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)

		// Verify puzzle was deleted
		_, err := puzzleRepo.GetByID(context.Background(), puzzle.ID)
		assert.Error(t, err, "Expected error retrieving deleted puzzle")
	})

	t.Run("DeletePuzzle_NotFound", func(t *testing.T) {
		// Make ADMIN delete request with non-existent ID
		req := makeAdminRequest(t, http.MethodDelete, "/nonexistent", nil)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusNotFound, resp.Code)
	})

	t.Run("DeletePuzzle_Idempotency", func(t *testing.T) {
		// Setup: Create a puzzle to delete
		puzzleToDelete := testutils.CreateTestPuzzle(t, puzzleRepo, game.ID, user.ID)
		require.NotEmpty(t, puzzleToDelete.ID)
		puzzleID := puzzleToDelete.ID

		idempotencyKey := testutils.GenerateIdempotencyKey()
		deleteURL := "/" + puzzleID

		// --- First Request ---
		req1 := makeAdminRequest(t, http.MethodDelete, deleteURL, nil)
		testutils.AddIdempotencyHeader(req1, idempotencyKey)
		resp1 := testutils.ExecuteRequest(t, router, req1)

		testutils.CheckResponseCode(t, http.StatusOK, resp1.Code)
		// Parse the response body (map[string]string)
		var respBody1 map[string]string
		testutils.ParseResponseBody(t, resp1, &respBody1)
		assert.Equal(t, "puzzle deleted successfully", respBody1["message"])

		// Verify puzzle was deleted from DB
		_, err := puzzleRepo.GetByID(context.Background(), puzzleID)
		assert.Error(t, err) // Expect ErrRecordNotFound or similar

		// --- Second Request (Same Key) ---
		req2 := makeAdminRequest(t, http.MethodDelete, deleteURL, nil)
		testutils.AddIdempotencyHeader(req2, idempotencyKey)
		resp2 := testutils.ExecuteRequest(t, router, req2)

		testutils.CheckResponseCode(t, http.StatusOK, resp2.Code)
		// Parse the second response body
		var respBody2 map[string]string
		testutils.ParseResponseBody(t, resp2, &respBody2)

		// Verify response bodies are identical
		assert.Equal(t, respBody1, respBody2, "Idempotent response body mismatch")

		// Verify puzzle remains deleted from DB
		_, err = puzzleRepo.GetByID(context.Background(), puzzleID)
		assert.Error(t, err)
	})
}
