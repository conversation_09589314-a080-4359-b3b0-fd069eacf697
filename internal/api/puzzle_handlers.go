package api

import (
	"encoding/json"
	"errors"
	"net/http"
	"strings"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/go-chi/chi/v5"
	"github.com/go-chi/render"
	"github.com/lib/pq"
)

type PuzzleHandler struct {
	puzzleRepo repository.IPuzzleRepository
}

func NewPuzzleHandler(puzzleRepo repository.IPuzzleRepository) *PuzzleHandler {
	return &PuzzleHandler{
		puzzleRepo: puzzleRepo,
	}
}

// PuzzleRoutes creates a router for puzzle endpoints
func PuzzleRoutes(puzzleRepo repository.IPuzzleRepository) http.Handler {
	h := NewPuzzleHandler(puzzleRepo)
	r := chi.NewRouter()

	r.Post("/", h.CreatePuzzle)
	r.Get("/", h.ListPuzzlesByUser)
	r.Delete("/{id}", h.DeletePuzzle)

	return r
}

type CreatePuzzleRequest struct {
	GameID      string             `json:"game_id"`
	UserID      string             `json:"user_id"`
	GameMove    int                `json:"game_move"`
	FEN         string             `json:"fen"`
	Moves       pq.StringArray     `json:"moves"`
	PrevCP      int                `json:"prev_cp"`
	CP          int                `json:"cp"`
	Theme       models.PuzzleTheme `json:"theme"`
	UserColor   models.Color       `json:"user_color"`
	PuzzleColor models.Color       `json:"puzzle_color"`
	Zugzwang    bool               `json:"zugzwang"`
	Tags        []string           `json:"tags"`
}

// CreatePuzzle handles creating a new puzzle
func (h *PuzzleHandler) CreatePuzzle(w http.ResponseWriter, r *http.Request) {
	var req CreatePuzzleRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		apiError(w, r, http.StatusBadRequest, err, "Invalid request body")
		return
	}

	// Validate request
	if req.GameID == "" || req.UserID == "" || req.FEN == "" || len(req.Moves) == 0 {
		apiError(w, r, http.StatusBadRequest, nil, "game_id, user_id, fen, and moves are required")
		return
	}

	// Create puzzle model
	puzzle := &models.Puzzle{
		GameID:      req.GameID,
		UserID:      req.UserID,
		GameMove:    req.GameMove,
		FEN:         req.FEN,
		Moves:       req.Moves,
		PrevCP:      req.PrevCP,
		CP:          req.CP,
		Theme:       req.Theme,
		UserColor:   req.UserColor,
		PuzzleColor: req.PuzzleColor,
		Zugzwang:    req.Zugzwang,
		Tags:        pq.StringArray(req.Tags),
	}

	// Save to database
	if err := h.puzzleRepo.Create(r.Context(), puzzle); err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to create puzzle")
		return
	}

	render.Status(r, http.StatusCreated)
	render.JSON(w, r, puzzle)
}

// ListPuzzlesByUser handles listing puzzles for a user with pagination and filtering
func (h *PuzzleHandler) ListPuzzlesByUser(w http.ResponseWriter, r *http.Request) {
	userID := r.URL.Query().Get("user_id")
	if userID == "" {
		apiError(w, r, http.StatusBadRequest, nil, "user_id query parameter is required")
		return
	}

	// Parse pagination parameters
	offset, limit, err := parsePaginationParams(r) // Use shared helper
	if err != nil {
		apiError(w, r, http.StatusBadRequest, nil, err.Error()) // err here is just a message string
		return
	}

	// Parse filter parameters
	var puzzleFilter repository.PuzzleFilter
	if tags := r.URL.Query().Get("tags"); tags != "" {
		puzzleFilter.Tags = strings.Split(tags, ",")
	}
	gameStartTime, err := parseTimeParam(r, "game_start_time") // Use shared helper
	if err != nil {
		apiError(w, r, http.StatusBadRequest, nil, err.Error()) // err here is just a message string
		return
	}
	puzzleFilter.GameStartTime = gameStartTime

	gameEndTime, err := parseTimeParam(r, "game_end_time") // Use shared helper
	if err != nil {
		apiError(w, r, http.StatusBadRequest, nil, err.Error()) // err here is just a message string
		return
	}
	puzzleFilter.GameEndTime = gameEndTime

	// Fetch puzzles using the repository's ListByUserID method
	puzzles, totalCount, err := h.puzzleRepo.ListByUserID(r.Context(), userID, puzzleFilter, offset, limit)
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to retrieve puzzles")
		return
	}

	// Build structured response
	response := map[string]interface{}{
		"puzzles":     puzzles,
		"total_count": totalCount,
		"offset":      offset,
		"limit":       limit,
	}

	render.JSON(w, r, response)
}

// DeletePuzzle handles deleting a puzzle by ID
func (h *PuzzleHandler) DeletePuzzle(w http.ResponseWriter, r *http.Request) {
	puzzleID := chi.URLParam(r, "id")
	if puzzleID == "" {
		apiError(w, r, http.StatusBadRequest, nil, "Puzzle ID is required")
		return
	}

	// Check if puzzle exists first
	_, err := h.puzzleRepo.GetByID(r.Context(), puzzleID)
	if err != nil {
		if errors.Is(err, repository.ErrNotFound) {
			apiError(w, r, http.StatusNotFound, nil, "Puzzle not found")
		} else {
			apiError(w, r, http.StatusInternalServerError, err, "Error checking puzzle existence")
		}
		return
	}

	// Delete the puzzle
	if err := h.puzzleRepo.Delete(r.Context(), puzzleID); err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to delete puzzle")
		return
	}

	render.Status(r, http.StatusOK)
	render.JSON(w, r, map[string]string{"message": "puzzle deleted successfully"})
}
