package api

import (
	"context"
	"encoding/json"
	"net/http"
	"testing"

	"github.com/chessticize/chessticize-server/internal/api/testutils"
	"github.com/chessticize/chessticize-server/internal/config"
	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/repository/fake"
	"github.com/chessticize/chessticize-server/internal/service"
	"github.com/go-chi/chi/v5"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func setupSignInEventTest(t *testing.T) (repository.IUserRepository, repository.IEventRepository, *service.AuthService, http.Handler) {
	// Create a fake DB for testing
	db := fake.NewDB(t)

	// Create repositories
	userRepo := fake.NewUserRepository(db)
	invitationCodeRepo := fake.NewFakeInvitationCodeRepository(db.DB)
	sessionTokenRepo := fake.NewFakeSessionTokenRepository(db.DB)
	eventRepo := fake.NewEventRepository(db.DB)

	// Create JWT config and session token config
	jwtConfig := testutils.GetTestJWTConfig()
	sessionTokenConfig := config.SessionTokenConfig{
		ExpiryDays: 30,
	}
	authService := service.NewAuthServiceWithSessionTokens(jwtConfig, sessionTokenConfig, sessionTokenRepo)
	eventService := service.NewEventService(eventRepo)

	// Create test config
	testConfig := &config.Config{
		JWT:          jwtConfig,
		SessionToken: sessionTokenConfig,
	}

	// Create the router
	r := chi.NewRouter()
	r.Route("/api/v1", func(r chi.Router) {
		firebaseAuthService := service.NewFirebaseAuthService(config.FirebaseConfig{
			ProjectID:    "demo-project",
			JWKSEndpoint: "http://localhost:9099/.well-known/jwks.json",
		})
		r.Mount("/auth", AuthRoutes(userRepo, invitationCodeRepo, authService, eventService, firebaseAuthService, testConfig))
	})

	return userRepo, eventRepo, authService, r
}

func TestSignInEvents(t *testing.T) {
	userRepo, eventRepo, authService, router := setupSignInEventTest(t)

	t.Run("PasswordLogin_Success_CreatesSuccessEvent", func(t *testing.T) {
		// Create a test user for this specific test
		testUser := testutils.CreateTestUser(t, userRepo)
		// Make login request with password
		loginReq := LoginRequest{
			Email:    testUser.Email,
			Password: "password", // Default password from testutils
		}

		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/login", loginReq)
		req.Header.Set("User-Agent", "test-browser/1.0")
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)

		// Verify event was created
		events, _, err := eventRepo.ListByUserID(context.TODO(), testUser.ID, repository.EventFilter{
			EventTypes: []models.EventType{models.EventTypeSignIn},
		}, 0, 10)
		require.NoError(t, err)
		require.Len(t, events, 1)

		event := events[0]
		assert.Equal(t, models.EventTypeSignIn, event.EventType)
		assert.Equal(t, models.EventSubTypeSuccess, *event.EventSubType)

		// Parse event data
		var eventData models.SignInEventData
		err = json.Unmarshal(event.EventData, &eventData)
		require.NoError(t, err)

		assert.True(t, eventData.Succeeded)
		assert.Equal(t, "password", eventData.SignInType)
		assert.Equal(t, "test-browser/1.0", eventData.UserAgent)
	})

	t.Run("PasswordLogin_Failure_CreatesFailureEvent", func(t *testing.T) {
		// Create a test user for this specific test
		testUser := testutils.CreateTestUser(t, userRepo)

		// Make login request with wrong password
		loginReq := LoginRequest{
			Email:    testUser.Email,
			Password: "wrongpassword",
		}

		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/login", loginReq)
		req.Header.Set("User-Agent", "test-browser/1.0")
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusUnauthorized, resp.Code)

		// Verify failure event was created
		events, _, err := eventRepo.ListByUserID(context.TODO(), testUser.ID, repository.EventFilter{
			EventTypes:    []models.EventType{models.EventTypeSignIn},
			EventSubTypes: []models.EventSubType{models.EventSubTypeFailure},
		}, 0, 10)
		require.NoError(t, err)
		require.Len(t, events, 1)

		event := events[0]
		assert.Equal(t, models.EventTypeSignIn, event.EventType)
		assert.Equal(t, models.EventSubTypeFailure, *event.EventSubType)

		// Parse event data
		var eventData models.SignInEventData
		err = json.Unmarshal(event.EventData, &eventData)
		require.NoError(t, err)

		assert.False(t, eventData.Succeeded)
		assert.Equal(t, "password", eventData.SignInType)
		assert.Equal(t, "test-browser/1.0", eventData.UserAgent)
	})

	t.Run("SessionTokenLogin_Success_CreatesSuccessEvent", func(t *testing.T) {
		// Create a test user for this specific test
		testUser := testutils.CreateTestUser(t, userRepo)

		// First, generate a session token by logging in with password
		sessionToken, err := authService.GenerateSessionToken(context.TODO(), testUser.ID, "test-browser/1.0")
		require.NoError(t, err)

		// Clear existing events
		// Note: In a real scenario, we'd have a way to clear events, but for testing we'll just count them

		// Get initial event count
		initialEvents, _, err := eventRepo.ListByUserID(context.TODO(), testUser.ID, repository.EventFilter{
			EventTypes: []models.EventType{models.EventTypeSignIn},
		}, 0, 100)
		require.NoError(t, err)
		initialCount := len(initialEvents)

		// Make login request with session token
		loginReq := LoginRequest{
			SessionToken: sessionToken.Token,
		}

		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/login", loginReq)
		req.Header.Set("User-Agent", "test-browser/2.0")
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)

		// Verify new event was created
		allEvents, _, err := eventRepo.ListByUserID(context.TODO(), testUser.ID, repository.EventFilter{
			EventTypes: []models.EventType{models.EventTypeSignIn},
		}, 0, 100)
		require.NoError(t, err)
		require.Len(t, allEvents, initialCount+1)

		// Find the latest event (session token login)
		var latestEvent *models.Event
		for i := range allEvents {
			if latestEvent == nil || allEvents[i].CreatedAt.After(latestEvent.CreatedAt) {
				latestEvent = &allEvents[i]
			}
		}
		require.NotNil(t, latestEvent)

		assert.Equal(t, models.EventTypeSignIn, latestEvent.EventType)
		assert.Equal(t, models.EventSubTypeSuccess, *latestEvent.EventSubType)

		// Parse event data
		var eventData models.SignInEventData
		err = json.Unmarshal(latestEvent.EventData, &eventData)
		require.NoError(t, err)

		assert.True(t, eventData.Succeeded)
		assert.Equal(t, "session_token", eventData.SignInType)
		assert.Equal(t, "test-browser/2.0", eventData.UserAgent)
	})

	t.Run("NonExistentUser_NoEvent", func(t *testing.T) {
		// Make login request with non-existent email
		loginReq := LoginRequest{
			Email:    "<EMAIL>",
			Password: "password123",
		}

		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/login", loginReq)
		req.Header.Set("User-Agent", "test-browser/1.0")
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusUnauthorized, resp.Code)

		// Since we don't have a user ID for non-existent users, no event should be created
		// This is expected behavior as mentioned in the implementation
	})
}

func TestRegistrationSignInEvents(t *testing.T) {
	userRepo, eventRepo, _, router := setupSignInEventTest(t)

	t.Run("Register_CreatesSuccessEvent", func(t *testing.T) {
		// Make registration request
		registerReq := RegisterRequest{
			Email:    testutils.RandomEmail(),
			Password: "testpassword123",
		}

		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/register", registerReq)
		req.Header.Set("User-Agent", "test-browser/1.0")
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusCreated, resp.Code)

		// Parse response to get user ID
		var registerResp RegisterResponse
		testutils.ParseResponseBody(t, resp, &registerResp)

		// Verify sign-in success event was created
		events, _, err := eventRepo.ListByUserID(context.TODO(), registerResp.ID, repository.EventFilter{
			EventTypes:    []models.EventType{models.EventTypeSignIn},
			EventSubTypes: []models.EventSubType{models.EventSubTypeSuccess},
		}, 0, 10)
		require.NoError(t, err)
		require.Len(t, events, 1)

		event := events[0]
		assert.Equal(t, models.EventTypeSignIn, event.EventType)
		assert.Equal(t, models.EventSubTypeSuccess, *event.EventSubType)

		// Parse event data
		var eventData models.SignInEventData
		err = json.Unmarshal(event.EventData, &eventData)
		require.NoError(t, err)

		assert.True(t, eventData.Succeeded)
		assert.Equal(t, "password", eventData.SignInType)
		assert.Equal(t, "test-browser/1.0", eventData.UserAgent)
	})

	t.Run("RegisterWithInvitation_CreatesSuccessEvent", func(t *testing.T) {
		// Create an invitation code first
		invitationCodeRepo := fake.NewFakeInvitationCodeRepository(fake.NewDB(t).DB)
		invitationCode := &models.InvitationCode{
			Code:            "test-invitation-code-" + testutils.RandomString(8),
			CreatedByUserID: "admin-user-id",
		}
		err := invitationCodeRepo.Create(context.TODO(), invitationCode)
		require.NoError(t, err)

		// Make registration request
		registerReq := RegisterWithInvitationRequest{
			Email:          testutils.RandomEmail(),
			Password:       "testpassword123",
			InvitationCode: invitationCode.Code,
		}

		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/register-with-invitation", registerReq)
		req.Header.Set("User-Agent", "test-browser/1.0")
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusCreated, resp.Code)

		// Parse response to get user ID
		var registerResp RegisterResponse
		testutils.ParseResponseBody(t, resp, &registerResp)

		// Verify sign-in success event was created
		events, _, err := eventRepo.ListByUserID(context.TODO(), registerResp.ID, repository.EventFilter{
			EventTypes:    []models.EventType{models.EventTypeSignIn},
			EventSubTypes: []models.EventSubType{models.EventSubTypeSuccess},
		}, 0, 10)
		require.NoError(t, err)
		require.Len(t, events, 1)

		event := events[0]
		assert.Equal(t, models.EventTypeSignIn, event.EventType)
		assert.Equal(t, models.EventSubTypeSuccess, *event.EventSubType)

		// Parse event data
		var eventData models.SignInEventData
		err = json.Unmarshal(event.EventData, &eventData)
		require.NoError(t, err)

		assert.True(t, eventData.Succeeded)
		assert.Equal(t, "password", eventData.SignInType)
		assert.Equal(t, "test-browser/1.0", eventData.UserAgent)
	})

	t.Run("AdminRegister_CreatesSuccessEvent", func(t *testing.T) {
		// Create an admin user and token for testing the protected endpoint
		adminUser := testutils.CreateTestUser(t, userRepo)
		adminToken := testutils.GenerateAdminToken(t, adminUser.ID, adminUser.Email)

		// Make admin registration request
		registerReq := RegisterRequest{
			Email:    testutils.RandomEmail(),
			Password: "testpassword123",
		}

		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/admin/register", registerReq)
		req.Header.Set("Authorization", "Bearer "+adminToken)
		req.Header.Set("User-Agent", "test-browser/1.0")
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusCreated, resp.Code)

		// Parse response to get user ID
		var registerResp RegisterResponse
		testutils.ParseResponseBody(t, resp, &registerResp)

		// Verify sign-in success event was created
		events, _, err := eventRepo.ListByUserID(context.TODO(), registerResp.ID, repository.EventFilter{
			EventTypes:    []models.EventType{models.EventTypeSignIn},
			EventSubTypes: []models.EventSubType{models.EventSubTypeSuccess},
		}, 0, 10)
		require.NoError(t, err)
		require.Len(t, events, 1)

		event := events[0]
		assert.Equal(t, models.EventTypeSignIn, event.EventType)
		assert.Equal(t, models.EventSubTypeSuccess, *event.EventSubType)

		// Parse event data
		var eventData models.SignInEventData
		err = json.Unmarshal(event.EventData, &eventData)
		require.NoError(t, err)

		assert.True(t, eventData.Succeeded)
		assert.Equal(t, "password", eventData.SignInType)
		assert.Equal(t, "test-browser/1.0", eventData.UserAgent)
	})
}
