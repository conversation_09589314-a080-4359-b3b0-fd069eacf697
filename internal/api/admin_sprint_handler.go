package api

import (
	"net/http"
	"strconv"

	"github.com/chessticize/chessticize-server/internal/service"
	"github.com/go-chi/chi/v5"
	"github.com/go-chi/render"
)

type AdminSprintHandler struct {
	sprintService service.ISprintService
}

func NewAdminSprintHandler(sprintService service.ISprintService) *AdminSprintHandler {
	return &AdminSprintHandler{
		sprintService: sprintService,
	}
}

// AdminSprintRoutes creates a router for admin sprint endpoints
func AdminSprintRoutes(sprintService service.ISprintService) http.Handler {
	h := NewAdminSprintHandler(sprintService)
	r := chi.NewRouter()

	r.Post("/cleanup", h.CleanupAbandonedSprints)

	return r
}

// API Response Models

type CleanupSprintsResponse struct {
	AbandonedCount int64  `json:"abandoned_count"`
	Message        string `json:"message"`
}

// Handler Methods

// CleanupAbandonedSprints handles POST /api/v1/admin/sprints/cleanup
// Query parameters:
// - grace_minutes: grace period in minutes before marking sprints as abandoned (default: 5)
// - limit: maximum number of sprints to process in one call (default: 1000)
func (h *AdminSprintHandler) CleanupAbandonedSprints(w http.ResponseWriter, r *http.Request) {
	// Parse query parameters
	graceMinutesStr := r.URL.Query().Get("grace_minutes")
	graceMinutes := 5 // default
	if graceMinutesStr != "" {
		if parsedGrace, err := strconv.Atoi(graceMinutesStr); err == nil && parsedGrace > 0 && parsedGrace <= 60 {
			graceMinutes = parsedGrace
		} else {
			apiError(w, r, http.StatusBadRequest, nil, "Invalid grace_minutes parameter. Must be between 1 and 60")
			return
		}
	}

	limitStr := r.URL.Query().Get("limit")
	limit := 1000 // default
	if limitStr != "" {
		if parsedLimit, err := strconv.Atoi(limitStr); err == nil && parsedLimit > 0 && parsedLimit <= 10000 {
			limit = parsedLimit
		} else {
			apiError(w, r, http.StatusBadRequest, nil, "Invalid limit parameter. Must be between 1 and 10000")
			return
		}
	}

	// Cleanup abandoned sprints
	abandonedCount, err := h.sprintService.CleanupAbandonedSprints(r.Context(), graceMinutes, limit)
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to cleanup abandoned sprints")
		return
	}

	resp := CleanupSprintsResponse{
		AbandonedCount: abandonedCount,
		Message:        "Successfully marked " + strconv.FormatInt(abandonedCount, 10) + " sprints as abandoned",
	}

	render.JSON(w, r, resp)
}
