package api

import (
	"context"
	"net/http"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/api/testutils"
	"github.com/chessticize/chessticize-server/internal/config"
	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/service"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"golang.org/x/crypto/bcrypt"
)

func setupSessionTokenTest(t *testing.T) (repository.IUserRepository, repository.ISessionTokenRepository, *service.AuthService, config.JWTConfig, http.Handler) {
	// Get fake repositories
	userRepo, gameRepo, puzzleRepo := testutils.CreateRepositories(t)
	taskRepo := repository.NewTaskRepository(testutils.TestDB(t))
	idempotencyRepo := repository.NewIdempotencyRepository(testutils.TestDB(t))
	invitationCodeRepo := repository.NewInvitationCodeRepository(testutils.TestDB(t))
	sessionTokenRepo := repository.NewSessionTokenRepository(testutils.TestDB(t))
	eventRepo := repository.NewEventRepository(testutils.TestDB(t))

	// Create JWT config
	jwtConfig := testutils.GetTestJWTConfig()
	sessionTokenConfig := config.SessionTokenConfig{ExpiryDays: 30}
	authService := service.NewAuthServiceWithSessionTokens(jwtConfig, sessionTokenConfig, sessionTokenRepo)
	eventService := service.NewEventService(eventRepo)

	// Setup the full router using dependencies
	cfg := &config.Config{
		JWT:          jwtConfig,
		SessionToken: sessionTokenConfig,
	}
	deps := &RouterDependencies{
		UserRepo:           userRepo,
		GameRepo:           gameRepo,
		PuzzleRepo:         puzzleRepo,
		TaskRepo:           taskRepo,
		IdempotencyRepo:    idempotencyRepo,
		InvitationCodeRepo: invitationCodeRepo,
		SessionTokenRepo:   sessionTokenRepo,
		EventRepo:          eventRepo,
		AuthService:        authService,
		EventService:       eventService,
		Config:             cfg,
	}
	router := setupRouter(deps)

	return userRepo, sessionTokenRepo, authService, jwtConfig, router
}

func TestLoginWithSessionToken(t *testing.T) {
	userRepo, sessionTokenRepo, authService, _, router := setupSessionTokenTest(t)

	// Create a test user
	email := testutils.RandomEmail()
	password := "testpassword123"
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	require.NoError(t, err)

	user := testutils.CreateUserWithPassword(t, userRepo, email, string(hashedPassword))

	t.Run("Login_EmailPassword_ReturnsSessionToken", func(t *testing.T) {
		loginReq := LoginRequest{
			Email:    email,
			Password: password,
		}

		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/login", loginReq)
		req.Header.Set("User-Agent", "Test Browser 1.0")
		resp := testutils.ExecuteRequest(t, router, req)

		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)

		var authResp AuthResponse
		testutils.ParseResponseBody(t, resp, &authResp)

		assert.NotEmpty(t, authResp.Token)
		assert.NotEmpty(t, authResp.SessionToken)

		// Verify session token was created in database (need to hash the token to find it)
		// Since we can't directly search by the raw token, we'll validate through the auth service
		_, err = authService.ValidateSessionToken(context.Background(), authResp.SessionToken)
		require.NoError(t, err)
	})

	t.Run("Login_SessionToken_Success", func(t *testing.T) {
		// First, create a session token
		sessionToken, err := authService.GenerateSessionToken(context.Background(), user.ID, "Test Browser 2.0")
		require.NoError(t, err)

		loginReq := LoginRequest{
			SessionToken: sessionToken.Token,
		}

		req2 := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/login", loginReq)
		resp := testutils.ExecuteRequest(t, router, req2)

		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)

		var authResp AuthResponse
		testutils.ParseResponseBody(t, resp, &authResp)

		assert.NotEmpty(t, authResp.Token)
		assert.Empty(t, authResp.SessionToken) // No new session token for session token login

		// Verify session token is still valid (we can't easily check expiration extension
		// without accessing the database directly, but we can verify it's still valid)
		_, err = authService.ValidateSessionToken(context.Background(), sessionToken.Token)
		require.NoError(t, err)
	})

	t.Run("Login_InvalidSessionToken", func(t *testing.T) {
		loginReq := LoginRequest{
			SessionToken: "invalid-token",
		}

		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/login", loginReq)
		resp := testutils.ExecuteRequest(t, router, req)

		testutils.CheckResponseCode(t, http.StatusUnauthorized, resp.Code)
	})

	t.Run("Login_ExpiredSessionToken", func(t *testing.T) {
		// Create an expired session token
		expiredToken := &models.SessionToken{
			UserID:    user.ID,
			Token:     "expired-token-123",
			UserAgent: "Test Browser",
			ExpiresAt: time.Now().Add(-1 * time.Hour),
		}
		err := sessionTokenRepo.Create(context.Background(), expiredToken)
		require.NoError(t, err)

		loginReq := LoginRequest{
			SessionToken: expiredToken.Token,
		}

		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/login", loginReq)
		resp := testutils.ExecuteRequest(t, router, req)

		testutils.CheckResponseCode(t, http.StatusUnauthorized, resp.Code)
	})

	t.Run("Login_MissingBothCredentials", func(t *testing.T) {
		loginReq := LoginRequest{
			// No email/password and no session token
		}

		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/login", loginReq)
		resp := testutils.ExecuteRequest(t, router, req)

		testutils.CheckResponseCode(t, http.StatusBadRequest, resp.Code)
	})
}

func TestRegisterWithSessionToken(t *testing.T) {
	userRepo, _, authService, _, router := setupSessionTokenTest(t)

	t.Run("Register_ReturnsSessionToken", func(t *testing.T) {
		registerReq := RegisterRequest{
			Email:    testutils.RandomEmail(),
			Password: "testpassword123",
		}

		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/register", registerReq)
		req.Header.Set("User-Agent", "Test Browser 1.0")
		resp := testutils.ExecuteRequest(t, router, req)

		testutils.CheckResponseCode(t, http.StatusCreated, resp.Code)

		var registerResp RegisterResponse
		testutils.ParseResponseBody(t, resp, &registerResp)

		assert.NotEmpty(t, registerResp.Token)
		assert.NotEmpty(t, registerResp.SessionToken)
		assert.NotEmpty(t, registerResp.ID)
		assert.Equal(t, registerReq.Email, registerResp.Email)

		// Verify user was created
		user, err := userRepo.GetByEmail(context.Background(), registerReq.Email)
		require.NoError(t, err)
		assert.Equal(t, registerResp.ID, user.ID)

		// Verify session token was created in database (validate through auth service)
		_, err = authService.ValidateSessionToken(context.Background(), registerResp.SessionToken)
		require.NoError(t, err)
	})
}

func TestSessionTokenManagement(t *testing.T) {
	userRepo, sessionTokenRepo, authService, _, router := setupSessionTokenTest(t)

	// Create a test user
	user := testutils.CreateTestUser(t, userRepo)

	// Create some session tokens
	token1, err := authService.GenerateSessionToken(context.Background(), user.ID, "Browser 1")
	require.NoError(t, err)
	token2, err := authService.GenerateSessionToken(context.Background(), user.ID, "Browser 2")
	require.NoError(t, err)

	t.Run("ListSessionTokens_Success", func(t *testing.T) {
		req := testutils.MakeAuthenticatedRequest(t, http.MethodGet, "/api/v1/auth/session-tokens", user.ID, user.Email, nil)
		resp := testutils.ExecuteRequest(t, router, req)

		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)

		var tokens []models.SessionTokenResponse
		testutils.ParseResponseBody(t, resp, &tokens)

		assert.Len(t, tokens, 2)
		assert.Contains(t, []string{token1.ID, token2.ID}, tokens[0].ID)
		assert.Contains(t, []string{token1.ID, token2.ID}, tokens[1].ID)
	})

	t.Run("RevokeSessionToken_Success", func(t *testing.T) {
		req := testutils.MakeAuthenticatedRequest(t, http.MethodDelete, "/api/v1/auth/session-tokens/"+token1.ID, user.ID, user.Email, nil)
		resp := testutils.ExecuteRequest(t, router, req)

		testutils.CheckResponseCode(t, http.StatusNoContent, resp.Code)

		// Verify token was deleted
		_, err := sessionTokenRepo.GetByID(context.Background(), token1.ID)
		assert.Error(t, err)
	})

	t.Run("RevokeSessionToken_NotFound", func(t *testing.T) {
		req := testutils.MakeAuthenticatedRequest(t, http.MethodDelete, "/api/v1/auth/session-tokens/non-existent", user.ID, user.Email, nil)
		resp := testutils.ExecuteRequest(t, router, req)

		testutils.CheckResponseCode(t, http.StatusNotFound, resp.Code)
	})

	t.Run("RevokeSessionToken_Forbidden", func(t *testing.T) {
		// Create another user
		otherUser := testutils.CreateTestUser(t, userRepo)

		// Try to revoke token2 as the other user
		req := testutils.MakeAuthenticatedRequest(t, http.MethodDelete, "/api/v1/auth/session-tokens/"+token2.ID, otherUser.ID, otherUser.Email, nil)
		resp := testutils.ExecuteRequest(t, router, req)

		testutils.CheckResponseCode(t, http.StatusForbidden, resp.Code)
	})

	t.Run("ListSessionTokens_Unauthorized", func(t *testing.T) {
		req := testutils.MakeRequest(t, http.MethodGet, "/api/v1/auth/session-tokens", nil)
		resp := testutils.ExecuteRequest(t, router, req)

		testutils.CheckResponseCode(t, http.StatusUnauthorized, resp.Code)
	})

	t.Run("RevokeSessionToken_Unauthorized", func(t *testing.T) {
		req := testutils.MakeRequest(t, http.MethodDelete, "/api/v1/auth/session-tokens/"+token2.ID, nil)
		resp := testutils.ExecuteRequest(t, router, req)

		testutils.CheckResponseCode(t, http.StatusUnauthorized, resp.Code)
	})
}
