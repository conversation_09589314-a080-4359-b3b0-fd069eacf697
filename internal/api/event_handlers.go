package api

import (
	"net/http"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/go-chi/render"
)

type EventHandler struct {
	eventRepo repository.IEventRepository
}

func NewEventHandler(eventRepo repository.IEventRepository) *EventHandler {
	return &EventHandler{
		eventRepo: eventRepo,
	}
}

// ListMyEvents handles listing events for the authenticated user (for /users/me/events)
func (h *EventHandler) ListMyEvents(w http.ResponseWriter, r *http.Request) {
	// Get user ID from context
	userID, err := getUserIDFromContext(r)
	if err != nil {
		apiError(w, r, http.StatusUnauthorized, err, "Unauthorized: Invalid user context")
		return
	}

	// Parse pagination parameters
	offset, limit, err := parsePaginationParams(r)
	if err != nil {
		apiError(w, r, http.StatusBadRequest, nil, err.Error())
		return
	}

	// Parse filter parameters
	filter := parseEventFilterParams(r)

	// Get events with pagination and filtering
	events, totalCount, err := h.eventRepo.ListByUserID(r.Context(), userID, filter, offset, limit)
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to list events")
		return
	}

	// Build response with pagination metadata
	response := map[string]interface{}{
		"events":      events,
		"total_count": totalCount,
		"offset":      offset,
		"limit":       limit,
	}

	render.JSON(w, r, response)
}

// parseEventFilterParams parses event filter parameters from query string
func parseEventFilterParams(r *http.Request) repository.EventFilter {
	filter := repository.EventFilter{}

	// Parse event types
	if eventTypesStr := r.URL.Query().Get("event_types"); eventTypesStr != "" {
		// Split by comma and convert to EventType slice
		// For simplicity, we'll accept a single event type for now
		filter.EventTypes = []models.EventType{models.EventType(eventTypesStr)}
	}

	// Parse start time
	if startTimeStr := r.URL.Query().Get("start_time"); startTimeStr != "" {
		if startTime, err := time.Parse(time.RFC3339, startTimeStr); err == nil {
			filter.StartTime = &startTime
		}
	}

	// Parse end time
	if endTimeStr := r.URL.Query().Get("end_time"); endTimeStr != "" {
		if endTime, err := time.Parse(time.RFC3339, endTimeStr); err == nil {
			filter.EndTime = &endTime
		}
	}

	return filter
}
