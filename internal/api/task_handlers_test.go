package api

import (
	"context"
	"encoding/json"
	"net/http"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/api/testutils"
	"github.com/chessticize/chessticize-server/internal/config"
	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/repository/fake"
	"github.com/chessticize/chessticize-server/internal/service"

	// "github.com/go-chi/chi/v5" // No longer needed for manual router setup
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// setupTaskTest sets up the router with fake dependencies for task API tests.
func setupTaskTest(t *testing.T) (http.Handler, repository.ITaskRepository, repository.IUserRepository, *fake.DB) {
	// Setup fake DB and Repos
	fakeDB := testutils.GetFakeDB(t)
	userRepo, gameRepo, puzzleRepo := testutils.GetFakeRepositories(t) // Use standard helper
	taskRepo := fake.NewFakeTaskRepository(fakeDB)                     // Keep task repo separate for direct manipulation if needed
	idempotencyRepo := fake.NewFakeIdempotencyRepository(fakeDB)

	cfg := &config.Config{
		JWT: testutils.GetTestJWTConfig(),
	}
	authService := service.NewAuthService(cfg.JWT)

	deps := &RouterDependencies{
		UserRepo:        userRepo, // Pass the IUserRepository
		GameRepo:        gameRepo,
		PuzzleRepo:      puzzleRepo,
		TaskRepo:        taskRepo,
		IdempotencyRepo: idempotencyRepo,
		AuthService:     authService,
		Config:          cfg,
	}
	router := setupRouter(deps)

	return router, taskRepo, userRepo, fakeDB
}

// TestTaskAPI tests the CRUD endpoints for tasks under admin scope.
func TestTaskAPI(t *testing.T) {
	router, taskRepo, _, fakeDB := setupTaskTest(t)
	defer testutils.CleanupTestDB(t)

	// Helper to make admin requests
	makeAdminRequest := func(t *testing.T, method, path string, body interface{}) *http.Request {
		// Ensure the path starts with the correct admin prefix
		fullPath := "/api/v1/admin/tasks" + path
		return testutils.MakeAdminRequest(t, method, fullPath, body)
	}

	t.Run("CreateTask - Default ScheduledAt", func(t *testing.T) {
		// Create test request without scheduled_at
		reqData := map[string]interface{}{
			"user_id":   "user-123",
			"task_type": "FetchChessGames",
			"task_data": map[string]string{"chess_profile_id": "profile-456"},
		}

		req := makeAdminRequest(t, http.MethodPost, "/", reqData)
		resp := testutils.ExecuteRequest(t, router, req)

		// Verify status code and basic response structure
		testutils.CheckResponseCode(t, http.StatusCreated, resp.Code)
		var createdTask models.Task
		err := json.Unmarshal(resp.Body.Bytes(), &createdTask)
		require.NoError(t, err)
		assert.NotEmpty(t, createdTask.ID)
		assert.Equal(t, "user-123", createdTask.UserID)
		assert.Equal(t, models.FetchChessGamesTask, createdTask.TaskType)
		assert.WithinDuration(t, createdTask.CreatedAt, createdTask.ScheduledAt, time.Second, "ScheduledAt should default to CreatedAt")
	})

	t.Run("CreateTask - Specific ScheduledAt", func(t *testing.T) {
		scheduleTime := time.Now().Add(1 * time.Hour).UTC()
		reqData := map[string]interface{}{
			"user_id":      "user-789",
			"task_type":    "EvaluateChessGame",
			"task_data":    map[string]string{"game_id": "game-xyz"},
			"scheduled_at": scheduleTime.Format(time.RFC3339Nano),
		}

		req := makeAdminRequest(t, http.MethodPost, "/", reqData)
		resp := testutils.ExecuteRequest(t, router, req)

		testutils.CheckResponseCode(t, http.StatusCreated, resp.Code)
		var createdTask models.Task
		err := json.Unmarshal(resp.Body.Bytes(), &createdTask)
		require.NoError(t, err)
		assert.NotEmpty(t, createdTask.ID)
		assert.Equal(t, "user-789", createdTask.UserID)
		assert.Equal(t, models.EvaluateChessGameTask, createdTask.TaskType)
		assert.WithinDuration(t, scheduleTime, createdTask.ScheduledAt, time.Millisecond, "ScheduledAt should match the provided value")
		assert.True(t, createdTask.ScheduledAt.After(createdTask.CreatedAt), "ScheduledAt should be after CreatedAt when specified")
	})

	// Add a task for Get/Update tests
	testTask := &models.Task{
		UserID:   "user-test",
		TaskType: models.EvaluateChessGameTask,
		TaskData: []byte(`{"game_id":"game-abc"}`),
	}
	err := taskRepo.Create(context.Background(), testTask)
	require.NoError(t, err)
	require.NotEmpty(t, testTask.ID)

	// Update testTask status so it's not pending for ClaimNextTask test
	testTask.Status = models.TaskStatusCompleted
	err = taskRepo.Update(context.Background(), testTask, testTask.UpdatedAt)
	require.NoError(t, err, "Failed to update testTask status before subtests")

	t.Run("ListTasks", func(t *testing.T) {
		req := makeAdminRequest(t, http.MethodGet, "/?limit=10", nil)
		resp := testutils.ExecuteRequest(t, router, req)

		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)
		assert.Contains(t, resp.Body.String(), "tasks")
	})

	t.Run("GetTask", func(t *testing.T) {
		req := makeAdminRequest(t, http.MethodGet, "/"+testTask.ID, nil)
		resp := testutils.ExecuteRequest(t, router, req)

		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)
		var fetchedTask models.Task
		err = json.Unmarshal(resp.Body.Bytes(), &fetchedTask)
		require.NoError(t, err)
		assert.Equal(t, testTask.ID, fetchedTask.ID)
		assert.Equal(t, testTask.UserID, fetchedTask.UserID)
		assert.NotZero(t, fetchedTask.ScheduledAt, "Fetched task should have a ScheduledAt time")
	})

	t.Run("ClaimNextTask", func(t *testing.T) {
		// Here we must mark all other tasks as completed in the table before creating a new task
		err := fakeDB.Exec("UPDATE tasks SET status = 'completed'").Error
		require.NoError(t, err)

		// Ensure there's a pending task to claim (create another one)
		pendingTask := &models.Task{
			UserID:   "user-claim",
			TaskType: models.GenerateChessPuzzlesTask,
			TaskData: []byte(`{"game_id":"game-xyz"}`),
		}
		err = taskRepo.Create(context.Background(), pendingTask)
		require.NoError(t, err)

		reqData := map[string]string{
			"worker_id": "worker-claim-123",
		}

		req := makeAdminRequest(t, http.MethodPost, "/claim", reqData)
		resp := testutils.ExecuteRequest(t, router, req)

		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)
		var claimedTask models.Task
		err = json.Unmarshal(resp.Body.Bytes(), &claimedTask)
		require.NoError(t, err)
		assert.Equal(t, pendingTask.ID, claimedTask.ID)
		assert.Equal(t, models.TaskStatusInProgress, claimedTask.Status)
		assert.Equal(t, "worker-claim-123", claimedTask.WorkerID.String)
		assert.True(t, claimedTask.WorkerID.Valid)
		assert.True(t, claimedTask.PickedUpAt.Valid)
	})

	t.Run("UpdateTask", func(t *testing.T) {
		// Create a new task specifically for this update test
		updateTestTask := &models.Task{
			UserID:   "user-update-test",
			TaskType: models.FetchChessGamesTask,
			TaskData: []byte(`{"profile_id":"prof-update"}`),
			Status:   models.TaskStatusPending, // Start as pending
		}
		err := taskRepo.Create(context.Background(), updateTestTask)
		require.NoError(t, err)
		require.NotEmpty(t, updateTestTask.ID)

		reqData := map[string]interface{}{
			"status": models.TaskStatusFailed, // Update status to failed
			"error":  "Update test failure",   // Add an error message
		}

		req := makeAdminRequest(t, http.MethodPut, "/"+updateTestTask.ID, reqData)
		resp := testutils.ExecuteRequest(t, router, req)

		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)
		var updatedTask models.Task
		err = json.Unmarshal(resp.Body.Bytes(), &updatedTask)
		require.NoError(t, err)
		assert.Equal(t, updateTestTask.ID, updatedTask.ID)
		assert.Equal(t, models.TaskStatusFailed, updatedTask.Status)
		assert.True(t, updatedTask.Error.Valid)
		assert.Equal(t, "Update test failure", updatedTask.Error.String)
		assert.True(t, updatedTask.UpdatedAt.After(updateTestTask.UpdatedAt), "UpdatedAt should have been modified")
	})

	t.Run("UpdateTask_CannotUpdateCompleted", func(t *testing.T) {
		// Create a task and mark it as completed directly via repo
		completedTask := &models.Task{
			UserID:   "user-completed-test",
			TaskType: models.EvaluateChessGameTask,
			TaskData: []byte(`{"game_id":"game-completed"}`),
			Status:   models.TaskStatusPending,
		}
		err := taskRepo.Create(context.Background(), completedTask)
		require.NoError(t, err)
		require.NotEmpty(t, completedTask.ID)

		// Update status to completed using the repo (bypassing API logic for setup)
		completedTask.Status = models.TaskStatusCompleted
		err = taskRepo.Update(context.Background(), completedTask, completedTask.UpdatedAt) // Fake repo might ignore UpdatedAt
		require.NoError(t, err, "Failed to mark task as completed in repo for test setup")

		// Attempt to update the completed task via API
		reqData := map[string]interface{}{
			"status": models.TaskStatusPending,
			"error":  "Trying to update completed task",
		}

		req := makeAdminRequest(t, http.MethodPut, "/"+completedTask.ID, reqData)
		resp := testutils.ExecuteRequest(t, router, req)

		// Expect a client error (e.g., 400 Bad Request or 409 Conflict)
		testutils.CheckResponseCode(t, http.StatusBadRequest, resp.Code) // Assuming 400 is returned
		assert.Contains(t, resp.Body.String(), "Completed tasks cannot change status", "Expected error message about terminal state")
	})

	t.Run("CreateTask_Idempotency", func(t *testing.T) {
		idempotencyKey := testutils.GenerateIdempotencyKey()
		userID := "user-idempotent-create"

		// Create request body
		reqData := map[string]interface{}{
			"user_id":   userID,
			"task_type": models.FetchChessGamesTask,
			"task_data": map[string]string{"profile_id": "prof-idem-create"},
		}

		// --- First Request ---
		req1 := makeAdminRequest(t, http.MethodPost, "/", reqData)
		testutils.AddIdempotencyHeader(req1, idempotencyKey) // Add header
		resp1 := testutils.ExecuteRequest(t, router, req1)

		// Check response (201 Created)
		testutils.CheckResponseCode(t, http.StatusCreated, resp1.Code)
		var createdTask1 models.Task
		err := json.Unmarshal(resp1.Body.Bytes(), &createdTask1)
		require.NoError(t, err)
		require.NotEmpty(t, createdTask1.ID)
		assert.Equal(t, userID, createdTask1.UserID)

		// Verify DB state (1 task)
		tasks1, err := taskRepo.ListByUserID(context.Background(), userID, nil)
		require.NoError(t, err)
		assert.Len(t, tasks1, 1)
		require.Equal(t, createdTask1.ID, tasks1[0].ID)

		// --- Second Request (Same Key, Same Body) ---
		req2 := makeAdminRequest(t, http.MethodPost, "/", reqData)
		testutils.AddIdempotencyHeader(req2, idempotencyKey) // Use same header
		resp2 := testutils.ExecuteRequest(t, router, req2)

		// Check response (201 Created - cached response)
		testutils.CheckResponseCode(t, http.StatusCreated, resp2.Code)
		var createdTask2 models.Task
		err = json.Unmarshal(resp2.Body.Bytes(), &createdTask2)
		require.NoError(t, err)

		// Verify response bodies are identical
		assert.Equal(t, createdTask1, createdTask2)

		// Verify DB state hasn't changed (still 1 task)
		tasks2, err := taskRepo.ListByUserID(context.Background(), userID, nil)
		require.NoError(t, err)
		assert.Len(t, tasks2, 1)
		assert.Equal(t, tasks1[0].ID, tasks2[0].ID) // Ensure it's the same task
	})

	t.Run("UpdateTask_Idempotency", func(t *testing.T) {
		// Setup: Create a task directly via repo
		userID := "user-idempotent-update"
		taskToUpdate := &models.Task{
			UserID:   userID,
			TaskType: models.EvaluateChessGameTask,
			TaskData: []byte(`{"game_id":"game-idem-update"}`),
			Status:   models.TaskStatusPending,
		}
		err := taskRepo.Create(context.Background(), taskToUpdate)
		require.NoError(t, err)
		require.NotEmpty(t, taskToUpdate.ID)
		initialUpdatedAt := taskToUpdate.UpdatedAt // Store initial UpdatedAt

		idempotencyKey := testutils.GenerateIdempotencyKey()
		updateURL := "/" + taskToUpdate.ID

		// Create update request body
		reqData := map[string]interface{}{
			"status": models.TaskStatusInProgress,
			"error":  "", // Clear any previous error
		}

		// --- First Request ---
		req1 := makeAdminRequest(t, http.MethodPut, updateURL, reqData)
		testutils.AddIdempotencyHeader(req1, idempotencyKey) // Add header
		resp1 := testutils.ExecuteRequest(t, router, req1)

		// Check response (200 OK)
		testutils.CheckResponseCode(t, http.StatusOK, resp1.Code)
		var updatedTask1 models.Task
		err = json.Unmarshal(resp1.Body.Bytes(), &updatedTask1)
		require.NoError(t, err)
		assert.Equal(t, taskToUpdate.ID, updatedTask1.ID)
		assert.Equal(t, models.TaskStatusInProgress, updatedTask1.Status)
		assert.False(t, updatedTask1.Error.Valid) // Error should be cleared/null
		assert.True(t, updatedTask1.UpdatedAt.After(initialUpdatedAt), "UpdatedAt should have changed")

		// Verify DB state (task is InProgress)
		tasks1, err := taskRepo.ListByUserID(context.Background(), userID, nil)
		require.NoError(t, err)
		require.Len(t, tasks1, 1)
		assert.Equal(t, models.TaskStatusInProgress, tasks1[0].Status)
		firstUpdateAt := tasks1[0].UpdatedAt // Store UpdatedAt after first request

		// --- Second Request (Same Key, Same Body) ---
		req2 := makeAdminRequest(t, http.MethodPut, updateURL, reqData)
		testutils.AddIdempotencyHeader(req2, idempotencyKey) // Use same header
		resp2 := testutils.ExecuteRequest(t, router, req2)

		// Check response (200 OK - cached response)
		testutils.CheckResponseCode(t, http.StatusOK, resp2.Code)
		var updatedTask2 models.Task
		err = json.Unmarshal(resp2.Body.Bytes(), &updatedTask2)
		require.NoError(t, err)

		// Verify response bodies are identical
		assert.Equal(t, updatedTask1, updatedTask2)

		// Verify DB state hasn't changed (still InProgress, UpdatedAt unchanged)
		tasks2, err := taskRepo.ListByUserID(context.Background(), userID, nil)
		require.NoError(t, err)
		require.Len(t, tasks2, 1)
		assert.Equal(t, models.TaskStatusInProgress, tasks2[0].Status)
		assert.Equal(t, firstUpdateAt, tasks2[0].UpdatedAt, "UpdatedAt should NOT have changed on second request")
	})
}

func TestTaskHandler_CleanupOldTasks(t *testing.T) {
	router, _, _, _ := setupTaskTest(t)
	defer testutils.CleanupTestDB(t)

	// Helper to make admin requests
	makeAdminRequest := func(t *testing.T, method, path string, body interface{}) *http.Request {
		// Ensure the path starts with the correct admin prefix
		fullPath := "/api/v1/admin/tasks" + path
		return testutils.MakeAdminRequest(t, method, fullPath, body)
	}

	tests := []struct {
		name        string
		queryParams string
		wantStatus  int
		wantBody    string // Expected JSON string or substring
	}{
		{
			name:        "Success case (no tasks to delete)",
			queryParams: "?cutoff=2024-01-01T00:00:00Z&statuses=completed,failed&limit=100",
			wantStatus:  http.StatusOK,
			wantBody:    `{"deleted_count":0}`, // Fake DB is empty, so 0 deleted
		},
		{
			name:        "Missing cutoff",
			queryParams: "?statuses=completed",
			wantStatus:  http.StatusBadRequest,
			wantBody:    `{"error":"cutoff query parameter is required`, // Check for substring
		},
		{
			name:        "Invalid cutoff format",
			queryParams: "?cutoff=2024-01-01",
			wantStatus:  http.StatusBadRequest,
			wantBody:    `{"error":"Invalid cutoff query parameter format`, // Check for substring
		},
		{
			name:        "Invalid status value",
			queryParams: "?cutoff=2024-01-01T00:00:00Z&statuses=completed,invalid",
			wantStatus:  http.StatusBadRequest,
			wantBody:    `{"error":"Invalid status value in statuses parameter: invalid"}`,
		},
		{
			name:        "Invalid limit",
			queryParams: "?cutoff=2024-01-01T00:00:00Z&limit=-1",
			wantStatus:  http.StatusBadRequest,
			wantBody:    `{"error":"Invalid limit query parameter`, // Check for substring
		},
		// We can't easily test repository errors without mocking, skip that case
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Use the helper to create an admin request
			req := makeAdminRequest(t, http.MethodPost, "/cleanup"+tt.queryParams, nil)
			resp := testutils.ExecuteRequest(t, router, req)

			testutils.CheckResponseCode(t, tt.wantStatus, resp.Code)
			assert.Contains(t, resp.Body.String(), tt.wantBody, "Response body mismatch")
		})
	}
}

func TestTaskHandler_ResetHangingTasks(t *testing.T) {
	router, _, _, _ := setupTaskTest(t)
	defer testutils.CleanupTestDB(t)

	// Helper to make admin requests
	makeAdminRequest := func(t *testing.T, method, path string, body interface{}) *http.Request {
		// Ensure the path starts with the correct admin prefix
		fullPath := "/api/v1/admin/tasks" + path
		return testutils.MakeAdminRequest(t, method, fullPath, body)
	}

	tests := []struct {
		name        string
		queryParams string
		wantStatus  int
		wantBody    string // Expected JSON string or substring
	}{
		{
			name:        "Success case (no tasks to reset)",
			queryParams: "?timeout=1h30m",
			wantStatus:  http.StatusOK,
			wantBody:    `{"reset_count":0}`, // Fake DB is empty, so 0 reset
		},
		{
			name:        "Missing timeout",
			queryParams: "",
			wantStatus:  http.StatusBadRequest,
			wantBody:    `{"error":"timeout query parameter is required`, // Check for substring
		},
		{
			name:        "Invalid timeout format",
			queryParams: "?timeout=1hour",
			wantStatus:  http.StatusBadRequest,
			wantBody:    `{"error":"Invalid timeout query parameter format`, // Check for substring
		},
		{
			name:        "Non-positive timeout",
			queryParams: "?timeout=-5m",
			wantStatus:  http.StatusBadRequest,
			wantBody:    `{"error":"timeout duration must be positive"}`,
		},
		// We can't easily test repository errors without mocking, skip that case
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Use the helper to create an admin request
			req := makeAdminRequest(t, http.MethodPost, "/reset-hanging"+tt.queryParams, nil)
			resp := testutils.ExecuteRequest(t, router, req)

			testutils.CheckResponseCode(t, tt.wantStatus, resp.Code)
			assert.Contains(t, resp.Body.String(), tt.wantBody, "Response body mismatch")
		})
	}
}
