package api

import (
	"context"
	"net/http"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/api/testutils"
	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestInvitationCodeAPI(t *testing.T) {
	userRepo, invitationCodeRepo, router := setupInvitationTest(t)

	// Create an admin user and token
	adminUser := testutils.CreateTestUser(t, userRepo)
	adminToken := testutils.GenerateAdminToken(t, adminUser.ID, adminUser.Email)

	t.Run("CreateInvitationCode_Success", func(t *testing.T) {
		// Create request with no expiration
		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/invitation-codes", CreateInvitationCodeRequest{})
		req.Header.Set("Authorization", "Bearer "+adminToken)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusCreated, resp.Code)

		// Parse response
		var respData InvitationCodeResponse
		testutils.ParseResponseBody(t, resp, &respData)

		// Verify response
		assert.NotEmpty(t, respData.ID)
		assert.NotEmpty(t, respData.Code)
		assert.False(t, respData.CreatedAt.IsZero())
		assert.Nil(t, respData.ExpiresAt)
		assert.False(t, respData.Used)

		// Verify code was created in DB
		code, err := invitationCodeRepo.GetByID(context.TODO(), respData.ID)
		require.NoError(t, err)
		assert.Equal(t, respData.Code, code.Code)
		assert.Equal(t, adminUser.ID, code.CreatedByUserID)
	})

	t.Run("CreateInvitationCode_WithExpiration", func(t *testing.T) {
		// Create request with expiration
		expiresInHours := 24
		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/invitation-codes", CreateInvitationCodeRequest{
			ExpiresInHours: &expiresInHours,
		})
		req.Header.Set("Authorization", "Bearer "+adminToken)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusCreated, resp.Code)

		// Parse response
		var respData InvitationCodeResponse
		testutils.ParseResponseBody(t, resp, &respData)

		// Verify response
		assert.NotEmpty(t, respData.ID)
		assert.NotEmpty(t, respData.Code)
		assert.False(t, respData.CreatedAt.IsZero())
		assert.NotNil(t, respData.ExpiresAt)
		assert.False(t, respData.Used)

		// Verify expiration time is approximately 24 hours from now
		expectedExpiry := time.Now().Add(24 * time.Hour)
		assert.WithinDuration(t, expectedExpiry, *respData.ExpiresAt, 5*time.Second)
	})

	t.Run("ListInvitationCodes_Success", func(t *testing.T) {
		// Create a few invitation codes first
		for i := 0; i < 3; i++ {
			code := &models.InvitationCode{
				Code:            "list-test-code-" + testutils.RandomString(8),
				CreatedByUserID: adminUser.ID,
			}
			err := invitationCodeRepo.Create(context.TODO(), code)
			require.NoError(t, err)
		}

		// Make request
		req := testutils.MakeRequest(t, http.MethodGet, "/api/v1/auth/invitation-codes", nil)
		req.Header.Set("Authorization", "Bearer "+adminToken)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)

		// Parse response
		var respData struct {
			Codes      []InvitationCodeResponse `json:"codes"`
			TotalCount int64                    `json:"total_count"`
		}
		testutils.ParseResponseBody(t, resp, &respData)

		// Verify response
		assert.GreaterOrEqual(t, respData.TotalCount, int64(3))
		assert.GreaterOrEqual(t, len(respData.Codes), 3)
	})
}
