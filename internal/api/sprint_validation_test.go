package api

import (
	"testing"

	"github.com/chessticize/chessticize-server/internal/service"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestEloTypeValidation_NewDurations tests that the new duration ranges work correctly
func TestEloTypeValidation_NewDurations(t *testing.T) {
	eloTypeService := service.NewEloTypeService()

	tests := []struct {
		name    string
		eloType string
		wantErr bool
		errMsg  string
	}{
		// Valid cases with new durations
		{
			name:    "valid minimum duration",
			eloType: "mixed 1/15",
			wantErr: false,
		},
		{
			name:    "valid maximum duration",
			eloType: "mixed 30/60",
			wantErr: false,
		},
		{
			name:    "valid mid-range duration",
			eloType: "fork 15/20",
			wantErr: false,
		},
		{
			name:    "valid with 15 second per-puzzle time",
			eloType: "sacrifice 5/15",
			wantErr: false,
		},
		// Invalid cases that should return 400
		{
			name:    "invalid duration - too high",
			eloType: "mixed 31/30",
			wantErr: true,
			errMsg:  "invalid duration '31': must be between 1 and 30 minutes",
		},
		{
			name:    "invalid duration - zero",
			eloType: "mixed 0/30",
			wantErr: true,
			errMsg:  "invalid duration '0': must be between 1 and 30 minutes",
		},
		{
			name:    "invalid per-puzzle time",
			eloType: "mixed 10/25",
			wantErr: true,
			errMsg:  "invalid per-puzzle time '25': must be one of 5, 10, 15, 20, 30, or 60 seconds",
		},
		{
			name:    "invalid theme",
			eloType: "invalidtheme 10/30",
			wantErr: true,
			errMsg:  "invalid theme 'invalidtheme': must be one of the supported themes",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := eloTypeService.GetEloTypeConfig(tt.eloType)
			if tt.wantErr {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

// TestSprintService_ErrorWrapping tests that sprint service wraps ELO type errors correctly
func TestSprintService_ErrorWrapping(t *testing.T) {
	eloTypeService := service.NewEloTypeService()

	// Test that invalid ELO types produce errors that contain "invalid ELO type:"
	invalidEloTypes := []string{
		"mixed 31/30",   // invalid duration
		"mixed 10/25",   // invalid per-puzzle time
		"invalid 10/30", // invalid theme
	}

	for _, eloType := range invalidEloTypes {
		t.Run("error_wrapping_"+eloType, func(t *testing.T) {
			_, err := eloTypeService.GetEloTypeConfig(eloType)
			require.Error(t, err)

			// Simulate how the sprint service wraps the error
			wrappedErr := "invalid ELO type: " + err.Error()

			// This should contain the prefix that the handler checks for
			assert.Contains(t, wrappedErr, "invalid ELO type:")
		})
	}
}

// TestOriginalErrorCase tests the specific error case from the user's report
func TestOriginalErrorCase(t *testing.T) {
	eloTypeService := service.NewEloTypeService()

	// Test the specific case that was failing: duration '3'
	_, err := eloTypeService.GetEloTypeConfig("mixed 3/30")
	require.NoError(t, err, "Duration '3' should now be valid (1-30 minutes)")

	// Test the case that was originally failing but should now work
	_, err = eloTypeService.GetEloTypeConfig("mixed 3/15")
	require.NoError(t, err, "Duration '3' with 15-second puzzles should be valid")

	// Test that the original invalid case still fails but with updated error message
	_, err = eloTypeService.GetEloTypeConfig("mixed 3/25")
	require.Error(t, err)
	assert.Contains(t, err.Error(), "invalid per-puzzle time '25': must be one of 5, 10, 15, 20, 30, or 60 seconds")
}
