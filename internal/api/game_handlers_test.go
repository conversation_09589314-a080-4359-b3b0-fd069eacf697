package api

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"strings"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/api/testutils"
	"github.com/chessticize/chessticize-server/internal/config"
	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/repository/fake"
	"github.com/chessticize/chessticize-server/internal/service"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// setupGameTest sets up the router with fake dependencies for game API tests.
func setupGameTest(t *testing.T) (http.Handler, repository.IGameRepository, repository.IUserRepository) {
	// Setup fake DB and Repos
	fakeDB := testutils.GetFakeDB(t)
	userRepo, gameRepo, puzzleRepo := testutils.GetFakeRepositories(t)
	taskRepo := fake.NewFakeTaskRepository(fakeDB)
	idempotencyRepo := fake.NewFakeIdempotencyRepository(fakeDB)

	cfg := &config.Config{
		JWT: testutils.GetTestJWTConfig(),
	}
	authService := service.NewAuthService(cfg.JWT)

	deps := &RouterDependencies{
		UserRepo:        userRepo,
		GameRepo:        gameRepo,
		PuzzleRepo:      puzzleRepo,
		TaskRepo:        taskRepo,
		IdempotencyRepo: idempotencyRepo,
		AuthService:     authService,
		Config:          cfg,
	}
	router := setupRouter(deps)

	return router, gameRepo, userRepo
}

func TestGameAPI(t *testing.T) {
	router, gameRepo, userRepo := setupGameTest(t)
	defer testutils.CleanupTestDB(t)

	// Helper to make admin requests
	makeAdminRequest := func(t *testing.T, method, path string, body interface{}) *http.Request {
		// Ensure the path starts with the correct admin prefix
		fullPath := "/api/v1/admin/games" + path
		return testutils.MakeAdminRequest(t, method, fullPath, body)
	}

	// Create a test user
	user := testutils.CreateTestUser(t, userRepo)

	// Define sample PGN data to be used in tests
	moveSequence := `1. e4 e5 2. Nf3 Nc6 3. Bb5 a6 4. Ba4 Nf6 5. O-O Be7 6. Re1 b5 7. Bb3 d6 8. c3 O-O 9. h3 Na5 10. Bc2 c5 11. d4 Qc7 `
	samplePGN := strings.Repeat(moveSequence, 10)

	t.Run("CreateGame", func(t *testing.T) {
		// Prepare player info strings
		whitePlayer := `{"username":"` + testutils.RandomString(10) + `", "is_ai": false}`
		blackPlayer := `{"username":"` + testutils.RandomString(10) + `", "is_ai": false}`

		// Create test game request using the sample PGN
		createGameReq := CreateGameRequest{
			UserID:        user.ID,
			Platform:      models.ChessDotCom,
			ChessUsername: "testuser",
			UserColor:     models.White,
			GameTime:      time.Now(),
			PGN:           samplePGN, // Use predefined sample PGN
			TimeControl:   "10+0",
			Rated:         true,
			WhitePlayer:   whitePlayer,
			BlackPlayer:   blackPlayer,
			Winner:        models.WinnerWhite,
			Result:        models.Mate,
		}

		// Make ADMIN request
		req := makeAdminRequest(t, http.MethodPost, "/", createGameReq)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusCreated, resp.Code)

		// Parse response
		var game models.Game
		testutils.ParseResponseBody(t, resp, &game)

		// Verify game was created
		assert.NotEmpty(t, game.ID)
		assert.Equal(t, user.ID, game.UserID)
		assert.Equal(t, "testuser", game.ChessUsername)
		assert.Equal(t, models.White, game.UserColor)
		assert.Equal(t, "10+0", game.TimeControl)
		assert.Equal(t, true, game.Rated)
		assert.Equal(t, models.WinnerWhite, game.Winner)
		assert.Equal(t, models.Mate, game.Result)

		// Verify PGN compression worked
		retrievedGame, err := gameRepo.GetByID(context.Background(), game.ID)
		require.NoError(t, err)

		// CompressedPGN should be smaller than original
		assert.Less(t, len(retrievedGame.CompressedPGN), len(samplePGN),
			"Compressed PGN should be smaller than original")

		// Decompress and verify content
		decompressedPGN, err := retrievedGame.GetPGN()
		require.NoError(t, err)
		assert.Equal(t, samplePGN, decompressedPGN,
			"Decompressed PGN should match original")
	})

	t.Run("CreateGame_Idempotency", func(t *testing.T) {
		idempotencyKey := testutils.GenerateIdempotencyKey()
		userID := user.ID // Use the user created in the main test scope
		// Get initial count using ListByUserID
		_, initialGameCount, err := gameRepo.ListByUserID(context.Background(), userID, repository.GameFilter{}, 0, 1)
		require.NoError(t, err)

		// Prepare player info strings
		whitePlayer := `{"username":"` + testutils.RandomString(10) + `_idem", "is_ai": false}`
		blackPlayer := `{"username":"` + testutils.RandomString(10) + `_idem", "is_ai": false}`

		// Create test game request
		createGameReq := CreateGameRequest{
			UserID:        userID,
			Platform:      models.LichessOrg,
			ChessUsername: "idem_user",
			UserColor:     models.Black,
			GameTime:      time.Now(),
			PGN:           "1. d4 d5", // Simple PGN
			TimeControl:   "5+3",
			Rated:         false,
			WhitePlayer:   whitePlayer,
			BlackPlayer:   blackPlayer,
			Winner:        models.WinnerNone, // Reverted to Draw
			Result:        models.Draw,       // Result is Draw
		}

		// --- First Request ---
		req1 := makeAdminRequest(t, http.MethodPost, "/", createGameReq)
		testutils.AddIdempotencyHeader(req1, idempotencyKey) // Add header
		resp1 := testutils.ExecuteRequest(t, router, req1)

		// Check response (201 Created)
		testutils.CheckResponseCode(t, http.StatusCreated, resp1.Code)
		var createdGame1 models.Game
		testutils.ParseResponseBody(t, resp1, &createdGame1)
		require.NotEmpty(t, createdGame1.ID)
		assert.Equal(t, userID, createdGame1.UserID)

		// Verify DB state (count increased by 1)
		_, countAfterFirst, err := gameRepo.ListByUserID(context.Background(), userID, repository.GameFilter{}, 0, 1)
		require.NoError(t, err)
		assert.Equal(t, initialGameCount+1, countAfterFirst)

		// --- Second Request (Same Key, Same Body) ---
		req2 := makeAdminRequest(t, http.MethodPost, "/", createGameReq)
		testutils.AddIdempotencyHeader(req2, idempotencyKey) // Use same header
		resp2 := testutils.ExecuteRequest(t, router, req2)

		// Check response (201 Created - cached response)
		testutils.CheckResponseCode(t, http.StatusCreated, resp2.Code)
		var createdGame2 models.Game
		testutils.ParseResponseBody(t, resp2, &createdGame2)

		// Verify response bodies are identical
		// Note: Comparing structs directly might fail due to time precision. Compare relevant fields.
		assert.Equal(t, createdGame1.ID, createdGame2.ID)
		assert.Equal(t, createdGame1.UserID, createdGame2.UserID)
		assert.Equal(t, createdGame1.Platform, createdGame2.Platform)
		assert.Equal(t, createdGame1.ChessUsername, createdGame2.ChessUsername)
		// Compare PGN after decompression for robustness
		pgn1, _ := createdGame1.GetPGN()
		pgn2, _ := createdGame2.GetPGN()
		assert.Equal(t, pgn1, pgn2)

		// Verify DB state hasn't changed (count is still +1)
		_, countAfterSecond, err := gameRepo.ListByUserID(context.Background(), userID, repository.GameFilter{}, 0, 1)
		require.NoError(t, err)
		assert.Equal(t, countAfterFirst, countAfterSecond, "Game count should not change on second idempotent call")
	})

	t.Run("GetGame", func(t *testing.T) {
		// Create a test game with known PGN
		game := testutils.CreateTestGameWithPGN(t, gameRepo, user.ID, samplePGN)
		// Make ADMIN GET request to retrieve the game
		req := makeAdminRequest(t, http.MethodGet, "/"+game.ID, nil)
		resp := testutils.ExecuteRequest(t, router, req)
		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)
		// Parse response
		var respBody map[string]interface{}
		testutils.ParseResponseBody(t, resp, &respBody)
		// Verify fields
		assert.Equal(t, game.ID, respBody["id"].(string))
		assert.Equal(t, user.ID, respBody["user_id"].(string))
		// PGN should match samplePGN
		assert.Equal(t, samplePGN, respBody["pgn"].(string))
	})

	t.Run("UpdateGame", func(t *testing.T) {
		// Create a test game with initial PGN
		game := testutils.CreateTestGameWithPGN(t, gameRepo, user.ID, samplePGN)
		// Define new PGN
		newPGN := "1. a4 a5 2. h4 h5"
		// Prepare update request payload
		updateReq := map[string]string{"pgn": newPGN}
		// Make ADMIN PUT request to update the game
		req := makeAdminRequest(t, http.MethodPut, "/"+game.ID, updateReq)
		resp := testutils.ExecuteRequest(t, router, req)
		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)
		// Parse response
		var respBody map[string]interface{}
		testutils.ParseResponseBody(t, resp, &respBody)
		// Verify updated fields
		assert.Equal(t, game.ID, respBody["id"].(string))
		assert.Equal(t, newPGN, respBody["pgn"].(string))
		// Verify updated_at field is updated
		assert.NotEqual(t, game.UpdatedAt.Format(time.RFC3339), respBody["updated_at"])
		// Verify persisted change in repository
		updatedGame, err := gameRepo.GetByID(context.Background(), game.ID)
		require.NoError(t, err)
		decompressed, err := updatedGame.GetPGN()
		require.NoError(t, err)
		assert.Equal(t, newPGN, decompressed)
	})

	t.Run("ListGamesByUser", func(t *testing.T) {
		// Create a test game with known PGN
		gameWithPGN := testutils.CreateTestGameWithPGN(t, gameRepo, user.ID, samplePGN)

		// Create some other test games (potentially without PGN or different PGN)
		testutils.CreateTestGame(t, gameRepo, user.ID)
		testutils.CreateTestGame(t, gameRepo, user.ID)

		// Make ADMIN request
		req := makeAdminRequest(t, http.MethodGet, "/user/"+user.ID, nil)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)

		// Parse response (expecting map structure now)
		var respBody map[string]interface{}
		testutils.ParseResponseBody(t, resp, &respBody)

		// Extract games from the response map
		gamesData, ok := respBody["games"].([]interface{})
		require.True(t, ok, "Response should contain a 'games' array")

		// Verify games were returned and check PGN for the specific game
		assert.GreaterOrEqual(t, len(gamesData), 3)
		assert.GreaterOrEqual(t, int(respBody["total_count"].(float64)), 3)

		foundGameWithPGN := false
		for _, item := range gamesData {
			itemMap, ok := item.(map[string]interface{})
			require.True(t, ok, "Game item should be a map")

			// Basic check for user ID
			assert.Equal(t, user.ID, itemMap["user_id"].(string))

			// Check if this is the game we added PGN to
			if itemMap["id"].(string) == gameWithPGN.ID {
				foundGameWithPGN = true
				// Check for the 'pgn' field
				pgnString, pgnOk := itemMap["pgn"].(string)
				require.True(t, pgnOk, "Game response should contain a 'pgn' field as a string")
				assert.NotEmpty(t, pgnString, "PGN field should not be empty")

				// Verify the PGN string matches the original sample PGN
				assert.Equal(t, string(samplePGN), pgnString, "PGN string from response should match original sample PGN")
			}
		}
		assert.True(t, foundGameWithPGN, "Expected to find the game with specific PGN in the list")
	})

	t.Run("ListGamesByUser_DateFilter", func(t *testing.T) {
		// Create a game specifically for this test with known PGN
		game := testutils.CreateTestGameWithPGN(t, gameRepo, user.ID, samplePGN)

		// Test date range
		startTime := game.GameTime.Add(-time.Hour)
		endTime := game.GameTime.Add(time.Hour)

		// Make ADMIN request with date filter
		url := fmt.Sprintf("/user/%s?start_time=%s&end_time=%s", user.ID, startTime.UTC().Format(time.RFC3339), endTime.UTC().Format(time.RFC3339))
		req := makeAdminRequest(t, http.MethodGet, url, nil)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)

		// Parse response (expecting map structure now)
		var respBody map[string]interface{}
		testutils.ParseResponseBody(t, resp, &respBody)

		// Extract games from the response map
		gamesData, ok := respBody["games"].([]interface{})
		require.True(t, ok, "Response should contain a 'games' array")

		// Verify filtered games were returned and check PGN
		assert.NotEmpty(t, gamesData, "Expected at least one game in the filtered response")

		foundGame := false
		for _, item := range gamesData {
			itemMap, ok := item.(map[string]interface{})
			require.True(t, ok, "Game item should be a map")

			if itemMap["id"].(string) == game.ID {
				foundGame = true
				// Check for the 'pgn' field
				pgnString, pgnOk := itemMap["pgn"].(string)
				require.True(t, pgnOk, "Filtered game response should contain a 'pgn' field as a string")
				assert.NotEmpty(t, pgnString, "PGN field in filtered response should not be empty")

				// Verify the PGN string matches the original sample PGN
				assert.Equal(t, string(samplePGN), pgnString, "PGN string from filtered response should match original sample PGN")
				break // Found the game, no need to check others
			}
		}
		assert.True(t, foundGame, "Expected the specific test game to be in the filtered list")
	})

	t.Run("DeleteGame", func(t *testing.T) {
		// Create a game to delete
		game := testutils.CreateTestGame(t, gameRepo, user.ID)

		// Make ADMIN delete request
		req := makeAdminRequest(t, http.MethodDelete, "/"+game.ID, nil)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)

		// Verify game was deleted
		_, err := gameRepo.GetByID(context.Background(), game.ID)
		assert.Error(t, err, "Expected error retrieving deleted game")
	})

	t.Run("DeleteGame_Idempotency", func(t *testing.T) {
		// Setup: Create a game to delete
		gameToDelete := testutils.CreateTestGame(t, gameRepo, user.ID)
		require.NotEmpty(t, gameToDelete.ID)
		gameID := gameToDelete.ID

		idempotencyKey := testutils.GenerateIdempotencyKey()
		deleteURL := "/" + gameID

		// --- First Request ---
		req1 := makeAdminRequest(t, http.MethodDelete, deleteURL, nil)
		testutils.AddIdempotencyHeader(req1, idempotencyKey) // Add header
		resp1 := testutils.ExecuteRequest(t, router, req1)

		// Check response (200 OK)
		testutils.CheckResponseCode(t, http.StatusOK, resp1.Code)
		// The handler returns a simple message, capture it for comparison
		bodyBytes1, err := io.ReadAll(resp1.Body)
		require.NoError(t, err)

		// Verify game was deleted from DB
		_, err = gameRepo.GetByID(context.Background(), gameID)
		assert.Error(t, err) // Expect ErrRecordNotFound or similar

		// --- Second Request (Same Key) ---
		req2 := makeAdminRequest(t, http.MethodDelete, deleteURL, nil)
		testutils.AddIdempotencyHeader(req2, idempotencyKey) // Use same header
		resp2 := testutils.ExecuteRequest(t, router, req2)

		// Check response (200 OK - cached response)
		testutils.CheckResponseCode(t, http.StatusOK, resp2.Code)
		bodyBytes2, err := io.ReadAll(resp2.Body)
		require.NoError(t, err)

		// Verify response bodies are identical
		assert.Equal(t, string(bodyBytes1), string(bodyBytes2), "Idempotent response body mismatch")

		// Verify game remains deleted from DB
		_, err = gameRepo.GetByID(context.Background(), gameID)
		assert.Error(t, err)
	})

	t.Run("DeleteGame_NotFound", func(t *testing.T) {
		// Make ADMIN delete request with non-existent ID
		req := makeAdminRequest(t, http.MethodDelete, "/nonexistent", nil)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusNotFound, resp.Code)
	})
}
