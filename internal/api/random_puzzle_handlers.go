package api

import (
	"net/http"
	"time"

	"github.com/chessticize/chessticize-server/internal/service"
	"github.com/go-chi/chi/v5"
	"github.com/go-chi/render"
)

type RandomPuzzleHandler struct {
	randomPuzzleService *service.RandomPuzzleService
}

func NewRandomPuzzleHandler(randomPuzzleService *service.RandomPuzzleService) *RandomPuzzleHandler {
	return &RandomPuzzleHandler{
		randomPuzzleService: randomPuzzleService,
	}
}

// RandomPuzzleRoutes creates a router for random puzzle endpoints
func RandomPuzzleRoutes(randomPuzzleService *service.RandomPuzzleService) http.Handler {
	h := NewRandomPuzzleHandler(randomPuzzleService)
	r := chi.NewRouter()

	r.Post("/lichess", h.GetRandomLichessPuzzles)
	r.Post("/user", h.GetRandomUserPuzzles)

	return r
}

// API Request/Response Models

type RandomLichessPuzzleRequest struct {
	MinRating int      `json:"min_rating" validate:"min=0,max=3000"`
	MaxRating int      `json:"max_rating" validate:"min=0,max=3000,gtfield=MinRating"`
	Themes    []string `json:"themes,omitempty"`
	Limit     int      `json:"limit" validate:"min=1,max=50"`
}

type RandomUserPuzzleRequest struct {
	MinMoveLength int        `json:"min_move_length" validate:"min=2,max=20"`
	MaxMoveLength int        `json:"max_move_length" validate:"min=2,max=20,gtfield=MinMoveLength"`
	Tags          []string   `json:"tags,omitempty"`
	Themes        []string   `json:"themes,omitempty"`
	GameTimeStart *time.Time `json:"game_time_start,omitempty"`
	GameTimeEnd   *time.Time `json:"game_time_end,omitempty"`
	Limit         int        `json:"limit" validate:"min=1,max=50"`
}

type RandomLichessPuzzleResponse struct {
	Puzzles []LichessPuzzleForRandom `json:"puzzles"`
	Count   int                      `json:"count"`
}

type RandomUserPuzzleResponse struct {
	Puzzles []UserPuzzleForRandom `json:"puzzles"`
	Count   int                   `json:"count"`
}

type LichessPuzzleForRandom struct {
	ID     string   `json:"id"`
	FEN    string   `json:"fen"`
	Moves  []string `json:"moves"`
	Rating int      `json:"rating"`
	Themes []string `json:"themes"`
}

type UserPuzzleForRandom struct {
	ID          string   `json:"id"`
	FEN         string   `json:"fen"`
	Moves       []string `json:"moves"`
	MoveLength  int      `json:"move_length"`
	Tags        []string `json:"tags"`
	Theme       string   `json:"theme"`
	UserColor   string   `json:"user_color"`
	PuzzleColor string   `json:"puzzle_color"`
}

// Handler Methods

// GetRandomLichessPuzzles handles POST /api/v1/random-puzzles/lichess
func (h *RandomPuzzleHandler) GetRandomLichessPuzzles(w http.ResponseWriter, r *http.Request) {
	userID, err := getUserIDFromContext(r)
	if err != nil {
		apiError(w, r, http.StatusUnauthorized, err, "Unauthorized: Invalid user context")
		return
	}

	var req RandomLichessPuzzleRequest
	if err := render.DecodeJSON(r.Body, &req); err != nil {
		apiError(w, r, http.StatusBadRequest, err, "Invalid request body")
		return
	}

	// Validate request
	if req.MinRating < 0 || req.MinRating > 3000 {
		apiError(w, r, http.StatusBadRequest, nil, "min_rating must be between 0 and 3000")
		return
	}
	if req.MaxRating < 0 || req.MaxRating > 3000 {
		apiError(w, r, http.StatusBadRequest, nil, "max_rating must be between 0 and 3000")
		return
	}
	if req.MinRating > req.MaxRating {
		apiError(w, r, http.StatusBadRequest, nil, "min_rating must be less than or equal to max_rating")
		return
	}
	if req.Limit < 1 || req.Limit > 50 {
		apiError(w, r, http.StatusBadRequest, nil, "limit must be between 1 and 50")
		return
	}

	// Convert to service request
	serviceRequest := service.RandomLichessPuzzleRequest{
		MinRating: req.MinRating,
		MaxRating: req.MaxRating,
		Themes:    req.Themes,
		Limit:     req.Limit,
	}

	// Get random puzzles
	puzzles, err := h.randomPuzzleService.GetRandomLichessPuzzles(r.Context(), userID, serviceRequest)
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to get random Lichess puzzles")
		return
	}

	// Convert to response format
	puzzleResponses := make([]LichessPuzzleForRandom, len(puzzles))
	for i, puzzle := range puzzles {
		// Convert pq.StringArray to []string for moves
		moves := make([]string, len(puzzle.Moves))
		copy(moves, puzzle.Moves)

		// Convert pq.StringArray to []string for themes
		themes := make([]string, len(puzzle.Themes))
		copy(themes, puzzle.Themes)

		puzzleResponses[i] = LichessPuzzleForRandom{
			ID:     puzzle.ID,
			FEN:    puzzle.FEN,
			Moves:  moves,
			Rating: puzzle.Rating,
			Themes: themes,
		}
	}

	resp := RandomLichessPuzzleResponse{
		Puzzles: puzzleResponses,
		Count:   len(puzzleResponses),
	}

	render.JSON(w, r, resp)
}

// GetRandomUserPuzzles handles POST /api/v1/random-puzzles/user
func (h *RandomPuzzleHandler) GetRandomUserPuzzles(w http.ResponseWriter, r *http.Request) {
	userID, err := getUserIDFromContext(r)
	if err != nil {
		apiError(w, r, http.StatusUnauthorized, err, "Unauthorized: Invalid user context")
		return
	}

	var req RandomUserPuzzleRequest
	if err := render.DecodeJSON(r.Body, &req); err != nil {
		apiError(w, r, http.StatusBadRequest, err, "Invalid request body")
		return
	}

	// Validate request
	if req.MinMoveLength < 2 || req.MinMoveLength > 20 {
		apiError(w, r, http.StatusBadRequest, nil, "min_move_length must be between 2 and 20")
		return
	}
	if req.MaxMoveLength < 2 || req.MaxMoveLength > 20 {
		apiError(w, r, http.StatusBadRequest, nil, "max_move_length must be between 2 and 20")
		return
	}
	if req.MinMoveLength > req.MaxMoveLength {
		apiError(w, r, http.StatusBadRequest, nil, "min_move_length must be less than or equal to max_move_length")
		return
	}
	// Ensure move lengths are even numbers
	if req.MinMoveLength%2 != 0 {
		apiError(w, r, http.StatusBadRequest, nil, "min_move_length must be an even number")
		return
	}
	if req.MaxMoveLength%2 != 0 {
		apiError(w, r, http.StatusBadRequest, nil, "max_move_length must be an even number")
		return
	}
	if req.Limit < 1 || req.Limit > 50 {
		apiError(w, r, http.StatusBadRequest, nil, "limit must be between 1 and 50")
		return
	}
	// Validate time range if provided
	if req.GameTimeStart != nil && req.GameTimeEnd != nil {
		if req.GameTimeStart.After(*req.GameTimeEnd) {
			apiError(w, r, http.StatusBadRequest, nil, "game_time_start must be before game_time_end")
			return
		}
	}

	// Convert to service request
	serviceRequest := service.RandomUserPuzzleRequest{
		MinMoveLength: req.MinMoveLength,
		MaxMoveLength: req.MaxMoveLength,
		Tags:          req.Tags,
		Themes:        req.Themes,
		GameTimeStart: req.GameTimeStart,
		GameTimeEnd:   req.GameTimeEnd,
		Limit:         req.Limit,
	}

	// Get random puzzles
	puzzles, err := h.randomPuzzleService.GetRandomUserPuzzles(r.Context(), userID, serviceRequest)
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to get random user puzzles")
		return
	}

	// Convert to response format
	puzzleResponses := make([]UserPuzzleForRandom, len(puzzles))
	for i, puzzle := range puzzles {
		// Convert pq.StringArray to []string for moves
		moves := make([]string, len(puzzle.Moves))
		copy(moves, puzzle.Moves)

		// Convert pq.StringArray to []string for tags
		tags := make([]string, len(puzzle.Tags))
		copy(tags, puzzle.Tags)

		puzzleResponses[i] = UserPuzzleForRandom{
			ID:          puzzle.ID,
			FEN:         puzzle.FEN,
			Moves:       moves,
			MoveLength:  len(moves),
			Tags:        tags,
			Theme:       string(puzzle.Theme),
			UserColor:   string(puzzle.UserColor),
			PuzzleColor: string(puzzle.PuzzleColor),
		}
	}

	resp := RandomUserPuzzleResponse{
		Puzzles: puzzleResponses,
		Count:   len(puzzleResponses),
	}

	render.JSON(w, r, resp)
}
