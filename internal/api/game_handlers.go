package api

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/utils"
	"github.com/go-chi/chi/v5"
	"github.com/go-chi/render"
)

// GameResponse defines the structure returned by the API for a game,
// including the decompressed PGN.
type GameResponse struct {
	models.Game        // Embed the core Game model
	PGN         string `json:"pgn"` // Decompressed PGN data as a string
}

type <PERSON><PERSON>andler struct {
	gameRepo repository.IGameRepository
}

func NewGameHandler(gameRepo repository.IGameRepository) *GameHandler {
	return &GameHandler{
		gameRepo: gameRepo,
	}
}

// GameRoutes creates a router for game endpoints
func GameRoutes(gameRepo repository.IGameRepository) http.Handler {
	h := NewGameHandler(gameRepo)
	r := chi.NewRouter()

	r.Post("/", h.CreateGame)
	r.Get("/user/{userId}", h.ListGamesByUser)
	r.Get("/{id}", h.GetGame)
	r.Put("/{id}", h.UpdateGame)
	r.Delete("/{id}", h.DeleteGame)

	return r
}

type CreateGameRequest struct {
	UserID        string               `json:"user_id"`
	Platform      models.ChessPlatform `json:"platform"`
	ChessUsername string               `json:"chess_username"`
	UserColor     models.Color         `json:"user_color"`
	GameTime      time.Time            `json:"game_time"`
	PGN           string               `json:"pgn"`
	TimeControl   string               `json:"time_control"`
	Rated         bool                 `json:"rated"`
	URL           *string              `json:"url,omitempty"`
	WhitePlayer   string               `json:"white_player"` // JSON serialized PlayerInfo
	BlackPlayer   string               `json:"black_player"` // JSON serialized PlayerInfo
	Winner        models.Winner        `json:"winner"`
	Result        models.GameResult    `json:"result"`
}

// CreateGame handles creating a new game
func (h *GameHandler) CreateGame(w http.ResponseWriter, r *http.Request) {
	var req CreateGameRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		apiError(w, r, http.StatusBadRequest, err, "Invalid request body")
		return
	}

	// Validate request
	if req.UserID == "" || req.ChessUsername == "" {
		apiError(w, r, http.StatusBadRequest, nil, "user_id and chess_username are required")
		return
	}

	// Compress the PGN data
	compressedPGN, err := utils.CompressPGN(req.PGN)
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to compress PGN data")
		return
	}

	// Create game model
	game := &models.Game{
		UserID:        req.UserID,
		Platform:      req.Platform,
		ChessUsername: req.ChessUsername,
		UserColor:     req.UserColor,
		GameTime:      req.GameTime,
		CompressedPGN: compressedPGN,
		TimeControl:   req.TimeControl,
		Rated:         req.Rated,
		URL:           req.URL,
		WhitePlayer:   req.WhitePlayer,
		BlackPlayer:   req.BlackPlayer,
		Winner:        req.Winner,
		Result:        req.Result,
	}

	// Save to database
	if err := h.gameRepo.Create(r.Context(), game); err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to create game")
		return
	}

	render.Status(r, http.StatusCreated)
	render.JSON(w, r, game)
}

// ListGamesByUser handles listing games for a user, with optional time filtering and pagination
func (h *GameHandler) ListGamesByUser(w http.ResponseWriter, r *http.Request) {
	userID := chi.URLParam(r, "userId")
	if userID == "" {
		apiError(w, r, http.StatusBadRequest, nil, "User ID is required")
		return
	}

	// Parse pagination parameters
	offset, limit, err := parsePaginationParams(r) // Use shared helper
	if err != nil {
		apiError(w, r, http.StatusBadRequest, nil, err.Error()) // err here is just a message string
		return
	}

	// Parse filter parameters
	var gameFilter repository.GameFilter
	startTime, err := parseTimeParam(r, "start_time") // Use shared helper
	if err != nil {
		apiError(w, r, http.StatusBadRequest, nil, err.Error()) // err here is just a message string
		return
	}
	gameFilter.StartTime = startTime

	endTime, err := parseTimeParam(r, "end_time") // Use shared helper
	if err != nil {
		apiError(w, r, http.StatusBadRequest, nil, err.Error()) // err here is just a message string
		return
	}
	gameFilter.EndTime = endTime

	// Future: Add Platform and Username filters if needed
	// if platform := r.URL.Query().Get("platform"); platform != "" { ... }
	// if username := r.URL.Query().Get("username"); username != "" { ... }

	// Fetch games using the repository's ListByUserID method
	games, totalCount, err := h.gameRepo.ListByUserID(r.Context(), userID, gameFilter, offset, limit)
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to retrieve games")
		return
	}

	// Create response slice with decompressed PGN
	gameResponses := make([]GameResponse, 0, len(games))
	for _, game := range games {
		decompressedPGN, err := game.GetPGN()
		if err != nil {
			// Log the error but continue if possible? Or fail the whole request?
			// For now, fail the whole request as one bad game shouldn't break the list,
			// but processing failure suggests a bigger issue.
			apiError(w, r, http.StatusInternalServerError, err, fmt.Sprintf("Failed to process PGN for game %s", game.ID))
			return
		}
		// Create the response object, embedding the original game and adding the PGN
		resp := GameResponse{
			Game: game,
			PGN:  string(decompressedPGN), // Convert byte slice to string
		}
		// Note: CompressedPGN has json:"-" tag so it won't be serialized in the response

		gameResponses = append(gameResponses, resp)
	}

	// Build structured response
	response := map[string]interface{}{
		"games":       gameResponses, // Use the slice with decompressed PGN
		"total_count": totalCount,
		"offset":      offset,
		"limit":       limit,
	}

	render.JSON(w, r, response)
}

// DeleteGame handles deleting a game by ID
func (h *GameHandler) DeleteGame(w http.ResponseWriter, r *http.Request) {
	gameID := chi.URLParam(r, "id")
	if gameID == "" {
		apiError(w, r, http.StatusBadRequest, nil, "Game ID is required")
		return
	}

	// Check if game exists first
	_, err := h.gameRepo.GetByID(r.Context(), gameID)
	if err != nil {
		if errors.Is(err, repository.ErrNotFound) {
			apiError(w, r, http.StatusNotFound, nil, "Game not found") // Don't expose internal error details
		} else {
			// Log the error here if desired
			apiError(w, r, http.StatusInternalServerError, err, "Error checking game existence") // More specific internal error message
		}
		return
	}

	// Delete the game
	if err := h.gameRepo.Delete(r.Context(), gameID); err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to delete game")
		return
	}

	render.Status(r, http.StatusOK)
	render.JSON(w, r, map[string]string{"message": "game deleted successfully"})
}

// GetGame handles retrieving a game by ID
func (h *GameHandler) GetGame(w http.ResponseWriter, r *http.Request) {
	gameID := chi.URLParam(r, "id")
	if gameID == "" {
		apiError(w, r, http.StatusBadRequest, nil, "Game ID is required")
		return
	}
	game, err := h.gameRepo.GetByID(r.Context(), gameID)
	if err != nil {
		if errors.Is(err, repository.ErrNotFound) {
			apiError(w, r, http.StatusNotFound, nil, "Game not found")
		} else {
			apiError(w, r, http.StatusInternalServerError, err, "Failed to retrieve game")
		}
		return
	}
	// Decompress PGN
	decompressedPGN, err := game.GetPGN()
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to decompress PGN")
		return
	}
	// Build response
	resp := GameResponse{
		Game: *game,
		PGN:  string(decompressedPGN),
	}
	render.JSON(w, r, resp)
}

// UpdateGame handles updating a game's PGN by ID
func (h *GameHandler) UpdateGame(w http.ResponseWriter, r *http.Request) {
	gameID := chi.URLParam(r, "id")
	if gameID == "" {
		apiError(w, r, http.StatusBadRequest, nil, "Game ID is required")
		return
	}
	var req UpdateGameRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		apiError(w, r, http.StatusBadRequest, err, "Invalid request body")
		return
	}
	// Compress new PGN data
	compressedPGN, err := utils.CompressPGN(req.PGN)
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to compress PGN data")
		return
	}
	// Retrieve existing game
	game, err := h.gameRepo.GetByID(r.Context(), gameID)
	if err != nil {
		if errors.Is(err, repository.ErrNotFound) {
			apiError(w, r, http.StatusNotFound, nil, "Game not found")
		} else {
			apiError(w, r, http.StatusInternalServerError, err, "Failed to retrieve game")
		}
		return
	}
	// Update the compressed PGN
	game.CompressedPGN = compressedPGN
	if err := h.gameRepo.Update(r.Context(), game); err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to update game")
		return
	}
	// Decompress PGN for response
	decompressedPGN, err := game.GetPGN()
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to decompress PGN")
		return
	}
	resp := GameResponse{
		Game: *game,
		PGN:  string(decompressedPGN),
	}
	render.JSON(w, r, resp)
}

// Add UpdateGameRequest struct
type UpdateGameRequest struct {
	PGN string `json:"pgn"` // New PGN data to update
}
