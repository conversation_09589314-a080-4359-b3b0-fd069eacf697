# API Layer Conventions

This document outlines the coding patterns and conventions used in the Chessticize API layer to ensure consistency and maintainability.

## Architecture Overview

The API layer follows a clean architecture approach:

```
Router → Handler → Repository → Database
```

- **Router**: Defines API endpoints and middleware using Chi router
- **Handler**: Contains business logic for processing requests
- **Repository**: Abstracts data access through interfaces
- **Models**: Define data structures shared across the application

## API Structure

### Router Configuration

The main router is defined in `router.go` and follows these conventions:

- Routes are organized by resource type
- Admin routes are secured with middleware
- Each resource type has its own dedicated route group
- Health check route is exposed at the root level

```go
// Example route mounting pattern
r.Route("/api/v1", func(r chi.Router) {
    // Public routes
    r.Mount("/auth", AuthRoutes(...))
    
    // Admin-only routes
    r.Route("/admin", func(r chi.Router) {
        r.Use(middleware.JWTAdminOnly(...))
        r.<PERSON>("/users", UserRoutes(...))
        r.<PERSON>("/games", GameRoutes(...))
        r.<PERSON>("/puzzles", PuzzleRoutes(...))
        r.<PERSON>("/tasks", TaskRoutes(...))
    })
})
```

### Resource Handlers

Each resource type (users, games, puzzles, tasks) has its own handler defined in a dedicated file:

- `{resource}_handlers.go`: Contains handler methods for the resource
- `{resource}_handlers_test.go`: Contains tests for the handler

Handler files follow this pattern:

```go
// Example handler structure
type ResourceHandler struct {
    repo repository.IResourceRepository
}

func NewResourceHandler(repo repository.IResourceRepository) *ResourceHandler {
    return &ResourceHandler{repo: repo}
}

func ResourceRoutes(repo repository.IResourceRepository) http.Handler {
    h := NewResourceHandler(repo)
    r := chi.NewRouter()
    
    r.Get("/", h.List)
    r.Post("/", h.Create)
    r.Get("/{id}", h.Get)
    r.Put("/{id}", h.Update)
    r.Delete("/{id}", h.Delete)
    
    return r
}
```

## Error Handling

- Use HTTP status codes consistently to indicate error types
- Return JSON responses with error messages
- Distinguish between client errors (4xx) and server errors (5xx)

```go
// Error response pattern
if err != nil {
    render.Status(r, http.StatusBadRequest) // Or other appropriate status
    render.JSON(w, r, map[string]string{"error": "Error message"})
    return
}
```

## Request/Response Handling

- Define request structs for each endpoint to validate inputs
- Use render package for consistent JSON responses
- Use URL parameters for resource IDs
- Use query parameters for filtering and pagination
- Return appropriate status codes (201 for creation, 200 for success)

```go
// Example request struct
type CreateResourceRequest struct {
    Name string `json:"name"`
    Type string `json:"type"`
}

// Response pattern
render.Status(r, http.StatusCreated)
render.JSON(w, r, resource)
```

## Repository Pattern

- Abstract database operations behind repository interfaces
- Define repository interfaces in `repository/interfaces.go`
- Implement concrete repositories in separate files
- Use context for database operations to support cancellation

```go
// Repository interface pattern
type IResourceRepository interface {
    Create(ctx context.Context, resource *models.Resource) error
    GetByID(ctx context.Context, id string) (*models.Resource, error)
    // Additional methods...
}
```

## Testing Patterns

### Test Setup

- Use fake repositories for unit testing
- Use `testutils` package for common testing utilities
- Create test entities (users, games, etc.) in setup

```go
// Example test setup
userRepo, gameRepo, resourceRepo := testutils.GetFakeRepositories(t)
defer testutils.CleanupTestDB(t)

handler := ResourceRoutes(resourceRepo)
```

### Test Pattern

- Test each endpoint separately with subtests
- Verify response status codes
- Parse and verify response bodies
- Test both success and failure cases
- Test edge cases (not found, invalid input, etc.)

```go
t.Run("CreateResource", func(t *testing.T) {
    // Setup test data
    req := testutils.MakeRequest(t, http.MethodPost, "/", reqData)
    resp := testutils.ExecuteRequest(t, handler, req)
    
    // Check response
    testutils.CheckResponseCode(t, http.StatusCreated, resp.Code)
    
    // Parse and verify response
    var resource models.Resource
    testutils.ParseResponseBody(t, resp, &resource)
    
    // Assertions
    assert.NotEmpty(t, resource.ID)
    assert.Equal(t, expectedValue, resource.Field)
})
```

## Optimistic Locking

For updates, use optimistic locking to prevent race conditions:

- Track `UpdatedAt` timestamps
- Verify the timestamp on updates
- Return conflict errors if timestamps don't match

```go
// Optimistic locking pattern
expectedUpdatedAt := resource.UpdatedAt
if err := repo.Update(ctx, resource, expectedUpdatedAt); err != nil {
    if err == repository.ErrResourceUpdateConflict {
        render.Status(r, http.StatusConflict)
        render.JSON(w, r, map[string]string{"error": "Resource was modified by another process"})
        return
    }
    // Handle other errors
}
```

## Adding New API Endpoints

When adding new API endpoints:

1. Define request/response structures
2. Add new methods to the appropriate repository interface
3. Implement the repository methods
4. Create handler methods to process the requests
5. Add routes to connect the endpoints to the handlers
6. Write tests for the new endpoints

## Best Practices

1. **Validation**: Validate all request inputs before processing
2. **Error Handling**: Provide meaningful error messages
3. **Logging**: Log errors and important events
4. **Transactions**: Use transactions for operations affecting multiple entities
5. **Pagination**: Support pagination for list endpoints
6. **Authentication**: Secure endpoints with appropriate middleware
7. **Resource Naming**: Use consistent resource naming (plural nouns)
8. **HTTP Methods**: Use appropriate HTTP methods (GET, POST, PUT, DELETE)
9. **Status Codes**: Use appropriate HTTP status codes
10. **Documentation**: Comment complex logic and document API endpoints

## Authentication and Authorization

The API uses JWT-based authentication and role-based authorization to secure endpoints:

- **Public routes**: No authentication required (e.g., /health, /auth/login)
- **User routes**: Require a valid user JWT token
- **Admin routes**: Require a valid admin JWT token

### Authentication Implementation

Authentication is implemented using middleware:

```go
// JWT Authentication middleware
r.Use(middleware.JWTAuth(cfg.JWT))

// Admin-only middleware
r.Use(middleware.JWTAdminOnly(cfg.JWT))
```

### Authentication Testing

All authenticated endpoints must be registered in `auth_test.go` to ensure proper authentication testing:

```go
// Example API test case registration
ApiTestCases = append(ApiTestCases, []ApiTestCase{
    // New admin endpoints
    {"ListTasks", "/api/v1/admin/tasks", http.MethodGet, "admin"},
    {"CreateTask", "/api/v1/admin/tasks", http.MethodPost, "admin"},
    {"ClaimTask", "/api/v1/admin/tasks/claim", http.MethodPost, "admin"},
    {"GetTask", "/api/v1/admin/tasks/{taskId}", http.MethodGet, "admin"},
    {"UpdateTask", "/api/v1/admin/tasks/{taskId}", http.MethodPut, "admin"},
    
    // New user endpoints
    {"ListMyTasks", "/api/v1/users/me/tasks", http.MethodGet, "user"},
}...)
```

This ensures that all routes are automatically tested for:
- Rejection when no token is provided
- Rejection when an invalid token is provided
- Rejection when a user token is provided for admin routes

### Adding New Authenticated Endpoints

When adding new endpoints that require authentication:

1. Implement the endpoint with appropriate handlers and routes
2. Add the endpoint to `ApiTestCases` in `auth_test.go` (as shown above)
3. Specify the endpoint type ("admin" or "user")
4. Run the auth tests to verify proper authentication

### Token Management

Tokens are managed by the auth service:
- Admin tokens include an `is_admin` claim
- Tokens have a configurable expiration time
- Token verification checks signature, expiration, and required claims

## Task Pattern

For asynchronous task processing:

1. Create tasks via POST /admin/tasks/
2. Claim tasks via POST /admin/tasks/claim
3. Update task status via PUT /admin/tasks/{id}
4. List tasks via GET /admin/tasks/
5. Get a specific task via GET /admin/tasks/{id}

Tasks use optimistic locking for updates and track their status, error messages, and worker assignments. 