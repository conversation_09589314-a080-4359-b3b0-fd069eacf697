package api

import (
	"testing"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/stretchr/testify/assert"
)

func TestArrowDuelValidation(t *testing.T) {
	t.Run("ValidateArrowDuelFields_Success", func(t *testing.T) {
		// Test valid arrow-duel request
		req := PostPuzzleAttemptRequest{
			Moves:          []string{"e2e4"},
			Solved:         true,
			TimeSpent:      15,
			AttemptType:    "arrow_duel",
			CandidateMoves: []string{"e2e3", "e2e4"},
		}
		chosenMove := "e2e4"
		req.ChosenMove = &chosenMove

		// Validate attempt type
		attemptType := models.GetAttemptTypeOrDefault((*models.AttemptType)(&req.AttemptType))
		assert.Equal(t, models.AttemptTypeArrowDuel, attemptType)

		// Validate candidate moves
		assert.Len(t, req.CandidateMoves, 2)

		// Validate chosen move
		assert.NotNil(t, req.<PERSON><PERSON><PERSON>ove)
		assert.Equal(t, "e2e4", *req.<PERSON>senMove)

		// Validate that chosen move is in candidate moves
		chosenMoveValue := *req.ChosenMove
		assert.True(t, chosenMoveValue == req.CandidateMoves[0] || chosenMoveValue == req.CandidateMoves[1])
	})

	t.Run("ValidateArrowDuelFields_InvalidCandidateMoves", func(t *testing.T) {
		// Test invalid candidate moves (wrong count)
		req := PostPuzzleAttemptRequest{
			Moves:          []string{"e2e4"},
			Solved:         true,
			TimeSpent:      15,
			AttemptType:    "arrow_duel",
			CandidateMoves: []string{"e2e3"}, // Only one move
		}
		chosenMove := "e2e3"
		req.ChosenMove = &chosenMove

		// Validate attempt type
		attemptType := models.GetAttemptTypeOrDefault((*models.AttemptType)(&req.AttemptType))
		assert.Equal(t, models.AttemptTypeArrowDuel, attemptType)

		// Validate candidate moves - should fail
		assert.NotEqual(t, 2, len(req.CandidateMoves))
	})

	t.Run("ValidateArrowDuelFields_MissingChosenMove", func(t *testing.T) {
		// Test missing chosen move
		req := PostPuzzleAttemptRequest{
			Moves:          []string{"e2e4"},
			Solved:         true,
			TimeSpent:      15,
			AttemptType:    "arrow_duel",
			CandidateMoves: []string{"e2e3", "e2e4"},
			// ChosenMove is nil
		}

		// Validate attempt type
		attemptType := models.GetAttemptTypeOrDefault((*models.AttemptType)(&req.AttemptType))
		assert.Equal(t, models.AttemptTypeArrowDuel, attemptType)

		// Validate chosen move - should fail
		assert.Nil(t, req.ChosenMove)
	})

	t.Run("ValidateArrowDuelFields_InvalidChosenMove", func(t *testing.T) {
		// Test chosen move not in candidate moves
		req := PostPuzzleAttemptRequest{
			Moves:          []string{"e2e4"},
			Solved:         true,
			TimeSpent:      15,
			AttemptType:    "arrow_duel",
			CandidateMoves: []string{"e2e3", "e2e4"},
		}
		chosenMove := "e2e5" // Not in candidate moves
		req.ChosenMove = &chosenMove

		// Validate attempt type
		attemptType := models.GetAttemptTypeOrDefault((*models.AttemptType)(&req.AttemptType))
		assert.Equal(t, models.AttemptTypeArrowDuel, attemptType)

		// Validate that chosen move is NOT in candidate moves
		chosenMoveValue := *req.ChosenMove
		assert.False(t, chosenMoveValue == req.CandidateMoves[0] || chosenMoveValue == req.CandidateMoves[1])
	})

	t.Run("ValidateRegularAttempt_WithoutArrowDuelFields", func(t *testing.T) {
		// Test regular attempt without arrow-duel fields
		req := PostPuzzleAttemptRequest{
			Moves:       []string{"e2e4"},
			Solved:      true,
			TimeSpent:   30,
			AttemptType: "regular",
			// No CandidateMoves or ChosenMove
		}

		// Validate attempt type
		attemptType := models.GetAttemptTypeOrDefault((*models.AttemptType)(&req.AttemptType))
		assert.Equal(t, models.AttemptTypeRegular, attemptType)

		// Validate that arrow-duel fields are empty/nil
		assert.Empty(t, req.CandidateMoves)
		assert.Nil(t, req.ChosenMove)
	})

	t.Run("ValidateDefaultAttemptType", func(t *testing.T) {
		// Test default attempt type (empty string should default to regular)
		req := PostPuzzleAttemptRequest{
			Moves:     []string{"e2e4"},
			Solved:    true,
			TimeSpent: 30,
			// AttemptType is empty
		}

		// Validate attempt type defaults to regular
		attemptType := models.GetAttemptTypeOrDefault((*models.AttemptType)(&req.AttemptType))
		assert.Equal(t, models.AttemptTypeRegular, attemptType)
	})
}

func TestLichessArrowDuelValidation(t *testing.T) {
	t.Run("ValidateLichessArrowDuelFields_Success", func(t *testing.T) {
		// Test valid Lichess arrow-duel request
		req := PostLichessPuzzleAttemptRequest{
			Moves:          []string{"Qh5"},
			Solved:         true,
			TimeSpent:      12,
			AttemptType:    "arrow_duel",
			CandidateMoves: []string{"Qh4", "Qh5"},
		}
		chosenMove := "Qh5"
		req.ChosenMove = &chosenMove

		// Validate attempt type
		attemptType := models.GetAttemptTypeOrDefault((*models.AttemptType)(&req.AttemptType))
		assert.Equal(t, models.AttemptTypeArrowDuel, attemptType)

		// Validate candidate moves
		assert.Len(t, req.CandidateMoves, 2)

		// Validate chosen move
		assert.NotNil(t, req.ChosenMove)
		assert.Equal(t, "Qh5", *req.ChosenMove)

		// Validate that chosen move is in candidate moves
		chosenMoveValue := *req.ChosenMove
		assert.True(t, chosenMoveValue == req.CandidateMoves[0] || chosenMoveValue == req.CandidateMoves[1])
	})

	t.Run("ValidateLichessRegularAttempt_DefaultType", func(t *testing.T) {
		// Test Lichess regular attempt without specifying attempt type
		req := PostLichessPuzzleAttemptRequest{
			Moves:     []string{"Qh5"},
			Solved:    true,
			TimeSpent: 20,
			// AttemptType not specified - should default to regular
		}

		// Validate attempt type defaults to regular
		attemptType := models.GetAttemptTypeOrDefault((*models.AttemptType)(&req.AttemptType))
		assert.Equal(t, models.AttemptTypeRegular, attemptType)

		// Validate that arrow-duel fields are empty/nil
		assert.Empty(t, req.CandidateMoves)
		assert.Nil(t, req.ChosenMove)
	})
}

func TestPuzzleEventDataArrowDuel(t *testing.T) {
	t.Run("CreatePuzzleEventData_ArrowDuel", func(t *testing.T) {
		// Test creating puzzle event data for arrow-duel
		candidateMoves := []string{"e2e3", "e2e4"}
		chosenMove := "e2e4"

		eventData := models.PuzzleEventData{
			PuzzleID:       "test-puzzle-123",
			PuzzleType:     models.PuzzleTypeUser,
			AttemptType:    models.AttemptTypeArrowDuel,
			Solved:         true,
			TimeSpent:      15,
			MovesPlayed:    []string{"e2e4"},
			CandidateMoves: candidateMoves,
			ChosenMove:     &chosenMove,
		}

		// Validate event data
		assert.Equal(t, "test-puzzle-123", eventData.PuzzleID)
		assert.Equal(t, models.PuzzleTypeUser, eventData.PuzzleType)
		assert.Equal(t, models.AttemptTypeArrowDuel, eventData.AttemptType)
		assert.True(t, eventData.Solved)
		assert.Equal(t, 15, eventData.TimeSpent)
		assert.Equal(t, []string{"e2e4"}, eventData.MovesPlayed)
		assert.Equal(t, candidateMoves, eventData.CandidateMoves)
		assert.NotNil(t, eventData.ChosenMove)
		assert.Equal(t, "e2e4", *eventData.ChosenMove)
	})

	t.Run("CreatePuzzleEventData_Regular", func(t *testing.T) {
		// Test creating puzzle event data for regular attempt
		eventData := models.PuzzleEventData{
			PuzzleID:    "test-puzzle-456",
			PuzzleType:  models.PuzzleTypeLichess,
			AttemptType: models.AttemptTypeRegular,
			Solved:      false,
			TimeSpent:   25,
			MovesPlayed: []string{"Qh4"},
			// CandidateMoves and ChosenMove should be empty/nil for regular attempts
		}

		// Validate event data
		assert.Equal(t, "test-puzzle-456", eventData.PuzzleID)
		assert.Equal(t, models.PuzzleTypeLichess, eventData.PuzzleType)
		assert.Equal(t, models.AttemptTypeRegular, eventData.AttemptType)
		assert.False(t, eventData.Solved)
		assert.Equal(t, 25, eventData.TimeSpent)
		assert.Equal(t, []string{"Qh4"}, eventData.MovesPlayed)
		assert.Empty(t, eventData.CandidateMoves)
		assert.Nil(t, eventData.ChosenMove)
	})
}
