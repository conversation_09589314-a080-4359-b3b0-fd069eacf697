package api

import (
	"context"
	"encoding/json"
	"net/http"
	"testing"

	"github.com/chessticize/chessticize-server/internal/api/testutils"
	"github.com/chessticize/chessticize-server/internal/config"
	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/repository/fake"
	"github.com/chessticize/chessticize-server/internal/service"
	"github.com/go-chi/chi/v5"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func setupPublicRegistrationTest(t *testing.T) (repository.IUserRepository, repository.IEventRepository, http.Handler) {
	// Create a fake DB for testing
	db := fake.NewDB(t)

	// Create repositories
	userRepo := fake.NewUserRepository(db)
	invitationCodeRepo := fake.NewFakeInvitationCodeRepository(db.DB)
	sessionTokenRepo := fake.NewFakeSessionTokenRepository(db.DB)
	eventRepo := fake.NewEventRepository(db.DB)

	// Create JWT config and session token config
	jwtConfig := testutils.GetTestJWTConfig()
	sessionTokenConfig := config.SessionTokenConfig{
		ExpiryDays: 30,
	}
	authService := service.NewAuthServiceWithSessionTokens(jwtConfig, sessionTokenConfig, sessionTokenRepo)
	eventService := service.NewEventService(eventRepo)

	// Create test config
	testConfig := &config.Config{
		JWT:          jwtConfig,
		SessionToken: sessionTokenConfig,
	}

	// Create the router
	r := chi.NewRouter()
	r.Route("/api/v1", func(r chi.Router) {
		firebaseAuthService := service.NewFirebaseAuthService(config.FirebaseConfig{
			ProjectID:    "demo-project",
			JWKSEndpoint: "http://localhost:9099/.well-known/jwks.json",
		})
		r.Mount("/auth", AuthRoutes(userRepo, invitationCodeRepo, authService, eventService, firebaseAuthService, testConfig))
	})

	return userRepo, eventRepo, r
}

func TestPublicRegistration(t *testing.T) {
	userRepo, eventRepo, router := setupPublicRegistrationTest(t)

	t.Run("Register_Success", func(t *testing.T) {
		registerReq := RegisterRequest{
			Email:    testutils.RandomEmail(),
			Password: "validpassword123",
		}

		// Make request
		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/register", registerReq)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusCreated, resp.Code)

		// Parse response
		var registerResp RegisterResponse
		testutils.ParseResponseBody(t, resp, &registerResp)

		// Verify response
		assert.Equal(t, registerReq.Email, registerResp.Email)
		assert.NotEmpty(t, registerResp.ID)
		assert.NotEmpty(t, registerResp.Token)
		assert.NotEmpty(t, registerResp.SessionToken)

		// Verify user was created
		user, err := userRepo.GetByEmail(context.TODO(), registerReq.Email)
		require.NoError(t, err)
		assert.Equal(t, registerReq.Email, user.Email)

		// Verify sign-in event was created
		events, _, err := eventRepo.ListByUserID(context.TODO(), user.ID, repository.EventFilter{
			EventTypes:    []models.EventType{models.EventTypeSignIn},
			EventSubTypes: []models.EventSubType{models.EventSubTypeSuccess},
		}, 0, 10)
		require.NoError(t, err)
		require.Len(t, events, 1)
		assert.Equal(t, models.EventTypeSignIn, events[0].EventType)
	})

	t.Run("Register_InvalidEmail", func(t *testing.T) {
		registerReq := RegisterRequest{
			Email:    "", // Empty email
			Password: "validpassword123",
		}

		// Make request
		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/register", registerReq)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusBadRequest, resp.Code)
	})

	t.Run("Register_InvalidPassword", func(t *testing.T) {
		registerReq := RegisterRequest{
			Email:    testutils.RandomEmail(),
			Password: "123", // Too short
		}

		// Make request
		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/register", registerReq)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusBadRequest, resp.Code)
	})

	t.Run("Register_DuplicateEmail", func(t *testing.T) {
		email := testutils.RandomEmail()

		// Create first user
		registerReq1 := RegisterRequest{
			Email:    email,
			Password: "validpassword123",
		}

		req1 := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/register", registerReq1)
		resp1 := testutils.ExecuteRequest(t, router, req1)
		testutils.CheckResponseCode(t, http.StatusCreated, resp1.Code)

		// Try to create second user with same email
		registerReq2 := RegisterRequest{
			Email:    email,
			Password: "anotherpassword123",
		}

		req2 := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/register", registerReq2)
		resp2 := testutils.ExecuteRequest(t, router, req2)
		testutils.CheckResponseCode(t, http.StatusConflict, resp2.Code)
	})

	t.Run("Register_LongPassword", func(t *testing.T) {
		registerReq := RegisterRequest{
			Email:    testutils.RandomEmail(),
			Password: "this-is-a-very-long-password-that-exceeds-the-maximum-allowed-length-of-40-characters",
		}

		// Make request
		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/register", registerReq)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusBadRequest, resp.Code)
	})
}

func TestPublicRegistrationSignInEvents(t *testing.T) {
	userRepo, eventRepo, router := setupPublicRegistrationTest(t)

	t.Run("Register_CreatesSuccessEvent", func(t *testing.T) {
		// Make registration request
		registerReq := RegisterRequest{
			Email:    testutils.RandomEmail(),
			Password: "testpassword123",
		}

		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/register", registerReq)
		req.Header.Set("User-Agent", "TestAgent/1.0")
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusCreated, resp.Code)

		// Get the created user
		user, err := userRepo.GetByEmail(context.TODO(), registerReq.Email)
		require.NoError(t, err)

		// Verify sign-in event was created
		events, _, err := eventRepo.ListByUserID(context.TODO(), user.ID, repository.EventFilter{
			EventTypes:    []models.EventType{models.EventTypeSignIn},
			EventSubTypes: []models.EventSubType{models.EventSubTypeSuccess},
		}, 0, 10)
		require.NoError(t, err)
		require.Len(t, events, 1)

		event := events[0]
		assert.Equal(t, models.EventTypeSignIn, event.EventType)
		assert.Equal(t, user.ID, event.UserID)

		// Parse event data
		var eventData models.SignInEventData
		err = json.Unmarshal(event.EventData, &eventData)
		require.NoError(t, err)

		// Verify event data
		assert.True(t, eventData.Succeeded)
		assert.Equal(t, "password", eventData.SignInType)
		assert.Equal(t, "TestAgent/1.0", eventData.UserAgent)
	})
}
