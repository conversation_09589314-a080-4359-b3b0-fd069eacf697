package api

import (
	"context"
	"net/http"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/api/testutils"
	"github.com/chessticize/chessticize-server/internal/config"
	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/repository/fake"
	"github.com/chessticize/chessticize-server/internal/service"
	"github.com/go-chi/chi/v5"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func setupInvitationTest(t *testing.T) (repository.IUserRepository, repository.IInvitationCodeRepository, http.Handler) {
	// Create a fake DB for testing
	db := fake.NewDB(t)

	// Create repositories
	userRepo := fake.NewUserRepository(db)
	invitationCodeRepo := fake.NewFakeInvitationCodeRepository(db.DB)
	sessionTokenRepo := fake.NewFakeSessionTokenRepository(db.DB)
	eventRepo := fake.NewEventRepository(db.DB)

	// Create JWT config and session token config
	jwtConfig := testutils.GetTestJWTConfig()
	sessionTokenConfig := config.SessionTokenConfig{
		ExpiryDays: 30,
	}
	authService := service.NewAuthServiceWithSessionTokens(jwtConfig, sessionTokenConfig, sessionTokenRepo)
	eventService := service.NewEventService(eventRepo)

	// Create test config
	testConfig := &config.Config{
		JWT:          jwtConfig,
		SessionToken: sessionTokenConfig,
	}

	// Create the router
	r := chi.NewRouter()
	r.Route("/api/v1", func(r chi.Router) {
		firebaseAuthService := service.NewFirebaseAuthService(config.FirebaseConfig{
			ProjectID:    "demo-project",
			JWKSEndpoint: "http://localhost:9099/.well-known/jwks.json",
		})
		r.Mount("/auth", AuthRoutes(userRepo, invitationCodeRepo, authService, eventService, firebaseAuthService, testConfig))
	})

	return userRepo, invitationCodeRepo, r
}

func TestRegisterWithInvitationAPI(t *testing.T) {
	userRepo, invitationCodeRepo, router := setupInvitationTest(t)

	// Create a test invitation code
	invitationCode := &models.InvitationCode{
		Code:            "test-invitation-code",
		CreatedByUserID: "admin-user-id",
	}
	err := invitationCodeRepo.Create(context.TODO(), invitationCode)
	require.NoError(t, err)

	t.Run("RegisterWithInvitation_Success", func(t *testing.T) {
		registerReq := RegisterWithInvitationRequest{
			Email:          testutils.RandomEmail(),
			Password:       "validpassword123",
			InvitationCode: invitationCode.Code,
		}

		// Make request
		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/register-with-invitation", registerReq)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusCreated, resp.Code)

		// Parse response - now returns RegisterResponse with user info and tokens
		var registerResp RegisterResponse
		testutils.ParseResponseBody(t, resp, &registerResp)

		// Verify response
		assert.Equal(t, registerReq.Email, registerResp.Email)
		assert.NotEmpty(t, registerResp.ID)
		assert.NotEmpty(t, registerResp.Token)
		assert.NotEmpty(t, registerResp.SessionToken)

		// Verify user was created
		user, err := userRepo.GetByEmail(context.TODO(), registerReq.Email)
		require.NoError(t, err)
		assert.Equal(t, registerReq.Email, user.Email)

		// Verify invitation code was marked as used
		updatedCode, err := invitationCodeRepo.GetByCode(context.TODO(), invitationCode.Code)
		require.NoError(t, err)
		assert.NotNil(t, updatedCode.UsedAt)
		assert.NotNil(t, updatedCode.UsedByUserID)
		assert.Equal(t, user.ID, *updatedCode.UsedByUserID)
	})

	// Create another invitation code for the next tests
	expiredCode := &models.InvitationCode{
		Code:            "expired-invitation-code",
		CreatedByUserID: "admin-user-id",
	}
	expiredTime := time.Now().Add(-24 * time.Hour)
	expiredCode.ExpiresAt = &expiredTime
	err = invitationCodeRepo.Create(context.TODO(), expiredCode)
	require.NoError(t, err)

	t.Run("RegisterWithInvitation_InvalidCode", func(t *testing.T) {
		registerReq := RegisterWithInvitationRequest{
			Email:          testutils.RandomEmail(),
			Password:       "validpassword123",
			InvitationCode: "non-existent-code",
		}

		// Make request
		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/register-with-invitation", registerReq)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusBadRequest, resp.Code)
	})

	t.Run("RegisterWithInvitation_ExpiredCode", func(t *testing.T) {
		registerReq := RegisterWithInvitationRequest{
			Email:          testutils.RandomEmail(),
			Password:       "validpassword123",
			InvitationCode: expiredCode.Code,
		}

		// Make request
		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/register-with-invitation", registerReq)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusBadRequest, resp.Code)
	})

	// Create a used invitation code
	usedCode := &models.InvitationCode{
		Code:            "used-invitation-code",
		CreatedByUserID: "admin-user-id",
	}
	err = invitationCodeRepo.Create(context.TODO(), usedCode)
	require.NoError(t, err)

	// Mark it as used
	now := time.Now()
	userID := "existing-user-id"
	usedCode.UsedAt = &now
	usedCode.UsedByUserID = &userID
	err = invitationCodeRepo.Update(context.TODO(), usedCode)
	require.NoError(t, err)

	t.Run("RegisterWithInvitation_UsedCode", func(t *testing.T) {
		registerReq := RegisterWithInvitationRequest{
			Email:          testutils.RandomEmail(),
			Password:       "validpassword123",
			InvitationCode: usedCode.Code,
		}

		// Make request
		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/register-with-invitation", registerReq)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusBadRequest, resp.Code)
	})
}

func TestInvitationCodeAdminAPI(t *testing.T) {
	userRepo, invitationCodeRepo, router := setupInvitationTest(t)

	// Create an admin user and token
	adminUser := testutils.CreateTestUser(t, userRepo)
	adminToken := testutils.GenerateAdminToken(t, adminUser.ID, adminUser.Email)

	// Create a non-admin user and token
	regularUser := testutils.CreateTestUser(t, userRepo)
	regularToken, err := testutils.CreateTestAuthService(testutils.GetTestJWTConfig()).GenerateToken(regularUser.ID, regularUser.Email, false)
	require.NoError(t, err)

	t.Run("CreateInvitationCode_Success", func(t *testing.T) {
		// Create request with no expiration
		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/invitation-codes", CreateInvitationCodeRequest{})
		req.Header.Set("Authorization", "Bearer "+adminToken)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusCreated, resp.Code)

		// Parse response
		var respData InvitationCodeResponse
		testutils.ParseResponseBody(t, resp, &respData)

		// Verify response
		assert.NotEmpty(t, respData.ID)
		assert.NotEmpty(t, respData.Code)
		assert.False(t, respData.CreatedAt.IsZero())
		assert.Nil(t, respData.ExpiresAt)
		assert.False(t, respData.Used)

		// Verify code was created in DB
		code, err := invitationCodeRepo.GetByID(context.TODO(), respData.ID)
		require.NoError(t, err)
		assert.Equal(t, respData.Code, code.Code)
		assert.Equal(t, adminUser.ID, code.CreatedByUserID)
	})

	t.Run("CreateInvitationCode_WithExpiration", func(t *testing.T) {
		// Create request with expiration
		expiresInHours := 24
		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/invitation-codes", CreateInvitationCodeRequest{
			ExpiresInHours: &expiresInHours,
		})
		req.Header.Set("Authorization", "Bearer "+adminToken)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusCreated, resp.Code)

		// Parse response
		var respData InvitationCodeResponse
		testutils.ParseResponseBody(t, resp, &respData)

		// Verify response
		assert.NotEmpty(t, respData.ID)
		assert.NotEmpty(t, respData.Code)
		assert.False(t, respData.CreatedAt.IsZero())
		assert.NotNil(t, respData.ExpiresAt)
		assert.False(t, respData.Used)

		// Verify expiration time is approximately 24 hours from now
		expectedExpiry := time.Now().Add(24 * time.Hour)
		assert.WithinDuration(t, expectedExpiry, *respData.ExpiresAt, 5*time.Second)
	})

	t.Run("CreateInvitationCode_Unauthorized", func(t *testing.T) {
		// Make request without token
		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/invitation-codes", CreateInvitationCodeRequest{})
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusUnauthorized, resp.Code)
	})

	t.Run("CreateInvitationCode_Forbidden", func(t *testing.T) {
		// Make request with non-admin token
		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/invitation-codes", CreateInvitationCodeRequest{})
		req.Header.Set("Authorization", "Bearer "+regularToken)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusForbidden, resp.Code)
	})

	t.Run("ListInvitationCodes_Success", func(t *testing.T) {
		// Create a few invitation codes first
		for i := 0; i < 3; i++ {
			code := &models.InvitationCode{
				Code:            "list-test-code-" + testutils.RandomString(8),
				CreatedByUserID: adminUser.ID,
			}
			err := invitationCodeRepo.Create(context.TODO(), code)
			require.NoError(t, err)
		}

		// Make request
		req := testutils.MakeRequest(t, http.MethodGet, "/api/v1/auth/invitation-codes", nil)
		req.Header.Set("Authorization", "Bearer "+adminToken)
		resp := testutils.ExecuteRequest(t, router, req)

		// Check response
		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)

		// Parse response
		var respData struct {
			Codes      []InvitationCodeResponse `json:"codes"`
			TotalCount int64                    `json:"total_count"`
		}
		testutils.ParseResponseBody(t, resp, &respData)

		// Verify response
		assert.GreaterOrEqual(t, respData.TotalCount, int64(3))
		assert.GreaterOrEqual(t, len(respData.Codes), 3)
	})
}
