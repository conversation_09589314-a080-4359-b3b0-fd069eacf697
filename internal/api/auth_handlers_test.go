package api

import (
	"net/http"
	"testing"

	"github.com/chessticize/chessticize-server/internal/api/testutils"
	"github.com/chessticize/chessticize-server/internal/config"
	"github.com/chessticize/chessticize-server/internal/middleware"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/repository/fake"
	"github.com/chessticize/chessticize-server/internal/service"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// Helper function to set up common components for auth tests
func setupAuthTest(t *testing.T) (repository.IUserRepository, *service.AuthService, config.JWTConfig, http.Handler) {
	// Setup fake DB and Repos
	fakeDB := testutils.GetFakeDB(t)
	userRepo, gameRepo, puzzleRepo := testutils.GetFakeRepositories(t)
	taskRepo := fake.NewFakeTaskRepository(fakeDB)
	idempotencyRepo := fake.NewFakeIdempotencyRepository(fakeDB)
	sessionTokenRepo := fake.NewFakeSessionTokenRepository(fakeDB.DB)
	eventRepo := fake.NewEventRepository(fakeDB.DB)
	t.Cleanup(func() {
		testutils.CleanupTestDB(t)
	})

	// Create JWT config and session token config
	jwtConfig := testutils.GetTestJWTConfig()
	sessionTokenConfig := config.SessionTokenConfig{
		ExpiryDays: 30,
	}
	authService := service.NewAuthServiceWithSessionTokens(jwtConfig, sessionTokenConfig, sessionTokenRepo)
	eventService := service.NewEventService(eventRepo)

	// Setup the full router using dependencies
	cfg := &config.Config{JWT: jwtConfig, SessionToken: sessionTokenConfig}
	deps := &RouterDependencies{
		UserRepo:         userRepo,
		GameRepo:         gameRepo,
		PuzzleRepo:       puzzleRepo,
		TaskRepo:         taskRepo,
		IdempotencyRepo:  idempotencyRepo,
		SessionTokenRepo: sessionTokenRepo,
		EventRepo:        eventRepo,
		AuthService:      authService,
		EventService:     eventService,
		Config:           cfg,
	}
	router := setupRouter(deps)

	return userRepo, authService, jwtConfig, router // Return the full router
}

func TestLoginAPI(t *testing.T) {
	userRepo, _, jwtConfig, router := setupAuthTest(t) // Use the full router

	t.Run("Login_Success", func(t *testing.T) {
		// Create a test user with known password
		hashedPassword, err := testutils.HashPassword("testpassword")
		require.NoError(t, err)

		email := testutils.RandomEmail()
		user := testutils.CreateUserWithPassword(t, userRepo, email, hashedPassword)

		// Create login request
		loginReq := LoginRequest{
			Email:    email,
			Password: "testpassword",
		}

		// Make request to the correct path
		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/login", loginReq)
		resp := testutils.ExecuteRequest(t, router, req) // Use the full router

		// Check response
		testutils.CheckResponseCode(t, http.StatusOK, resp.Code)

		// Parse response
		var authResp AuthResponse
		testutils.ParseResponseBody(t, resp, &authResp)

		// Verify tokens were returned
		assert.NotEmpty(t, authResp.Token)
		assert.NotEmpty(t, authResp.SessionToken) // Session token should be present

		// Validate the token
		claims, err := middleware.ValidateToken(authResp.Token, jwtConfig.Secret)
		require.NoError(t, err)
		assert.Equal(t, user.ID, claims.UserID)
		assert.Equal(t, email, claims.Email)
		assert.False(t, claims.IsAdmin)
	})

	t.Run("Login_InvalidCredentials_WrongPassword", func(t *testing.T) {
		// Create a user first
		hashedPassword, err := testutils.HashPassword("testpassword")
		require.NoError(t, err)
		email := testutils.RandomEmail()
		_ = testutils.CreateUserWithPassword(t, userRepo, email, hashedPassword)

		// Create login request with invalid credentials
		loginReq := LoginRequest{
			Email:    email,
			Password: "wrongpassword",
		}

		// Make request to the correct path
		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/login", loginReq)
		resp := testutils.ExecuteRequest(t, router, req) // Use the full router

		// Check response
		testutils.CheckResponseCode(t, http.StatusUnauthorized, resp.Code)
	})

	t.Run("Login_InvalidCredentials_UserNotFound", func(t *testing.T) {
		// Create login request with invalid credentials
		loginReq := LoginRequest{
			Email:    "<EMAIL>",
			Password: "anypassword",
		}

		// Make request to the correct path
		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/login", loginReq)
		resp := testutils.ExecuteRequest(t, router, req) // Use the full router

		// Check response
		testutils.CheckResponseCode(t, http.StatusUnauthorized, resp.Code)
	})

	t.Run("Login_MissingFields", func(t *testing.T) {
		testCases := []struct {
			name string
			body LoginRequest
		}{
			{"MissingEmail", LoginRequest{Password: "password"}},
			{"MissingPassword", LoginRequest{Email: "<EMAIL>"}},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/login", tc.body)
				resp := testutils.ExecuteRequest(t, router, req)
				testutils.CheckResponseCode(t, http.StatusBadRequest, resp.Code)
			})
		}
	})
}

func TestRegisterAPI(t *testing.T) {
	// Assign jwtConfig to _ as it's not used directly in this test function
	userRepo, authService, _, router := setupAuthTest(t) // Use the full router

	// Create an admin user and token for testing the protected endpoint
	adminEmail := testutils.RandomEmail()
	adminUser := testutils.CreateTestUser(t, userRepo) // Create a dummy user for the token ID/email
	adminToken := testutils.GenerateAdminToken(t, adminUser.ID, adminEmail)

	// Create a non-admin user and token for testing permissions
	nonAdminEmail := testutils.RandomEmail()
	nonAdminUser := testutils.CreateTestUser(t, userRepo)
	nonAdminToken, err := authService.GenerateToken(nonAdminUser.ID, nonAdminEmail, false)
	require.NoError(t, err)

	t.Run("Register_Success", func(t *testing.T) {
		registerReq := RegisterRequest{
			Email:    testutils.RandomEmail(),
			Password: "validpassword123",
		}

		// Make request to the correct path
		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/admin/register", registerReq)
		req.Header.Set("Authorization", "Bearer "+adminToken)
		resp := testutils.ExecuteRequest(t, router, req) // Use the full router

		testutils.CheckResponseCode(t, http.StatusCreated, resp.Code)

		// Parse response body - now returns RegisterResponse with user info and tokens
		var registerResp RegisterResponse
		testutils.ParseResponseBody(t, resp, &registerResp)

		// Verify response body content
		assert.Equal(t, registerReq.Email, registerResp.Email)
		assert.NotEmpty(t, registerResp.ID)           // ID should be assigned
		assert.NotEmpty(t, registerResp.Token)        // JWT token should be present
		assert.NotEmpty(t, registerResp.SessionToken) // Session token should be present

		// Verify user was actually created in the DB (optional, but good practice)
		createdUser, err := userRepo.GetByEmail(req.Context(), registerReq.Email)
		require.NoError(t, err)
		assert.Equal(t, registerReq.Email, createdUser.Email)
		// Optionally verify password hash if needed, though typically not done
	})

	t.Run("Register_Conflict", func(t *testing.T) {
		// Create a user with a specific email first
		existingEmail := testutils.RandomEmail()
		hashedPassword, err := testutils.HashPassword("password123")
		require.NoError(t, err)
		_ = testutils.CreateUserWithPassword(t, userRepo, existingEmail, hashedPassword)

		// Now try to register with the *same* existing email
		registerReq := RegisterRequest{
			Email:    existingEmail, // Use the email that was just created
			Password: "anotherpassword",
		}

		// Make request to the correct path
		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/admin/register", registerReq)
		req.Header.Set("Authorization", "Bearer "+adminToken)
		resp := testutils.ExecuteRequest(t, router, req) // Use the full router

		testutils.CheckResponseCode(t, http.StatusConflict, resp.Code)
	})

	t.Run("Register_InvalidPasswordLength", func(t *testing.T) {
		testCases := []struct {
			name string
			body RegisterRequest
		}{
			{"TooShort", RegisterRequest{Email: testutils.RandomEmail(), Password: "12345"}},
			{"TooLong", RegisterRequest{Email: testutils.RandomEmail(), Password: testutils.RandomString(41)}},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				// Make request to the correct path
				req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/admin/register", tc.body)
				req.Header.Set("Authorization", "Bearer "+adminToken)
				resp := testutils.ExecuteRequest(t, router, req) // Use the full router
				testutils.CheckResponseCode(t, http.StatusBadRequest, resp.Code)
			})
		}
	})

	t.Run("Register_MissingEmail", func(t *testing.T) {
		registerReq := RegisterRequest{
			Password: "validpassword",
		}
		// Make request to the correct path
		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/admin/register", registerReq)
		req.Header.Set("Authorization", "Bearer "+adminToken)
		resp := testutils.ExecuteRequest(t, router, req) // Use the full router
		testutils.CheckResponseCode(t, http.StatusBadRequest, resp.Code)
	})

	t.Run("Register_Unauthorized_NoToken", func(t *testing.T) {
		registerReq := RegisterRequest{
			Email:    testutils.RandomEmail(),
			Password: "validpassword",
		}
		// Make request to the correct path
		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/admin/register", registerReq)
		// No Authorization header
		resp := testutils.ExecuteRequest(t, router, req) // Use the full router
		testutils.CheckResponseCode(t, http.StatusUnauthorized, resp.Code)
	})

	t.Run("Register_Forbidden_NonAdminToken", func(t *testing.T) {
		registerReq := RegisterRequest{
			Email:    testutils.RandomEmail(),
			Password: "validpassword",
		}
		// Make request to the correct path
		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/admin/register", registerReq)
		req.Header.Set("Authorization", "Bearer "+nonAdminToken) // Use non-admin token
		resp := testutils.ExecuteRequest(t, router, req)         // Use the full router
		testutils.CheckResponseCode(t, http.StatusForbidden, resp.Code)
	})

	t.Run("Register_InvalidToken", func(t *testing.T) {
		registerReq := RegisterRequest{
			Email:    testutils.RandomEmail(),
			Password: "validpassword",
		}
		// Make request to the correct path
		req := testutils.MakeRequest(t, http.MethodPost, "/api/v1/auth/admin/register", registerReq)
		req.Header.Set("Authorization", "Bearer invalid-token-string")
		resp := testutils.ExecuteRequest(t, router, req) // Use the full router
		testutils.CheckResponseCode(t, http.StatusUnauthorized, resp.Code)
	})
}
