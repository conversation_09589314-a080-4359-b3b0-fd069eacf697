package api

import (
	"net/http"

	"github.com/chessticize/chessticize-server/internal/config"
	"github.com/chessticize/chessticize-server/internal/db"
	"github.com/chessticize/chessticize-server/internal/graphql"
	"github.com/chessticize/chessticize-server/internal/logger"
	"github.com/chessticize/chessticize-server/internal/middleware"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/service"
	"github.com/chessticize/chessticize-server/internal/verification"
	"github.com/go-chi/chi/v5"
	chimiddleware "github.com/go-chi/chi/v5/middleware"
	"github.com/go-chi/cors"
)

// RouterDependencies holds the dependencies required to set up the router.
type RouterDependencies struct {
	UserRepo                 repository.IUserRepository
	GameRepo                 repository.IGameRepository
	PuzzleRepo               repository.IPuzzleRepository
	TaskRepo                 repository.ITaskRepository
	PuzzleQueueRepo          repository.IPuzzleQueueRepository
	IdempotencyRepo          repository.IIdempotencyRepository
	InvitationCodeRepo       repository.IInvitationCodeRepository
	SessionTokenRepo         repository.ISessionTokenRepository
	EventRepo                repository.IEventRepository
	UserDailyStatsRepo       repository.IUserDailyStatsRepository
	UserEloRepo              repository.IUserEloRepository
	UserSprintDailyStatsRepo repository.IUserSprintDailyStatsRepository
	QuestRequirementRepo     repository.IQuestRequirementRepository
	AuthService              *service.AuthService
	EventService             *service.EventService
	FirebaseAuthService      *service.FirebaseAuthService
	SprintService            service.ISprintService
	PuzzleService            service.IPuzzleService
	RandomPuzzleService      *service.RandomPuzzleService
	ChessVerifier            verification.IChessProfileVerifier
	Config                   *config.Config
}

// NewRouter initializes dependencies and sets up the main application router.
func NewRouter(db *db.Database, cfg *config.Config) http.Handler {
	// Initialize repositories
	userRepo := repository.NewUserRepository(db.DB)
	gameRepo := repository.NewGameRepository(db.DB)
	puzzleRepo := repository.NewPuzzleRepository(db.DB)
	taskRepo := repository.NewTaskRepository(db.DB)
	puzzleQueueRepo := repository.NewPuzzleQueueRepository(db.DB)
	idempotencyRepo := repository.NewIdempotencyRepository(db.DB)
	invitationCodeRepo := repository.NewInvitationCodeRepository(db.DB)
	sessionTokenRepo := repository.NewSessionTokenRepository(db.DB)
	eventRepo := repository.NewEventRepository(db.DB)
	userDailyStatsRepo := repository.NewUserDailyStatsRepository(db.DB)
	userEloRepo := repository.NewUserEloRepository(db.DB)
	userSprintDailyStatsRepo := repository.NewUserSprintDailyStatsRepository(db.DB)
	questRequirementRepo := repository.NewQuestRequirementRepository(db.DB)

	// Sprint-related repositories
	sprintRepo := repository.NewSprintRepository(db.DB)
	sprintPuzzleRepo := repository.NewSprintPuzzleRepository(db.DB)
	sprintPuzzleAttemptRepo := repository.NewSprintPuzzleAttemptRepository(db.DB)
	eloHistoryRepo := repository.NewEloHistoryRepository(db.DB)
	lichessPuzzleRepo := repository.NewLichessPuzzleRepository(db.DB)
	userLichessPuzzleStatsRepo := repository.NewUserLichessPuzzleStatsRepository(db.DB)

	// Initialize services
	authService := service.NewAuthServiceWithSessionTokens(cfg.JWT, cfg.SessionToken, sessionTokenRepo)
	eventService := service.NewEventService(eventRepo)
	firebaseAuthService := service.NewFirebaseAuthService(cfg.Firebase)
	chessVerifier := verification.NewChessProfileVerifier()

	// Sprint-related services
	eloService := service.NewEloService(userEloRepo, eloHistoryRepo)
	eloTypeService := service.NewEloTypeService()
	puzzleService := service.NewPuzzleService(sprintRepo, sprintPuzzleRepo, sprintPuzzleAttemptRepo, lichessPuzzleRepo, userLichessPuzzleStatsRepo, userEloRepo, eventService)
	sprintService := service.NewSprintService(sprintRepo, sprintPuzzleRepo, userEloRepo, puzzleService, eloService, eloTypeService, eventService)

	// Random puzzle service
	userPuzzleStatsRepo := repository.NewUserPuzzleStatsRepository(db.DB)
	randomPuzzleService := service.NewRandomPuzzleService(lichessPuzzleRepo, puzzleRepo, userPuzzleStatsRepo, userLichessPuzzleStatsRepo)

	// Create dependency struct
	deps := &RouterDependencies{
		UserRepo:                 userRepo,
		GameRepo:                 gameRepo,
		PuzzleRepo:               puzzleRepo,
		TaskRepo:                 taskRepo,
		PuzzleQueueRepo:          puzzleQueueRepo,
		IdempotencyRepo:          idempotencyRepo,
		InvitationCodeRepo:       invitationCodeRepo,
		SessionTokenRepo:         sessionTokenRepo,
		EventRepo:                eventRepo,
		UserDailyStatsRepo:       userDailyStatsRepo,
		UserEloRepo:              userEloRepo,
		UserSprintDailyStatsRepo: userSprintDailyStatsRepo,
		QuestRequirementRepo:     questRequirementRepo,
		AuthService:              authService,
		EventService:             eventService,
		FirebaseAuthService:      firebaseAuthService,
		SprintService:            sprintService,
		PuzzleService:            puzzleService,
		RandomPuzzleService:      randomPuzzleService,
		ChessVerifier:            chessVerifier,
		Config:                   cfg,
	}

	return setupRouter(deps)
}

// setupRouter configures the chi router with middleware and routes using the provided dependencies.
// This function is designed to be testable.
func setupRouter(deps *RouterDependencies) http.Handler {
	r := chi.NewRouter()

	// Middleware
	r.Use(chimiddleware.Logger) // chi Logger to log requests
	r.Use(chimiddleware.Recoverer)
	r.Use(chimiddleware.RequestID) // Sets request ID
	r.Use(middleware.Logger)       // Uses request ID to create request-scoped logger
	r.Use(chimiddleware.RealIP)

	// CORS configuration
	r.Use(cors.Handler(cors.Options{
		AllowedOrigins:   []string{"https://*", "http://*"},
		AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders:   []string{"Accept", "Authorization", "Content-Type", "X-CSRF-Token", middleware.IdempotencyKeyHeader}, // Add Idempotency-Key
		ExposedHeaders:   []string{"Link", chimiddleware.RequestIDHeader},                                                      // Expose the request ID header
		AllowCredentials: true,
		MaxAge:           300,
	}))

	// Health check
	r.Get("/health", healthCheck)

	// API routes
	r.Route("/api/v1", func(r chi.Router) {
		// Authentication routes (login and register are public)
		// Pass repos, authService, eventService, firebaseAuthService, and cfg from deps
		r.Mount("/auth", AuthRoutes(deps.UserRepo, deps.InvitationCodeRepo, deps.AuthService, deps.EventService, deps.FirebaseAuthService, deps.Config))

		// User-specific routes (authenticated)
		r.Route("/users/me", func(r chi.Router) {
			r.Use(middleware.JWTAuth(deps.Config.JWT))
			r.Use(middleware.Idempotency(deps.IdempotencyRepo)) // Apply Idempotency middleware AFTER auth

			// Initialize handler with all required repos from deps
			hUser := NewUserHandler(deps.UserRepo, deps.GameRepo, deps.PuzzleRepo, deps.TaskRepo, deps.PuzzleQueueRepo, deps.UserDailyStatsRepo, deps.UserEloRepo, deps.UserSprintDailyStatsRepo, deps.QuestRequirementRepo, deps.ChessVerifier, deps.EventService)

			// Get current user details
			r.Get("/", hUser.GetMe)

			// Chess Profiles for the current user
			r.Route("/chess-profiles", func(r chi.Router) {
				r.Get("/", hUser.ListMyChessProfiles)
				r.Post("/", hUser.CreateMyChessProfile)
				r.Delete("/{profileID}", hUser.DeleteMyChessProfile)
			})

			// Games for the current user
			r.Get("/games", hUser.ListMyGames)

			// Puzzles for the current user
			r.Get("/puzzles", hUser.ListMyPuzzles)
			r.Post("/puzzles/{puzzleID}/attempts", hUser.PostPuzzleAttempt)

			// Lichess puzzles
			r.Post("/lichess-puzzles/{puzzleID}/attempts", hUser.PostLichessPuzzleAttempt)

			// Sprint endpoints
			r.Mount("/sprint", SprintRoutes(deps.SprintService, deps.PuzzleService))

			// Random puzzle endpoints
			r.Mount("/random-puzzles", RandomPuzzleRoutes(deps.RandomPuzzleService))

			// Puzzle queue endpoints
			r.Mount("/puzzle-queue", PuzzleQueueRoutes(deps.PuzzleQueueRepo, deps.PuzzleRepo))

			// Events for the current user
			eventHandler := NewEventHandler(deps.EventRepo)
			r.Get("/events", eventHandler.ListMyEvents)
		})

		// GraphQL API
		r.Route("/graphql", func(r chi.Router) {
			// GraphQL API endpoint
			r.Mount("/query", graphql.GraphQLHandler(deps.UserRepo, deps.GameRepo, deps.PuzzleRepo, deps.Config.JWT))
		})

		// Admin-only routes requiring JWT authentication
		r.Route("/admin", func(r chi.Router) {
			// Apply admin authentication middleware
			r.Use(middleware.JWTAdminOnly(deps.Config.JWT))     // Use JWTAdminOnly middleware
			r.Use(middleware.Idempotency(deps.IdempotencyRepo)) // Apply Idempotency middleware AFTER auth

			// Users (pass repos from deps)
			r.Mount("/users", UserRoutes(deps.UserRepo, deps.GameRepo, deps.PuzzleRepo, deps.TaskRepo, deps.PuzzleQueueRepo, deps.UserDailyStatsRepo, deps.UserEloRepo, deps.UserSprintDailyStatsRepo, deps.QuestRequirementRepo, deps.ChessVerifier, deps.EventService))

			// Games
			r.Mount("/games", GameRoutes(deps.GameRepo))

			// Puzzles
			r.Mount("/puzzles", PuzzleRoutes(deps.PuzzleRepo))

			// Tasks
			r.Mount("/tasks", TaskRoutes(deps.TaskRepo))

			// Sprint admin endpoints
			r.Mount("/sprints", AdminSprintRoutes(deps.SprintService))

			// Quest requirements
			r.Mount("/quest-requirements", AdminQuestRoutes(deps.QuestRequirementRepo))
		})
	})

	return r
}

func healthCheck(w http.ResponseWriter, r *http.Request) {
	// Use logger from context if available, otherwise default log
	log := logger.FromContext(r.Context()) // Get logger from context
	w.Header().Set("Content-Type", "text/plain")
	if _, err := w.Write([]byte("OK")); err != nil {
		log.Error().Err(err).Msg("Error writing health check response")
	}
}
