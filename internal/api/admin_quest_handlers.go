package api

import (
	"encoding/json"
	"net/http"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/go-chi/chi/v5"
	"github.com/go-chi/render"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type AdminQuestHandler struct {
	questRepo repository.IQuestRequirementRepository
}

func NewAdminQuestHandler(questRepo repository.IQuestRequirementRepository) *AdminQuestHandler {
	return &AdminQuestHandler{
		questRepo: questRepo,
	}
}

type CreateQuestRequirementRequest struct {
	Type        string `json:"type"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Target      int    `json:"target"`
	IsActive    bool   `json:"is_active"`
}

type UpdateQuestRequirementRequest struct {
	Name        *string `json:"name,omitempty"`
	Description *string `json:"description,omitempty"`
	Target      *int    `json:"target,omitempty"`
	IsActive    *bool   `json:"is_active,omitempty"`
}

// ListQuestRequirements handles GET /api/admin/quest-requirements
func (h *AdminQuestHandler) ListQuestRequirements(w http.ResponseWriter, r *http.Request) {
	questRequirements, err := h.questRepo.ListQuestRequirements(r.Context())
	if err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to retrieve quest requirements")
		return
	}

	render.JSON(w, r, questRequirements)
}

// GetQuestRequirement handles GET /api/admin/quest-requirements/{id}
func (h *AdminQuestHandler) GetQuestRequirement(w http.ResponseWriter, r *http.Request) {
	id := chi.URLParam(r, "id")
	if id == "" {
		apiError(w, r, http.StatusBadRequest, nil, "Quest requirement ID is required")
		return
	}

	questRequirement, err := h.questRepo.GetQuestRequirementByID(r.Context(), id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			apiError(w, r, http.StatusNotFound, nil, "Quest requirement not found")
		} else {
			apiError(w, r, http.StatusInternalServerError, err, "Failed to retrieve quest requirement")
		}
		return
	}

	render.JSON(w, r, questRequirement)
}

// CreateQuestRequirement handles POST /api/admin/quest-requirements
func (h *AdminQuestHandler) CreateQuestRequirement(w http.ResponseWriter, r *http.Request) {
	var req CreateQuestRequirementRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		apiError(w, r, http.StatusBadRequest, err, "Invalid request body")
		return
	}

	// Validate required fields
	if req.Type == "" || req.Name == "" {
		apiError(w, r, http.StatusBadRequest, nil, "Type and name are required")
		return
	}

	// Validate quest type
	if req.Type != "sprint" && req.Type != "arrow_duel" {
		apiError(w, r, http.StatusBadRequest, nil, "Invalid quest type. Must be 'sprint' or 'arrow_duel'")
		return
	}

	// Set default target if not provided
	if req.Target <= 0 {
		req.Target = 1
	}

	questRequirement := &models.DailyQuestRequirement{
		ID:          uuid.New().String(),
		Type:        req.Type,
		Name:        req.Name,
		Description: req.Description,
		Target:      req.Target,
		IsActive:    req.IsActive,
	}

	if err := h.questRepo.CreateQuestRequirement(r.Context(), questRequirement); err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to create quest requirement")
		return
	}

	render.Status(r, http.StatusCreated)
	render.JSON(w, r, questRequirement)
}

// UpdateQuestRequirement handles PUT /api/admin/quest-requirements/{id}
func (h *AdminQuestHandler) UpdateQuestRequirement(w http.ResponseWriter, r *http.Request) {
	id := chi.URLParam(r, "id")
	if id == "" {
		apiError(w, r, http.StatusBadRequest, nil, "Quest requirement ID is required")
		return
	}

	var req UpdateQuestRequirementRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		apiError(w, r, http.StatusBadRequest, err, "Invalid request body")
		return
	}

	// Get existing quest requirement
	questRequirement, err := h.questRepo.GetQuestRequirementByID(r.Context(), id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			apiError(w, r, http.StatusNotFound, nil, "Quest requirement not found")
		} else {
			apiError(w, r, http.StatusInternalServerError, err, "Failed to retrieve quest requirement")
		}
		return
	}

	// Update fields if provided
	if req.Name != nil {
		questRequirement.Name = *req.Name
	}
	if req.Description != nil {
		questRequirement.Description = *req.Description
	}
	if req.Target != nil {
		if *req.Target <= 0 {
			apiError(w, r, http.StatusBadRequest, nil, "Target must be greater than 0")
			return
		}
		questRequirement.Target = *req.Target
	}
	if req.IsActive != nil {
		questRequirement.IsActive = *req.IsActive
	}

	if err := h.questRepo.UpdateQuestRequirement(r.Context(), questRequirement); err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to update quest requirement")
		return
	}

	render.JSON(w, r, questRequirement)
}

// DeleteQuestRequirement handles DELETE /api/admin/quest-requirements/{id}
func (h *AdminQuestHandler) DeleteQuestRequirement(w http.ResponseWriter, r *http.Request) {
	id := chi.URLParam(r, "id")
	if id == "" {
		apiError(w, r, http.StatusBadRequest, nil, "Quest requirement ID is required")
		return
	}

	// Check if quest requirement exists
	_, err := h.questRepo.GetQuestRequirementByID(r.Context(), id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			apiError(w, r, http.StatusNotFound, nil, "Quest requirement not found")
		} else {
			apiError(w, r, http.StatusInternalServerError, err, "Failed to retrieve quest requirement")
		}
		return
	}

	if err := h.questRepo.DeleteQuestRequirement(r.Context(), id); err != nil {
		apiError(w, r, http.StatusInternalServerError, err, "Failed to delete quest requirement")
		return
	}

	w.WriteHeader(http.StatusNoContent)
}

// AdminQuestRoutes creates routes for admin quest management
func AdminQuestRoutes(questRepo repository.IQuestRequirementRepository) http.Handler {
	h := NewAdminQuestHandler(questRepo)
	r := chi.NewRouter()

	r.Get("/", h.ListQuestRequirements)
	r.Post("/", h.CreateQuestRequirement)
	r.Get("/{id}", h.GetQuestRequirement)
	r.Put("/{id}", h.UpdateQuestRequirement)
	r.Delete("/{id}", h.DeleteQuestRequirement)

	return r
}
