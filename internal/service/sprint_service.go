package service

import (
	"context"
	"fmt"
	"time"

	"github.com/chessticize/chessticize-server/internal/logger"
	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/google/uuid"
)

type SprintService struct {
	sprintRepo       repository.ISprintRepository
	sprintPuzzleRepo repository.ISprintPuzzleRepository
	eloRepo          repository.IUserEloRepository
	puzzleService    IPuzzleService
	eloService       IEloService
	eloTypeService   IEloTypeService
	eventService     *EventService
}

func NewSprintService(
	sprintRepo repository.ISprintRepository,
	sprintPuzzleRepo repository.ISprintPuzzleRepository,
	eloRepo repository.IUserEloRepository,
	puzzleService IPuzzleService,
	eloService IEloService,
	eloTypeService IEloTypeService,
	eventService *EventService,
) ISprintService {
	return &SprintService{
		sprintRepo:       sprintRepo,
		sprintPuzzleRepo: sprintPuzzleRepo,
		eloRepo:          eloRepo,
		puzzleService:    puzzleService,
		eloService:       eloService,
		eloTypeService:   eloTypeService,
		eventService:     eventService,
	}
}

// StartSprint creates a new sprint session for a user
func (s *SprintService) StartSprint(ctx context.Context, userID string, eloType string) (*models.Sprint, error) {
	// Validate and get ELO type configuration
	eloConfig, err := s.eloTypeService.GetEloTypeConfig(eloType)
	if err != nil {
		return nil, fmt.Errorf("invalid ELO type: %w", err)
	}

	// Get or initialize user's ELO rating
	userElo, err := s.eloService.GetUserElo(ctx, userID, eloType)
	if err != nil {
		if err == repository.ErrNotFound {
			// Initialize ELO if it doesn't exist
			userElo, err = s.eloService.InitializeUserElo(ctx, userID, eloType)
			if err != nil {
				return nil, fmt.Errorf("failed to initialize user ELO: %w", err)
			}
		} else {
			return nil, fmt.Errorf("failed to get user ELO: %w", err)
		}
	}

	// Create the sprint with configuration from ELO type
	sprint := &models.Sprint{
		ID:               uuid.New().String(),
		UserID:           userID,
		EloType:          eloType,
		Status:           models.SprintStatusActive,
		TargetPuzzles:    eloConfig.TargetPuzzles,
		TimeLimitSeconds: eloConfig.DurationSeconds,
		MaxMistakes:      eloConfig.MaxMistakes,
		PuzzlesSolved:    0,
		MistakesMade:     0,
		StartedAt:        time.Now(),
		EloRatingBefore:  userElo.Rating,
	}

	err = s.sprintRepo.Create(ctx, sprint)
	if err != nil {
		return nil, fmt.Errorf("failed to create sprint: %w", err)
	}

	// Create sprint start event
	err = s.eventService.CreatePuzzleSprintStartEvent(ctx, userID, sprint.ID)
	if err != nil {
		// Log error but don't fail the sprint creation
		log := logger.FromContext(ctx)
		log.Error().Err(err).Str("sprint_id", sprint.ID).Str("user_id", userID).Msg("Failed to create sprint start event")
	}

	return sprint, nil
}

// GetSprintState retrieves the current state of a sprint session
func (s *SprintService) GetSprintState(ctx context.Context, sprintID string, userID string) (*models.Sprint, error) {
	sprint, err := s.GetSprintWithValidation(ctx, sprintID, userID)
	if err != nil {
		return nil, err
	}

	return sprint, nil
}

// GetNextPuzzles generates and returns the next batch of puzzles for a sprint
func (s *SprintService) GetNextPuzzles(ctx context.Context, sprintID string, userID string, count int) ([]models.LichessPuzzle, error) {
	// Validate sprint access and get current state
	sprint, err := s.GetSprintWithValidation(ctx, sprintID, userID)
	if err != nil {
		return nil, err
	}

	// Check if sprint is still active
	if sprint.Status != models.SprintStatusActive {
		return nil, fmt.Errorf("sprint is not active")
	}

	// Get ELO type configuration for theme filtering
	eloConfig, err := s.eloTypeService.GetEloTypeConfig(sprint.EloType)
	if err != nil {
		return nil, fmt.Errorf("invalid ELO type: %w", err)
	}

	// Get count of existing puzzles for sequence calculation
	existingCount, err := s.sprintPuzzleRepo.CountBySprintID(ctx, sprintID)
	if err != nil {
		return nil, fmt.Errorf("failed to count existing sprint puzzles: %w", err)
	}

	// Get already generated puzzle IDs to exclude them
	existingPuzzles, err := s.sprintPuzzleRepo.GetBySprintID(ctx, sprintID)
	if err != nil {
		return nil, fmt.Errorf("failed to get existing sprint puzzles: %w", err)
	}

	excludeIDs := make([]string, len(existingPuzzles))
	for i, puzzle := range existingPuzzles {
		excludeIDs[i] = puzzle.LichessPuzzleID
	}

	// Calculate the next sequence number based on existing puzzles count
	nextSequenceStart := existingCount + 1

	// Select puzzles with theme filtering
	var themes []string
	if eloConfig.Theme != "" {
		themes = []string{eloConfig.Theme}
	}

	puzzles, err := s.puzzleService.SelectPuzzlesForUser(ctx, userID, sprint.EloType, count, excludeIDs, themes)
	if err != nil {
		return nil, fmt.Errorf("failed to select puzzles for sprint: %w", err)
	}

	// Create sprint puzzle records
	sprintPuzzles := make([]models.SprintPuzzle, len(puzzles))
	for i, puzzle := range puzzles {
		sprintPuzzles[i] = models.SprintPuzzle{
			ID:               uuid.New().String(),
			SprintID:         sprint.ID,
			LichessPuzzleID:  puzzle.ID,
			SequenceInSprint: nextSequenceStart + i,
		}
	}

	err = s.sprintPuzzleRepo.CreateBatch(ctx, sprintPuzzles)
	if err != nil {
		return nil, fmt.Errorf("failed to create sprint puzzles: %w", err)
	}

	return puzzles, nil
}

// GetSprintPuzzleCount returns the count of puzzles generated for a sprint
func (s *SprintService) GetSprintPuzzleCount(ctx context.Context, sprintID string, userID string) (int, error) {
	// Validate sprint access
	_, err := s.GetSprintWithValidation(ctx, sprintID, userID)
	if err != nil {
		return 0, err
	}

	// Get count of existing puzzles
	count, err := s.sprintPuzzleRepo.CountBySprintID(ctx, sprintID)
	if err != nil {
		return 0, fmt.Errorf("failed to count sprint puzzles: %w", err)
	}

	return count, nil
}

// EndSprint ends a sprint session and calculates final results
func (s *SprintService) EndSprint(ctx context.Context, sprintID string, userID string) (*models.Sprint, error) {
	sprint, err := s.GetSprintWithValidation(ctx, sprintID, userID)
	if err != nil {
		return nil, err
	}

	// Check if sprint is already ended
	if sprint.Status != models.SprintStatusActive {
		return sprint, nil
	}

	// Calculate final status
	endTime := time.Now()
	duration := int(endTime.Sub(sprint.StartedAt).Seconds())

	var finalStatus models.SprintStatus
	sprintWon := false

	// Determine success based on completion and mistakes
	// We trust the client-side timer gating, so we only need to check:
	// 1. If puzzles solved >= target AND mistakes <= max mistakes -> success
	// 2. If puzzles solved < target AND mistakes <= max mistakes -> timeout
	// 3. If mistakes > max mistakes -> fail by mistakes
	if sprint.MistakesMade > sprint.MaxMistakes {
		finalStatus = models.SprintStatusCompletedFailMistakes
	} else if sprint.PuzzlesSolved >= sprint.TargetPuzzles {
		finalStatus = models.SprintStatusCompletedSuccess
		sprintWon = true
	} else {
		// puzzles solved < target AND mistakes <= max mistakes -> timeout
		finalStatus = models.SprintStatusCompletedFailTime
	}

	// Calculate ELO change
	eloChange, err := s.eloService.CalculateSprintRatingChange(ctx, userID, sprint.EloType, sprintWon)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate ELO change: %w", err)
	}

	// Update sprint with final results
	sprint.Status = finalStatus
	sprint.EndedAt = &endTime
	sprint.DurationSeconds = &duration
	sprint.EloRatingAfter = &eloChange.RatingAfter
	sprint.EloChange = &eloChange.RatingChange

	err = s.sprintRepo.Update(ctx, sprint)
	if err != nil {
		return nil, fmt.Errorf("failed to update sprint: %w", err)
	}

	// Update user's ELO rating
	err = s.eloService.UpdateUserElo(ctx, userID, sprint.EloType, eloChange, sprintID)
	if err != nil {
		return nil, fmt.Errorf("failed to update user ELO: %w", err)
	}

	// Determine attempt type from ELO type
	eloConfig, err := s.eloTypeService.GetEloTypeConfig(sprint.EloType)
	if err != nil {
		return nil, fmt.Errorf("failed to get ELO type config: %w", err)
	}

	// Create sprint end event
	failedPuzzles := sprint.MistakesMade
	err = s.eventService.CreatePuzzleSprintEndEvent(ctx, userID, sprintID, finalStatus, duration, sprint.PuzzlesSolved, failedPuzzles, sprint.EloType, eloChange.RatingChange, eloConfig.AttemptType)
	if err != nil {
		// Log error but don't fail the sprint completion
		log := logger.FromContext(ctx)
		log.Error().Err(err).Str("sprint_id", sprintID).Str("user_id", userID).Str("status", string(finalStatus)).Msg("Failed to create sprint end event")
	}

	return sprint, nil
}

// EndSprintWithClientResults ends a sprint session with optional client-provided final results
func (s *SprintService) EndSprintWithClientResults(ctx context.Context, sprintID string, userID string, clientPuzzlesSolved *int, clientMistakesMade *int) (*models.Sprint, error) {
	sprint, err := s.GetSprintWithValidation(ctx, sprintID, userID)
	if err != nil {
		return nil, err
	}

	// Check if sprint is already ended
	if sprint.Status != models.SprintStatusActive {
		return sprint, nil
	}

	// Use client-provided results if available, otherwise use server-calculated values
	finalPuzzlesSolved := sprint.PuzzlesSolved
	finalMistakesMade := sprint.MistakesMade

	if clientPuzzlesSolved != nil {
		finalPuzzlesSolved = *clientPuzzlesSolved
		// Update the sprint record with client-provided value
		sprint.PuzzlesSolved = finalPuzzlesSolved
	}

	if clientMistakesMade != nil {
		finalMistakesMade = *clientMistakesMade
		// Update the sprint record with client-provided value
		sprint.MistakesMade = finalMistakesMade
	}

	// Calculate final status based on final results
	endTime := time.Now()
	duration := int(endTime.Sub(sprint.StartedAt).Seconds())

	var finalStatus models.SprintStatus
	sprintWon := false

	// Determine success based on completion and mistakes
	if finalMistakesMade > sprint.MaxMistakes {
		finalStatus = models.SprintStatusCompletedFailMistakes
	} else if finalPuzzlesSolved >= sprint.TargetPuzzles {
		finalStatus = models.SprintStatusCompletedSuccess
		sprintWon = true
	} else {
		// puzzles solved < target AND mistakes <= max mistakes -> timeout
		finalStatus = models.SprintStatusCompletedFailTime
	}

	// Calculate ELO change
	eloChange, err := s.eloService.CalculateSprintRatingChange(ctx, userID, sprint.EloType, sprintWon)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate ELO change: %w", err)
	}

	// Update sprint with final results
	sprint.Status = finalStatus
	sprint.EndedAt = &endTime
	sprint.DurationSeconds = &duration
	sprint.EloRatingAfter = &eloChange.RatingAfter
	sprint.EloChange = &eloChange.RatingChange

	err = s.sprintRepo.Update(ctx, sprint)
	if err != nil {
		return nil, fmt.Errorf("failed to update sprint: %w", err)
	}

	// Update user's ELO rating
	err = s.eloService.UpdateUserElo(ctx, userID, sprint.EloType, eloChange, sprintID)
	if err != nil {
		return nil, fmt.Errorf("failed to update user ELO: %w", err)
	}

	// Determine attempt type from ELO type
	eloConfig, err := s.eloTypeService.GetEloTypeConfig(sprint.EloType)
	if err != nil {
		return nil, fmt.Errorf("failed to get ELO type config: %w", err)
	}

	// Create sprint end event
	failedPuzzles := finalMistakesMade
	err = s.eventService.CreatePuzzleSprintEndEvent(ctx, userID, sprintID, finalStatus, duration, finalPuzzlesSolved, failedPuzzles, sprint.EloType, eloChange.RatingChange, eloConfig.AttemptType)
	if err != nil {
		// Log error but don't fail the sprint completion
		log := logger.FromContext(ctx)
		log.Error().Err(err).Str("sprint_id", sprintID).Str("user_id", userID).Str("status", string(finalStatus)).Msg("Failed to create sprint end event")
	}

	return sprint, nil
}

// GetSprintPuzzles retrieves sprint puzzles with attempt information and filtering
func (s *SprintService) GetSprintPuzzles(ctx context.Context, sprintID string, userID string, filter repository.SprintPuzzleFilter, offset int, limit int) ([]repository.SprintPuzzleWithAttempt, int64, error) {
	// Validate sprint access
	err := s.ValidateSprintAccess(ctx, sprintID, userID)
	if err != nil {
		return nil, 0, err
	}

	// Get sprint puzzles with attempt information
	return s.sprintPuzzleRepo.GetSprintPuzzlesWithAttempts(ctx, sprintID, userID, filter, offset, limit)
}

// GetSprintWithValidation retrieves a sprint and validates user access
func (s *SprintService) GetSprintWithValidation(ctx context.Context, sprintID string, userID string) (*models.Sprint, error) {
	sprint, err := s.sprintRepo.GetByID(ctx, sprintID)
	if err != nil {
		return nil, fmt.Errorf("failed to get sprint: %w", err)
	}

	if sprint.UserID != userID {
		return nil, fmt.Errorf("user does not have access to this sprint")
	}

	return sprint, nil
}

// ValidateSprintAccess validates that a user has access to a specific sprint
func (s *SprintService) ValidateSprintAccess(ctx context.Context, sprintID string, userID string) error {
	_, err := s.GetSprintWithValidation(ctx, sprintID, userID)
	return err
}

// CleanupAbandonedSprints marks sprints as abandoned if they exceed the timeout
func (s *SprintService) CleanupAbandonedSprints(ctx context.Context, graceMinutes int, limit int) (int64, error) {
	cutoffTime := time.Now().Add(-time.Duration(graceMinutes) * time.Minute)
	return s.sprintRepo.MarkAbandonedSprints(ctx, cutoffTime, limit)
}
