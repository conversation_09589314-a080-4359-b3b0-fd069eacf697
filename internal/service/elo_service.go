package service

import (
	"context"
	"fmt"
	"math"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/google/uuid"
)

const (
	// Glicko-2 constants
	DefaultRating          = 600.0
	DefaultRatingDeviation = 100.0
	DefaultOpponentRD      = 50.0
	DefaultVolatility      = 0.06
	SystemConstant         = 0.5 // tau in Glicko-2
	ConvergenceTolerance   = 0.000001
	MinimumRating          = int(DefaultRating) // Minimum ELO floor - players cannot go below this
)

type EloService struct {
	eloRepo        repository.IUserEloRepository
	eloHistoryRepo repository.IEloHistoryRepository
}

func NewEloService(
	eloRepo repository.IUserEloRepository,
	eloHistoryRepo repository.IEloHistoryRepository,
) IEloService {
	return &EloService{
		eloRepo:        eloRepo,
		eloHistoryRepo: eloHistoryRepo,
	}
}

// CalculateSprintRatingChange calculates the ELO rating change for a sprint result using Glicko-2
func (s *EloService) CalculateSprintRatingChange(ctx context.Context, userID string, eloType string, sprintWon bool) (*EloChange, error) {
	// Get current user ELO
	userElo, err := s.GetUserElo(ctx, userID, eloType)
	if err != nil {
		return nil, fmt.Errorf("failed to get user ELO: %w", err)
	}

	// Convert to Glicko-2 scale
	mu := (float64(userElo.Rating) - DefaultRating) / 173.7178
	phi := userElo.RatingDeviation / 173.7178
	sigma := userElo.Volatility

	// For sprint, we treat it as a game against a "system" with rating equal to user's rating
	// This creates a balanced opponent for rating calculation
	opponentRating := float64(userElo.Rating)
	opponentRD := DefaultOpponentRD

	opponentMu := (opponentRating - DefaultRating) / 173.7178
	opponentPhi := opponentRD / 173.7178

	// Calculate expected score
	g := 1.0 / math.Sqrt(1.0+3.0*opponentPhi*opponentPhi/math.Pi/math.Pi)
	E := 1.0 / (1.0 + math.Exp(-g*(mu-opponentMu)))

	// Actual score (1 for win, 0 for loss)
	var score float64
	if sprintWon {
		score = 1.0
	} else {
		score = 0.0
	}

	// Calculate variance
	v := 1.0 / (g * g * E * (1.0 - E))

	// Calculate delta
	delta := v * g * (score - E)

	// Update volatility using iterative algorithm
	a := math.Log(sigma * sigma)

	f := func(x float64) float64 {
		ex := math.Exp(x)
		return ex*(delta*delta-phi*phi-v-ex)/(2.0*math.Pow(phi*phi+v+ex, 2.0)) - (x-a)/(SystemConstant*SystemConstant)
	}

	A := a
	var B float64
	if delta*delta > phi*phi+v {
		B = math.Log(delta*delta - phi*phi - v)
	} else {
		k := 1.0
		for f(a-k*SystemConstant) < 0 {
			k++
		}
		B = a - k*SystemConstant
	}

	fA := f(A)
	fB := f(B)

	// Iterative process to find new volatility
	for math.Abs(B-A) > ConvergenceTolerance {
		C := A + (A-B)*fA/(fB-fA)
		fC := f(C)
		if fC*fB < 0 {
			A = B
			fA = fB
		} else {
			fA = fA / 2.0
		}
		B = C
		fB = fC
	}

	newSigma := math.Exp(A / 2.0)

	// Update rating deviation
	phiStar := math.Sqrt(phi*phi + newSigma*newSigma)
	newPhi := 1.0 / math.Sqrt(1.0/phiStar/phiStar+1.0/v)

	// Update rating
	newMu := mu + newPhi*newPhi*g*(score-E)

	// Convert back to original scale
	newRating := int(math.Round(newMu*173.7178 + DefaultRating))
	newRatingDeviation := newPhi * 173.7178

	// Ensure rating doesn't go below minimum floor
	if newRating < MinimumRating {
		newRating = MinimumRating
	}

	return &EloChange{
		RatingBefore:          userElo.Rating,
		RatingAfter:           newRating,
		RatingChange:          newRating - userElo.Rating,
		RatingDeviationBefore: userElo.RatingDeviation,
		RatingDeviationAfter:  newRatingDeviation,
	}, nil
}

// UpdateUserElo updates a user's ELO rating and creates a history record
func (s *EloService) UpdateUserElo(ctx context.Context, userID string, eloType string, eloChange *EloChange, sprintID string) error {
	// Get current ELO record
	userElo, err := s.eloRepo.GetByUserIDAndEloType(ctx, userID, eloType)
	if err != nil {
		return fmt.Errorf("failed to get user ELO: %w", err)
	}

	// Update ELO values
	userElo.Rating = eloChange.RatingAfter
	userElo.RatingDeviation = eloChange.RatingDeviationAfter
	userElo.GamesPlayed++
	userElo.LastActiveAt = time.Now()

	// Update volatility (simplified - in full Glicko-2 this would be calculated)
	// For now, gradually decrease volatility as more games are played
	if userElo.GamesPlayed > 10 {
		userElo.Volatility = math.Max(0.04, userElo.Volatility*0.99)
	}

	// Save updated ELO
	err = s.eloRepo.Save(ctx, userElo)
	if err != nil {
		return fmt.Errorf("failed to save user ELO: %w", err)
	}

	// Create history record
	eloHistory := &models.EloHistory{
		ID:                    uuid.New().String(),
		UserID:                userID,
		EloType:               eloType,
		RatingBefore:          eloChange.RatingBefore,
		RatingAfter:           eloChange.RatingAfter,
		RatingChange:          eloChange.RatingChange,
		RatingDeviationBefore: eloChange.RatingDeviationBefore,
		RatingDeviationAfter:  eloChange.RatingDeviationAfter,
		SprintID:              sprintID,
		CreatedAt:             time.Now(),
	}

	err = s.eloHistoryRepo.Create(ctx, eloHistory)
	if err != nil {
		return fmt.Errorf("failed to create ELO history: %w", err)
	}

	return nil
}

// GetUserElo retrieves a user's current ELO rating for a specific type
func (s *EloService) GetUserElo(ctx context.Context, userID string, eloType string) (*models.UserElo, error) {
	return s.eloRepo.GetByUserIDAndEloType(ctx, userID, eloType)
}

// InitializeUserElo creates an initial ELO rating for a user if it doesn't exist
func (s *EloService) InitializeUserElo(ctx context.Context, userID string, eloType string) (*models.UserElo, error) {
	userElo := &models.UserElo{
		UserID:          userID,
		EloType:         eloType,
		Rating:          int(DefaultRating),
		RatingDeviation: DefaultRatingDeviation,
		Volatility:      DefaultVolatility,
		GamesPlayed:     0,
		LastActiveAt:    time.Now(),
	}

	err := s.eloRepo.Save(ctx, userElo)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize user ELO: %w", err)
	}

	return userElo, nil
}
