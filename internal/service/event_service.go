package service

import (
	"context"
	"encoding/json"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/google/uuid"
)

// EventService provides business logic for event operations
type EventService struct {
	eventRepo repository.IEventRepository
}

// NewEventService creates a new event service
func NewEventService(eventRepo repository.IEventRepository) *EventService {
	return &EventService{
		eventRepo: eventRepo,
	}
}

// CreateSignInEvent creates a sign-in event for a user
func (s *EventService) CreateSignInEvent(ctx context.Context, userID string, data models.SignInEventData) error {
	eventData, err := json.Marshal(data)
	if err != nil {
		return err
	}

	event := &models.Event{
		ID:        uuid.New().String(),
		UserID:    userID,
		EventType: models.EventTypeSignIn,
		EventData: eventData,
		EventTime: time.Now(),
	}

	return s.eventRepo.Create(ctx, event)
}

// CreateSignInSuccessEvent creates a successful sign-in event
func (s *EventService) CreateSignInSuccessEvent(ctx context.Context, userID string, signInType string, userAgent string) error {
	data := models.SignInEventData{
		Succeeded:  true,
		SignInType: signInType,
		UserAgent:  userAgent,
	}

	eventData, err := json.Marshal(data)
	if err != nil {
		return err
	}

	subType := models.EventSubTypeSuccess
	event := &models.Event{
		ID:           uuid.New().String(),
		UserID:       userID,
		EventType:    models.EventTypeSignIn,
		EventSubType: &subType,
		EventData:    eventData,
		EventTime:    time.Now(),
	}

	return s.eventRepo.Create(ctx, event)
}

// CreateRegistrationEvent creates a registration event for a user
func (s *EventService) CreateRegistrationEvent(ctx context.Context, userID string, signInType string, userAgent string) error {
	data := models.RegistrationEventData{
		SignInType: signInType,
		UserAgent:  userAgent,
	}

	eventData, err := json.Marshal(data)
	if err != nil {
		return err
	}

	event := &models.Event{
		ID:        uuid.New().String(),
		UserID:    userID,
		EventType: models.EventTypeRegistration,
		EventData: eventData,
		EventTime: time.Now(),
	}

	return s.eventRepo.Create(ctx, event)
}

// CreateSignInFailureEvent creates a failed sign-in event
func (s *EventService) CreateSignInFailureEvent(ctx context.Context, userID string, signInType string, userAgent string) error {
	data := models.SignInEventData{
		Succeeded:  false,
		SignInType: signInType,
		UserAgent:  userAgent,
	}

	eventData, err := json.Marshal(data)
	if err != nil {
		return err
	}

	subType := models.EventSubTypeFailure
	event := &models.Event{
		ID:           uuid.New().String(),
		UserID:       userID,
		EventType:    models.EventTypeSignIn,
		EventSubType: &subType,
		EventData:    eventData,
		EventTime:    time.Now(),
	}

	return s.eventRepo.Create(ctx, event)
}

// CreateGameReviewStartEvent creates a game review start event
func (s *EventService) CreateGameReviewStartEvent(ctx context.Context, userID string, gameID string) error {
	data := models.GameReviewEventData{
		GameID: gameID,
	}

	eventData, err := json.Marshal(data)
	if err != nil {
		return err
	}

	subType := models.EventSubTypeStart
	event := &models.Event{
		ID:           uuid.New().String(),
		UserID:       userID,
		EventType:    models.EventTypeGameReview,
		EventSubType: &subType,
		EventData:    eventData,
		EventTime:    time.Now(),
	}

	return s.eventRepo.Create(ctx, event)
}

// CreateGameReviewEndEvent creates a game review end event
func (s *EventService) CreateGameReviewEndEvent(ctx context.Context, userID string, gameID string, duration int) error {
	data := models.GameReviewEventData{
		GameID:   gameID,
		Duration: &duration,
	}

	eventData, err := json.Marshal(data)
	if err != nil {
		return err
	}

	subType := models.EventSubTypeEnd
	event := &models.Event{
		ID:           uuid.New().String(),
		UserID:       userID,
		EventType:    models.EventTypeGameReview,
		EventSubType: &subType,
		EventData:    eventData,
		EventTime:    time.Now(),
	}

	return s.eventRepo.Create(ctx, event)
}

// CreatePuzzleSprintStartEvent creates a puzzle sprint start event
func (s *EventService) CreatePuzzleSprintStartEvent(ctx context.Context, userID string, sprintID string) error {
	data := models.PuzzleSprintEventData{
		SprintID: sprintID,
	}

	eventData, err := json.Marshal(data)
	if err != nil {
		return err
	}

	subType := models.EventSubTypeStart
	event := &models.Event{
		ID:           uuid.New().String(),
		UserID:       userID,
		EventType:    models.EventTypePuzzleSprint,
		EventSubType: &subType,
		EventData:    eventData,
		EventTime:    time.Now(),
	}

	return s.eventRepo.Create(ctx, event)
}

// CreatePuzzleSprintEndEvent creates a puzzle sprint end event
func (s *EventService) CreatePuzzleSprintEndEvent(ctx context.Context, userID string, sprintID string, status models.SprintStatus, duration int, succeededPuzzles int, failedPuzzles int, eloType string, eloChange int, attemptType models.AttemptType) error {
	data := models.PuzzleSprintEventData{
		SprintID:         sprintID,
		Status:           status,
		Duration:         &duration,
		SucceededPuzzles: &succeededPuzzles,
		FailedPuzzles:    &failedPuzzles,
		EloType:          eloType,
		EloChange:        &eloChange,
		AttemptType:      attemptType,
	}

	eventData, err := json.Marshal(data)
	if err != nil {
		return err
	}

	subType := models.EventSubTypeEnd
	event := &models.Event{
		ID:           uuid.New().String(),
		UserID:       userID,
		EventType:    models.EventTypePuzzleSprint,
		EventSubType: &subType,
		EventData:    eventData,
		EventTime:    time.Now(),
	}

	return s.eventRepo.Create(ctx, event)
}

// CreatePuzzleEvent creates a puzzle completion event
func (s *EventService) CreatePuzzleEvent(ctx context.Context, userID string, data models.PuzzleEventData) error {
	eventData, err := json.Marshal(data)
	if err != nil {
		return err
	}

	event := &models.Event{
		ID:        uuid.New().String(),
		UserID:    userID,
		EventType: models.EventTypePuzzle,
		EventData: eventData,
		EventTime: time.Now(),
	}

	return s.eventRepo.Create(ctx, event)
}

// CreateEventWithCustomID creates an event with a custom ID (for idempotency)
// This is useful when the calling service wants to provide its own UUID for idempotency
func (s *EventService) CreateEventWithCustomID(ctx context.Context, eventID string, userID string, eventType models.EventType, eventSubType *models.EventSubType, eventData json.RawMessage) error {
	event := &models.Event{
		ID:           eventID,
		UserID:       userID,
		EventType:    eventType,
		EventSubType: eventSubType,
		EventData:    eventData,
		EventTime:    time.Now(),
	}

	return s.eventRepo.Create(ctx, event)
}

// GetEvent retrieves an event by ID
func (s *EventService) GetEvent(ctx context.Context, eventID string) (*models.Event, error) {
	return s.eventRepo.GetByID(ctx, eventID)
}

// ListUserEvents retrieves events for a user with filtering and pagination
func (s *EventService) ListUserEvents(ctx context.Context, userID string, filter repository.EventFilter, offset int, limit int) ([]models.Event, int64, error) {
	return s.eventRepo.ListByUserID(ctx, userID, filter, offset, limit)
}
