package service

import (
	"context"
	"testing"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/repository/common"
	"github.com/lib/pq"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

// Mock repositories for testing
type MockLichessPuzzleRepository struct {
	mock.Mock
}

func (m *MockLichessPuzzleRepository) GetByID(ctx context.Context, id string) (*models.LichessPuzzle, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*models.LichessPuzzle), args.Error(1)
}

func (m *MockLichessPuzzleRepository) List(ctx context.Context, offset, limit int) ([]models.LichessPuzzle, int64, error) {
	args := m.Called(ctx, offset, limit)
	return args.Get(0).([]models.LichessPuzzle), args.Get(1).(int64), args.Error(2)
}

func (m *MockLichessPuzzleRepository) Save(ctx context.Context, puzzle *models.LichessPuzzle) error {
	args := m.Called(ctx, puzzle)
	return args.Error(0)
}

func (m *MockLichessPuzzleRepository) SaveBatch(ctx context.Context, puzzles []models.LichessPuzzle) (*repository.BatchSaveResult, error) {
	args := m.Called(ctx, puzzles)
	return args.Get(0).(*repository.BatchSaveResult), args.Error(1)
}

func (m *MockLichessPuzzleRepository) GetByRatingRange(ctx context.Context, minRating int, maxRating int, themes []string, excludeIDs []string, limit int) ([]models.LichessPuzzle, error) {
	args := m.Called(ctx, minRating, maxRating, themes, excludeIDs, limit)
	return args.Get(0).([]models.LichessPuzzle), args.Error(1)
}

func (m *MockLichessPuzzleRepository) GetRandomPuzzles(ctx context.Context, filter common.LichessPuzzleFilter, limit int) ([]models.LichessPuzzle, error) {
	args := m.Called(ctx, filter, limit)
	return args.Get(0).([]models.LichessPuzzle), args.Error(1)
}

type MockPuzzleRepository struct {
	mock.Mock
}

func (m *MockPuzzleRepository) Create(ctx context.Context, puzzle *models.Puzzle) error {
	args := m.Called(ctx, puzzle)
	return args.Error(0)
}

func (m *MockPuzzleRepository) GetByID(ctx context.Context, id string) (*models.Puzzle, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*models.Puzzle), args.Error(1)
}

func (m *MockPuzzleRepository) GetByGameID(ctx context.Context, gameID string) ([]models.Puzzle, error) {
	args := m.Called(ctx, gameID)
	return args.Get(0).([]models.Puzzle), args.Error(1)
}

func (m *MockPuzzleRepository) Update(ctx context.Context, puzzle *models.Puzzle) error {
	args := m.Called(ctx, puzzle)
	return args.Error(0)
}

func (m *MockPuzzleRepository) Delete(ctx context.Context, id string) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockPuzzleRepository) ListByUserID(ctx context.Context, userID string, filter repository.PuzzleFilter, offset int, limit int) ([]models.Puzzle, int64, error) {
	args := m.Called(ctx, userID, filter, offset, limit)
	return args.Get(0).([]models.Puzzle), args.Get(1).(int64), args.Error(2)
}

func (m *MockPuzzleRepository) GetPuzzleStats(ctx context.Context, userID string, filter repository.PuzzleFilter, offset int, limit int, grouping *common.TimeGrouping) ([]*models.PuzzleStats, error) {
	args := m.Called(ctx, userID, filter, offset, limit, grouping)
	return args.Get(0).([]*models.PuzzleStats), args.Error(1)
}

func (m *MockPuzzleRepository) GetRandomPuzzles(ctx context.Context, userID string, filter common.UserPuzzleFilter, limit int) ([]models.Puzzle, error) {
	args := m.Called(ctx, userID, filter, limit)
	return args.Get(0).([]models.Puzzle), args.Error(1)
}

type MockUserPuzzleStatsRepository struct {
	mock.Mock
}

func (m *MockUserPuzzleStatsRepository) GetByUserID(ctx context.Context, userID string) ([]models.UserPuzzleStats, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]models.UserPuzzleStats), args.Error(1)
}

func (m *MockUserPuzzleStatsRepository) GetByUserIDAndPuzzleID(ctx context.Context, userID string, puzzleID string) (*models.UserPuzzleStats, error) {
	args := m.Called(ctx, userID, puzzleID)
	return args.Get(0).(*models.UserPuzzleStats), args.Error(1)
}

func (m *MockUserPuzzleStatsRepository) GetDislikedPuzzleIDs(ctx context.Context, userID string) ([]string, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]string), args.Error(1)
}

type MockUserLichessPuzzleStatsRepository struct {
	mock.Mock
}

func (m *MockUserLichessPuzzleStatsRepository) GetByUserIDAndPuzzleID(ctx context.Context, userID string, puzzleID string) (*models.UserLichessPuzzleStats, error) {
	args := m.Called(ctx, userID, puzzleID)
	return args.Get(0).(*models.UserLichessPuzzleStats), args.Error(1)
}

func (m *MockUserLichessPuzzleStatsRepository) GetDislikedPuzzleIDs(ctx context.Context, userID string) ([]string, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]string), args.Error(1)
}

func TestRandomPuzzleService_GetRandomLichessPuzzles(t *testing.T) {
	mockLichessRepo := &MockLichessPuzzleRepository{}
	mockPuzzleRepo := &MockPuzzleRepository{}
	mockUserPuzzleStatsRepo := &MockUserPuzzleStatsRepository{}
	mockUserLichessPuzzleStatsRepo := &MockUserLichessPuzzleStatsRepository{}
	service := NewRandomPuzzleService(mockLichessRepo, mockPuzzleRepo, mockUserPuzzleStatsRepo, mockUserLichessPuzzleStatsRepo)

	ctx := context.Background()
	userID := "test-user-123"

	t.Run("successful request", func(t *testing.T) {
		request := RandomLichessPuzzleRequest{
			MinRating: 1400,
			MaxRating: 1600,
			Themes:    []string{"tactics"},
			Limit:     5,
		}

		expectedPuzzles := []models.LichessPuzzle{
			{
				ID:     "puzzle1",
				Rating: 1450,
				Themes: pq.StringArray{"tactics"},
			},
			{
				ID:     "puzzle2",
				Rating: 1550,
				Themes: pq.StringArray{"tactics"},
			},
		}

		// Mock getting disliked lichess puzzle IDs (return empty list)
		mockUserLichessPuzzleStatsRepo.On("GetDislikedPuzzleIDs", ctx, userID).
			Return([]string{}, nil)

		expectedFilter := common.LichessPuzzleFilter{
			MinRating:  1400,
			MaxRating:  1600,
			Themes:     []string{"tactics"},
			ExcludeIDs: []string{},
		}
		mockLichessRepo.On("GetRandomPuzzles", ctx, expectedFilter, 5).
			Return(expectedPuzzles, nil)

		puzzles, err := service.GetRandomLichessPuzzles(ctx, userID, request)
		require.NoError(t, err)
		assert.Equal(t, expectedPuzzles, puzzles)

		mockLichessRepo.AssertExpectations(t)
		mockUserLichessPuzzleStatsRepo.AssertExpectations(t)
	})

	t.Run("invalid request - min rating too high", func(t *testing.T) {
		request := RandomLichessPuzzleRequest{
			MinRating: 3500,
			MaxRating: 1600,
			Limit:     5,
		}

		_, err := service.GetRandomLichessPuzzles(ctx, userID, request)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "min_rating must be between 0 and 3000")
	})

	t.Run("invalid request - min > max", func(t *testing.T) {
		request := RandomLichessPuzzleRequest{
			MinRating: 1600,
			MaxRating: 1400,
			Limit:     5,
		}

		_, err := service.GetRandomLichessPuzzles(ctx, userID, request)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "min_rating must be less than or equal to max_rating")
	})

	t.Run("invalid request - limit too high", func(t *testing.T) {
		request := RandomLichessPuzzleRequest{
			MinRating: 1400,
			MaxRating: 1600,
			Limit:     100,
		}

		_, err := service.GetRandomLichessPuzzles(ctx, userID, request)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "limit must be between 1 and 50")
	})
}

func TestRandomPuzzleService_GetRandomUserPuzzles(t *testing.T) {
	mockLichessRepo := &MockLichessPuzzleRepository{}
	mockPuzzleRepo := &MockPuzzleRepository{}
	mockUserPuzzleStatsRepo := &MockUserPuzzleStatsRepository{}
	mockUserLichessPuzzleStatsRepo := &MockUserLichessPuzzleStatsRepository{}
	service := NewRandomPuzzleService(mockLichessRepo, mockPuzzleRepo, mockUserPuzzleStatsRepo, mockUserLichessPuzzleStatsRepo)

	ctx := context.Background()
	userID := "test-user-123"

	t.Run("successful request", func(t *testing.T) {
		request := RandomUserPuzzleRequest{
			MinMoveLength: 4,
			MaxMoveLength: 6,
			Tags:          []string{"endgame"},
			Themes:        []string{"tactics"},
			Limit:         3,
		}

		expectedPuzzles := []models.Puzzle{
			{
				ID:   "user-puzzle1",
				Tags: pq.StringArray{"endgame"},
			},
		}

		// Mock getting disliked puzzle IDs (return empty list)
		mockUserPuzzleStatsRepo.On("GetDislikedPuzzleIDs", ctx, userID).
			Return([]string{}, nil)

		expectedFilter := common.UserPuzzleFilter{
			MinMoveLength: 4,
			MaxMoveLength: 6,
			Tags:          []string{"endgame"},
			Themes:        []string{"tactics"},
			GameTimeStart: nil,
			GameTimeEnd:   nil,
			DislikedIDs:   []string{},
		}
		mockPuzzleRepo.On("GetRandomPuzzles", ctx, userID, expectedFilter, 3).
			Return(expectedPuzzles, nil)

		puzzles, err := service.GetRandomUserPuzzles(ctx, userID, request)
		require.NoError(t, err)
		assert.Equal(t, expectedPuzzles, puzzles)

		mockPuzzleRepo.AssertExpectations(t)
		mockUserPuzzleStatsRepo.AssertExpectations(t)
	})
}
