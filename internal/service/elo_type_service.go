package service

import (
	"fmt"
	"strings"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository/common"
)

// EloTypeConfig represents the configuration for a specific ELO type
type EloTypeConfig struct {
	Theme            string             // empty for "mixed", or specific lichess theme
	DurationSeconds  int                // total sprint duration in seconds
	PerPuzzleSeconds int                // time per puzzle in seconds
	TargetPuzzles    int                // calculated target number of puzzles
	MaxMistakes      int                // maximum allowed mistakes (always 2 for now)
	AttemptType      models.AttemptType // NEW: regular or arrow_duel
}

// EloTypeService provides validation and configuration for ELO types
type EloTypeService struct{}

// NewEloTypeService creates a new EloTypeService
func NewEloTypeService() IEloTypeService {
	return &EloTypeService{}
}

// Valid themes from lichess puzzle database
var validThemes = map[string]bool{
	"mixed":             true, // special case - no theme filtering
	"fork":              true,
	"mateIn1":           true,
	"mateIn2":           true,
	"sacrifice":         true,
	"pin":               true,
	"advancedPawn":      true,
	"discoveredAttack":  true,
	"deflection":        true,
	"hangingPiece":      true,
	"attraction":        true,
	"pawnEndgame":       true,
	"backRankMate":      true,
	"mateIn3":           true,
	"promotion":         true,
	"skewer":            true,
	"intermezzo":        true,
	"trappedPiece":      true,
	"zugzwang":          true,
	"capturingDefender": true,
	"doubleCheck":       true,
	"mateIn4":           true,
	"interference":      true,
	"smotheredMate":     true,
	"xRayAttack":        true,
}

// Valid durations in minutes -> seconds (1-30 minutes)
var validDurations = map[string]int{
	"1":  60,   // 1 minute
	"2":  120,  // 2 minutes
	"3":  180,  // 3 minutes
	"4":  240,  // 4 minutes
	"5":  300,  // 5 minutes
	"6":  360,  // 6 minutes
	"7":  420,  // 7 minutes
	"8":  480,  // 8 minutes
	"9":  540,  // 9 minutes
	"10": 600,  // 10 minutes
	"11": 660,  // 11 minutes
	"12": 720,  // 12 minutes
	"13": 780,  // 13 minutes
	"14": 840,  // 14 minutes
	"15": 900,  // 15 minutes
	"16": 960,  // 16 minutes
	"17": 1020, // 17 minutes
	"18": 1080, // 18 minutes
	"19": 1140, // 19 minutes
	"20": 1200, // 20 minutes
	"21": 1260, // 21 minutes
	"22": 1320, // 22 minutes
	"23": 1380, // 23 minutes
	"24": 1440, // 24 minutes
	"25": 1500, // 25 minutes
	"26": 1560, // 26 minutes
	"27": 1620, // 27 minutes
	"28": 1680, // 28 minutes
	"29": 1740, // 29 minutes
	"30": 1800, // 30 minutes
}

// Valid per-puzzle times in seconds
var validPerPuzzleTimes = map[string]int{
	"5":  5,
	"10": 10,
	"15": 15,
	"20": 20,
	"30": 30,
	"60": 60,
}

const (
	// Default ELO type
	DefaultEloType = "mixed 10/30"

	// Fixed max mistakes for now
	FixedMaxMistakes = 2
)

// ValidateEloType validates whether an ELO type string is valid
func (s *EloTypeService) ValidateEloType(eloType string) error {
	_, err := s.parseAndValidateEloType(eloType)
	return err
}

// GetEloTypeConfig parses and returns the configuration for an ELO type
func (s *EloTypeService) GetEloTypeConfig(eloType string) (*EloTypeConfig, error) {
	return s.parseAndValidateEloType(eloType)
}

// IsArrowDuelEloType checks if an ELO type is for arrow-duel mode
func (s *EloTypeService) IsArrowDuelEloType(eloType string) bool {
	return strings.HasPrefix(eloType, "arrowduel ")
}

// GetPuzzleFilter creates a lichess puzzle filter based on user rating and ELO type config
func (s *EloTypeService) GetPuzzleFilter(userRating int, config *EloTypeConfig) (*common.LichessPuzzleFilter, error) {
	if config == nil {
		return nil, fmt.Errorf("config cannot be nil")
	}

	// Calculate rating range (±100 from user's rating, minimum 800)
	minRating := userRating - 100
	maxRating := userRating + 100
	if minRating < 800 {
		minRating = 800
	}

	// Prepare themes filter
	var themes []string
	if config.Theme != "" && config.Theme != "mixed" {
		themes = []string{config.Theme}
	}

	return &common.LichessPuzzleFilter{
		MinRating:  minRating,
		MaxRating:  maxRating,
		Themes:     themes,
		ExcludeIDs: nil, // Will be set by caller if needed
	}, nil
}

// parseAndValidateEloType parses and validates an ELO type string
// Expected format: "{theme} {duration}/{per_puzzle_time}" or "arrowduel {duration}/{per_puzzle_time}"
// Example: "mixed 10/30", "fork 5/20", "mateIn1 20/60", "arrowduel 5/30"
func (s *EloTypeService) parseAndValidateEloType(eloType string) (*EloTypeConfig, error) {
	if eloType == "" {
		eloType = DefaultEloType
	}

	// Check if this is an arrow-duel ELO type
	attemptType := models.AttemptTypeRegular
	var theme string
	var durationAndTime string

	if strings.HasPrefix(eloType, "arrowduel ") {
		// Arrow-duel ELO type format: "arrowduel {duration}/{per_puzzle_time}"
		attemptType = models.AttemptTypeArrowDuel
		theme = "mixed" // Arrow-duel always uses mixed themes
		durationAndTime = strings.TrimPrefix(eloType, "arrowduel ")
	} else {
		// Regular ELO type format: "{theme} {duration}/{per_puzzle_time}"
		parts := strings.Split(strings.TrimSpace(eloType), " ")
		if len(parts) != 2 {
			return nil, fmt.Errorf("invalid ELO type format: expected '{theme} {duration}/{per_puzzle_time}' or 'arrowduel {duration}/{per_puzzle_time}', got '%s'", eloType)
		}

		theme = parts[0]
		durationAndTime = parts[1]

		// Validate theme for regular attempts
		if !validThemes[theme] {
			return nil, fmt.Errorf("invalid theme '%s': must be one of the supported themes", theme)
		}
	}

	// Split duration and per-puzzle time
	timeParts := strings.Split(durationAndTime, "/")
	if len(timeParts) != 2 {
		return nil, fmt.Errorf("invalid duration/time format: expected '{duration}/{per_puzzle_time}', got '%s'", durationAndTime)
	}

	durationStr := timeParts[0]
	perPuzzleTimeStr := timeParts[1]

	// Validate and convert duration
	durationSeconds, exists := validDurations[durationStr]
	if !exists {
		return nil, fmt.Errorf("invalid duration '%s': must be between 1 and 30 minutes", durationStr)
	}

	// Validate and convert per-puzzle time
	perPuzzleSeconds, exists := validPerPuzzleTimes[perPuzzleTimeStr]
	if !exists {
		return nil, fmt.Errorf("invalid per-puzzle time '%s': must be one of 5, 10, 15, 20, 30, or 60 seconds", perPuzzleTimeStr)
	}

	// Calculate target puzzles
	targetPuzzles := s.calculateTargetPuzzles(durationSeconds, perPuzzleSeconds)

	// Convert theme for filter (empty string for "mixed")
	filterTheme := theme
	if theme == "mixed" {
		filterTheme = ""
	}

	return &EloTypeConfig{
		Theme:            filterTheme,
		DurationSeconds:  durationSeconds,
		PerPuzzleSeconds: perPuzzleSeconds,
		TargetPuzzles:    targetPuzzles,
		MaxMistakes:      FixedMaxMistakes,
		AttemptType:      attemptType,
	}, nil
}

// calculateTargetPuzzles calculates the target number of puzzles based on duration and per-puzzle time
func (s *EloTypeService) calculateTargetPuzzles(durationSeconds, perPuzzleSeconds int) int {
	// Basic calculation: duration / per-puzzle time
	return durationSeconds / perPuzzleSeconds
}

// GetValidThemes returns a list of all valid themes
func (s *EloTypeService) GetValidThemes() []string {
	themes := make([]string, 0, len(validThemes))
	for theme := range validThemes {
		themes = append(themes, theme)
	}
	return themes
}

// GetValidDurations returns a list of all valid durations in minutes
func (s *EloTypeService) GetValidDurations() []string {
	durations := make([]string, 0, len(validDurations))
	for duration := range validDurations {
		durations = append(durations, duration)
	}
	return durations
}

// GetValidPerPuzzleTimes returns a list of all valid per-puzzle times in seconds
func (s *EloTypeService) GetValidPerPuzzleTimes() []string {
	times := make([]string, 0, len(validPerPuzzleTimes))
	for time := range validPerPuzzleTimes {
		times = append(times, time)
	}
	return times
}
