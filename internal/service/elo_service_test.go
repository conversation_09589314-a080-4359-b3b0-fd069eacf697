package service

import (
	"context"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/repository/fake"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestEloService(t *testing.T) {
	// Setup fake repositories
	fakeDB := fake.NewDB(t)
	defer func() {
		if err := fakeDB.Close(); err != nil {
			t.Logf("Failed to close fake DB: %v", err)
		}
	}()

	eloRepo := fake.NewUserEloRepository(fakeDB)
	eloHistoryRepo := fake.NewEloHistoryRepository(fakeDB)

	eloService := NewEloService(eloRepo, eloHistoryRepo)
	ctx := context.Background()

	// Create a test user first (required for foreign key constraint)
	userID := uuid.New().String()
	testUser := struct {
		ID           string `gorm:"type:varchar(36);primary_key"`
		Email        string `gorm:"type:varchar(255);unique;not null"`
		PasswordHash string `gorm:"type:varchar(255);not null"`
	}{
		ID:           userID,
		Email:        "<EMAIL>",
		PasswordHash: "hashedpassword",
	}
	err := fakeDB.DB.Table("users").Create(&testUser).Error
	require.NoError(t, err)

	eloType := "overall_intuition"

	t.Run("InitializeUserElo", func(t *testing.T) {
		userElo, err := eloService.InitializeUserElo(ctx, userID, eloType)
		require.NoError(t, err)
		assert.Equal(t, userID, userElo.UserID)
		assert.Equal(t, eloType, userElo.EloType)
		assert.Equal(t, int(DefaultRating), userElo.Rating)
		assert.Equal(t, DefaultRatingDeviation, userElo.RatingDeviation)
		assert.Equal(t, DefaultVolatility, userElo.Volatility)
		assert.Equal(t, 0, userElo.GamesPlayed)
		assert.False(t, userElo.LastActiveAt.IsZero())
	})

	t.Run("GetUserElo", func(t *testing.T) {
		userElo, err := eloService.GetUserElo(ctx, userID, eloType)
		require.NoError(t, err)
		assert.Equal(t, userID, userElo.UserID)
		assert.Equal(t, eloType, userElo.EloType)
		assert.Equal(t, int(DefaultRating), userElo.Rating)
	})

	t.Run("CalculateSprintRatingChange_Win", func(t *testing.T) {
		eloChange, err := eloService.CalculateSprintRatingChange(ctx, userID, eloType, true)
		require.NoError(t, err)
		assert.Equal(t, int(DefaultRating), eloChange.RatingBefore)
		assert.Greater(t, eloChange.RatingAfter, eloChange.RatingBefore) // Should increase for win
		assert.Greater(t, eloChange.RatingChange, 0)                     // Positive change
		assert.Equal(t, DefaultRatingDeviation, eloChange.RatingDeviationBefore)
		assert.Less(t, eloChange.RatingDeviationAfter, eloChange.RatingDeviationBefore) // RD should decrease
	})

	t.Run("CalculateSprintRatingChange_Loss", func(t *testing.T) {
		eloChange, err := eloService.CalculateSprintRatingChange(ctx, userID, eloType, false)
		require.NoError(t, err)
		assert.Equal(t, int(DefaultRating), eloChange.RatingBefore)
		// Since user starts at minimum rating (600), loss cannot decrease rating further
		assert.GreaterOrEqual(t, eloChange.RatingAfter, MinimumRating) // Should not go below minimum
		assert.LessOrEqual(t, eloChange.RatingChange, 0)               // Non-positive change (0 if at minimum)
	})

	t.Run("MinimumRatingFloor", func(t *testing.T) {
		// Create a user with very low rating to test the floor
		lowRatingUserID := uuid.New().String()
		lowRatingUser := struct {
			ID           string `gorm:"type:varchar(36);primary_key"`
			Email        string `gorm:"type:varchar(255);unique;not null"`
			PasswordHash string `gorm:"type:varchar(255);not null"`
		}{
			ID:           lowRatingUserID,
			Email:        "<EMAIL>",
			PasswordHash: "hashedpassword",
		}
		err := fakeDB.DB.Table("users").Create(&lowRatingUser).Error
		require.NoError(t, err)

		// Initialize with minimum rating
		lowRatingElo, err := eloService.InitializeUserElo(ctx, lowRatingUserID, eloType)
		require.NoError(t, err)

		// Manually set a very low rating to test the floor
		lowRatingElo.Rating = MinimumRating
		lowRatingElo.RatingDeviation = 50.0 // Low deviation to make rating changes more predictable
		err = eloRepo.Save(ctx, lowRatingElo)
		require.NoError(t, err)

		// Calculate rating change for a loss (should not go below minimum)
		eloChange, err := eloService.CalculateSprintRatingChange(ctx, lowRatingUserID, eloType, false)
		require.NoError(t, err)

		// Rating should not go below the minimum floor
		assert.GreaterOrEqual(t, eloChange.RatingAfter, MinimumRating, "Rating should not go below minimum floor")
		assert.Equal(t, MinimumRating, eloChange.RatingBefore)
	})

	t.Run("UpdateUserElo", func(t *testing.T) {
		// Calculate a rating change
		eloChange, err := eloService.CalculateSprintRatingChange(ctx, userID, eloType, true)
		require.NoError(t, err)

		// Create a test sprint (required for foreign key constraint)
		sprintID := uuid.New().String()
		testSprint := struct {
			ID               string    `gorm:"type:varchar(36);primary_key"`
			UserID           string    `gorm:"type:varchar(36);not null"`
			EloType          string    `gorm:"type:varchar(100);not null"`
			Status           string    `gorm:"type:varchar(50);not null"`
			TargetPuzzles    int       `gorm:"not null;default:20"`
			TimeLimitSeconds int       `gorm:"not null;default:600"`
			PuzzlesSolved    int       `gorm:"not null;default:0"`
			MistakesMade     int       `gorm:"not null;default:0"`
			EloRatingBefore  int       `gorm:"not null"`
			StartedAt        time.Time `gorm:"not null"`
			CreatedAt        time.Time `gorm:"not null"`
			UpdatedAt        time.Time `gorm:"not null"`
		}{
			ID:               sprintID,
			UserID:           userID,
			EloType:          eloType,
			Status:           "active",
			TargetPuzzles:    20,
			TimeLimitSeconds: 600,
			PuzzlesSolved:    0,
			MistakesMade:     0,
			EloRatingBefore:  1200,
			StartedAt:        time.Now(),
			CreatedAt:        time.Now(),
			UpdatedAt:        time.Now(),
		}
		err = fakeDB.DB.Table("sprints").Create(&testSprint).Error
		require.NoError(t, err)

		// Update the ELO
		err = eloService.UpdateUserElo(ctx, userID, eloType, eloChange, sprintID)
		require.NoError(t, err)

		// Verify the ELO was updated
		updatedElo, err := eloService.GetUserElo(ctx, userID, eloType)
		require.NoError(t, err)
		assert.Equal(t, eloChange.RatingAfter, updatedElo.Rating)
		assert.Equal(t, eloChange.RatingDeviationAfter, updatedElo.RatingDeviation)
		assert.Equal(t, 1, updatedElo.GamesPlayed)

		// Verify history was created
		history, _, err := eloHistoryRepo.ListByUserID(ctx, userID, &eloType, 0, 10)
		require.NoError(t, err)
		assert.Len(t, history, 1)
		assert.Equal(t, eloChange.RatingBefore, history[0].RatingBefore)
		assert.Equal(t, eloChange.RatingAfter, history[0].RatingAfter)
		assert.Equal(t, eloChange.RatingChange, history[0].RatingChange)
		assert.Equal(t, sprintID, history[0].SprintID)
	})

	t.Run("MultipleGames_RatingConvergence", func(t *testing.T) {
		// Test that rating changes become smaller as more games are played
		// and rating deviation decreases

		initialElo, err := eloService.GetUserElo(ctx, userID, eloType)
		require.NoError(t, err)

		for i := 0; i < 5; i++ {
			eloChange, err := eloService.CalculateSprintRatingChange(ctx, userID, eloType, true)
			require.NoError(t, err)

			// Create a test sprint for each iteration
			sprintID := uuid.New().String()
			testSprint := struct {
				ID               string    `gorm:"type:varchar(36);primary_key"`
				UserID           string    `gorm:"type:varchar(36);not null"`
				EloType          string    `gorm:"type:varchar(100);not null"`
				Status           string    `gorm:"type:varchar(50);not null"`
				TargetPuzzles    int       `gorm:"not null;default:20"`
				TimeLimitSeconds int       `gorm:"not null;default:600"`
				PuzzlesSolved    int       `gorm:"not null;default:0"`
				MistakesMade     int       `gorm:"not null;default:0"`
				EloRatingBefore  int       `gorm:"not null"`
				StartedAt        time.Time `gorm:"not null"`
				CreatedAt        time.Time `gorm:"not null"`
				UpdatedAt        time.Time `gorm:"not null"`
			}{
				ID:               sprintID,
				UserID:           userID,
				EloType:          eloType,
				Status:           "active",
				TargetPuzzles:    20,
				TimeLimitSeconds: 600,
				PuzzlesSolved:    0,
				MistakesMade:     0,
				EloRatingBefore:  1200,
				StartedAt:        time.Now(),
				CreatedAt:        time.Now(),
				UpdatedAt:        time.Now(),
			}
			err = fakeDB.DB.Table("sprints").Create(&testSprint).Error
			require.NoError(t, err)

			err = eloService.UpdateUserElo(ctx, userID, eloType, eloChange, sprintID)
			require.NoError(t, err)

			if i > 0 {
				// Rating changes should generally get smaller as RD decreases
				// (though this isn't guaranteed in every single game)
				assert.Less(t, eloChange.RatingDeviationAfter, eloChange.RatingDeviationBefore)
			}
		}

		finalElo, err := eloService.GetUserElo(ctx, userID, eloType)
		require.NoError(t, err)

		// After multiple wins, rating should be higher
		assert.Greater(t, finalElo.Rating, initialElo.Rating)
		// Rating deviation should be lower
		assert.Less(t, finalElo.RatingDeviation, initialElo.RatingDeviation)
		// Games played should be updated
		assert.Equal(t, 6, finalElo.GamesPlayed) // 1 from previous test + 5 from this test
	})
}

func TestEloService_ArrowDuel(t *testing.T) {
	fakeDB := fake.NewDB(t)
	eloRepo := repository.NewUserEloRepository(fakeDB.DB)
	eloHistoryRepo := repository.NewEloHistoryRepository(fakeDB.DB)
	eloService := NewEloService(eloRepo, eloHistoryRepo)
	ctx := context.Background()

	userID := "test-user-arrow-duel"
	testUser := models.User{
		ID:    userID,
		Email: "<EMAIL>",
	}
	err := fakeDB.DB.Table("users").Create(&testUser).Error
	require.NoError(t, err)

	t.Run("InitializeUserElo_ArrowDuel", func(t *testing.T) {
		eloType := "arrowduel"

		userElo, err := eloService.InitializeUserElo(ctx, userID, eloType)
		require.NoError(t, err)
		assert.Equal(t, userID, userElo.UserID)
		assert.Equal(t, eloType, userElo.EloType)
		assert.Equal(t, int(DefaultRating), userElo.Rating)
		assert.Equal(t, DefaultRatingDeviation, userElo.RatingDeviation)
		assert.Equal(t, DefaultVolatility, userElo.Volatility)
		assert.Equal(t, 0, userElo.GamesPlayed)
		assert.False(t, userElo.LastActiveAt.IsZero())
	})

	t.Run("CalculateSprintRatingChange_ArrowDuel_Win", func(t *testing.T) {
		eloType := "arrowduel"

		eloChange, err := eloService.CalculateSprintRatingChange(ctx, userID, eloType, true)
		require.NoError(t, err)
		assert.Equal(t, int(DefaultRating), eloChange.RatingBefore)
		assert.Greater(t, eloChange.RatingAfter, eloChange.RatingBefore) // Should increase for win
		assert.Greater(t, eloChange.RatingChange, 0)                     // Positive change
		assert.Equal(t, DefaultRatingDeviation, eloChange.RatingDeviationBefore)
		assert.Less(t, eloChange.RatingDeviationAfter, eloChange.RatingDeviationBefore) // RD should decrease
	})

	t.Run("CalculateSprintRatingChange_ArrowDuel_Loss", func(t *testing.T) {
		eloType := "arrowduel"

		eloChange, err := eloService.CalculateSprintRatingChange(ctx, userID, eloType, false)
		require.NoError(t, err)
		assert.LessOrEqual(t, eloChange.RatingAfter, eloChange.RatingBefore)            // Should decrease or stay same for loss
		assert.LessOrEqual(t, eloChange.RatingChange, 0)                                // Non-positive change
		assert.Less(t, eloChange.RatingDeviationAfter, eloChange.RatingDeviationBefore) // RD should still decrease
	})

	t.Run("ValidateEloType_ArrowDuel", func(t *testing.T) {
		// Test that arrow-duel ELO types are valid
		eloTypeService := NewEloTypeService()
		validArrowDuelTypes := []string{
			"arrowduel 5/30",
			"arrowduel 10/20",
			"arrowduel 15/60",
		}

		for _, eloType := range validArrowDuelTypes {
			err := eloTypeService.ValidateEloType(eloType)
			assert.NoError(t, err, "ELO type %s should be valid for arrow-duel", eloType)
		}
	})

	t.Run("ArrowDuel_ELO_Separation", func(t *testing.T) {
		// Test that arrow-duel ELO is separate from regular ELO types
		regularEloType := "mixed 10/30"
		arrowDuelEloType := "arrowduel"

		// Initialize both ELO types
		regularElo, err := eloService.InitializeUserElo(ctx, userID, regularEloType)
		require.NoError(t, err)

		arrowDuelElo, err := eloService.InitializeUserElo(ctx, userID, arrowDuelEloType)
		require.NoError(t, err)

		// Verify they are separate records
		assert.NotEqual(t, regularElo.ID, arrowDuelElo.ID)
		assert.Equal(t, regularEloType, regularElo.EloType)
		assert.Equal(t, arrowDuelEloType, arrowDuelElo.EloType)
		assert.Equal(t, userID, regularElo.UserID)
		assert.Equal(t, userID, arrowDuelElo.UserID)

		// Both should start with default rating
		assert.Equal(t, int(DefaultRating), regularElo.Rating)
		assert.Equal(t, int(DefaultRating), arrowDuelElo.Rating)
	})

	t.Run("ArrowDuel_ELO_Updates_Independent", func(t *testing.T) {
		regularEloType := "mixed 10/30"
		arrowDuelEloType := "arrowduel"

		// Calculate rating changes for both types
		regularEloChange, err := eloService.CalculateSprintRatingChange(ctx, userID, regularEloType, true)
		require.NoError(t, err)

		arrowDuelEloChange, err := eloService.CalculateSprintRatingChange(ctx, userID, arrowDuelEloType, false)
		require.NoError(t, err)

		// Create mock sprints for updates
		regularSprintID := "regular-sprint-123"
		arrowDuelSprintID := "arrow-duel-sprint-456"

		// Create sprint records
		regularSprint := models.Sprint{
			ID:               regularSprintID,
			UserID:           userID,
			EloType:          regularEloType,
			Status:           "completed",
			TargetPuzzles:    20,
			TimeLimitSeconds: 600,
			PuzzlesSolved:    15,
			MistakesMade:     5,
			EloRatingBefore:  1200,
			StartedAt:        time.Now().Add(-time.Hour),
			EndedAt:          &[]time.Time{time.Now()}[0],
			CreatedAt:        time.Now().Add(-time.Hour),
			UpdatedAt:        time.Now(),
		}
		err = fakeDB.DB.Table("sprints").Create(&regularSprint).Error
		require.NoError(t, err)

		arrowDuelSprint := models.Sprint{
			ID:               arrowDuelSprintID,
			UserID:           userID,
			EloType:          arrowDuelEloType,
			Status:           "completed",
			TargetPuzzles:    20,
			TimeLimitSeconds: 600,
			PuzzlesSolved:    10,
			MistakesMade:     10,
			EloRatingBefore:  1200,
			StartedAt:        time.Now().Add(-time.Hour),
			EndedAt:          &[]time.Time{time.Now()}[0],
			CreatedAt:        time.Now().Add(-time.Hour),
			UpdatedAt:        time.Now(),
		}
		err = fakeDB.DB.Table("sprints").Create(&arrowDuelSprint).Error
		require.NoError(t, err)

		// Update ELOs
		err = eloService.UpdateUserElo(ctx, userID, regularEloType, regularEloChange, regularSprintID)
		require.NoError(t, err)

		err = eloService.UpdateUserElo(ctx, userID, arrowDuelEloType, arrowDuelEloChange, arrowDuelSprintID)
		require.NoError(t, err)

		// Verify that the ELOs are updated independently
		// Regular ELO should have increased (win)
		assert.Greater(t, regularEloChange.RatingAfter, regularEloChange.RatingBefore)

		// Arrow-duel ELO should have decreased or stayed same (loss)
		assert.LessOrEqual(t, arrowDuelEloChange.RatingAfter, arrowDuelEloChange.RatingBefore)

		// Verify the changes are different
		assert.NotEqual(t, regularEloChange.RatingChange, arrowDuelEloChange.RatingChange)
	})
}
