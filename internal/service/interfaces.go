package service

import (
	"context"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/repository/common"
)

// PuzzleAttemptResult represents the result of a puzzle attempt
type PuzzleAttemptResult struct {
	PuzzleID         string    `json:"puzzle_id"`
	SequenceInSprint int       `json:"sequence_in_sprint"`
	UserMoves        []string  `json:"user_moves"`
	WasCorrect       bool      `json:"was_correct"`
	TimeTakenMs      int       `json:"time_taken_ms"`
	AttemptedAt      time.Time `json:"attempted_at"`

	// Arrow-duel specific fields (only used when AttemptType is "arrow_duel")
	AttemptType    string   `json:"attempt_type,omitempty"`    // NEW: "regular" or "arrow_duel"
	CandidateMoves []string `json:"candidate_moves,omitempty"` // NEW: [blunder_move, correct_move]
	ChosenMove     *string  `json:"chosen_move,omitempty"`     // NEW: Move chosen by player
}

// SprintResultResponse represents the response after submitting puzzle results
type SprintResultResponse struct {
	ProcessedCount int                 `json:"processed_count"`
	SessionStatus  models.SprintStatus `json:"session_status"`
	MistakesCount  int                 `json:"mistakes_count"`
}

// EloChange represents a change in ELO rating
type EloChange struct {
	RatingBefore          int     `json:"rating_before"`
	RatingAfter           int     `json:"rating_after"`
	RatingChange          int     `json:"rating_change"`
	RatingDeviationBefore float64 `json:"rating_deviation_before"`
	RatingDeviationAfter  float64 `json:"rating_deviation_after"`
}

// ISprintService defines the interface for sprint service operations
type ISprintService interface {
	// StartSprint creates a new sprint session for a user
	StartSprint(ctx context.Context, userID string, eloType string) (*models.Sprint, error)

	// GetSprintState retrieves the current state of a sprint session
	GetSprintState(ctx context.Context, sprintID string, userID string) (*models.Sprint, error)

	// EndSprint ends a sprint session and calculates final results
	EndSprint(ctx context.Context, sprintID string, userID string) (*models.Sprint, error)

	// EndSprintWithClientResults ends a sprint session with optional client-provided final results
	EndSprintWithClientResults(ctx context.Context, sprintID string, userID string, clientPuzzlesSolved *int, clientMistakesMade *int) (*models.Sprint, error)

	// GetNextPuzzles generates and returns the next batch of puzzles for a sprint
	GetNextPuzzles(ctx context.Context, sprintID string, userID string, count int) ([]models.LichessPuzzle, error)

	// GetSprintPuzzleCount returns the count of puzzles generated for a sprint
	GetSprintPuzzleCount(ctx context.Context, sprintID string, userID string) (int, error)

	// ValidateSprintAccess validates that a user has access to a specific sprint
	ValidateSprintAccess(ctx context.Context, sprintID string, userID string) error

	// CleanupAbandonedSprints marks sprints as abandoned if they exceed the timeout
	CleanupAbandonedSprints(ctx context.Context, graceMinutes int, limit int) (int64, error)

	// GetSprintPuzzles retrieves sprint puzzles with attempt information and filtering
	GetSprintPuzzles(ctx context.Context, sprintID string, userID string, filter repository.SprintPuzzleFilter, offset int, limit int) ([]repository.SprintPuzzleWithAttempt, int64, error)
}

// IPuzzleService defines the interface for puzzle service operations
type IPuzzleService interface {
	// GetPuzzlesForSprint retrieves puzzles for a sprint session with pagination
	GetPuzzlesForSprint(ctx context.Context, sprintID string, userID string, count int, offset int) ([]models.LichessPuzzle, int, error)

	// SubmitPuzzleResults processes puzzle attempt results for a sprint
	SubmitPuzzleResults(ctx context.Context, sprintID string, userID string, results []PuzzleAttemptResult) (*SprintResultResponse, error)

	// SelectPuzzlesForUser selects appropriate puzzles for a user based on their ELO rating and themes
	SelectPuzzlesForUser(ctx context.Context, userID string, eloType string, count int, excludeIDs []string, themes []string) ([]models.LichessPuzzle, error)
}

// IEloService defines the interface for ELO rating service operations
type IEloService interface {
	// CalculateSprintRatingChange calculates the ELO rating change for a sprint result
	CalculateSprintRatingChange(ctx context.Context, userID string, eloType string, sprintWon bool) (*EloChange, error)

	// UpdateUserElo updates a user's ELO rating and creates a history record
	UpdateUserElo(ctx context.Context, userID string, eloType string, eloChange *EloChange, sprintID string) error

	// GetUserElo retrieves a user's current ELO rating for a specific type
	GetUserElo(ctx context.Context, userID string, eloType string) (*models.UserElo, error)

	// InitializeUserElo creates an initial ELO rating for a user if it doesn't exist
	InitializeUserElo(ctx context.Context, userID string, eloType string) (*models.UserElo, error)
}

// IEloTypeService defines the interface for ELO type validation and configuration
type IEloTypeService interface {
	// ValidateEloType validates whether an ELO type string is valid
	ValidateEloType(eloType string) error

	// GetEloTypeConfig parses and returns the configuration for an ELO type
	GetEloTypeConfig(eloType string) (*EloTypeConfig, error)

	// IsArrowDuelEloType checks if an ELO type is for arrow-duel mode
	IsArrowDuelEloType(eloType string) bool

	// GetPuzzleFilter creates a lichess puzzle filter based on user rating and ELO type config
	GetPuzzleFilter(userRating int, config *EloTypeConfig) (*common.LichessPuzzleFilter, error)

	// GetValidThemes returns a list of all valid themes
	GetValidThemes() []string

	// GetValidDurations returns a list of all valid durations in minutes
	GetValidDurations() []string

	// GetValidPerPuzzleTimes returns a list of all valid per-puzzle times in seconds
	GetValidPerPuzzleTimes() []string
}
