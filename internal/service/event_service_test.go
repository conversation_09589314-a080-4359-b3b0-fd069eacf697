package service

import (
	"context"
	"encoding/json"
	"testing"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	repoTesting "github.com/chessticize/chessticize-server/internal/repository/testing"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestEventService(t *testing.T) {
	// Test with SQLite by default for faster tests
	provider := repoTesting.GetTestDBProvider(repoTesting.FakeRepository)
	defer provider.Cleanup(t)

	testEventService(t, provider)
}

// TestRealEventService runs tests against the real PostgreSQL repository implementation
// This will be skipped if the test database is not available
func TestRealEventService(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping real database tests")
	}
	provider := repoTesting.GetTestDBProvider(repoTesting.RealRepository)
	defer provider.Cleanup(t)

	testEventService(t, provider)
}

func testEventService(t *testing.T, provider repoTesting.TestDBProvider) {
	eventRepo := provider.GetEventRepository(t)
	userRepo := provider.GetUserRepository(t)
	gameRepo := provider.GetGameRepository(t)
	puzzleRepo := provider.GetPuzzleRepository(t)
	eventService := NewEventService(eventRepo)
	ctx := context.Background()

	// Create a test user first
	user := &models.User{
		Email:        repoTesting.RandomEmail(),
		PasswordHash: "hashedpassword",
	}
	err := userRepo.Create(ctx, user)
	require.NoError(t, err)

	// Create test game and puzzle for puzzle events
	game := repoTesting.CreateTestGame(t, gameRepo, user.ID)
	testPuzzle := repoTesting.CreateTestPuzzle(t, puzzleRepo, game.ID, user.ID)

	t.Run("CreateSignInEvent", func(t *testing.T) {
		data := models.SignInEventData{
			UserAgent: "Mozilla/5.0",
		}

		err := eventService.CreateSignInEvent(ctx, user.ID, data)
		assert.NoError(t, err)

		// Verify the event was created by listing user events
		events, totalCount, err := eventService.ListUserEvents(ctx, user.ID, repository.EventFilter{
			EventTypes: []models.EventType{models.EventTypeSignIn},
		}, 0, 10)
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, int(totalCount), 1)
		assert.GreaterOrEqual(t, len(events), 1)

		// Check the event data
		found := false
		for _, event := range events {
			if event.EventType == models.EventTypeSignIn {
				var eventData models.SignInEventData
				err := json.Unmarshal(event.EventData, &eventData)
				assert.NoError(t, err)
				assert.Equal(t, data.UserAgent, eventData.UserAgent)
				found = true
				break
			}
		}
		assert.True(t, found, "Sign-in event not found")
	})

	t.Run("CreateGameReviewStartEvent", func(t *testing.T) {
		gameID := uuid.New().String()

		err := eventService.CreateGameReviewStartEvent(ctx, user.ID, gameID)
		assert.NoError(t, err)

		// Verify the event was created
		events, _, err := eventService.ListUserEvents(ctx, user.ID, repository.EventFilter{
			EventTypes: []models.EventType{models.EventTypeGameReview},
		}, 0, 10)
		assert.NoError(t, err)

		// Find the start event
		found := false
		for _, event := range events {
			if event.EventType == models.EventTypeGameReview &&
				event.EventSubType != nil &&
				*event.EventSubType == models.EventSubTypeStart {
				var eventData models.GameReviewEventData
				err := json.Unmarshal(event.EventData, &eventData)
				assert.NoError(t, err)
				assert.Equal(t, gameID, eventData.GameID)
				assert.Nil(t, eventData.Duration) // Should be nil for start event
				found = true
				break
			}
		}
		assert.True(t, found, "Game review start event not found")
	})

	t.Run("CreateGameReviewEndEvent", func(t *testing.T) {
		gameID := uuid.New().String()
		duration := 300 // 5 minutes

		err := eventService.CreateGameReviewEndEvent(ctx, user.ID, gameID, duration)
		assert.NoError(t, err)

		// Verify the event was created
		events, _, err := eventService.ListUserEvents(ctx, user.ID, repository.EventFilter{
			EventTypes: []models.EventType{models.EventTypeGameReview},
		}, 0, 10)
		assert.NoError(t, err)

		// Find the end event
		found := false
		for _, event := range events {
			if event.EventType == models.EventTypeGameReview &&
				event.EventSubType != nil &&
				*event.EventSubType == models.EventSubTypeEnd {
				var eventData models.GameReviewEventData
				err := json.Unmarshal(event.EventData, &eventData)
				assert.NoError(t, err)
				assert.Equal(t, gameID, eventData.GameID)
				assert.NotNil(t, eventData.Duration)
				assert.Equal(t, duration, *eventData.Duration)
				found = true
				break
			}
		}
		assert.True(t, found, "Game review end event not found")
	})

	t.Run("CreatePuzzleSprintStartEvent", func(t *testing.T) {
		sprintID := uuid.New().String()

		err := eventService.CreatePuzzleSprintStartEvent(ctx, user.ID, sprintID)
		assert.NoError(t, err)

		// Verify the event was created
		events, _, err := eventService.ListUserEvents(ctx, user.ID, repository.EventFilter{
			EventTypes: []models.EventType{models.EventTypePuzzleSprint},
		}, 0, 10)
		assert.NoError(t, err)

		// Find the start event
		found := false
		for _, event := range events {
			if event.EventType == models.EventTypePuzzleSprint &&
				event.EventSubType != nil &&
				*event.EventSubType == models.EventSubTypeStart {
				var eventData models.PuzzleSprintEventData
				err := json.Unmarshal(event.EventData, &eventData)
				assert.NoError(t, err)
				assert.Equal(t, sprintID, eventData.SprintID)
				assert.Nil(t, eventData.Duration)
				found = true
				break
			}
		}
		assert.True(t, found, "Puzzle sprint start event not found")
	})

	t.Run("CreatePuzzleSprintEndEvent", func(t *testing.T) {
		sprintID := uuid.New().String()
		status := models.SprintStatusCompletedSuccess
		duration := 600 // 10 minutes
		succededPuzzles := 12
		failedPuzzles := 3
		eloType := "puzzle_sprint"
		eloChange := 8

		err := eventService.CreatePuzzleSprintEndEvent(ctx, user.ID, sprintID, status, duration, succededPuzzles, failedPuzzles, eloType, eloChange, models.AttemptTypeRegular)
		assert.NoError(t, err)

		// Verify the event was created
		events, _, err := eventService.ListUserEvents(ctx, user.ID, repository.EventFilter{
			EventTypes: []models.EventType{models.EventTypePuzzleSprint},
		}, 0, 10)
		assert.NoError(t, err)

		// Find the end event
		found := false
		for _, event := range events {
			if event.EventType == models.EventTypePuzzleSprint &&
				event.EventSubType != nil &&
				*event.EventSubType == models.EventSubTypeEnd {
				var eventData models.PuzzleSprintEventData
				err := json.Unmarshal(event.EventData, &eventData)
				assert.NoError(t, err)
				assert.Equal(t, sprintID, eventData.SprintID)
				assert.NotNil(t, eventData.Duration)
				assert.Equal(t, duration, *eventData.Duration)
				assert.NotNil(t, eventData.SucceededPuzzles)
				assert.Equal(t, succededPuzzles, *eventData.SucceededPuzzles)
				assert.NotNil(t, eventData.FailedPuzzles)
				assert.Equal(t, failedPuzzles, *eventData.FailedPuzzles)
				assert.Equal(t, eloType, eventData.EloType)
				assert.NotNil(t, eventData.EloChange)
				assert.Equal(t, eloChange, *eventData.EloChange)
				found = true
				break
			}
		}
		assert.True(t, found, "Puzzle sprint end event not found")
	})

	t.Run("CreatePuzzleEvent", func(t *testing.T) {
		data := models.PuzzleEventData{
			PuzzleID:    testPuzzle.ID,
			PuzzleType:  models.PuzzleTypeUser,
			Solved:      true,
			TimeSpent:   45,
			MovesPlayed: []string{"e4", "e5", "Nf3"},
		}

		err := eventService.CreatePuzzleEvent(ctx, user.ID, data)
		assert.NoError(t, err)

		// Verify the event was created
		events, _, err := eventService.ListUserEvents(ctx, user.ID, repository.EventFilter{
			EventTypes: []models.EventType{models.EventTypePuzzle},
		}, 0, 10)
		assert.NoError(t, err)

		// Find the puzzle event
		found := false
		for _, event := range events {
			if event.EventType == models.EventTypePuzzle {
				var eventData models.PuzzleEventData
				err := json.Unmarshal(event.EventData, &eventData)
				assert.NoError(t, err)
				assert.Equal(t, data.PuzzleID, eventData.PuzzleID)
				assert.Equal(t, data.PuzzleType, eventData.PuzzleType)
				assert.Equal(t, data.Solved, eventData.Solved)
				assert.Equal(t, data.TimeSpent, eventData.TimeSpent)
				assert.Equal(t, data.MovesPlayed, eventData.MovesPlayed)
				found = true
				break
			}
		}
		assert.True(t, found, "Puzzle event not found")
	})

	t.Run("CreateEventWithCustomID", func(t *testing.T) {
		customID := uuid.New().String()
		eventData := json.RawMessage(`{"custom":"data"}`)

		err := eventService.CreateEventWithCustomID(ctx, customID, user.ID, models.EventTypeSignIn, nil, eventData)
		assert.NoError(t, err)

		// Verify the event was created with the custom ID
		event, err := eventService.GetEvent(ctx, customID)
		assert.NoError(t, err)
		assert.Equal(t, customID, event.ID)
		assert.Equal(t, user.ID, event.UserID)
		assert.Equal(t, models.EventTypeSignIn, event.EventType)
		assert.JSONEq(t, `{"custom":"data"}`, string(event.EventData))

		// Test idempotency - creating again with same ID should fail
		err = eventService.CreateEventWithCustomID(ctx, customID, user.ID, models.EventTypeGameReview, nil, json.RawMessage(`{"different":"data"}`))
		assert.Error(t, err)
		assert.Equal(t, repository.ErrEventAlreadyExists, err)
	})

	t.Run("CreatePuzzleSprintEndEvent_ArrowDuel", func(t *testing.T) {
		sprintID := uuid.New().String()
		status := models.SprintStatusCompletedSuccess
		duration := 300 // 5 minutes
		succeededPuzzles := 8
		failedPuzzles := 1
		eloType := "arrowduel 5/30"
		eloChange := 12

		err := eventService.CreatePuzzleSprintEndEvent(ctx, user.ID, sprintID, status, duration, succeededPuzzles, failedPuzzles, eloType, eloChange, models.AttemptTypeArrowDuel)
		assert.NoError(t, err)

		// Verify the event was created with arrow-duel attempt type
		events, _, err := eventService.ListUserEvents(ctx, user.ID, repository.EventFilter{
			EventTypes: []models.EventType{models.EventTypePuzzleSprint},
		}, 0, 10)
		assert.NoError(t, err)

		// Find the arrow-duel end event
		found := false
		for _, event := range events {
			if event.EventType == models.EventTypePuzzleSprint && event.EventSubType != nil && *event.EventSubType == models.EventSubTypeEnd {
				var eventData models.PuzzleSprintEventData
				err := json.Unmarshal(event.EventData, &eventData)
				assert.NoError(t, err)
				if eventData.SprintID == sprintID {
					assert.Equal(t, status, eventData.Status)
					assert.Equal(t, duration, *eventData.Duration)
					assert.Equal(t, succeededPuzzles, *eventData.SucceededPuzzles)
					assert.Equal(t, failedPuzzles, *eventData.FailedPuzzles)
					assert.Equal(t, eloType, eventData.EloType)
					assert.Equal(t, eloChange, *eventData.EloChange)
					assert.Equal(t, models.AttemptTypeArrowDuel, eventData.AttemptType)
					found = true
					break
				}
			}
		}
		assert.True(t, found, "Arrow-duel puzzle sprint end event not found")
	})

}
