package service

import (
	"context"
	"fmt"
	"testing"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/repository/common"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

// Mock UserEloRepository for testing
type TestMockUserEloRepository struct {
	mock.Mock
}

func (m *TestMockUserEloRepository) GetByUserIDAndEloType(ctx context.Context, userID string, eloType string) (*models.UserElo, error) {
	args := m.Called(ctx, userID, eloType)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.UserElo), args.Error(1)
}

func (m *TestMockUserEloRepository) GetByUserID(ctx context.Context, userID string) ([]models.UserElo, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]models.UserElo), args.Error(1)
}

func (m *TestMockUserEloRepository) Save(ctx context.Context, userElo *models.UserElo) error {
	args := m.Called(ctx, userElo)
	return args.Error(0)
}

// TestPuzzleService_SelectPuzzlesForUser_FallbackStrategies tests the enhanced puzzle selection logic
func TestPuzzleService_SelectPuzzlesForUser_FallbackStrategies(t *testing.T) {
	ctx := context.Background()

	t.Run("low_rating_user_fallback_to_no_themes", func(t *testing.T) {
		// Create mock repositories
		lichessRepo := &MockLichessPuzzleRepository{}
		eloRepo := &TestMockUserEloRepository{}

		// User has very low ELO rating
		eloRepo.On("GetByUserIDAndEloType", ctx, "low-elo-user", "fork 10/30").Return(&models.UserElo{Rating: 900}, nil)

		// First 4 strategies with themes fail (no puzzles found)
		for i, ratingRange := range []struct{ min, max int }{
			{800, 1000}, // ±100
			{800, 1100}, // ±200
			{800, 1300}, // ±400
			{800, 1500}, // ±600
		} {
			filter := common.LichessPuzzleFilter{
				MinRating:  ratingRange.min,
				MaxRating:  ratingRange.max,
				Themes:     []string{"fork"}, // With themes
				ExcludeIDs: []string{},
			}
			lichessRepo.On("GetRandomPuzzles", ctx, filter, 10).Return([]models.LichessPuzzle{}, nil).Once()
			t.Logf("Strategy %d: Rating range %d-%d with themes - no puzzles", i+1, ratingRange.min, ratingRange.max)
		}

		// Fifth strategy (±100 without themes) succeeds
		filter5 := common.LichessPuzzleFilter{
			MinRating:  800,
			MaxRating:  1000,
			Themes:     nil, // No themes
			ExcludeIDs: []string{},
		}
		puzzles := make([]models.LichessPuzzle, 10)
		for i := range puzzles {
			puzzles[i] = models.LichessPuzzle{ID: fmt.Sprintf("puzzle_%d", i), Rating: 900}
		}
		lichessRepo.On("GetRandomPuzzles", ctx, filter5, 10).Return(puzzles, nil).Once()

		// Create service
		service := &PuzzleService{
			lichessPuzzleRepo: lichessRepo,
			eloRepo:           eloRepo,
		}

		// Call the method
		result, err := service.SelectPuzzlesForUser(ctx, "low-elo-user", "fork 10/30", 10, []string{}, []string{"fork"})

		// Verify results
		require.NoError(t, err)
		assert.Len(t, result, 10)
		assert.Equal(t, "puzzle_0", result[0].ID)

		// Verify all expectations were met
		lichessRepo.AssertExpectations(t)
		eloRepo.AssertExpectations(t)
	})

	t.Run("normal_rating_user_succeeds_immediately", func(t *testing.T) {
		// Create mock repositories
		lichessRepo := &MockLichessPuzzleRepository{}
		eloRepo := &TestMockUserEloRepository{}

		// User has normal ELO rating
		eloRepo.On("GetByUserIDAndEloType", ctx, "normal-user", "mixed 10/30").Return(&models.UserElo{Rating: 1500}, nil)

		// First strategy (±100 with themes) succeeds immediately
		filter := common.LichessPuzzleFilter{
			MinRating:  1400,
			MaxRating:  1600,
			Themes:     []string{"tactics"},
			ExcludeIDs: []string{},
		}
		puzzles := make([]models.LichessPuzzle, 10)
		for i := range puzzles {
			puzzles[i] = models.LichessPuzzle{ID: fmt.Sprintf("tactics_puzzle_%d", i), Rating: 1500}
		}
		lichessRepo.On("GetRandomPuzzles", ctx, filter, 10).Return(puzzles, nil).Once()

		// Create service
		service := &PuzzleService{
			lichessPuzzleRepo: lichessRepo,
			eloRepo:           eloRepo,
		}

		// Call the method
		result, err := service.SelectPuzzlesForUser(ctx, "normal-user", "mixed 10/30", 10, []string{}, []string{"tactics"})

		// Verify results
		require.NoError(t, err)
		assert.Len(t, result, 10)
		assert.Equal(t, "tactics_puzzle_0", result[0].ID)

		// Verify all expectations were met
		lichessRepo.AssertExpectations(t)
		eloRepo.AssertExpectations(t)
	})

	t.Run("no_elo_user_uses_default_rating", func(t *testing.T) {
		// Create mock repositories
		lichessRepo := &MockLichessPuzzleRepository{}
		eloRepo := &TestMockUserEloRepository{}

		// User has no ELO record
		eloRepo.On("GetByUserIDAndEloType", ctx, "new-user", "mixed 10/30").Return(nil, repository.ErrNotFound)

		// First strategy uses default rating 600 (±100 = 500-700, but floored at 800)
		filter := common.LichessPuzzleFilter{
			MinRating:  800, // Floored at 800 for puzzle selection
			MaxRating:  700,
			Themes:     []string{"endgame"},
			ExcludeIDs: []string{},
		}
		puzzles := make([]models.LichessPuzzle, 5)
		for i := range puzzles {
			puzzles[i] = models.LichessPuzzle{ID: fmt.Sprintf("endgame_puzzle_%d", i), Rating: 600}
		}
		lichessRepo.On("GetRandomPuzzles", ctx, filter, 5).Return(puzzles, nil).Once()

		// Create service
		service := &PuzzleService{
			lichessPuzzleRepo: lichessRepo,
			eloRepo:           eloRepo,
		}

		// Call the method
		result, err := service.SelectPuzzlesForUser(ctx, "new-user", "mixed 10/30", 5, []string{}, []string{"endgame"})

		// Verify results
		require.NoError(t, err)
		assert.Len(t, result, 5)
		assert.Equal(t, "endgame_puzzle_0", result[0].ID)

		// Verify all expectations were met
		lichessRepo.AssertExpectations(t)
		eloRepo.AssertExpectations(t)
	})

	t.Run("ultimate_fallback_to_full_range", func(t *testing.T) {
		// Create mock repositories
		lichessRepo := &MockLichessPuzzleRepository{}
		eloRepo := &TestMockUserEloRepository{}

		// User has very low ELO rating
		eloRepo.On("GetByUserIDAndEloType", ctx, "extreme-low-user", "fork 10/30").Return(&models.UserElo{Rating: 800}, nil)

		// All strategies with themes fail
		themeStrategies := []struct{ min, max int }{
			{800, 900},  // ±100
			{800, 1000}, // ±200
			{800, 1200}, // ±400
			{800, 1400}, // ±600
		}
		for _, ratingRange := range themeStrategies {
			filter := common.LichessPuzzleFilter{
				MinRating:  ratingRange.min,
				MaxRating:  ratingRange.max,
				Themes:     []string{"fork"},
				ExcludeIDs: []string{},
			}
			lichessRepo.On("GetRandomPuzzles", ctx, filter, 10).Return([]models.LichessPuzzle{}, nil).Once()
		}

		// All strategies without themes also fail
		noThemeStrategies := []struct{ min, max int }{
			{800, 900},  // ±100 no themes
			{800, 1000}, // ±200 no themes
			{800, 1200}, // ±400 no themes
		}
		for _, ratingRange := range noThemeStrategies {
			filter := common.LichessPuzzleFilter{
				MinRating:  ratingRange.min,
				MaxRating:  ratingRange.max,
				Themes:     nil,
				ExcludeIDs: []string{},
			}
			lichessRepo.On("GetRandomPuzzles", ctx, filter, 10).Return([]models.LichessPuzzle{}, nil).Once()
		}

		// Ultimate fallback (full range, no themes) succeeds
		fallbackFilter := common.LichessPuzzleFilter{
			MinRating:  800,
			MaxRating:  2800,
			Themes:     nil,
			ExcludeIDs: []string{},
		}
		puzzles := make([]models.LichessPuzzle, 10)
		for i := range puzzles {
			puzzles[i] = models.LichessPuzzle{ID: fmt.Sprintf("fallback_puzzle_%d", i), Rating: 1500}
		}
		lichessRepo.On("GetRandomPuzzles", ctx, fallbackFilter, 10).Return(puzzles, nil).Once()

		// Create service
		service := &PuzzleService{
			lichessPuzzleRepo: lichessRepo,
			eloRepo:           eloRepo,
		}

		// Call the method
		result, err := service.SelectPuzzlesForUser(ctx, "extreme-low-user", "fork 10/30", 10, []string{}, []string{"fork"})

		// Verify results
		require.NoError(t, err)
		assert.Len(t, result, 10)
		assert.Equal(t, "fallback_puzzle_0", result[0].ID)

		// Verify all expectations were met
		lichessRepo.AssertExpectations(t)
		eloRepo.AssertExpectations(t)
	})
}
