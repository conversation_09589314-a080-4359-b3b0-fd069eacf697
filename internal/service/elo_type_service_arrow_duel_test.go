package service

import (
	"testing"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestEloTypeService_ArrowDuel(t *testing.T) {
	eloTypeService := NewEloTypeService()

	t.Run("IsArrowDuelEloType", func(t *testing.T) {
		tests := []struct {
			name     string
			eloType  string
			expected bool
		}{
			{
				name:     "arrow-duel ELO type",
				eloType:  "arrowduel 5/30",
				expected: true,
			},
			{
				name:     "arrow-duel ELO type different duration",
				eloType:  "arrowduel 10/20",
				expected: true,
			},
			{
				name:     "regular ELO type",
				eloType:  "mixed 10/30",
				expected: false,
			},
			{
				name:     "regular ELO type with theme",
				eloType:  "fork 5/20",
				expected: false,
			},
			{
				name:     "empty string",
				eloType:  "",
				expected: false,
			},
			{
				name:     "partial match",
				eloType:  "arrowduel",
				expected: false,
			},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				result := eloTypeService.IsArrowDuelEloType(tt.eloType)
				assert.Equal(t, tt.expected, result)
			})
		}
	})

	t.Run("GetEloTypeConfig_ArrowDuel", func(t *testing.T) {
		tests := []struct {
			name                  string
			eloType               string
			expectedTargetPuzzles int
			expectedDuration      int
			expectedMaxMistakes   int
			expectedTheme         string
			expectedAttemptType   models.AttemptType
			wantErr               bool
		}{
			{
				name:                  "arrow-duel 5/30",
				eloType:               "arrowduel 5/30",
				expectedTargetPuzzles: 10,
				expectedDuration:      300,
				expectedMaxMistakes:   2,
				expectedTheme:         "", // mixed theme for arrow-duel
				expectedAttemptType:   models.AttemptTypeArrowDuel,
				wantErr:               false,
			},
			{
				name:                  "arrow-duel 10/20",
				eloType:               "arrowduel 10/20",
				expectedTargetPuzzles: 30,
				expectedDuration:      600,
				expectedMaxMistakes:   2,
				expectedTheme:         "", // mixed theme for arrow-duel
				expectedAttemptType:   models.AttemptTypeArrowDuel,
				wantErr:               false,
			},
			{
				name:                  "arrow-duel 15/60",
				eloType:               "arrowduel 15/60",
				expectedTargetPuzzles: 15,
				expectedDuration:      900,
				expectedMaxMistakes:   2,
				expectedTheme:         "", // mixed theme for arrow-duel
				expectedAttemptType:   models.AttemptTypeArrowDuel,
				wantErr:               false,
			},
			{
				name:                  "regular mixed 10/30",
				eloType:               "mixed 10/30",
				expectedTargetPuzzles: 20,
				expectedDuration:      600,
				expectedMaxMistakes:   2,
				expectedTheme:         "", // mixed theme
				expectedAttemptType:   models.AttemptTypeRegular,
				wantErr:               false,
			},
			{
				name:                  "regular fork 5/20",
				eloType:               "fork 5/20",
				expectedTargetPuzzles: 15,
				expectedDuration:      300,
				expectedMaxMistakes:   2,
				expectedTheme:         "fork",
				expectedAttemptType:   models.AttemptTypeRegular,
				wantErr:               false,
			},
			{
				name:    "invalid arrow-duel duration",
				eloType: "arrowduel 31/30",
				wantErr: true,
			},
			{
				name:    "invalid arrow-duel per-puzzle time",
				eloType: "arrowduel 10/25",
				wantErr: true,
			},
			{
				name:    "invalid arrow-duel format",
				eloType: "arrowduel",
				wantErr: true,
			},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				config, err := eloTypeService.GetEloTypeConfig(tt.eloType)

				if tt.wantErr {
					assert.Error(t, err)
					assert.Nil(t, config)
					return
				}

				require.NoError(t, err)
				require.NotNil(t, config)

				// Verify configuration matches expected values
				assert.Equal(t, tt.expectedTargetPuzzles, config.TargetPuzzles)
				assert.Equal(t, tt.expectedDuration, config.DurationSeconds)
				assert.Equal(t, tt.expectedMaxMistakes, config.MaxMistakes)
				assert.Equal(t, tt.expectedTheme, config.Theme)
				assert.Equal(t, tt.expectedAttemptType, config.AttemptType)

				// Test puzzle filter generation
				userRating := 1500
				filter, err := eloTypeService.GetPuzzleFilter(userRating, config)
				require.NoError(t, err)
				require.NotNil(t, filter)

				// Verify filter configuration
				assert.Equal(t, userRating-100, filter.MinRating)
				assert.Equal(t, userRating+100, filter.MaxRating)
				if tt.expectedTheme == "" {
					assert.Nil(t, filter.Themes)
				} else {
					assert.Equal(t, []string{tt.expectedTheme}, filter.Themes)
				}
			})
		}
	})

	t.Run("ValidateEloType_ArrowDuel", func(t *testing.T) {
		validArrowDuelTypes := []string{
			"arrowduel 5/30",
			"arrowduel 10/20",
			"arrowduel 15/60",
			"arrowduel 1/5",
			"arrowduel 30/60",
		}

		for _, eloType := range validArrowDuelTypes {
			t.Run("valid_"+eloType, func(t *testing.T) {
				err := eloTypeService.ValidateEloType(eloType)
				assert.NoError(t, err)
			})
		}

		invalidArrowDuelTypes := []string{
			"arrowduel 31/30",       // invalid duration (over 30 minutes)
			"arrowduel 10/25",       // invalid per-puzzle time
			"arrowduel10/30",        // missing space
			"arrowduel 10-30",       // wrong separator
			"arrowduel extra 10/30", // too many parts
			"arrowduel",             // incomplete
		}

		for _, eloType := range invalidArrowDuelTypes {
			t.Run("invalid_"+eloType, func(t *testing.T) {
				err := eloTypeService.ValidateEloType(eloType)
				assert.Error(t, err)
			})
		}
	})

	t.Run("ArrowDuel_AlwaysUsesMixedTheme", func(t *testing.T) {
		// Arrow-duel should always use mixed theme regardless of what's specified
		config, err := eloTypeService.GetEloTypeConfig("arrowduel 10/30")
		require.NoError(t, err)
		require.NotNil(t, config)

		// Theme should be empty (which means mixed)
		assert.Equal(t, "", config.Theme)
		assert.Equal(t, models.AttemptTypeArrowDuel, config.AttemptType)

		// Test puzzle filter generation
		userRating := 1500
		filter, err := eloTypeService.GetPuzzleFilter(userRating, config)
		require.NoError(t, err)
		require.NotNil(t, filter)

		// Should have no theme filtering (mixed)
		assert.Nil(t, filter.Themes)
	})

	t.Run("BackwardCompatibility_DefaultEloType", func(t *testing.T) {
		// Empty ELO type should default to regular mixed type
		config, err := eloTypeService.GetEloTypeConfig("")
		require.NoError(t, err)
		require.NotNil(t, config)

		assert.Equal(t, models.AttemptTypeRegular, config.AttemptType)
		assert.Equal(t, "", config.Theme)            // mixed theme
		assert.Equal(t, 20, config.TargetPuzzles)    // 10 minutes / 30 seconds
		assert.Equal(t, 600, config.DurationSeconds) // 10 minutes
		assert.Equal(t, 2, config.MaxMistakes)
	})
}
