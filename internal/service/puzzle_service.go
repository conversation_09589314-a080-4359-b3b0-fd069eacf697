package service

import (
	"context"
	"errors"
	"fmt"

	"github.com/chessticize/chessticize-server/internal/logger"
	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/repository/common"
	"github.com/lib/pq"
	"gorm.io/gorm"
)

type PuzzleService struct {
	sprintRepo              repository.ISprintRepository
	sprintPuzzleRepo        repository.ISprintPuzzleRepository
	sprintPuzzleAttemptRepo repository.ISprintPuzzleAttemptRepository
	lichessPuzzleRepo       repository.ILichessPuzzleRepository
	userLichessPuzzleRepo   repository.IUserLichessPuzzleStatsRepository
	eloRepo                 repository.IUserEloRepository
	eventService            *EventService
}

func NewPuzzleService(
	sprintRepo repository.ISprintRepository,
	sprintPuzzleRepo repository.ISprintPuzzleRepository,
	sprintPuzzleAttemptRepo repository.ISprintPuzzleAttemptRepository,
	lichessPuzzleRepo repository.ILichessPuzzleRepository,
	userLichessPuzzleRepo repository.IUserLichessPuzzleStatsRepository,
	eloRepo repository.IUserEloRepository,
	eventService *EventService,
) IPuzzleService {
	return &PuzzleService{
		sprintRepo:              sprintRepo,
		sprintPuzzleRepo:        sprintPuzzleRepo,
		sprintPuzzleAttemptRepo: sprintPuzzleAttemptRepo,
		lichessPuzzleRepo:       lichessPuzzleRepo,
		userLichessPuzzleRepo:   userLichessPuzzleRepo,
		eloRepo:                 eloRepo,
		eventService:            eventService,
	}
}

// GetPuzzlesForSprint retrieves puzzles for a sprint session with pagination
func (s *PuzzleService) GetPuzzlesForSprint(ctx context.Context, sprintID string, userID string, count int, offset int) ([]models.LichessPuzzle, int, error) {
	// Validate sprint access
	sprint, err := s.sprintRepo.GetByID(ctx, sprintID)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get sprint: %w", err)
	}

	if sprint.UserID != userID {
		return nil, 0, fmt.Errorf("user does not have access to this sprint")
	}

	// Get sprint puzzles
	sprintPuzzles, err := s.sprintPuzzleRepo.GetBySprintID(ctx, sprintID)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get sprint puzzles: %w", err)
	}

	totalCount := len(sprintPuzzles)

	// Apply pagination
	start := offset
	end := offset + count
	if start >= len(sprintPuzzles) {
		return []models.LichessPuzzle{}, totalCount, nil
	}
	if end > len(sprintPuzzles) {
		end = len(sprintPuzzles)
	}

	// Get the actual puzzle data
	puzzles := make([]models.LichessPuzzle, 0, end-start)
	for i := start; i < end; i++ {
		puzzle, err := s.lichessPuzzleRepo.GetByID(ctx, sprintPuzzles[i].LichessPuzzleID)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to get puzzle %s: %w", sprintPuzzles[i].LichessPuzzleID, err)
		}
		puzzles = append(puzzles, *puzzle)
	}

	return puzzles, totalCount, nil
}

// SubmitPuzzleResults processes puzzle attempt results for a sprint
func (s *PuzzleService) SubmitPuzzleResults(ctx context.Context, sprintID string, userID string, results []PuzzleAttemptResult) (*SprintResultResponse, error) {
	// Validate sprint access
	sprint, err := s.sprintRepo.GetByID(ctx, sprintID)
	if err != nil {
		return nil, fmt.Errorf("failed to get sprint: %w", err)
	}

	if sprint.UserID != userID {
		return nil, fmt.Errorf("user does not have access to this sprint")
	}

	if sprint.Status != models.SprintStatusActive {
		return nil, fmt.Errorf("sprint is not active")
	}

	// Prepare sprint puzzle attempts for batch creation
	attempts := make([]models.SprintPuzzleAttempt, 0, len(results))
	processedCount := 0

	for _, result := range results {
		// Check if this puzzle attempt already exists to prevent duplicates
		existingAttempt, err := s.sprintPuzzleAttemptRepo.GetBySprintUserAndPuzzle(ctx, sprintID, userID, result.PuzzleID)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("failed to check for existing attempt: %w", err)
		}

		// Skip if attempt already exists (deduplication)
		if existingAttempt != nil {
			log := logger.FromContext(ctx)
			log.Warn().
				Str("user_id", userID).
				Str("sprint_id", sprintID).
				Str("puzzle_id", result.PuzzleID).
				Msg("Duplicate puzzle attempt detected, skipping")
			continue
		}

		// Set default attempt type if not provided
		attemptType := result.AttemptType
		if attemptType == "" {
			attemptType = "regular"
		}

		// Create sprint puzzle attempt record
		attempt := models.SprintPuzzleAttempt{
			SprintID:         sprintID,
			UserID:           userID,
			LichessPuzzleID:  result.PuzzleID,
			SequenceInSprint: result.SequenceInSprint,
			UserMoves:        pq.StringArray(result.UserMoves),
			WasCorrect:       result.WasCorrect,
			TimeTakenMs:      result.TimeTakenMs,
			AttemptedAt:      result.AttemptedAt,
			AttemptType:      attemptType,
			CandidateMoves:   pq.StringArray(result.CandidateMoves),
			ChosenMove:       result.ChosenMove,
		}
		attempts = append(attempts, attempt)

		// Create puzzle event for tracking in UserLichessPuzzleStats
		// This will be processed by the database trigger to update stats
		puzzleEventData := models.PuzzleEventData{
			PuzzleID:       result.PuzzleID,
			PuzzleType:     models.PuzzleTypeLichess,
			AttemptType:    models.AttemptType(attemptType),
			Solved:         result.WasCorrect,
			TimeSpent:      result.TimeTakenMs / 1000, // Convert to seconds
			MovesPlayed:    result.UserMoves,
			CandidateMoves: result.CandidateMoves, // Will be empty for regular attempts
			ChosenMove:     result.ChosenMove,     // Will be nil for regular attempts
		}

		// Create the puzzle event (this will trigger stats updates via database triggers)
		err = s.eventService.CreatePuzzleEvent(ctx, userID, puzzleEventData)
		if err != nil {
			// Note: We don't fail the sprint if event creation fails, just log it
			log := logger.FromContext(ctx)
			log.Error().Err(err).Str("user_id", userID).Str("puzzle_id", result.PuzzleID).Msg("Failed to create puzzle event")
		}

		// Update sprint counters
		if result.WasCorrect {
			sprint.PuzzlesSolved++
		} else {
			sprint.MistakesMade++
		}
		processedCount++
	}

	// Save sprint puzzle attempts in batch
	if len(attempts) > 0 {
		err = s.sprintPuzzleAttemptRepo.CreateBatch(ctx, attempts)
		if err != nil {
			return nil, fmt.Errorf("failed to save sprint puzzle attempts: %w", err)
		}
	}

	// Update sprint
	err = s.sprintRepo.Update(ctx, sprint)
	if err != nil {
		return nil, fmt.Errorf("failed to update sprint: %w", err)
	}

	// Check if sprint should be completed
	sessionStatus := sprint.Status
	// Note: Sprint completion is handled by the client calling EndSprint
	// This allows for more flexible sprint management and user control

	return &SprintResultResponse{
		ProcessedCount: processedCount,
		SessionStatus:  sessionStatus,
		MistakesCount:  sprint.MistakesMade,
	}, nil
}

// SelectPuzzlesForUser selects appropriate puzzles for a user based on their ELO rating and themes
func (s *PuzzleService) SelectPuzzlesForUser(ctx context.Context, userID string, eloType string, count int, excludeIDs []string, themes []string) ([]models.LichessPuzzle, error) {
	// Get user's current ELO rating
	userElo, err := s.eloRepo.GetByUserIDAndEloType(ctx, userID, eloType)
	if err != nil {
		// If no ELO found, use a default rating
		if err == repository.ErrNotFound {
			userElo = &models.UserElo{
				Rating: int(DefaultRating), // Default starting rating
			}
		} else {
			return nil, fmt.Errorf("failed to get user ELO: %w", err)
		}
	}

	// Try multiple search strategies with progressively wider ranges
	searchStrategies := []struct {
		name        string
		minRating   int
		maxRating   int
		useThemes   bool
		description string
	}{
		{
			name:        "preferred",
			minRating:   max(800, userElo.Rating-100),
			maxRating:   userElo.Rating + 100,
			useThemes:   true,
			description: "±100 rating with themes",
		},
		{
			name:        "wider",
			minRating:   max(800, userElo.Rating-200),
			maxRating:   userElo.Rating + 200,
			useThemes:   true,
			description: "±200 rating with themes",
		},
		{
			name:        "much_wider",
			minRating:   max(800, userElo.Rating-400),
			maxRating:   userElo.Rating + 400,
			useThemes:   true,
			description: "±400 rating with themes",
		},
		{
			name:        "very_wide",
			minRating:   max(800, userElo.Rating-600),
			maxRating:   userElo.Rating + 600,
			useThemes:   true,
			description: "±600 rating with themes",
		},
		{
			name:        "no_themes_preferred",
			minRating:   max(800, userElo.Rating-100),
			maxRating:   userElo.Rating + 100,
			useThemes:   false,
			description: "±100 rating without themes",
		},
		{
			name:        "no_themes_wider",
			minRating:   max(800, userElo.Rating-200),
			maxRating:   userElo.Rating + 200,
			useThemes:   false,
			description: "±200 rating without themes",
		},
		{
			name:        "no_themes_much_wider",
			minRating:   max(800, userElo.Rating-400),
			maxRating:   userElo.Rating + 400,
			useThemes:   false,
			description: "±400 rating without themes",
		},
		{
			name:        "fallback",
			minRating:   800,
			maxRating:   2800, // Cover the full range of puzzle ratings
			useThemes:   false,
			description: "full rating range without themes",
		},
	}

	var puzzles []models.LichessPuzzle
	log := logger.FromContext(ctx)

	for _, strategy := range searchStrategies {
		if len(puzzles) >= count {
			break // We have enough puzzles
		}

		// Determine themes to use for this strategy
		var strategyThemes []string
		if strategy.useThemes {
			strategyThemes = themes
		}

		// Create filter for this strategy
		filter := common.LichessPuzzleFilter{
			MinRating:  strategy.minRating,
			MaxRating:  strategy.maxRating,
			Themes:     strategyThemes,
			ExcludeIDs: excludeIDs,
		}

		// Try to get additional puzzles with this strategy
		needed := count - len(puzzles)
		additionalPuzzles, err := s.lichessPuzzleRepo.GetRandomPuzzles(ctx, filter, needed)
		if err != nil {
			log.Warn().
				Err(err).
				Str("strategy", strategy.name).
				Str("user_id", userID).
				Str("elo_type", eloType).
				Int("user_rating", userElo.Rating).
				Msg("Failed to get puzzles with strategy")
			continue
		}

		if len(additionalPuzzles) > 0 {
			log.Debug().
				Str("strategy", strategy.name).
				Str("description", strategy.description).
				Int("found", len(additionalPuzzles)).
				Int("needed", needed).
				Int("user_rating", userElo.Rating).
				Str("user_id", userID).
				Msg("Found puzzles with search strategy")

			puzzles = append(puzzles, additionalPuzzles...)

			// Add the newly found puzzle IDs to excludeIDs to prevent duplicates in subsequent strategies
			for _, puzzle := range additionalPuzzles {
				excludeIDs = append(excludeIDs, puzzle.ID)
			}
		}
	}

	if len(puzzles) == 0 {
		log.Warn().
			Str("user_id", userID).
			Str("elo_type", eloType).
			Int("user_rating", userElo.Rating).
			Strs("themes", themes).
			Int("exclude_count", len(excludeIDs)).
			Msg("No puzzles found with any search strategy")
		return nil, fmt.Errorf("no puzzles available for user rating %d with themes %v", userElo.Rating, themes)
	}

	// Limit to requested count if we found more than needed
	if len(puzzles) > count {
		puzzles = puzzles[:count]
	}

	log.Debug().
		Int("found", len(puzzles)).
		Int("requested", count).
		Int("user_rating", userElo.Rating).
		Str("user_id", userID).
		Msg("Successfully selected puzzles for user")

	return puzzles, nil
}

// max returns the maximum of two integers
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}
