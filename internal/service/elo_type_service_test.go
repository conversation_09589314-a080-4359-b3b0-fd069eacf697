package service

import (
	"testing"

	"github.com/chessticize/chessticize-server/internal/repository/common"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestEloTypeService_ValidateEloType(t *testing.T) {
	service := NewEloTypeService()

	tests := []struct {
		name    string
		eloType string
		wantErr bool
		errMsg  string
	}{
		{
			name:    "valid default type",
			eloType: "mixed 10/30",
			wantErr: false,
		},
		{
			name:    "valid fork theme",
			eloType: "fork 5/20",
			wantErr: false,
		},
		{
			name:    "valid mateIn1 theme",
			eloType: "mateIn1 20/60",
			wantErr: false,
		},
		{
			name:    "empty string uses default",
			eloType: "",
			wantErr: false,
		},
		{
			name:    "invalid theme",
			eloType: "invalidtheme 10/30",
			wantErr: true,
			errMsg:  "invalid theme",
		},
		{
			name:    "invalid duration",
			eloType: "mixed 31/30",
			wantErr: true,
			errMsg:  "invalid duration",
		},
		{
			name:    "invalid per-puzzle time",
			eloType: "mixed 10/25",
			wantErr: true,
			errMsg:  "invalid per-puzzle time",
		},
		{
			name:    "missing space separator",
			eloType: "mixed10/30",
			wantErr: true,
			errMsg:  "invalid ELO type format",
		},
		{
			name:    "missing slash separator",
			eloType: "mixed 10-30",
			wantErr: true,
			errMsg:  "invalid duration/time format",
		},
		{
			name:    "too many parts",
			eloType: "mixed extra 10/30",
			wantErr: true,
			errMsg:  "invalid ELO type format",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := service.ValidateEloType(tt.eloType)
			if tt.wantErr {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestEloTypeService_GetEloTypeConfig(t *testing.T) {
	service := NewEloTypeService()

	tests := []struct {
		name     string
		eloType  string
		expected *EloTypeConfig
		wantErr  bool
	}{
		{
			name:    "default mixed type",
			eloType: "mixed 10/30",
			expected: &EloTypeConfig{
				Theme:            "",
				DurationSeconds:  600,
				PerPuzzleSeconds: 30,
				TargetPuzzles:    20,
				MaxMistakes:      2,
				AttemptType:      "regular",
			},
			wantErr: false,
		},
		{
			name:    "fork theme short duration",
			eloType: "fork 5/20",
			expected: &EloTypeConfig{
				Theme:            "fork",
				DurationSeconds:  300,
				PerPuzzleSeconds: 20,
				TargetPuzzles:    15,
				MaxMistakes:      2,
				AttemptType:      "regular",
			},
			wantErr: false,
		},
		{
			name:    "long duration with fast puzzles",
			eloType: "mateIn1 20/5",
			expected: &EloTypeConfig{
				Theme:            "mateIn1",
				DurationSeconds:  1200,
				PerPuzzleSeconds: 5,
				TargetPuzzles:    240, // 1200 / 5 = 240
				MaxMistakes:      2,
				AttemptType:      "regular",
			},
			wantErr: false,
		},
		{
			name:    "short duration with slow puzzles",
			eloType: "sacrifice 5/60",
			expected: &EloTypeConfig{
				Theme:            "sacrifice",
				DurationSeconds:  300,
				PerPuzzleSeconds: 60,
				TargetPuzzles:    5, // 300 / 60 = 5
				MaxMistakes:      2,
				AttemptType:      "regular",
			},
			wantErr: false,
		},
		{
			name:    "minimum duration with 15 second puzzles",
			eloType: "mixed 1/15",
			expected: &EloTypeConfig{
				Theme:            "",
				DurationSeconds:  60,
				PerPuzzleSeconds: 15,
				TargetPuzzles:    4, // 60 / 15 = 4
				MaxMistakes:      2,
				AttemptType:      "regular",
			},
			wantErr: false,
		},
		{
			name:    "maximum duration with fast puzzles",
			eloType: "fork 30/5",
			expected: &EloTypeConfig{
				Theme:            "fork",
				DurationSeconds:  1800,
				PerPuzzleSeconds: 5,
				TargetPuzzles:    360, // 1800 / 5 = 360
				MaxMistakes:      2,
				AttemptType:      "regular",
			},
			wantErr: false,
		},
		{
			name:    "empty string uses default",
			eloType: "",
			expected: &EloTypeConfig{
				Theme:            "",
				DurationSeconds:  600,
				PerPuzzleSeconds: 30,
				TargetPuzzles:    20,
				MaxMistakes:      2,
				AttemptType:      "regular",
			},
			wantErr: false,
		},
		{
			name:    "invalid type",
			eloType: "invalid 10/30",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config, err := service.GetEloTypeConfig(tt.eloType)
			if tt.wantErr {
				require.Error(t, err)
				assert.Nil(t, config)
			} else {
				require.NoError(t, err)
				require.NotNil(t, config)
				assert.Equal(t, tt.expected, config)
			}
		})
	}
}

func TestEloTypeService_GetPuzzleFilter(t *testing.T) {
	service := NewEloTypeService()

	tests := []struct {
		name       string
		userRating int
		config     *EloTypeConfig
		expected   *common.LichessPuzzleFilter
		wantErr    bool
	}{
		{
			name:       "mixed theme no filtering",
			userRating: 1500,
			config: &EloTypeConfig{
				Theme:            "",
				DurationSeconds:  600,
				PerPuzzleSeconds: 30,
				TargetPuzzles:    20,
				MaxMistakes:      2,
			},
			expected: &common.LichessPuzzleFilter{
				MinRating:  1400,
				MaxRating:  1600,
				Themes:     nil,
				ExcludeIDs: nil,
			},
			wantErr: false,
		},
		{
			name:       "specific theme filtering",
			userRating: 1200,
			config: &EloTypeConfig{
				Theme:            "fork",
				DurationSeconds:  300,
				PerPuzzleSeconds: 20,
				TargetPuzzles:    15,
				MaxMistakes:      2,
			},
			expected: &common.LichessPuzzleFilter{
				MinRating:  1100,
				MaxRating:  1300,
				Themes:     []string{"fork"},
				ExcludeIDs: nil,
			},
			wantErr: false,
		},
		{
			name:       "low rating with minimum floor",
			userRating: 900,
			config: &EloTypeConfig{
				Theme:            "mateIn1",
				DurationSeconds:  600,
				PerPuzzleSeconds: 30,
				TargetPuzzles:    20,
				MaxMistakes:      2,
			},
			expected: &common.LichessPuzzleFilter{
				MinRating:  800, // floored at 800
				MaxRating:  1000,
				Themes:     []string{"mateIn1"},
				ExcludeIDs: nil,
			},
			wantErr: false,
		},
		{
			name:       "nil config",
			userRating: 1500,
			config:     nil,
			wantErr:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			filter, err := service.GetPuzzleFilter(tt.userRating, tt.config)
			if tt.wantErr {
				require.Error(t, err)
				assert.Nil(t, filter)
			} else {
				require.NoError(t, err)
				require.NotNil(t, filter)
				assert.Equal(t, tt.expected, filter)
			}
		})
	}
}

func TestEloTypeService_CalculateTargetPuzzles(t *testing.T) {
	service := &EloTypeService{}

	tests := []struct {
		name             string
		durationSeconds  int
		perPuzzleSeconds int
		expected         int
	}{
		{
			name:             "normal calculation",
			durationSeconds:  600,
			perPuzzleSeconds: 30,
			expected:         20,
		},
		{
			name:             "small duration calculation",
			durationSeconds:  60,
			perPuzzleSeconds: 30,
			expected:         2, // 60 / 30 = 2
		},
		{
			name:             "large duration calculation",
			durationSeconds:  1200,
			perPuzzleSeconds: 5,
			expected:         240, // 1200 / 5 = 240
		},
		{
			name:             "another calculation",
			durationSeconds:  150,
			perPuzzleSeconds: 30,
			expected:         5, // 150 / 30 = 5
		},
		{
			name:             "yet another calculation",
			durationSeconds:  180,
			perPuzzleSeconds: 30,
			expected:         6, // 180 / 30 = 6
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.calculateTargetPuzzles(tt.durationSeconds, tt.perPuzzleSeconds)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestEloTypeService_GetValidOptions(t *testing.T) {
	service := NewEloTypeService()

	t.Run("GetValidThemes", func(t *testing.T) {
		themes := service.GetValidThemes()
		assert.Contains(t, themes, "mixed")
		assert.Contains(t, themes, "fork")
		assert.Contains(t, themes, "mateIn1")
		assert.Contains(t, themes, "sacrifice")
		assert.GreaterOrEqual(t, len(themes), 25) // Should have all the themes from the list
	})

	t.Run("GetValidDurations", func(t *testing.T) {
		durations := service.GetValidDurations()
		assert.Contains(t, durations, "1")
		assert.Contains(t, durations, "5")
		assert.Contains(t, durations, "10")
		assert.Contains(t, durations, "20")
		assert.Contains(t, durations, "30")
		assert.Len(t, durations, 30) // 1-30 minutes
	})

	t.Run("GetValidPerPuzzleTimes", func(t *testing.T) {
		times := service.GetValidPerPuzzleTimes()
		assert.Contains(t, times, "5")
		assert.Contains(t, times, "10")
		assert.Contains(t, times, "15")
		assert.Contains(t, times, "20")
		assert.Contains(t, times, "30")
		assert.Contains(t, times, "60")
		assert.Len(t, times, 6)
	})
}
