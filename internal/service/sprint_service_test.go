package service

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestSprintService_EloTypeServiceIntegration tests that SprintService correctly integrates with EloTypeService
func TestSprintService_EloTypeServiceIntegration(t *testing.T) {
	// Create real EloTypeService
	eloTypeService := NewEloTypeService()

	tests := []struct {
		name                  string
		eloType               string
		expectedTargetPuzzles int
		expectedDuration      int
		expectedMaxMistakes   int
		expectedTheme         string
		wantErr               bool
	}{
		{
			name:                  "default mixed type",
			eloType:               "mixed 10/30",
			expectedTargetPuzzles: 20,
			expectedDuration:      600,
			expectedMaxMistakes:   2,
			expectedTheme:         "", // empty for mixed
			wantErr:               false,
		},
		{
			name:                  "fork theme short duration",
			eloType:               "fork 5/20",
			expectedTargetPuzzles: 15,
			expectedDuration:      300,
			expectedMaxMistakes:   2,
			expectedTheme:         "fork",
			wantErr:               false,
		},
		{
			name:                  "mateIn1 theme long duration",
			eloType:               "mateIn1 20/60",
			expectedTargetPuzzles: 20,
			expectedDuration:      1200,
			expectedMaxMistakes:   2,
			expectedTheme:         "mateIn1",
			wantErr:               false,
		},
		{
			name:    "invalid elo type",
			eloType: "invalid 10/30",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Test EloTypeService configuration
			config, err := eloTypeService.GetEloTypeConfig(tt.eloType)
			if tt.wantErr {
				require.Error(t, err)
				assert.Nil(t, config)
				return
			}

			require.NoError(t, err)
			require.NotNil(t, config)

			// Verify configuration matches expected values
			assert.Equal(t, tt.expectedTargetPuzzles, config.TargetPuzzles)
			assert.Equal(t, tt.expectedDuration, config.DurationSeconds)
			assert.Equal(t, tt.expectedMaxMistakes, config.MaxMistakes)
			assert.Equal(t, tt.expectedTheme, config.Theme)

			// Test puzzle filter generation
			userRating := 1500
			filter, err := eloTypeService.GetPuzzleFilter(userRating, config)
			require.NoError(t, err)
			require.NotNil(t, filter)

			// Verify filter configuration
			assert.Equal(t, userRating-100, filter.MinRating)
			assert.Equal(t, userRating+100, filter.MaxRating)
			if tt.expectedTheme == "" {
				assert.Nil(t, filter.Themes)
			} else {
				assert.Equal(t, []string{tt.expectedTheme}, filter.Themes)
			}
		})
	}
}

// TestSprintService_EloTypeValidation tests that invalid ELO types are properly rejected
func TestSprintService_EloTypeValidation(t *testing.T) {
	eloTypeService := NewEloTypeService()

	invalidEloTypes := []string{
		"invalid 10/30",
		"mixed 31/30",       // invalid duration (over 30 minutes)
		"mixed 10/25",       // invalid per-puzzle time
		"mixed10/30",        // missing space
		"mixed 10-30",       // wrong separator
		"mixed extra 10/30", // too many parts
		"",                  // empty should use default and be valid
	}

	for _, eloType := range invalidEloTypes {
		t.Run("invalid_"+eloType, func(t *testing.T) {
			if eloType == "" {
				// Empty string should use default and be valid
				err := eloTypeService.ValidateEloType(eloType)
				assert.NoError(t, err)
			} else {
				err := eloTypeService.ValidateEloType(eloType)
				assert.Error(t, err)
			}
		})
	}
}
