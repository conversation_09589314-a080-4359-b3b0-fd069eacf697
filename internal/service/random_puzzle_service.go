package service

import (
	"context"
	"fmt"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/chessticize/chessticize-server/internal/repository/common"
)

// RandomLichessPuzzleRequest represents a request for random Lichess puzzles
type RandomLichessPuzzleRequest struct {
	MinRating int      `json:"min_rating" validate:"min=0,max=3000"`
	MaxRating int      `json:"max_rating" validate:"min=0,max=3000,gtfield=MinRating"`
	Themes    []string `json:"themes,omitempty"`
	Limit     int      `json:"limit" validate:"min=1,max=50"`
}

// RandomUserPuzzleRequest represents a request for random user puzzles
type RandomUserPuzzleRequest struct {
	MinMoveLength int        `json:"min_move_length" validate:"min=2,max=20"`
	MaxMoveLength int        `json:"max_move_length" validate:"min=2,max=20,gtfield=MinMoveLength"`
	Tags          []string   `json:"tags,omitempty"`
	Themes        []string   `json:"themes,omitempty"`
	GameTimeStart *time.Time `json:"game_time_start,omitempty"`
	GameTimeEnd   *time.Time `json:"game_time_end,omitempty"`
	Limit         int        `json:"limit" validate:"min=1,max=50"`
}

// RandomPuzzleService provides random puzzle selection functionality
type RandomPuzzleService struct {
	lichessPuzzleRepo          repository.ILichessPuzzleRepository
	puzzleRepo                 repository.IPuzzleRepository
	userPuzzleStatsRepo        repository.IUserPuzzleStatsRepository
	userLichessPuzzleStatsRepo repository.IUserLichessPuzzleStatsRepository
}

// NewRandomPuzzleService creates a new random puzzle service
func NewRandomPuzzleService(
	lichessPuzzleRepo repository.ILichessPuzzleRepository,
	puzzleRepo repository.IPuzzleRepository,
	userPuzzleStatsRepo repository.IUserPuzzleStatsRepository,
	userLichessPuzzleStatsRepo repository.IUserLichessPuzzleStatsRepository,
) *RandomPuzzleService {
	return &RandomPuzzleService{
		lichessPuzzleRepo:          lichessPuzzleRepo,
		puzzleRepo:                 puzzleRepo,
		userPuzzleStatsRepo:        userPuzzleStatsRepo,
		userLichessPuzzleStatsRepo: userLichessPuzzleStatsRepo,
	}
}

// GetRandomLichessPuzzles retrieves random Lichess puzzles for practice
// Automatically excludes puzzles disliked by the user
func (s *RandomPuzzleService) GetRandomLichessPuzzles(ctx context.Context, userID string, request RandomLichessPuzzleRequest) ([]models.LichessPuzzle, error) {
	// Validate request
	if err := s.validateLichessRequest(request); err != nil {
		return nil, fmt.Errorf("invalid request: %w", err)
	}

	// Get disliked lichess puzzle IDs for the user
	dislikedIDs, err := s.userLichessPuzzleStatsRepo.GetDislikedPuzzleIDs(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get disliked lichess puzzle IDs: %w", err)
	}

	// Use the repository method with filter
	filter := common.LichessPuzzleFilter{
		MinRating:  request.MinRating,
		MaxRating:  request.MaxRating,
		Themes:     request.Themes,
		ExcludeIDs: dislikedIDs,
	}
	puzzles, err := s.lichessPuzzleRepo.GetRandomPuzzles(ctx, filter, request.Limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get random Lichess puzzles: %w", err)
	}

	return puzzles, nil
}

// GetRandomUserPuzzles retrieves random user-generated puzzles for practice
// Automatically excludes puzzles disliked by the user
func (s *RandomPuzzleService) GetRandomUserPuzzles(ctx context.Context, userID string, request RandomUserPuzzleRequest) ([]models.Puzzle, error) {
	// Validate request
	if err := s.validateUserRequest(request); err != nil {
		return nil, fmt.Errorf("invalid request: %w", err)
	}

	// Get disliked puzzle IDs for the user
	dislikedIDs, err := s.userPuzzleStatsRepo.GetDislikedPuzzleIDs(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get disliked puzzle IDs: %w", err)
	}

	// Create filter with all the user-specified criteria
	filter := common.UserPuzzleFilter{
		MinMoveLength: request.MinMoveLength,
		MaxMoveLength: request.MaxMoveLength,
		Tags:          request.Tags,
		Themes:        request.Themes,
		GameTimeStart: request.GameTimeStart,
		GameTimeEnd:   request.GameTimeEnd,
		DislikedIDs:   dislikedIDs,
	}
	puzzles, err := s.puzzleRepo.GetRandomPuzzles(ctx, userID, filter, request.Limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get random user puzzles: %w", err)
	}

	return puzzles, nil
}

// validateLichessRequest validates the random Lichess puzzle request
func (s *RandomPuzzleService) validateLichessRequest(request RandomLichessPuzzleRequest) error {
	if request.MinRating < 0 || request.MinRating > 3000 {
		return fmt.Errorf("min_rating must be between 0 and 3000")
	}
	if request.MaxRating < 0 || request.MaxRating > 3000 {
		return fmt.Errorf("max_rating must be between 0 and 3000")
	}
	if request.MinRating > request.MaxRating {
		return fmt.Errorf("min_rating must be less than or equal to max_rating")
	}
	if request.Limit < 1 || request.Limit > 50 {
		return fmt.Errorf("limit must be between 1 and 50")
	}
	return nil
}

// validateUserRequest validates the random user puzzle request
func (s *RandomPuzzleService) validateUserRequest(request RandomUserPuzzleRequest) error {
	if request.MinMoveLength < 2 || request.MinMoveLength > 20 {
		return fmt.Errorf("min_move_length must be between 2 and 20")
	}
	if request.MaxMoveLength < 2 || request.MaxMoveLength > 20 {
		return fmt.Errorf("max_move_length must be between 2 and 20")
	}
	if request.MinMoveLength > request.MaxMoveLength {
		return fmt.Errorf("min_move_length must be less than or equal to max_move_length")
	}
	// Ensure move lengths are even numbers
	if request.MinMoveLength%2 != 0 {
		return fmt.Errorf("min_move_length must be an even number")
	}
	if request.MaxMoveLength%2 != 0 {
		return fmt.Errorf("max_move_length must be an even number")
	}
	if request.Limit < 1 || request.Limit > 50 {
		return fmt.Errorf("limit must be between 1 and 50")
	}
	// Validate time range if provided
	if request.GameTimeStart != nil && request.GameTimeEnd != nil {
		if request.GameTimeStart.After(*request.GameTimeEnd) {
			return fmt.Errorf("game_time_start must be before game_time_end")
		}
	}
	return nil
}
