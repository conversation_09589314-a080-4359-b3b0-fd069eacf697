package service

import (
	"context"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"time"

	"github.com/chessticize/chessticize-server/internal/config"
	"github.com/chessticize/chessticize-server/internal/middleware"
	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/repository"
	"github.com/golang-jwt/jwt/v5"
)

// hashSessionToken hashes a session token using SHA-256
// SHA-256 is used for session tokens to make them non-reversible when stored in the database.
// While SHA-256 is cryptographically stronger than needed for this use case (we're not protecting
// against collision attacks), it's widely trusted, fast enough, and provides good security.
func hashSessionToken(token string) string {
	hasher := sha256.New()
	hasher.Write([]byte(token))
	return hex.EncodeToString(hasher.Sum(nil))
}

type AuthService struct {
	jwtConfig          config.JWTConfig
	sessionTokenConfig config.SessionTokenConfig
	sessionTokenRepo   repository.ISessionTokenRepository
}

func NewAuthService(jwtConfig config.JWTConfig) *AuthService {
	return &AuthService{
		jwtConfig: jwtConfig,
	}
}

func NewAuthServiceWithSessionTokens(jwtConfig config.JWTConfig, sessionTokenConfig config.SessionTokenConfig, sessionTokenRepo repository.ISessionTokenRepository) *AuthService {
	return &AuthService{
		jwtConfig:          jwtConfig,
		sessionTokenConfig: sessionTokenConfig,
		sessionTokenRepo:   sessionTokenRepo,
	}
}

// GenerateToken creates a new JWT token for a user
func (s *AuthService) GenerateToken(userID, email string, isAdmin bool) (string, error) {
	expirationTime := time.Now().Add(time.Duration(s.jwtConfig.ExpiryMinutes) * time.Minute)

	claims := &middleware.Claims{
		UserID:  userID,
		Email:   email,
		IsAdmin: isAdmin,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(s.jwtConfig.Secret))

	if err != nil {
		return "", err
	}

	return tokenString, nil
}

// GenerateSessionToken creates a new session token for a user
func (s *AuthService) GenerateSessionToken(ctx context.Context, userID, userAgent string) (*models.SessionToken, error) {
	// Generate a random token
	tokenBytes := make([]byte, 32)
	if _, err := rand.Read(tokenBytes); err != nil {
		return nil, err
	}
	tokenString := base64.URLEncoding.EncodeToString(tokenBytes)

	// Hash the token before storing (like a password)
	tokenHash := hashSessionToken(tokenString)

	// Create session token
	sessionToken := &models.SessionToken{
		UserID:    userID,
		Token:     tokenHash, // Store the hash, not the raw token
		UserAgent: userAgent,
		ExpiresAt: time.Now().Add(time.Duration(s.sessionTokenConfig.ExpiryDays) * 24 * time.Hour),
	}

	err := s.sessionTokenRepo.Create(ctx, sessionToken)
	if err != nil {
		return nil, err
	}

	// Return the session token with the raw token for the response
	// but keep the hash in the database
	sessionToken.Token = tokenString
	return sessionToken, nil
}

// ValidateSessionToken validates a session token and returns the associated user
func (s *AuthService) ValidateSessionToken(ctx context.Context, tokenString string) (*models.SessionToken, error) {
	// Hash the input token to compare with stored hashes
	tokenHash := hashSessionToken(tokenString)

	sessionToken, err := s.sessionTokenRepo.GetByToken(ctx, tokenHash)
	if err != nil {
		return nil, err
	}

	// Check if token is expired
	if sessionToken.IsExpired() {
		return nil, repository.ErrNotFound
	}

	return sessionToken, nil
}

// ExtendSessionToken extends the expiration of a session token
func (s *AuthService) ExtendSessionToken(ctx context.Context, tokenString string) (*models.SessionToken, error) {
	// Hash the input token to compare with stored hashes
	tokenHash := hashSessionToken(tokenString)

	sessionToken, err := s.sessionTokenRepo.GetByToken(ctx, tokenHash)
	if err != nil {
		return nil, err
	}

	// Extend expiration
	sessionToken.ExpiresAt = time.Now().Add(time.Duration(s.sessionTokenConfig.ExpiryDays) * 24 * time.Hour)

	err = s.sessionTokenRepo.Update(ctx, sessionToken)
	if err != nil {
		return nil, err
	}

	return sessionToken, nil
}

// RevokeSessionToken revokes a session token by deleting it
func (s *AuthService) RevokeSessionToken(ctx context.Context, tokenID string) error {
	return s.sessionTokenRepo.Delete(ctx, tokenID)
}

// ListUserSessionTokens lists all session tokens for a user
func (s *AuthService) ListUserSessionTokens(ctx context.Context, userID string) ([]models.SessionToken, error) {
	return s.sessionTokenRepo.ListByUserID(ctx, userID)
}

// CleanupExpiredTokens removes expired session tokens
func (s *AuthService) CleanupExpiredTokens(ctx context.Context) (int64, error) {
	return s.sessionTokenRepo.DeleteExpired(ctx)
}

// GetSessionTokenByID retrieves a session token by its ID
func (s *AuthService) GetSessionTokenByID(ctx context.Context, tokenID string) (*models.SessionToken, error) {
	return s.sessionTokenRepo.GetByID(ctx, tokenID)
}
