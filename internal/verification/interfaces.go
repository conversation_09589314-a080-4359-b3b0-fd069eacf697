package verification

import (
	"context"

	"github.com/chessticize/chessticize-server/internal/models"
)

// IChessProfileVerifier defines the interface for verifying chess platform usernames
type IChessProfileVerifier interface {
	// VerifyProfile verifies that a username exists on the specified chess platform
	// Returns nil if the profile exists, or an error if it doesn't exist or verification fails
	VerifyProfile(ctx context.Context, platform models.ChessPlatform, username string) error
}
