package fake

import (
	"context"
	"fmt"
	"testing"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestFakeChessProfileVerifier_VerifyProfile_ValidUsers(t *testing.T) {
	verifier := NewFakeChessProfileVerifier()
	ctx := context.Background()

	testCases := []struct {
		name     string
		platform models.ChessPlatform
		username string
	}{
		{"ChessDotCom valid user", models.ChessDotCom, "zsmickycat"},
		{"ChessDotCom test user", models.ChessDotCom, "testuser1"},
		{"ChessDotCom any user", models.ChessDotCom, "anyusername"},
		{"Lichess valid user", models.LichessOrg, "zsmickycat"},
		{"Lichess test user", models.LichessOrg, "testuser1"},
		{"Lichess any user", models.LichessOrg, "anyusername"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := verifier.VerifyProfile(ctx, tc.platform, tc.username)
			assert.NoError(t, err, "Expected %s to be valid on %s (blacklist approach)", tc.username, tc.platform)
		})
	}
}

func TestFakeChessProfileVerifier_VerifyProfile_InvalidUsers(t *testing.T) {
	verifier := NewFakeChessProfileVerifier()
	ctx := context.Background()

	testCases := []struct {
		name     string
		platform models.ChessPlatform
		username string
		errorMsg string
	}{
		{"ChessDotCom nonexistent", models.ChessDotCom, "nonexistent", "chess.com account does not exist"},
		{"ChessDotCom notfound", models.ChessDotCom, "notfound", "chess.com account does not exist"},
		{"Lichess nonexistent", models.LichessOrg, "nonexistent", "lichess account does not exist"},
		{"Lichess notfound", models.LichessOrg, "notfound", "lichess account does not exist"},
		{"Lichess error404", models.LichessOrg, "error404", "lichess account does not exist"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := verifier.VerifyProfile(ctx, tc.platform, tc.username)
			require.Error(t, err)
			assert.Contains(t, err.Error(), tc.errorMsg)
		})
	}
}

func TestFakeChessProfileVerifier_VerifyProfile_UnsupportedPlatform(t *testing.T) {
	verifier := NewFakeChessProfileVerifier()
	ctx := context.Background()

	err := verifier.VerifyProfile(ctx, "unsupported", "testuser")
	require.Error(t, err)
	assert.Contains(t, err.Error(), "unsupported platform")
}

func TestFakeChessProfileVerifier_AddValidUsername(t *testing.T) {
	verifier := NewFakeChessProfileVerifier()
	ctx := context.Background()

	// In blacklist approach, "newuser" should initially be valid (not in blacklist)
	err := verifier.VerifyProfile(ctx, models.ChessDotCom, "newuser")
	assert.NoError(t, err, "newuser should be valid by default in blacklist approach")

	// Add "newuser" to blacklist first
	verifier.AddInvalidUsername(models.ChessDotCom, "newuser", fmt.Errorf("test error"))
	err = verifier.VerifyProfile(ctx, models.ChessDotCom, "newuser")
	require.Error(t, err)

	// Now use AddValidUsername to remove it from blacklist
	verifier.AddValidUsername(models.ChessDotCom, "newuser")
	err = verifier.VerifyProfile(ctx, models.ChessDotCom, "newuser")
	assert.NoError(t, err, "newuser should be valid after removing from blacklist")
}

func TestFakeChessProfileVerifier_AddErrorUsername(t *testing.T) {
	verifier := NewFakeChessProfileVerifier()
	ctx := context.Background()

	customError := assert.AnError
	verifier.AddErrorUsername(models.ChessDotCom, "erroruser", customError)

	err := verifier.VerifyProfile(ctx, models.ChessDotCom, "erroruser")
	require.Error(t, err)
	assert.Equal(t, customError, err)
}

func TestFakeChessProfileVerifier_RemoveUsername(t *testing.T) {
	verifier := NewFakeChessProfileVerifier()
	ctx := context.Background()

	// Add "testuser1" to blacklist first
	verifier.AddInvalidUsername(models.ChessDotCom, "testuser1", fmt.Errorf("test error"))
	err := verifier.VerifyProfile(ctx, models.ChessDotCom, "testuser1")
	require.Error(t, err)

	// Remove "testuser1" from blacklist
	verifier.RemoveUsername(models.ChessDotCom, "testuser1")

	// Now "testuser1" should be valid (not in blacklist)
	err = verifier.VerifyProfile(ctx, models.ChessDotCom, "testuser1")
	assert.NoError(t, err, "testuser1 should be valid after removing from blacklist")
}

func TestFakeChessProfileVerifier_AddValidUsername_NewPlatform(t *testing.T) {
	verifier := NewFakeChessProfileVerifier()
	ctx := context.Background()

	// Clear existing platforms to test initialization
	verifier.InvalidUsernames = make(map[models.ChessPlatform]map[string]error)

	// Add user to blacklist first, then remove it
	verifier.AddInvalidUsername(models.ChessDotCom, "newuser", fmt.Errorf("test error"))
	verifier.AddValidUsername(models.ChessDotCom, "newuser")

	err := verifier.VerifyProfile(ctx, models.ChessDotCom, "newuser")
	assert.NoError(t, err)
}

func TestFakeChessProfileVerifier_AddErrorUsername_NewPlatform(t *testing.T) {
	verifier := NewFakeChessProfileVerifier()
	ctx := context.Background()

	// Clear existing platforms to test initialization
	verifier.InvalidUsernames = make(map[models.ChessPlatform]map[string]error)

	customError := assert.AnError
	verifier.AddErrorUsername(models.ChessDotCom, "erroruser", customError)

	err := verifier.VerifyProfile(ctx, models.ChessDotCom, "erroruser")
	require.Error(t, err)
	assert.Equal(t, customError, err)
}

// Test that demonstrates how to use the fake verifier in other tests
func TestFakeChessProfileVerifier_UsageExample(t *testing.T) {
	// Create a fake verifier for testing
	verifier := NewFakeChessProfileVerifier()
	ctx := context.Background()

	// Test successful verification (any username not in blacklist is valid)
	err := verifier.VerifyProfile(ctx, models.ChessDotCom, "zsmickycat")
	assert.NoError(t, err)

	err = verifier.VerifyProfile(ctx, models.ChessDotCom, "anyusername")
	assert.NoError(t, err, "any username should be valid in blacklist approach")

	// Test failed verification (predefined blacklisted usernames)
	err = verifier.VerifyProfile(ctx, models.ChessDotCom, "nonexistent")
	require.Error(t, err)
	assert.Contains(t, err.Error(), "does not exist")

	// Add a user to blacklist that should return a specific error
	verifier.AddInvalidUsername(models.LichessOrg, "servererror", assert.AnError)
	err = verifier.VerifyProfile(ctx, models.LichessOrg, "servererror")
	require.Error(t, err)
	assert.Equal(t, assert.AnError, err)

	// Remove a user from blacklist (making it valid)
	verifier.RemoveInvalidUsername(models.LichessOrg, "servererror")
	err = verifier.VerifyProfile(ctx, models.LichessOrg, "servererror")
	assert.NoError(t, err, "user should be valid after removing from blacklist")
}
