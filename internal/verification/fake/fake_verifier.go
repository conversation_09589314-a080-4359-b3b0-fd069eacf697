package fake

import (
	"context"
	"fmt"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/verification"
)

// FakeChessProfileVerifier implements IChessProfileVerifier for testing
// Uses a blacklist approach: all usernames are valid unless explicitly marked as invalid
type FakeChessProfileVerifier struct {
	// InvalidUsernames maps platform to usernames that should return specific errors
	InvalidUsernames map[models.ChessPlatform]map[string]error
}

// NewFakeChessProfileVerifier creates a new fake verifier with default test data
// By default, all usernames are valid except for a few predefined invalid ones
func NewFakeChessProfileVerifier() *FakeChessProfileVerifier {
	return &FakeChessProfileVerifier{
		InvalidUsernames: map[models.ChessPlatform]map[string]error{
			models.ChessDotCom: {
				"nonexistent": fmt.Errorf("chess.com account does not exist"),
				"notfound":    fmt.Errorf("chess.com account does not exist"),
				"error404":    fmt.Errorf("chess.com account does not exist"),
			},
			models.LichessOrg: {
				"nonexistent": fmt.Errorf("lichess account does not exist"),
				"notfound":    fmt.Errorf("lichess account does not exist"),
				"error404":    fmt.Errorf("lichess account does not exist"),
			},
		},
	}
}

// VerifyProfile implements the IChessProfileVerifier interface
// Uses blacklist approach: returns error only if username is in InvalidUsernames, otherwise succeeds
func (f *FakeChessProfileVerifier) VerifyProfile(ctx context.Context, platform models.ChessPlatform, username string) error {
	// Check if this username should return a specific error (blacklist)
	if platformErrors, exists := f.InvalidUsernames[platform]; exists {
		if err, hasError := platformErrors[username]; hasError {
			return err
		}
	}

	// Check for unsupported platform
	switch platform {
	case models.ChessDotCom, models.LichessOrg:
		// Username is valid (not in blacklist)
		return nil
	default:
		return fmt.Errorf("unsupported platform: %s", platform)
	}
}

// AddInvalidUsername adds a username that should return a specific error (blacklist)
func (f *FakeChessProfileVerifier) AddInvalidUsername(platform models.ChessPlatform, username string, err error) {
	if f.InvalidUsernames[platform] == nil {
		f.InvalidUsernames[platform] = make(map[string]error)
	}
	f.InvalidUsernames[platform][username] = err
}

// RemoveInvalidUsername removes a username from the blacklist (making it valid)
func (f *FakeChessProfileVerifier) RemoveInvalidUsername(platform models.ChessPlatform, username string) {
	if f.InvalidUsernames[platform] != nil {
		delete(f.InvalidUsernames[platform], username)
	}
}

// AddValidUsername is a convenience method that removes a username from blacklist
// In blacklist approach, this just removes the username from invalid list
func (f *FakeChessProfileVerifier) AddValidUsername(platform models.ChessPlatform, username string) {
	f.RemoveInvalidUsername(platform, username)
}

// AddErrorUsername is an alias for AddInvalidUsername for backward compatibility
func (f *FakeChessProfileVerifier) AddErrorUsername(platform models.ChessPlatform, username string, err error) {
	f.AddInvalidUsername(platform, username, err)
}

// RemoveUsername is an alias for RemoveInvalidUsername for backward compatibility
func (f *FakeChessProfileVerifier) RemoveUsername(platform models.ChessPlatform, username string) {
	f.RemoveInvalidUsername(platform, username)
}

// Ensure FakeChessProfileVerifier implements the interface
var _ verification.IChessProfileVerifier = (*FakeChessProfileVerifier)(nil)
