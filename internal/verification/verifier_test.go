package verification

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestChessProfileVerifier_VerifyProfile_ChessDotCom_Success(t *testing.T) {
	// Create a test server that simulates chess.com API
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		assert.Equal(t, "/pub/player/testuser", r.URL.Path)
		w.Header().Set("Content-Type", "application/json")
		response := map[string]interface{}{
			"player_id": 12345,
			"username":  "testuser",
		}
		if err := json.NewEncoder(w).Encode(response); err != nil {
			http.Error(w, "Failed to encode response", http.StatusInternalServerError)
		}
	}))
	defer server.Close()

	// Create verifier with custom client and URLs
	client := &http.Client{}
	verifier := NewChessProfileVerifierWithConfig(client, server.URL, "")

	ctx := context.Background()
	err := verifier.VerifyProfile(ctx, models.ChessDotCom, "testuser")
	assert.NoError(t, err)
}

func TestChessProfileVerifier_VerifyProfile_ChessDotCom_NotFound(t *testing.T) {
	// Create a test server that simulates chess.com 404 response
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		assert.Equal(t, "/pub/player/nonexistent", r.URL.Path)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusNotFound)
		response := map[string]interface{}{
			"code":    0,
			"message": "User \"nonexistent\" not found.",
		}
		if err := json.NewEncoder(w).Encode(response); err != nil {
			http.Error(w, "Failed to encode response", http.StatusInternalServerError)
		}
	}))
	defer server.Close()

	client := &http.Client{}
	verifier := NewChessProfileVerifierWithConfig(client, server.URL, "")

	ctx := context.Background()
	err := verifier.VerifyProfile(ctx, models.ChessDotCom, "nonexistent")
	require.Error(t, err)
	assert.Contains(t, err.Error(), "does not exist")
}

func TestChessProfileVerifier_VerifyProfile_Lichess_Success(t *testing.T) {
	// Create a test server that simulates lichess API
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		assert.Equal(t, "/api/user/testuser", r.URL.Path)
		w.Header().Set("Content-Type", "application/json")
		response := map[string]interface{}{
			"id":       "testuser",
			"username": "testuser",
		}
		if err := json.NewEncoder(w).Encode(response); err != nil {
			http.Error(w, "Failed to encode response", http.StatusInternalServerError)
		}
	}))
	defer server.Close()

	client := &http.Client{}
	verifier := NewChessProfileVerifierWithConfig(client, "", server.URL)

	ctx := context.Background()
	err := verifier.VerifyProfile(ctx, models.LichessOrg, "testuser")
	assert.NoError(t, err)
}

func TestChessProfileVerifier_VerifyProfile_Lichess_NotFound(t *testing.T) {
	// Create a test server that simulates lichess 404 response
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		assert.Equal(t, "/api/user/nonexistent", r.URL.Path)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusNotFound)
		response := map[string]interface{}{
			"error": "Not found",
		}
		if err := json.NewEncoder(w).Encode(response); err != nil {
			http.Error(w, "Failed to encode response", http.StatusInternalServerError)
		}
	}))
	defer server.Close()

	client := &http.Client{}
	verifier := NewChessProfileVerifierWithConfig(client, "", server.URL)

	ctx := context.Background()
	err := verifier.VerifyProfile(ctx, models.LichessOrg, "nonexistent")
	require.Error(t, err)
	assert.Contains(t, err.Error(), "does not exist")
}

func TestChessProfileVerifier_VerifyProfile_UnsupportedPlatform(t *testing.T) {
	verifier := NewChessProfileVerifier()
	ctx := context.Background()

	err := verifier.VerifyProfile(ctx, "unsupported", "testuser")
	require.Error(t, err)
	assert.Contains(t, err.Error(), "unsupported platform")
}

// Live tests - these test against the actual APIs
// These should be run with the -tags=live flag or similar mechanism

func TestChessProfileVerifier_LiveTest_ChessDotCom_ValidUser(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping live test in short mode")
	}

	verifier := NewChessProfileVerifier()
	ctx := context.Background()

	// Test with known valid username
	err := verifier.VerifyProfile(ctx, models.ChessDotCom, "zsmickycat")
	assert.NoError(t, err, "zsmickycat should be a valid chess.com user")
}

func TestChessProfileVerifier_LiveTest_ChessDotCom_InvalidUser(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping live test in short mode")
	}

	verifier := NewChessProfileVerifier()
	ctx := context.Background()

	// Test with known invalid username
	err := verifier.VerifyProfile(ctx, models.ChessDotCom, "thisuserdoesnotexist12345")
	require.Error(t, err)
	assert.Contains(t, err.Error(), "does not exist")
}

func TestChessProfileVerifier_LiveTest_Lichess_ValidUser(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping live test in short mode")
	}

	verifier := NewChessProfileVerifier()
	ctx := context.Background()

	// Test with known valid username
	err := verifier.VerifyProfile(ctx, models.LichessOrg, "zsmickycat")
	assert.NoError(t, err, "zsmickycat should be a valid lichess user")
}

func TestChessProfileVerifier_LiveTest_Lichess_InvalidUser(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping live test in short mode")
	}

	verifier := NewChessProfileVerifier()
	ctx := context.Background()

	// Test with known invalid username
	err := verifier.VerifyProfile(ctx, models.LichessOrg, "thisuserdoesnotexist12345")
	require.Error(t, err)
	assert.Contains(t, err.Error(), "does not exist")
}
