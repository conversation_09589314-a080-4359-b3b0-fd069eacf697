# Chess Profile Verification

This package provides a chess profile verification service that can verify usernames on chess.com and lichess.org platforms.

## Features

- **Interface-based design**: Easy to mock and test
- **Real implementation**: Makes actual HTTP calls to chess.com and lichess.org APIs
- **Fake implementation**: For testing without external dependencies (uses blacklist approach)
- **Configurable**: Supports custom HTTP clients and base URLs for testing
- **Comprehensive error handling**: Distinguishes between network errors and user-not-found errors

## Usage

### Basic Usage

```go
package main

import (
    "context"
    "fmt"
    "log"

    "github.com/chessticize/chessticize-server/internal/models"
    "github.com/chessticize/chessticize-server/internal/verification"
)

func main() {
    // Create a verifier
    verifier := verification.NewChessProfileVerifier()

    ctx := context.Background()

    // Verify a chess.com profile
    err := verifier.VerifyProfile(ctx, models.ChessDotCom, "zsmickycat")
    if err != nil {
        log.Printf("Chess.com verification failed: %v", err)
    } else {
        fmt.Println("Chess.com profile verified successfully!")
    }

    // Verify a lichess profile
    err = verifier.VerifyProfile(ctx, models.LichessOrg, "zsmickycat")
    if err != nil {
        log.Printf("Lichess verification failed: %v", err)
    } else {
        fmt.Println("Lichess profile verified successfully!")
    }
}
```

### Using in Tests

```go
package mypackage

import (
    "context"
    "testing"

    "github.com/chessticize/chessticize-server/internal/models"
    "github.com/chessticize/chessticize-server/internal/verification/fake"
    "github.com/stretchr/testify/assert"
)

func TestMyFunction(t *testing.T) {
    // Create a fake verifier for testing (uses blacklist approach)
    verifier := fake.NewFakeChessProfileVerifier()

    // By default, all usernames are valid except those in the blacklist
    ctx := context.Background()

    // Test successful verification (any username not in blacklist)
    err := verifier.VerifyProfile(ctx, models.ChessDotCom, "mytestuser")
    assert.NoError(t, err)

    // Test failed verification (predefined blacklisted usernames)
    err = verifier.VerifyProfile(ctx, models.ChessDotCom, "nonexistent")
    assert.Error(t, err)
    assert.Contains(t, err.Error(), "does not exist")

    // Add custom invalid username to blacklist
    verifier.AddInvalidUsername(models.LichessOrg, "erroruser", errors.New("custom error"))
    err = verifier.VerifyProfile(ctx, models.LichessOrg, "erroruser")
    assert.Error(t, err)
}
```

### Custom Configuration

```go
// Create verifier with custom HTTP client
client := &http.Client{Timeout: 5 * time.Second}
verifier := verification.NewChessProfileVerifierWithClient(client)

// Create verifier with custom URLs (useful for testing)
verifier := verification.NewChessProfileVerifierWithConfig(
    client,
    "https://test-chess-api.com",  // Custom chess.com URL
    "https://test-lichess.com",    // Custom lichess URL
)
```

## API Reference

### Interface

```go
type IChessProfileVerifier interface {
    VerifyProfile(ctx context.Context, platform models.ChessPlatform, username string) error
}
```

### Real Implementation

- `NewChessProfileVerifier()`: Creates verifier with default settings
- `NewChessProfileVerifierWithClient(client *http.Client)`: Creates verifier with custom HTTP client
- `NewChessProfileVerifierWithConfig(client *http.Client, chessDotComURL, lichessURL string)`: Creates verifier with custom configuration

### Fake Implementation (Blacklist Approach)

- `NewFakeChessProfileVerifier()`: Creates fake verifier with default blacklist
- `AddInvalidUsername(platform, username, error)`: Adds a username to blacklist with specific error
- `RemoveInvalidUsername(platform, username)`: Removes a username from blacklist (making it valid)
- `AddValidUsername(platform, username)`: Convenience method that removes username from blacklist
- `AddErrorUsername(platform, username, error)`: Alias for AddInvalidUsername (backward compatibility)
- `RemoveUsername(platform, username)`: Alias for RemoveInvalidUsername (backward compatibility)

## Error Handling

The verifier returns specific error messages:

- `"chess.com account does not exist"` - When chess.com user is not found
- `"lichess account does not exist"` - When lichess user is not found
- `"unsupported platform: X"` - When platform is not supported
- Network errors are wrapped with context

## Testing

Run all tests:
```bash
go test ./internal/verification/... -v
```

Run only unit tests (skip live tests):
```bash
go test ./internal/verification/... -v -short
```

The package includes both unit tests with mock servers and live tests that verify against actual APIs using the username "zsmickycat".

## Default Test Data (Blacklist Approach)

The fake verifier uses a blacklist approach where **all usernames are valid by default** except those explicitly blacklisted:

**Blacklisted usernames** (both platforms):
- `nonexistent` - Returns "account does not exist" error
- `notfound` - Returns "account does not exist" error
- `error404` - Returns "account does not exist" error

**All other usernames are valid**, including:
- `zsmickycat` (used in live tests)
- `testuser1`, `testuser2`, `validuser`
- Any random username like `myuser`, `alice`, `bob123`, etc.

This approach is more realistic for testing since most usernames would be valid in real scenarios.
