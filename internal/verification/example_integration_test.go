package verification_test

import (
	"context"
	"testing"

	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/chessticize/chessticize-server/internal/verification"
	"github.com/chessticize/chessticize-server/internal/verification/fake"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// Example showing how to integrate chess profile verification
// into a service that creates chess profiles
type ChessProfileService struct {
	verifier verification.IChessProfileVerifier
}

func NewChessProfileService(verifier verification.IChessProfileVerifier) *ChessProfileService {
	return &ChessProfileService{
		verifier: verifier,
	}
}

func (s *ChessProfileService) CreateProfile(ctx context.Context, platform models.ChessPlatform, username string) error {
	// Verify the profile exists before creating
	if err := s.verifier.VerifyProfile(ctx, platform, username); err != nil {
		return err
	}

	// Here you would create the profile in your database
	// For this example, we just return success
	return nil
}

// Test the integration with fake verifier
func TestChessProfileService_CreateProfile_WithFakeVerifier(t *testing.T) {
	// Create fake verifier for testing
	fakeVerifier := fake.NewFakeChessProfileVerifier()
	service := NewChessProfileService(fakeVerifier)

	ctx := context.Background()

	// Test successful profile creation
	err := service.CreateProfile(ctx, models.ChessDotCom, "zsmickycat")
	assert.NoError(t, err, "Should successfully create profile for valid user")

	// Test failed profile creation
	err = service.CreateProfile(ctx, models.ChessDotCom, "nonexistent")
	require.Error(t, err)
	assert.Contains(t, err.Error(), "does not exist")

	// Test with custom test data
	fakeVerifier.AddValidUsername(models.LichessOrg, "customtestuser")
	err = service.CreateProfile(ctx, models.LichessOrg, "customtestuser")
	assert.NoError(t, err, "Should successfully create profile for custom valid user")
}

// Test the integration with real verifier (live test)
func TestChessProfileService_CreateProfile_WithRealVerifier(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping live test in short mode")
	}

	// Create real verifier for live testing
	realVerifier := verification.NewChessProfileVerifier()
	service := NewChessProfileService(realVerifier)

	ctx := context.Background()

	// Test with known valid user
	err := service.CreateProfile(ctx, models.ChessDotCom, "zsmickycat")
	assert.NoError(t, err, "Should successfully verify real chess.com user")

	err = service.CreateProfile(ctx, models.LichessOrg, "zsmickycat")
	assert.NoError(t, err, "Should successfully verify real lichess user")

	// Test with known invalid user
	err = service.CreateProfile(ctx, models.ChessDotCom, "thisuserdoesnotexist12345")
	require.Error(t, err)
	assert.Contains(t, err.Error(), "does not exist")
}

// Example showing how to switch between real and fake implementations
func TestChessProfileService_SwitchImplementations(t *testing.T) {
	ctx := context.Background()

	// Test with fake implementation
	fakeVerifier := fake.NewFakeChessProfileVerifier()
	fakeService := NewChessProfileService(fakeVerifier)

	err := fakeService.CreateProfile(ctx, models.ChessDotCom, "testuser1")
	assert.NoError(t, err, "Fake verifier should work")

	// Test with real implementation (if not in short mode)
	if !testing.Short() {
		realVerifier := verification.NewChessProfileVerifier()
		realService := NewChessProfileService(realVerifier)

		err = realService.CreateProfile(ctx, models.ChessDotCom, "zsmickycat")
		assert.NoError(t, err, "Real verifier should work")
	}
}

// Example showing error handling patterns
func TestChessProfileService_ErrorHandling(t *testing.T) {
	fakeVerifier := fake.NewFakeChessProfileVerifier()
	service := NewChessProfileService(fakeVerifier)

	ctx := context.Background()

	testCases := []struct {
		name     string
		platform models.ChessPlatform
		username string
		wantErr  string
	}{
		{
			name:     "chess.com user not found",
			platform: models.ChessDotCom,
			username: "nonexistent",
			wantErr:  "chess.com account does not exist",
		},
		{
			name:     "lichess user not found",
			platform: models.LichessOrg,
			username: "nonexistent",
			wantErr:  "lichess account does not exist",
		},
		{
			name:     "unsupported platform",
			platform: "unsupported",
			username: "testuser",
			wantErr:  "unsupported platform",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := service.CreateProfile(ctx, tc.platform, tc.username)
			require.Error(t, err)
			assert.Contains(t, err.Error(), tc.wantErr)
		})
	}
}
