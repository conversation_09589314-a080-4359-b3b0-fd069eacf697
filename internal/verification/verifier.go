package verification

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/chessticize/chessticize-server/internal/models"
)

// ChessProfileVerifier implements IChessProfileVerifier using real HTTP calls
type ChessProfileVerifier struct {
	httpClient     *http.Client
	chessDotComURL string
	lichessURL     string
}

// NewChessProfileVerifier creates a new ChessProfileVerifier with a configured HTTP client
func NewChessProfileVerifier() *ChessProfileVerifier {
	return &ChessProfileVerifier{
		httpClient: &http.Client{
			Timeout: 10 * time.Second,
		},
		chessDotComURL: "https://api.chess.com",
		lichessURL:     "https://lichess.org",
	}
}

// NewChessProfileVerifierWithClient creates a new ChessProfileVerifier with a custom HTTP client
func NewChessProfileVerifierWithClient(client *http.Client) *ChessProfileVerifier {
	return &ChessProfileVerifier{
		httpClient:     client,
		chessDotComURL: "https://api.chess.com",
		lichessURL:     "https://lichess.org",
	}
}

// NewChessProfileVerifierWithConfig creates a new ChessProfileVerifier with custom configuration
func NewChessProfileVerifierWithConfig(client *http.Client, chessDotComURL, lichessURL string) *ChessProfileVerifier {
	return &ChessProfileVerifier{
		httpClient:     client,
		chessDotComURL: chessDotComURL,
		lichessURL:     lichessURL,
	}
}

// VerifyProfile verifies that a username exists on the specified chess platform
func (v *ChessProfileVerifier) VerifyProfile(ctx context.Context, platform models.ChessPlatform, username string) error {
	switch platform {
	case models.ChessDotCom:
		return v.verifyChessDotCom(ctx, username)
	case models.LichessOrg:
		return v.verifyLichess(ctx, username)
	default:
		return fmt.Errorf("unsupported platform: %s", platform)
	}
}

// verifyChessDotCom verifies a chess.com username
func (v *ChessProfileVerifier) verifyChessDotCom(ctx context.Context, username string) error {
	url := fmt.Sprintf("%s/pub/player/%s", v.chessDotComURL, username)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := v.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to verify chess.com account: %w", err)
	}
	defer func() {
		_ = resp.Body.Close() // Ignore close error
	}()

	if resp.StatusCode == http.StatusNotFound {
		return fmt.Errorf("chess.com account does not exist")
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("failed to verify chess.com account: HTTP %d", resp.StatusCode)
	}

	// Parse response to ensure it has the expected structure
	var data map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&data); err != nil {
		return fmt.Errorf("failed to parse chess.com response: %w", err)
	}

	// Check if player_id exists in the response
	if _, exists := data["player_id"]; !exists {
		return fmt.Errorf("chess.com account does not exist")
	}

	return nil
}

// verifyLichess verifies a lichess.org username
func (v *ChessProfileVerifier) verifyLichess(ctx context.Context, username string) error {
	url := fmt.Sprintf("%s/api/user/%s", v.lichessURL, username)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := v.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to verify lichess account: %w", err)
	}
	defer func() {
		_ = resp.Body.Close() // Ignore close error
	}()

	if resp.StatusCode == http.StatusNotFound {
		return fmt.Errorf("lichess account does not exist")
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("failed to verify lichess account: HTTP %d", resp.StatusCode)
	}

	// Parse response to ensure it has the expected structure
	var data map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&data); err != nil {
		return fmt.Errorf("failed to parse lichess response: %w", err)
	}

	// Check if id exists in the response
	if _, exists := data["id"]; !exists {
		return fmt.Errorf("lichess account does not exist")
	}

	return nil
}
