package utils

import (
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestPGNCompression(t *testing.T) {
	t.Run("Compress and Decompress", func(t *testing.T) {
		// Sample PGN data - repeat a sequence to ensure it's large enough to benefit from compression
		moveSequence := `1. e4 e5 2. Nf3 Nc6 3. Bb5 a6 4. Ba4 Nf6 5. O-O Be7 6. Re1 b5 7. Bb3 d6 8. c3 O-O 9. h3 Na5 10. Bc2 c5 11. d4 Qc7 `
		originalPGN := strings.Repeat(moveSequence, 10)

		// Compress the PGN
		compressed, err := CompressPGN(originalPGN)
		require.NoError(t, err)
		require.NotNil(t, compressed)

		// Verify compression actually reduced size
		assert.Less(t, len(compressed), len(originalPGN), "Compressed data should be smaller than original")

		// Decompress the PGN
		decompressed, err := DecompressPGN(compressed)
		require.NoError(t, err)
		require.NotNil(t, decompressed)

		// Verify the decompressed data matches the original
		assert.Equal(t, originalPGN, decompressed)
	})

	t.Run("Empty PGN", func(t *testing.T) {
		// Test with empty PGN
		emptyPGN := ""

		// Compress empty PGN
		compressed, err := CompressPGN(emptyPGN)
		require.NoError(t, err)
		require.NotNil(t, compressed)

		// Decompress empty PGN
		decompressed, err := DecompressPGN(compressed)
		require.NoError(t, err)
		require.NotNil(t, decompressed)

		// Verify empty PGN roundtrip
		assert.Equal(t, emptyPGN, decompressed)
	})

	t.Run("Large PGN", func(t *testing.T) {
		// Create a large PGN string by repeating a move sequence
		moveSequence := "1. e4 e5 2. Nf3 Nc6 3. Bb5 a6 4. Ba4 Nf6 5. O-O Be7 "
		largePGN := strings.Repeat(moveSequence, 100)

		// Compress large PGN
		compressed, err := CompressPGN(largePGN)
		require.NoError(t, err)
		require.NotNil(t, compressed)

		// Verify compression is effective for large data
		assert.Less(t, float64(len(compressed))/float64(len(largePGN)), 0.5,
			"Large PGN should compress to less than 50% of original size")

		// Decompress large PGN
		decompressed, err := DecompressPGN(compressed)
		require.NoError(t, err)
		require.NotNil(t, decompressed)

		// Verify large PGN roundtrip
		assert.Equal(t, largePGN, decompressed)
	})
}
