package utils

import (
	"bytes"

	"github.com/pierrec/lz4/v4"
)

// CompressPGN compresses PGN data using LZ4
func CompressPGN(pgn string) ([]byte, error) {
	var buf bytes.Buffer
	writer := lz4.NewWriter(&buf)

	_, err := writer.Write([]byte(pgn))
	if err != nil {
		return nil, err
	}

	err = writer.Close()
	if err != nil {
		return nil, err
	}

	return buf.Bytes(), nil
}

// DecompressPGN decompresses PGN data using LZ4
func DecompressPGN(compressed []byte) (string, error) {
	reader := lz4.NewReader(bytes.NewReader(compressed))

	var buf bytes.Buffer
	_, err := buf.ReadFrom(reader)
	if err != nil {
		return "", err
	}

	return buf.String(), nil
}
