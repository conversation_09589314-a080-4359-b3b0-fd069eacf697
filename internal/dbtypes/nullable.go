package dbtypes

import (
	"database/sql"
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"
)

// NullString represents a string that may be null.
// It implements the json.Marshaler and json.Unmarshaler interfaces,
// as well as the sql.Scanner and driver.Valuer interfaces.
type NullString struct {
	sql.NullString
}

// MarshalJSON implements the json.Marshaler interface.
func (ns NullString) MarshalJSON() ([]byte, error) {
	if !ns.Valid {
		return []byte("null"), nil
	}
	return json.Marshal(ns.String)
}

// UnmarshalJSON implements the json.Unmarshaler interface.
func (ns *NullString) UnmarshalJSON(data []byte) error {
	// Unmarshalling null is handled by the Valid field implicitly.
	// If data is "null", ns.String will remain empty and ns.Valid will be false.
	var s *string
	if err := json.Unmarshal(data, &s); err != nil {
		return err
	}
	if s != nil {
		ns.Valid = true
		ns.String = *s
	} else {
		ns.Valid = false
		ns.String = "" // Explicitly set to empty string when null
	}
	return nil
}

// <PERSON><PERSON> implements the sql.Scanner interface.
func (ns *NullString) Scan(value interface{}) error {
	return ns.NullString.Scan(value)
}

// Value implements the driver.Valuer interface.
func (ns NullString) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return ns.String, nil
}

// NullTime represents a time.Time that may be null.
// It implements the json.Marshaler and json.Unmarshaler interfaces,
// as well as the sql.Scanner and driver.Valuer interfaces.
type NullTime struct {
	sql.NullTime
}

// MarshalJSON implements the json.Marshaler interface.
// It formats the time as RFC3339Nano or returns null.
func (nt NullTime) MarshalJSON() ([]byte, error) {
	if !nt.Valid {
		return []byte("null"), nil
	}
	// Format time in a standard way, e.g., RFC3339Nano
	return json.Marshal(nt.Time.UTC().Format(time.RFC3339Nano))
}

// UnmarshalJSON implements the json.Unmarshaler interface.
// It parses the time from RFC3339Nano format or handles null.
func (nt *NullTime) UnmarshalJSON(data []byte) error {
	var t *string
	if err := json.Unmarshal(data, &t); err != nil {
		// Allow unmarshalling from a raw null value
		if string(data) == "null" {
			nt.Valid = false
			nt.Time = time.Time{} // Zero value for time
			return nil
		}
		return err // Return error for other invalid formats
	}

	if t != nil {
		// Attempt to parse the string as RFC3339Nano
		parsedTime, err := time.Parse(time.RFC3339Nano, *t)
		if err != nil {
			return err // Return error if parsing fails
		}
		nt.Valid = true
		nt.Time = parsedTime.UTC() // Store in UTC
	} else {
		// This case handles explicit JSON null
		nt.Valid = false
		nt.Time = time.Time{} // Zero value for time
	}
	return nil
}

// Scan implements the sql.Scanner interface.
// It handles scanning nil, time.Time, []byte, and string types.
func (nt *NullTime) Scan(value interface{}) error {
	if value == nil {
		nt.Time, nt.Valid = time.Time{}, false
		return nil
	}

	var err error
	switch v := value.(type) {
	case time.Time:
		nt.Time, nt.Valid = v.UTC(), true
	case []byte:
		// Try parsing common formats from byte slice
		nt.Time, err = parseTime(string(v))
		nt.Valid = (err == nil)
	case string:
		// Try parsing common formats from string
		nt.Time, err = parseTime(v)
		nt.Valid = (err == nil)
	default:
		// Fallback to sql.NullTime's Scan for other driver-specific types
		err = nt.NullTime.Scan(value)
		if err == nil && nt.Valid {
			nt.Time = nt.Time.UTC() // Ensure UTC if scanned successfully by NullTime
		}
		return err // Return the error from NullTime.Scan
	}

	if err != nil {
		nt.Valid = false
		return fmt.Errorf("NullTime.Scan: failed to scan/parse time: %w", err)
	}

	return nil
}

// Value implements the driver.Valuer interface.
func (nt NullTime) Value() (driver.Value, error) {
	if !nt.Valid {
		return nil, nil
	}
	// Return the time directly; the database driver handles formatting.
	return nt.Time.UTC(), nil // Ensure time written to DB is UTC
}

// parseTime tries to parse a string into a time.Time using common formats.
func parseTime(str string) (time.Time, error) {
	formats := []string{
		time.RFC3339Nano,
		time.RFC3339,
		"2006-01-02 15:04:05",       // Common SQL timestamp format without TZ
		"2006-01-02 15:04:05-07",    // Format used in test
		"2006-01-02 15:04:05Z07:00", // ISO 8601 with TZ
		"2006-01-02",                // Date only
	}

	var t time.Time
	var err error

	for _, format := range formats {
		t, err = time.Parse(format, str)
		if err == nil {
			return t.UTC(), nil // Parsed successfully, return in UTC
		}
	}

	// If none of the formats worked, return the last error
	return time.Time{}, fmt.Errorf("unable to parse time string \"%s\" with known formats", str)
}
