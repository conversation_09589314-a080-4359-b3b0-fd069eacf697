package dbtypes

import (
	"database/sql"
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestNullString_MarshalJSON(t *testing.T) {
	t.Run("Valid String", func(t *testing.T) {
		ns := NullString{sql.NullString{String: "hello", Valid: true}}
		data, err := json.<PERSON>(ns)
		assert.NoError(t, err)
		assert.JSONEq(t, `"hello"`, string(data))
	})

	t.Run("Null String", func(t *testing.T) {
		ns := NullString{sql.NullString{String: "", Valid: false}}
		data, err := json.<PERSON>(ns)
		assert.NoError(t, err)
		assert.JSONEq(t, `null`, string(data))
	})
}

func TestNullString_UnmarshalJSON(t *testing.T) {
	t.Run("Valid String", func(t *testing.T) {
		var ns NullString
		err := json.Unmarshal([]byte(`"world"`), &ns)
		assert.NoError(t, err)
		assert.True(t, ns.Valid)
		assert.Equal(t, "world", ns.String)
	})

	t.Run("Null String", func(t *testing.T) {
		var ns NullString
		err := json.Unmarshal([]byte(`null`), &ns)
		assert.NoError(t, err)
		assert.False(t, ns.Valid)
		assert.Equal(t, "", ns.String) // Should be empty string when null
	})

	t.Run("Empty String", func(t *testing.T) {
		var ns NullString
		err := json.Unmarshal([]byte(`""`), &ns)
		assert.NoError(t, err)
		assert.True(t, ns.Valid) // Empty string is a valid value
		assert.Equal(t, "", ns.String)
	})

	t.Run("Invalid JSON", func(t *testing.T) {
		var ns NullString
		err := json.Unmarshal([]byte(`not json`), &ns)
		assert.Error(t, err)
	})
}

func TestNullString_Scan(t *testing.T) {
	t.Run("Scan String", func(t *testing.T) {
		var ns NullString
		err := ns.Scan("scanned")
		assert.NoError(t, err)
		assert.True(t, ns.Valid)
		assert.Equal(t, "scanned", ns.String)
	})

	t.Run("Scan Null", func(t *testing.T) {
		var ns NullString
		err := ns.Scan(nil)
		assert.NoError(t, err)
		assert.False(t, ns.Valid)
	})

	t.Run("Scan Bytes", func(t *testing.T) {
		var ns NullString
		err := ns.Scan([]byte("bytes"))
		assert.NoError(t, err)
		assert.True(t, ns.Valid)
		assert.Equal(t, "bytes", ns.String)
	})
}

func TestNullString_Value(t *testing.T) {
	t.Run("Value String", func(t *testing.T) {
		ns := NullString{sql.NullString{String: "valued", Valid: true}}
		val, err := ns.Value()
		assert.NoError(t, err)
		assert.Equal(t, "valued", val)
	})

	t.Run("Value Null", func(t *testing.T) {
		ns := NullString{sql.NullString{Valid: false}}
		val, err := ns.Value()
		assert.NoError(t, err)
		assert.Nil(t, val)
	})
}

// --- NullTime Tests ---

func TestNullTime_MarshalJSON(t *testing.T) {
	t.Run("Valid Time", func(t *testing.T) {
		now := time.Now().UTC().Truncate(time.Microsecond) // Truncate for consistent comparison
		nt := NullTime{sql.NullTime{Time: now, Valid: true}}
		data, err := json.Marshal(nt)
		assert.NoError(t, err)
		// Expect RFC3339Nano format
		expectedJSON := `"` + now.Format(time.RFC3339Nano) + `"`
		assert.JSONEq(t, expectedJSON, string(data))
	})

	t.Run("Null Time", func(t *testing.T) {
		nt := NullTime{sql.NullTime{Valid: false}}
		data, err := json.Marshal(nt)
		assert.NoError(t, err)
		assert.JSONEq(t, `null`, string(data))
	})
}

func TestNullTime_UnmarshalJSON(t *testing.T) {
	t.Run("Valid Time String", func(t *testing.T) {
		var nt NullTime
		// Use a specific time string in RFC3339Nano format
		timeStr := "2023-10-27T10:30:05.123456Z"
		timeJSON := `"` + timeStr + `"`
		err := json.Unmarshal([]byte(timeJSON), &nt)
		assert.NoError(t, err)
		assert.True(t, nt.Valid)
		expectedTime, _ := time.Parse(time.RFC3339Nano, timeStr)
		// Compare UTC times
		assert.Equal(t, expectedTime.UTC(), nt.Time.UTC())
		assert.Equal(t, time.UTC, nt.Time.Location(), "Time should be in UTC")
	})

	t.Run("Null Time", func(t *testing.T) {
		var nt NullTime
		err := json.Unmarshal([]byte(`null`), &nt)
		assert.NoError(t, err)
		assert.False(t, nt.Valid)
		assert.True(t, nt.Time.IsZero()) // Should be zero time when null
	})

	t.Run("Invalid Time Format", func(t *testing.T) {
		var nt NullTime
		err := json.Unmarshal([]byte(`"not-a-time"`), &nt)
		assert.Error(t, err)
	})

	t.Run("Invalid JSON", func(t *testing.T) {
		var nt NullTime
		err := json.Unmarshal([]byte(`invalid`), &nt)
		assert.Error(t, err)
	})
}

func TestNullTime_Scan(t *testing.T) {
	t.Run("Scan Time", func(t *testing.T) {
		var nt NullTime
		now := time.Now().Truncate(0) // Remove monotonic clock reading if present
		err := nt.Scan(now)
		assert.NoError(t, err)
		assert.True(t, nt.Valid)
		// Scan should convert to UTC
		assert.Equal(t, now.UTC(), nt.Time)
		assert.Equal(t, time.UTC, nt.Time.Location(), "Scanned time should be in UTC")
	})

	t.Run("Scan Null", func(t *testing.T) {
		var nt NullTime
		err := nt.Scan(nil)
		assert.NoError(t, err)
		assert.False(t, nt.Valid)
	})

	t.Run("Scan String (Database might return string)", func(t *testing.T) {
		var nt NullTime
		timeStr := "2023-11-01 14:00:00+00" // Example format from some DBs
		// Note: The standard sql library handles various string conversions.
		// We rely on sql.NullTime's Scan for this behavior.
		err := nt.Scan(timeStr)
		assert.NoError(t, err)
		assert.True(t, nt.Valid)
		expectedTime, _ := time.Parse("2006-01-02 15:04:05-07", timeStr)
		assert.Equal(t, expectedTime.UTC(), nt.Time)
		assert.Equal(t, time.UTC, nt.Time.Location(), "Scanned time from string should be in UTC")

	})
}

func TestNullTime_Value(t *testing.T) {
	t.Run("Value Time", func(t *testing.T) {
		// Ensure the time has a non-UTC timezone initially to test conversion
		loc, _ := time.LoadLocation("America/New_York")
		nowInLoc := time.Now().In(loc)
		nt := NullTime{sql.NullTime{Time: nowInLoc, Valid: true}}
		val, err := nt.Value()
		assert.NoError(t, err)
		// Value should return time.Time in UTC
		assert.IsType(t, time.Time{}, val)
		assert.Equal(t, nowInLoc.UTC(), val.(time.Time))
		assert.Equal(t, time.UTC, val.(time.Time).Location(), "Value time should be in UTC")

	})

	t.Run("Value Null", func(t *testing.T) {
		nt := NullTime{sql.NullTime{Valid: false}}
		val, err := nt.Value()
		assert.NoError(t, err)
		assert.Nil(t, val)
	})
}
