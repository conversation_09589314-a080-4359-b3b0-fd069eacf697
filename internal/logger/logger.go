package logger

import (
	"context"
	"os"
	"time"

	"github.com/rs/zerolog"
)

type loggerKey struct{}

var (
	// GlobalLogger is the application-wide logger instance.
	GlobalLogger zerolog.Logger
)

func init() {
	// Configure zerolog for development: pretty printing, console output.
	// For production, consider JSON output and configurable level.
	output := zerolog.ConsoleWriter{Out: os.Stdout, TimeFormat: time.RFC3339}
	GlobalLogger = zerolog.New(output).With().Timestamp().Logger()
	zerolog.SetGlobalLevel(zerolog.InfoLevel) // Default level, can be configured via env var
}

// WithLogger returns a new context with the provided logger attached.
func WithLogger(ctx context.Context, logger zerolog.Logger) context.Context {
	return context.WithValue(ctx, loggerKey{}, logger)
}

// FromContext retrieves the logger from the context.
// If no logger is found, it returns the global logger.
func FromContext(ctx context.Context) *zerolog.Logger {
	if logger, ok := ctx.Value(loggerKey{}).(zerolog.Logger); ok {
		return &logger
	}
	// Fallback to global logger if none found in context
	return &GlobalLogger
}
