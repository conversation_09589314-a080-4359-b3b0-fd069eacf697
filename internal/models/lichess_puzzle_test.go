package models

import (
	"testing"
	"time"

	"github.com/lib/pq"
	"github.com/stretchr/testify/assert"
)

func TestLichessPuzzle(t *testing.T) {
	t.Run("TableName", func(t *testing.T) {
		puzzle := LichessPuzzle{}
		assert.Equal(t, "lichess_puzzles", puzzle.TableName())
	})

	t.Run("CreateLichessPuzzle", func(t *testing.T) {
		puzzle := LichessPuzzle{
			ID:              "00008",
			FEN:             "r6k/pp2r2p/4Rp1Q/3p4/8/1N1P2R1/PqP2bPP/7K b - - 0 24",
			Moves:           pq.StringArray{"f2g3", "e6e7", "b2b1", "b3c1", "b1c1", "h6c1"},
			Rating:          1798,
			RatingDeviation: 77,
			Popularity:      95,
			NbPlays:         8020,
			Themes:          pq.StringArray{"crushing", "hangingPiece", "long", "middlegame"},
			GameURL:         "https://lichess.org/787zsVup/black#48",
			OpeningTags:     pq.StringArray{},
			CreatedAt:       time.Now(),
			UpdatedAt:       time.Now(),
		}

		assert.Equal(t, "00008", puzzle.ID)
		assert.Equal(t, 1798, puzzle.Rating)
		assert.Equal(t, 4, len(puzzle.Themes))
		assert.Contains(t, puzzle.Themes, "crushing")
		assert.Contains(t, puzzle.Themes, "hangingPiece")
	})
}

func TestUserLichessPuzzleStats(t *testing.T) {
	t.Run("TableName", func(t *testing.T) {
		stats := UserLichessPuzzleStats{}
		assert.Equal(t, "user_lichess_puzzle_stats", stats.TableName())
	})

	t.Run("CreateUserLichessPuzzleStats", func(t *testing.T) {
		stats := UserLichessPuzzleStats{
			ID:                 "test-id",
			UserID:             "user-123",
			LichessPuzzleID:    "00008",
			Attempts:           5,
			SuccessCount:       3,
			TotalTime:          150,
			AverageTime:        30.0,
			LastAttemptTime:    time.Now(),
			LastAttemptSuccess: true,
			CreatedAt:          time.Now(),
			UpdatedAt:          time.Now(),
		}

		assert.Equal(t, "user-123", stats.UserID)
		assert.Equal(t, "00008", stats.LichessPuzzleID)
		assert.Equal(t, 5, stats.Attempts)
		assert.Equal(t, 3, stats.SuccessCount)
		assert.Equal(t, 150, stats.TotalTime)
		assert.Equal(t, 30.0, stats.AverageTime)
		assert.True(t, stats.LastAttemptSuccess)
	})
}

func TestUserPuzzleStats(t *testing.T) {
	t.Run("CreateUserPuzzleStats", func(t *testing.T) {
		stats := UserPuzzleStats{
			ID:                 "test-id",
			UserID:             "user-123",
			PuzzleID:           "user-puzzle-456",
			Attempts:           2,
			SuccessCount:       1,
			TotalTime:          60,
			AverageTime:        30.0,
			LastAttemptTime:    time.Now(),
			LastAttemptSuccess: false,
			CreatedAt:          time.Now(),
			UpdatedAt:          time.Now(),
		}

		assert.Equal(t, "user-123", stats.UserID)
		assert.Equal(t, "user-puzzle-456", stats.PuzzleID)
		assert.Equal(t, 2, stats.Attempts)
		assert.Equal(t, 1, stats.SuccessCount)
		assert.False(t, stats.LastAttemptSuccess)
	})
}

func TestUserLichessPuzzleStatsWithDislike(t *testing.T) {
	t.Run("CreateUserLichessPuzzleStatsWithDislike", func(t *testing.T) {
		now := time.Now()
		stats := UserLichessPuzzleStats{
			ID:                 "test-id",
			UserID:             "user-123",
			LichessPuzzleID:    "00008",
			Attempts:           3,
			SuccessCount:       2,
			TotalTime:          90,
			AverageTime:        30.0,
			LastAttemptTime:    now,
			LastAttemptSuccess: true,
			IsDisliked:         true,
			DislikedAt:         &now,
			CreatedAt:          now,
			UpdatedAt:          now,
		}

		assert.Equal(t, "user-123", stats.UserID)
		assert.Equal(t, "00008", stats.LichessPuzzleID)
		assert.Equal(t, 3, stats.Attempts)
		assert.Equal(t, 2, stats.SuccessCount)
		assert.True(t, stats.LastAttemptSuccess)
		assert.True(t, stats.IsDisliked)
		assert.NotNil(t, stats.DislikedAt)
		assert.Equal(t, now, *stats.DislikedAt)
	})
}
