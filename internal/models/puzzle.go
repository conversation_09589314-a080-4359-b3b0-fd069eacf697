package models

import (
	"time"

	"github.com/lib/pq"
)

// Puzzle represents a chess puzzle generated from a game position
type Puzzle struct {
	ID          string         `gorm:"type:varchar(36);primary_key" json:"id"`
	GameID      string         `gorm:"type:varchar(36);not null;index" json:"game_id"`
	UserID      string         `gorm:"type:varchar(36);not null;index" json:"user_id"`
	GameMove    int            `gorm:"not null" json:"game_move"`
	FEN         string         `gorm:"type:varchar(100);not null" json:"fen"`
	Moves       pq.StringArray `gorm:"type:text[];not null" json:"moves"`
	PrevCP      int            `gorm:"not null" json:"prev_cp"`
	CP          int            `gorm:"not null" json:"cp"`
	Theme       PuzzleTheme    `gorm:"type:varchar(50);not null" json:"theme"`
	UserColor   Color          `gorm:"type:varchar(10);not null" json:"user_color"`
	PuzzleColor Color          `gorm:"type:varchar(10);not null" json:"puzzle_color"`
	Zugzwang    bool           `gorm:"not null;default:false" json:"zugzwang"`
	Tags        pq.StringArray `gorm:"type:text[];index:idx_user_tags,composite:user_id,gin" json:"tags"`

	// Timestamps handled by GORM
	CreatedAt time.Time `gorm:"not null;index" json:"created_at"`
	UpdatedAt time.Time `gorm:"not null" json:"updated_at"`

	// Relationships
	Game Game `gorm:"foreignKey:GameID" json:"-"`
	User User `gorm:"foreignKey:UserID" json:"-"`
}

// TagCount represents a tag and its count
type TagCount struct {
	Tag   string `json:"tag"`
	Count int    `json:"count"`
}

// ThemeCount represents a puzzle theme and its count
type ThemeCount struct {
	Theme PuzzleTheme `json:"theme"`
	Count int         `json:"count"`
}

// ColorCount represents a color and its count
type ColorCount struct {
	Color Color `json:"color"`
	Count int   `json:"count"`
}

// GameMoveBucket represents a range of game moves and the count of puzzles in that range
type GameMoveBucket struct {
	Name    string `json:"name"`
	MinMove int    `json:"min_move"`
	MaxMove int    `json:"max_move"`
	Count   int    `json:"count"`
}

// MoveLengthBucket represents a puzzle move length and its count
type MoveLengthBucket struct {
	Length int `json:"length"`
	Count  int `json:"count"`
}

// PuzzleStats represents aggregated statistics about puzzles
type PuzzleStats struct {
	TagCounts         []TagCount         `json:"tag_counts"`
	ThemeCounts       []ThemeCount       `json:"theme_counts"`
	UserColorCounts   []ColorCount       `json:"user_color_counts"`
	GameMoveBuckets   []GameMoveBucket   `json:"game_move_buckets"`
	MoveLengthCounts  []MoveLengthBucket `json:"move_length_counts"`
	TotalCount        int64              `json:"total_count"`
	UniqueGameCount   int64              `json:"unique_game_count"`
	AverageMoveLength float64            `json:"average_move_length"`
	PeriodStart       *time.Time         `json:"period_start,omitempty"`
	PeriodEnd         *time.Time         `json:"period_end,omitempty"`
}
