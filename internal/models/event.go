package models

import (
	"encoding/json"
	"time"
)

// Event represents a user event for stats tracking
type Event struct {
	ID           string          `gorm:"type:varchar(36);primary_key" json:"id"`                 // UUID acting as idempotency key
	UserID       string          `gorm:"type:varchar(36);not null;index" json:"user_id"`         // User who triggered the event
	EventType    EventType       `gorm:"type:varchar(50);not null;index" json:"event_type"`      // Type of event
	EventSubType *EventSubType   `gorm:"type:varchar(50);index" json:"event_sub_type,omitempty"` // Optional sub-type
	EventData    json.RawMessage `gorm:"type:jsonb" json:"event_data"`                           // Flexible JSON data
	EventTime    time.Time       `gorm:"not null;index" json:"event_time"`                       // When the event occurred

	// Timestamps handled by GORM
	CreatedAt time.Time `gorm:"not null;index" json:"created_at"`
	UpdatedAt time.Time `gorm:"not null" json:"updated_at"`

	// Relationships
	User User `gorm:"foreignKey:UserID" json:"-"`
}

// SignInEventData represents data for sign-in events
type SignInEventData struct {
	Succeeded  bool   `json:"succeeded"`            // Whether the sign-in attempt succeeded
	SignInType string `json:"sign_in_type"`         // "password", "session_token", "firebase", "google.com", "github.com", etc.
	UserAgent  string `json:"user_agent,omitempty"` // User agent string
}

// RegistrationEventData represents data for registration events
type RegistrationEventData struct {
	SignInType string `json:"sign_in_type"`         // "password", "firebase", "google.com", "github.com", etc.
	UserAgent  string `json:"user_agent,omitempty"` // User agent string
}

// GameReviewEventData represents data for game review events
type GameReviewEventData struct {
	GameID   string `json:"game_id"`
	Duration *int   `json:"duration,omitempty"` // Duration in seconds (for end events)
}

// PuzzleSprintEventData represents data for puzzle sprint events
type PuzzleSprintEventData struct {
	SprintID         string       `json:"sprint_id"`
	Status           SprintStatus `json:"status,omitempty"`            // Final status of the sprint (for end events)
	Duration         *int         `json:"duration,omitempty"`          // Duration in seconds (for end events)
	SucceededPuzzles *int         `json:"succeeded_puzzles,omitempty"` // Number of puzzles solved successfully (for end events)
	FailedPuzzles    *int         `json:"failed_puzzles,omitempty"`    // Number of puzzles failed (for end events)
	EloType          string       `json:"elo_type,omitempty"`          // Type of ELO rating affected (for end events)
	EloChange        *int         `json:"elo_change,omitempty"`        // ELO rating change, e.g., +8 or -8 (for end events)
	AttemptType      AttemptType  `json:"attempt_type,omitempty"`      // NEW: defaults to "regular" if not specified
}

// PuzzleType represents the type/source of a puzzle
type PuzzleType string

const (
	PuzzleTypeUser    PuzzleType = "user"    // User-generated puzzles from their own games
	PuzzleTypeLichess PuzzleType = "lichess" // Lichess puzzle database puzzles
)

// PuzzleEventData represents data for individual puzzle events
type PuzzleEventData struct {
	PuzzleID    string      `json:"puzzle_id"`
	PuzzleType  PuzzleType  `json:"puzzle_type"`            // Type/source of the puzzle
	AttemptType AttemptType `json:"attempt_type,omitempty"` // NEW: defaults to "regular" if not specified
	Solved      bool        `json:"solved"`
	TimeSpent   int         `json:"time_spent"`            // Time spent in seconds
	MovesPlayed []string    `json:"moves_played"`          // Actual moves made
	IsDisliked  *bool       `json:"is_disliked,omitempty"` // Optional: whether user dislikes this puzzle

	// Arrow-duel specific fields (only used when AttemptType is "arrow_duel")
	CandidateMoves []string `json:"candidate_moves,omitempty"` // [blunder_move, correct_move]
	ChosenMove     *string  `json:"chosen_move,omitempty"`     // Move chosen by player
}
