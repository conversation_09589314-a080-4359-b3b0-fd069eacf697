package models

import (
	"time"
)

// IdempotencyRecord stores information about a request processed with an idempotency key.
type IdempotencyRecord struct {
	ID                 string    `gorm:"primarykey;type:varchar(36)" json:"id"`
	UserID             string    `gorm:"index:idx_user_key_method_path,unique;not null;type:varchar(36)" json:"user_id"`
	IdempotencyKey     string    `gorm:"index:idx_user_key_method_path,unique;not null;type:varchar(128)" json:"idempotency_key"`
	RequestMethod      string    `gorm:"index:idx_user_key_method_path,unique;type:varchar(10);not null" json:"request_method"`
	RequestPath        string    `gorm:"index:idx_user_key_method_path,unique;type:varchar(255);not null" json:"request_path"`
	ResponseStatusCode int       `gorm:"not null" json:"response_status_code"`
	ResponseBody       []byte    `gorm:"type:bytea" json:"response_body"` // Use bytea for Postgres compatibility
	CreatedAt          time.Time `gorm:"not null" json:"created_at"`
	ExpiresAt          time.Time `gorm:"index;not null" json:"expires_at"`
}
