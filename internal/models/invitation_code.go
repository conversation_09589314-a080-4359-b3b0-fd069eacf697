package models

import (
	"time"
)

// InvitationCode represents a one-time use code for user registration
type InvitationCode struct {
	ID              string     `gorm:"type:varchar(36);primary_key" json:"id"`
	Code            string     `gorm:"type:varchar(64);unique;not null;index" json:"code"`
	CreatedAt       time.Time  `gorm:"not null;default:current_timestamp" json:"created_at"`
	ExpiresAt       *time.Time `json:"expires_at,omitempty"`
	UsedAt          *time.Time `json:"used_at,omitempty"`
	UsedByUserID    *string    `gorm:"type:varchar(36);index" json:"used_by_user_id,omitempty"`
	CreatedByUserID string     `gorm:"type:varchar(36);not null;index" json:"created_by_user_id"`
}

// IsValid checks if the invitation code is valid (not used and not expired)
func (ic *InvitationCode) IsValid() bool {
	// Check if already used
	if ic.UsedAt != nil {
		return false
	}

	// Check if expired
	if ic.ExpiresAt != nil && time.Now().After(*ic.ExpiresAt) {
		return false
	}

	return true
}

// MarkAsUsed marks the invitation code as used by the specified user
func (ic *InvitationCode) MarkAsUsed(userID string) {
	now := time.Now()
	ic.UsedAt = &now
	ic.UsedByUserID = &userID
}
