package models

// "database/sql"
import (
	"encoding/json"
	// "fmt"
	"time"

	"github.com/chessticize/chessticize-server/internal/dbtypes" // Corrected import path
)

// TaskType defines the type of task.
type TaskType string

const (
	FetchChessGamesTask      TaskType = "FetchChessGames"
	EvaluateChessGameTask    TaskType = "EvaluateChessGame"
	GenerateChessPuzzlesTask TaskType = "GenerateChessPuzzles"
)

// TaskStatus defines the status of a task.
type TaskStatus string

const (
	TaskStatusPending    TaskStatus = "pending"
	TaskStatusInProgress TaskStatus = "in_progress"
	TaskStatusCompleted  TaskStatus = "completed"
	TaskStatusFailed     TaskStatus = "failed"
)

// Task represents a background task in the system.
type Task struct {
	ID          string             `gorm:"primarykey;type:varchar(36)" json:"id"`
	UserID      string             `gorm:"index;not null;type:varchar(36)" json:"user_id"`
	TaskType    TaskType           `gorm:"type:varchar(50);index;not null" json:"task_type"`
	TaskData    json.RawMessage    `gorm:"type:jsonb" json:"task_data"` // Flexible data based on TaskType
	Status      TaskStatus         `gorm:"type:varchar(20);index:idx_status_scheduled,priority:1;default:'pending';not null" json:"status"`
	Error       dbtypes.NullString `gorm:"type:text" json:"error,omitempty"` // Use custom type from dbtypes
	Attempts    int                `gorm:"default:0;not null" json:"attempts"`
	WorkerID    dbtypes.NullString `gorm:"type:varchar(100);index" json:"worker_id,omitempty"` // Use custom type from dbtypes
	PickedUpAt  dbtypes.NullTime   `json:"picked_up_at,omitempty"`                             // Use custom type from dbtypes
	ScheduledAt time.Time          `gorm:"index:idx_status_scheduled,priority:2;not null" json:"scheduled_at"`
	CreatedAt   time.Time          `json:"created_at"`
	UpdatedAt   time.Time          `json:"updated_at"`
}

// FetchChessGamesData represents the data for FetchChessGames task.
type FetchChessGamesData struct {
	UserID         string `json:"user_id"`
	ChessProfileID string `json:"chess_profile_id"`
}

// EvaluateChessGameData represents the data for EvaluateChessGame task.
type EvaluateChessGameData struct {
	UserID string `json:"user_id"`
	GameID string `json:"game_id"`
}

// GenerateChessPuzzlesData represents the data for GenerateChessPuzzles task.
type GenerateChessPuzzlesData struct {
	UserID string `json:"user_id"`
	GameID string `json:"game_id"`
}
