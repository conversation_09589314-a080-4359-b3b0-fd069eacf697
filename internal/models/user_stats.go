package models

import (
	"time"
)

// UserPuzzleStats represents per-puzzle statistics for a user (for user-generated puzzles)
type UserPuzzleStats struct {
	ID                 string     `gorm:"type:varchar(36);primary_key" json:"id"`
	UserID             string     `gorm:"type:varchar(36);not null;index:idx_user_puzzle,unique;index:idx_user_last_attempt;index:idx_user_puzzle_disliked,composite:is_disliked" json:"user_id"`
	PuzzleID           string     `gorm:"type:varchar(255);not null;index:idx_user_puzzle,unique" json:"puzzle_id"`
	Attempts           int        `gorm:"default:0" json:"attempts"`
	SuccessCount       int        `gorm:"default:0" json:"success_count"`
	TotalTime          int        `gorm:"default:0" json:"total_time"`   // Total time spent in seconds
	AverageTime        float64    `gorm:"default:0" json:"average_time"` // Average time per attempt
	LastAttemptTime    time.Time  `gorm:"not null;default:current_timestamp;index:idx_user_last_attempt" json:"last_attempt_time"`
	LastAttemptSuccess bool       `gorm:"default:false" json:"last_attempt_success"`
	IsDisliked         bool       `gorm:"default:false;index:idx_user_puzzle_disliked,composite:user_id" json:"is_disliked"` // User marked this puzzle as disliked
	DislikedAt         *time.Time `gorm:"" json:"disliked_at,omitempty"`                                                     // When the puzzle was disliked
	CreatedAt          time.Time  `gorm:"not null;default:current_timestamp" json:"created_at"`
	UpdatedAt          time.Time  `gorm:"not null;default:current_timestamp" json:"updated_at"`

	// Relationships
	User   User   `gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE" json:"-"`
	Puzzle Puzzle `gorm:"foreignKey:PuzzleID;constraint:OnDelete:CASCADE" json:"-"`
}

// TableName returns the table name for UserPuzzleStats
func (UserPuzzleStats) TableName() string {
	return "user_puzzle_stats"
}

// UserDailyStats represents daily puzzle statistics for efficient aggregation
type UserDailyStats struct {
	ID     string    `gorm:"type:varchar(36);primary_key" json:"id"`
	UserID string    `gorm:"type:varchar(36);not null;index:idx_user_date,unique" json:"user_id"`
	Date   time.Time `gorm:"type:date;not null;index:idx_user_date,unique" json:"date"`

	// Existing puzzle stats
	PuzzleSuccess       int `gorm:"default:0" json:"puzzle_success"`        // Number of successful puzzles
	PuzzleTotal         int `gorm:"default:0" json:"puzzle_total"`          // Total puzzle attempts
	Streak              int `gorm:"default:0" json:"streak"`                // Legacy streak
	PuzzleTotalDuration int `gorm:"default:0" json:"puzzle_total_duration"` // Total time spent in seconds

	// Sprint statistics
	SprintSuccess       int `gorm:"default:0" json:"sprint_success"`        // Successful sprints
	SprintTotal         int `gorm:"default:0" json:"sprint_total"`          // Total sprint attempts
	SprintTotalDuration int `gorm:"default:0" json:"sprint_total_duration"` // Total sprint time

	// Arrow duel statistics
	ArrowDuelSuccess       int `gorm:"default:0" json:"arrow_duel_success"`        // Successful arrow duels
	ArrowDuelTotal         int `gorm:"default:0" json:"arrow_duel_total"`          // Total arrow duel attempts
	ArrowDuelTotalDuration int `gorm:"default:0" json:"arrow_duel_total_duration"` // Total arrow duel time

	// Daily quest tracking
	QuestCompleted bool `gorm:"default:false" json:"quest_completed"` // All daily quests completed
	QuestStreak    int  `gorm:"default:0" json:"quest_streak"`        // Consecutive days with all quests completed

	CreatedAt time.Time `gorm:"not null;default:current_timestamp" json:"created_at"`
	UpdatedAt time.Time `gorm:"not null;default:current_timestamp" json:"updated_at"`

	// Relationships
	User User `gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE" json:"-"`
}

// UserLichessPuzzleStats represents per-lichess-puzzle statistics for a user
type UserLichessPuzzleStats struct {
	ID                 string     `gorm:"type:varchar(36);primary_key" json:"id"`
	UserID             string     `gorm:"type:varchar(36);not null;index:idx_user_lichess_puzzle,unique;index:idx_user_lichess_last_attempt;index:idx_user_lichess_disliked,composite:is_disliked" json:"user_id"`
	LichessPuzzleID    string     `gorm:"type:varchar(10);not null;index:idx_user_lichess_puzzle,unique" json:"lichess_puzzle_id"` // Lichess puzzle ID (e.g., "00008")
	Attempts           int        `gorm:"default:0" json:"attempts"`
	SuccessCount       int        `gorm:"default:0" json:"success_count"`
	TotalTime          int        `gorm:"default:0" json:"total_time"`   // Total time spent in seconds
	AverageTime        float64    `gorm:"default:0" json:"average_time"` // Average time per attempt
	LastAttemptTime    time.Time  `gorm:"not null;default:current_timestamp;index:idx_user_lichess_last_attempt" json:"last_attempt_time"`
	LastAttemptSuccess bool       `gorm:"default:false" json:"last_attempt_success"`
	IsDisliked         bool       `gorm:"default:false;index:idx_user_lichess_disliked,composite:user_id" json:"is_disliked"` // User marked this puzzle as disliked
	DislikedAt         *time.Time `gorm:"" json:"disliked_at,omitempty"`                                                      // When the puzzle was disliked
	CreatedAt          time.Time  `gorm:"not null;default:current_timestamp" json:"created_at"`
	UpdatedAt          time.Time  `gorm:"not null;default:current_timestamp" json:"updated_at"`

	// Relationships
	User          User          `gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE" json:"-"`
	LichessPuzzle LichessPuzzle `gorm:"foreignKey:LichessPuzzleID;constraint:OnDelete:CASCADE" json:"lichess_puzzle"`
}

// TableName returns the table name for UserLichessPuzzleStats
func (UserLichessPuzzleStats) TableName() string {
	return "user_lichess_puzzle_stats"
}

// UserElo represents current ELO rating for a user per ELO type
type UserElo struct {
	ID              int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID          string    `gorm:"type:varchar(36);not null;index:idx_user_elo_type,unique" json:"user_id"`
	EloType         string    `gorm:"type:varchar(100);not null;index:idx_user_elo_type,unique" json:"elo_type"`
	Rating          int       `gorm:"not null" json:"rating"`
	RatingDeviation float64   `gorm:"not null" json:"rating_deviation"` // Glicko-2 RD
	Volatility      float64   `gorm:"not null" json:"volatility"`       // Glicko-2 Sigma
	GamesPlayed     int       `gorm:"not null;default:0" json:"games_played"`
	LastActiveAt    time.Time `gorm:"not null;default:current_timestamp" json:"last_active_at"`
	IsProvisional   bool      `gorm:"not null;default:true" json:"is_provisional"`
	CreatedAt       time.Time `gorm:"not null;default:current_timestamp" json:"created_at"`
	UpdatedAt       time.Time `gorm:"not null;default:current_timestamp" json:"updated_at"`

	// Relationships
	User User `gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE" json:"-"`
}

// UserPuzzleArrowDuelStats represents arrow-duel stats for user puzzles
type UserPuzzleArrowDuelStats struct {
	ID                 string     `gorm:"type:varchar(36);primary_key" json:"id"`
	UserID             string     `gorm:"type:varchar(36);not null;index:idx_user_puzzle_arrow_duel,unique" json:"user_id"`
	PuzzleID           string     `gorm:"type:varchar(255);not null;index:idx_user_puzzle_arrow_duel,unique" json:"puzzle_id"`
	Attempts           int        `gorm:"default:0" json:"attempts"`
	SuccessCount       int        `gorm:"default:0" json:"success_count"`
	TotalTime          int        `gorm:"default:0" json:"total_time"`
	AverageTime        float64    `gorm:"default:0" json:"average_time"`
	LastAttemptTime    time.Time  `gorm:"not null;default:current_timestamp" json:"last_attempt_time"`
	LastAttemptSuccess bool       `gorm:"default:false" json:"last_attempt_success"`
	IsDisliked         bool       `gorm:"default:false" json:"is_disliked"`
	DislikedAt         *time.Time `gorm:"" json:"disliked_at,omitempty"`
	CreatedAt          time.Time  `gorm:"not null;default:current_timestamp" json:"created_at"`
	UpdatedAt          time.Time  `gorm:"not null;default:current_timestamp" json:"updated_at"`

	// Relationships
	User   User   `gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE" json:"-"`
	Puzzle Puzzle `gorm:"foreignKey:PuzzleID;constraint:OnDelete:CASCADE" json:"-"`
}

// TableName returns the table name for UserPuzzleArrowDuelStats
func (UserPuzzleArrowDuelStats) TableName() string {
	return "user_puzzle_arrow_duel_stats"
}

// UserLichessPuzzleArrowDuelStats represents arrow-duel stats for Lichess puzzles
type UserLichessPuzzleArrowDuelStats struct {
	ID                 string     `gorm:"type:varchar(36);primary_key" json:"id"`
	UserID             string     `gorm:"type:varchar(36);not null;index:idx_user_lichess_arrow_duel,unique" json:"user_id"`
	LichessPuzzleID    string     `gorm:"type:varchar(10);not null;index:idx_user_lichess_arrow_duel,unique" json:"lichess_puzzle_id"`
	Attempts           int        `gorm:"default:0" json:"attempts"`
	SuccessCount       int        `gorm:"default:0" json:"success_count"`
	TotalTime          int        `gorm:"default:0" json:"total_time"`
	AverageTime        float64    `gorm:"default:0" json:"average_time"`
	LastAttemptTime    time.Time  `gorm:"not null;default:current_timestamp" json:"last_attempt_time"`
	LastAttemptSuccess bool       `gorm:"default:false" json:"last_attempt_success"`
	IsDisliked         bool       `gorm:"default:false" json:"is_disliked"`
	DislikedAt         *time.Time `gorm:"" json:"disliked_at,omitempty"`
	CreatedAt          time.Time  `gorm:"not null;default:current_timestamp" json:"created_at"`
	UpdatedAt          time.Time  `gorm:"not null;default:current_timestamp" json:"updated_at"`

	// Relationships
	User          User          `gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE" json:"-"`
	LichessPuzzle LichessPuzzle `gorm:"foreignKey:LichessPuzzleID;constraint:OnDelete:CASCADE" json:"-"`
}

// TableName returns the table name for UserLichessPuzzleArrowDuelStats
func (UserLichessPuzzleArrowDuelStats) TableName() string {
	return "user_lichess_puzzle_arrow_duel_stats"
}

// DailyQuestRequirement represents a quest requirement definition
type DailyQuestRequirement struct {
	ID          string    `gorm:"type:varchar(36);primary_key" json:"id"`
	Type        string    `gorm:"type:varchar(50);not null;unique" json:"type"` // "sprint", "arrow_duel"
	Name        string    `gorm:"type:varchar(100);not null" json:"name"`
	Description string    `gorm:"type:text" json:"description"`
	Target      int       `gorm:"default:1" json:"target"` // Required count (e.g., 1 sprint)
	IsActive    bool      `gorm:"default:true" json:"is_active"`
	CreatedAt   time.Time `gorm:"not null;default:current_timestamp" json:"created_at"`
	UpdatedAt   time.Time `gorm:"not null;default:current_timestamp" json:"updated_at"`
}

// TableName returns the table name for DailyQuestRequirement
func (DailyQuestRequirement) TableName() string {
	return "daily_quest_requirements"
}
