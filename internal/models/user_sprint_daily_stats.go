package models

import (
	"time"
)

// UserSprintDailyStats represents daily sprint statistics for efficient aggregation
// Similar to UserDailyStats but for sprint activities
type UserSprintDailyStats struct {
	ID                  string    `gorm:"type:varchar(36);primary_key" json:"id"`
	UserID              string    `gorm:"type:varchar(36);not null;index:idx_user_sprint_date,unique" json:"user_id"`
	Date                time.Time `gorm:"type:date;not null;index:idx_user_sprint_date,unique" json:"date"`
	EloType             string    `gorm:"type:varchar(100);not null;index:idx_user_sprint_date,unique" json:"elo_type"`
	SprintSuccess       int       `gorm:"default:0" json:"sprint_success"`        // Number of successful sprints
	SprintTotal         int       `gorm:"default:0" json:"sprint_total"`          // Total sprint attempts
	SprintTotalDuration int       `gorm:"default:0" json:"sprint_total_duration"` // Total time spent in seconds
	PuzzlesSolved       int       `gorm:"default:0" json:"puzzles_solved"`        // Total puzzles solved across all sprints
	PuzzlesAttempted    int       `gorm:"default:0" json:"puzzles_attempted"`     // Total puzzles attempted across all sprints
	Streak              int       `gorm:"default:0" json:"streak"`                // Consecutive days with sprint activity
	CreatedAt           time.Time `gorm:"not null;default:current_timestamp" json:"created_at"`
	UpdatedAt           time.Time `gorm:"not null;default:current_timestamp" json:"updated_at"`

	// Relationships
	User User `gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE" json:"-"`
}

// TableName returns the table name for UserSprintDailyStats
func (UserSprintDailyStats) TableName() string {
	return "user_sprint_daily_stats"
}
