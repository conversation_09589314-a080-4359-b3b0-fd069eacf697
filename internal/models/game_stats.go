package models

import "time"

// PlatformCount represents a chess platform and its count
type PlatformCount struct {
	Platform ChessPlatform `json:"platform"`
	Count    int           `json:"count"`
}

// ResultCount represents a game result and its count
type ResultCount struct {
	Result GameResult `json:"result"`
	Count  int        `json:"count"`
}

// TimeControlCount represents a time control and its count
type TimeControlCount struct {
	TimeControl string `json:"time_control"`
	Count       int    `json:"count"`
}

// RatedCount represents a rated status and its count
type RatedCount struct {
	Rated bool `json:"rated"`
	Count int  `json:"count"`
}

// GameStats represents aggregated statistics about games
type GameStats struct {
	PlatformCounts        []PlatformCount    `json:"platform_counts"`
	UserColorCounts       []ColorCount       `json:"user_color_counts"`
	ResultCounts          []ResultCount      `json:"result_counts"`
	TimeControlCounts     []TimeControlCount `json:"time_control_counts"`
	RatedCounts           []RatedCount       `json:"rated_counts"`
	AverageOpponentRating float64            `json:"average_opponent_rating"`
	TotalCount            int64              `json:"total_count"`
	PeriodStart           *time.Time         `json:"period_start,omitempty"`
	PeriodEnd             *time.Time         `json:"period_end,omitempty"`
}
