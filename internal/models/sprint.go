package models

import (
	"time"
)

// Sprint represents a puzzle sprint session
type Sprint struct {
	ID               string       `gorm:"type:varchar(36);primary_key" json:"id"`
	UserID           string       `gorm:"type:varchar(36);not null;index" json:"user_id"`
	EloType          string       `gorm:"type:varchar(100);not null" json:"elo_type"`
	Status           SprintStatus `gorm:"type:varchar(50);not null;index" json:"status"`
	TargetPuzzles    int          `gorm:"not null;default:20" json:"target_puzzles"`
	TimeLimitSeconds int          `gorm:"not null;default:600" json:"time_limit_seconds"`
	MaxMistakes      int          `gorm:"not null;default:2" json:"max_mistakes"`
	PuzzlesSolved    int          `gorm:"not null;default:0" json:"puzzles_solved"`
	MistakesMade     int          `gorm:"not null;default:0" json:"mistakes_made"`
	StartedAt        time.Time    `gorm:"not null;index" json:"started_at"`
	EndedAt          *time.Time   `gorm:"" json:"ended_at,omitempty"`
	DurationSeconds  *int         `gorm:"" json:"duration_seconds,omitempty"`
	EloRatingBefore  int          `gorm:"not null" json:"elo_rating_before"`
	EloRatingAfter   *int         `gorm:"" json:"elo_rating_after,omitempty"`
	EloChange        *int         `gorm:"" json:"elo_change,omitempty"`
	CreatedAt        time.Time    `gorm:"not null;default:current_timestamp" json:"created_at"`
	UpdatedAt        time.Time    `gorm:"not null;default:current_timestamp" json:"updated_at"`

	// Relationships
	User          User           `gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE" json:"-"`
	SprintPuzzles []SprintPuzzle `gorm:"foreignKey:SprintID;constraint:OnDelete:CASCADE" json:"sprint_puzzles,omitempty"`
}

// TableName returns the table name for Sprint
func (Sprint) TableName() string {
	return "sprints"
}

// SprintStats represents aggregated statistics for sprints
type SprintStats struct {
	Date                  *time.Time `json:"date,omitempty"`          // Date for time-grouped stats (nil for overall stats)
	SprintSuccess         int        `json:"sprint_success"`          // Number of successful sprints
	SprintTotal           int        `json:"sprint_total"`            // Total sprint attempts
	SprintTotalDuration   int        `json:"sprint_total_duration"`   // Total time spent in seconds
	PuzzlesSolved         int        `json:"puzzles_solved"`          // Total puzzles solved across all sprints
	PuzzlesAttempted      int        `json:"puzzles_attempted"`       // Total puzzles attempted across all sprints
	AverageSprintDuration float64    `json:"average_sprint_duration"` // Average sprint duration in seconds
	SuccessRate           float64    `json:"success_rate"`            // Success rate as percentage (0-100)
	PuzzleAccuracy        float64    `json:"puzzle_accuracy"`         // Puzzle accuracy as percentage (0-100)
}
