package models

// Color represents a chess piece color
type Color string

const (
	White Color = "white"
	Black Color = "black"
)

// GameResult represents the detailed result of a game
type GameResult string

const (
	Mate      GameResult = "mate"
	Resign    GameResult = "resign"
	OutOfTime GameResult = "outoftime"
	Abandoned GameResult = "abandoned"
	Draw      GameResult = "draw"
)

// Winner represents who won the game
type Winner string

const (
	WinnerWhite Winner = "white"
	WinnerBlack Winner = "black"
	WinnerNone  Winner = "none" // For draws or abandoned games
)

// PuzzleTheme represents different types of tactical opportunities in puzzles
type PuzzleTheme string

const (
	OpponentMistakeCaught PuzzleTheme = "opponent_mistake_caught" // User found and punished opponent's mistake
	OpponentMistakeMissed PuzzleTheme = "opponent_mistake_missed" // User missed punishing opponent's mistake
	OpponentBlunderCaught PuzzleTheme = "opponent_blunder_caught" // User found and punished opponent's blunder
	OpponentBlunderMissed PuzzleTheme = "opponent_blunder_missed" // User missed punishing opponent's blunder
	OwnMistakePunished    PuzzleTheme = "own_mistake_punished"    // Opponent found and punished user's mistake
	OwnMistakeEscaped     PuzzleTheme = "own_mistake_escaped"     // User made mistake but opponent missed punishment
	OwnBlunderPunished    PuzzleTheme = "own_blunder_punished"    // Opponent found and punished user's blunder
	OwnBlunderEscaped     PuzzleTheme = "own_blunder_escaped"     // User made blunder but opponent missed punishment
)

// EventType represents the type of user event
type EventType string

const (
	EventTypeSignIn       EventType = "sign_in"
	EventTypeRegistration EventType = "registration"
	EventTypeGameReview   EventType = "game_review"
	EventTypePuzzleSprint EventType = "puzzle_sprint"
	EventTypePuzzle       EventType = "puzzle"
)

// EventSubType represents the sub-type of user event
type EventSubType string

const (
	EventSubTypeStart   EventSubType = "start"
	EventSubTypeEnd     EventSubType = "end"
	EventSubTypeSuccess EventSubType = "success"
	EventSubTypeFailure EventSubType = "failure"
)

// SprintStatus represents the status of a sprint session
type SprintStatus string

const (
	SprintStatusActive                SprintStatus = "active"
	SprintStatusCompletedSuccess      SprintStatus = "completed_success"
	SprintStatusCompletedFailMistakes SprintStatus = "completed_fail_mistakes"
	SprintStatusCompletedFailTime     SprintStatus = "completed_fail_time"
	SprintStatusAbandoned             SprintStatus = "abandoned"
)

// AttemptType represents the type of puzzle attempt
type AttemptType string

const (
	AttemptTypeRegular   AttemptType = "regular"    // Normal puzzle solving (DEFAULT)
	AttemptTypeArrowDuel AttemptType = "arrow_duel" // Arrow-duel mode
)

// GetAttemptTypeOrDefault returns the attempt type or "regular" if empty/nil
func GetAttemptTypeOrDefault(attemptType *AttemptType) AttemptType {
	if attemptType == nil || *attemptType == "" {
		return AttemptTypeRegular
	}
	return *attemptType
}
