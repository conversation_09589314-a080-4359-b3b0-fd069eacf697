package models

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestUserPuzzleArrowDuelStats(t *testing.T) {
	t.Run("CreateUserPuzzleArrowDuelStats", func(t *testing.T) {
		now := time.Now()
		dislikedAt := now.Add(-time.Hour)

		stats := UserPuzzleArrowDuelStats{
			ID:                 "arrow-duel-stats-123",
			UserID:             "user-456",
			PuzzleID:           "puzzle-789",
			Attempts:           8,
			SuccessCount:       5,
			TotalTime:          240,
			AverageTime:        30.0,
			LastAttemptTime:    now,
			LastAttemptSuccess: true,
			IsDisliked:         true,
			DislikedAt:         &dislikedAt,
			CreatedAt:          now.Add(-time.Hour * 24),
			UpdatedAt:          now,
		}

		assert.Equal(t, "arrow-duel-stats-123", stats.ID)
		assert.Equal(t, "user-456", stats.UserID)
		assert.Equal(t, "puzzle-789", stats.PuzzleID)
		assert.Equal(t, 8, stats.Attempts)
		assert.Equal(t, 5, stats.SuccessCount)
		assert.Equal(t, 240, stats.TotalTime)
		assert.Equal(t, 30.0, stats.AverageTime)
		assert.True(t, stats.LastAttemptSuccess)
		assert.True(t, stats.IsDisliked)
		assert.NotNil(t, stats.DislikedAt)
		assert.Equal(t, dislikedAt, *stats.DislikedAt)
	})

	t.Run("UserPuzzleArrowDuelStats_NotDisliked", func(t *testing.T) {
		stats := UserPuzzleArrowDuelStats{
			ID:                 "arrow-duel-stats-456",
			UserID:             "user-789",
			PuzzleID:           "puzzle-123",
			Attempts:           3,
			SuccessCount:       2,
			TotalTime:          90,
			AverageTime:        30.0,
			LastAttemptTime:    time.Now(),
			LastAttemptSuccess: false,
			IsDisliked:         false,
			DislikedAt:         nil,
			CreatedAt:          time.Now().Add(-time.Hour),
			UpdatedAt:          time.Now(),
		}

		assert.False(t, stats.IsDisliked)
		assert.Nil(t, stats.DislikedAt)
		assert.False(t, stats.LastAttemptSuccess)
	})
}

func TestUserLichessPuzzleArrowDuelStats(t *testing.T) {
	t.Run("CreateUserLichessPuzzleArrowDuelStats", func(t *testing.T) {
		now := time.Now()

		stats := UserLichessPuzzleArrowDuelStats{
			ID:                 "lichess-arrow-duel-stats-123",
			UserID:             "user-456",
			LichessPuzzleID:    "00008",
			Attempts:           12,
			SuccessCount:       8,
			TotalTime:          360,
			AverageTime:        30.0,
			LastAttemptTime:    now,
			LastAttemptSuccess: true,
			IsDisliked:         false,
			DislikedAt:         nil,
			CreatedAt:          now.Add(-time.Hour * 48),
			UpdatedAt:          now,
		}

		assert.Equal(t, "lichess-arrow-duel-stats-123", stats.ID)
		assert.Equal(t, "user-456", stats.UserID)
		assert.Equal(t, "00008", stats.LichessPuzzleID)
		assert.Equal(t, 12, stats.Attempts)
		assert.Equal(t, 8, stats.SuccessCount)
		assert.Equal(t, 360, stats.TotalTime)
		assert.Equal(t, 30.0, stats.AverageTime)
		assert.True(t, stats.LastAttemptSuccess)
		assert.False(t, stats.IsDisliked)
		assert.Nil(t, stats.DislikedAt)
	})

	t.Run("UserLichessPuzzleArrowDuelStats_WithDislike", func(t *testing.T) {
		now := time.Now()
		dislikedAt := now.Add(-time.Minute * 30)

		stats := UserLichessPuzzleArrowDuelStats{
			ID:                 "lichess-arrow-duel-stats-456",
			UserID:             "user-789",
			LichessPuzzleID:    "000VW",
			Attempts:           5,
			SuccessCount:       1,
			TotalTime:          300,
			AverageTime:        60.0,
			LastAttemptTime:    now,
			LastAttemptSuccess: false,
			IsDisliked:         true,
			DislikedAt:         &dislikedAt,
			CreatedAt:          now.Add(-time.Hour * 2),
			UpdatedAt:          now,
		}

		assert.Equal(t, "000VW", stats.LichessPuzzleID)
		assert.Equal(t, 5, stats.Attempts)
		assert.Equal(t, 1, stats.SuccessCount)
		assert.Equal(t, 60.0, stats.AverageTime)
		assert.False(t, stats.LastAttemptSuccess)
		assert.True(t, stats.IsDisliked)
		assert.NotNil(t, stats.DislikedAt)
		assert.Equal(t, dislikedAt, *stats.DislikedAt)
	})
}

func TestAttemptType(t *testing.T) {
	t.Run("AttemptType_Constants", func(t *testing.T) {
		assert.Equal(t, AttemptType("regular"), AttemptTypeRegular)
		assert.Equal(t, AttemptType("arrow_duel"), AttemptTypeArrowDuel)
	})

	t.Run("GetAttemptTypeOrDefault_WithNil", func(t *testing.T) {
		result := GetAttemptTypeOrDefault(nil)
		assert.Equal(t, AttemptTypeRegular, result)
	})

	t.Run("GetAttemptTypeOrDefault_WithEmpty", func(t *testing.T) {
		empty := AttemptType("")
		result := GetAttemptTypeOrDefault(&empty)
		assert.Equal(t, AttemptTypeRegular, result)
	})

	t.Run("GetAttemptTypeOrDefault_WithRegular", func(t *testing.T) {
		regular := AttemptTypeRegular
		result := GetAttemptTypeOrDefault(&regular)
		assert.Equal(t, AttemptTypeRegular, result)
	})

	t.Run("GetAttemptTypeOrDefault_WithArrowDuel", func(t *testing.T) {
		arrowDuel := AttemptTypeArrowDuel
		result := GetAttemptTypeOrDefault(&arrowDuel)
		assert.Equal(t, AttemptTypeArrowDuel, result)
	})
}

func TestPuzzleEventData_ArrowDuel(t *testing.T) {
	t.Run("PuzzleEventData_ArrowDuel_Complete", func(t *testing.T) {
		chosenMove := "e2e4"
		isDisliked := true
		attemptType := AttemptTypeArrowDuel

		eventData := PuzzleEventData{
			PuzzleID:       "puzzle-123",
			PuzzleType:     PuzzleTypeUser,
			AttemptType:    attemptType,
			Solved:         true,
			TimeSpent:      45,
			MovesPlayed:    []string{"e2e4", "e7e5"},
			IsDisliked:     &isDisliked,
			CandidateMoves: []string{"e2e3", "e2e4"}, // [blunder, correct]
			ChosenMove:     &chosenMove,
		}

		assert.Equal(t, "puzzle-123", eventData.PuzzleID)
		assert.Equal(t, PuzzleTypeUser, eventData.PuzzleType)
		assert.Equal(t, AttemptTypeArrowDuel, eventData.AttemptType)
		assert.True(t, eventData.Solved)
		assert.Equal(t, 45, eventData.TimeSpent)
		assert.Len(t, eventData.MovesPlayed, 2)
		assert.NotNil(t, eventData.IsDisliked)
		assert.True(t, *eventData.IsDisliked)
		assert.Len(t, eventData.CandidateMoves, 2)
		assert.Equal(t, "e2e3", eventData.CandidateMoves[0]) // blunder
		assert.Equal(t, "e2e4", eventData.CandidateMoves[1]) // correct
		assert.NotNil(t, eventData.ChosenMove)
		assert.Equal(t, "e2e4", *eventData.ChosenMove)
	})

	t.Run("PuzzleEventData_Regular_BackwardCompatibility", func(t *testing.T) {
		// Test that regular puzzle events work without arrow-duel fields
		eventData := PuzzleEventData{
			PuzzleID:    "puzzle-456",
			PuzzleType:  PuzzleTypeLichess,
			Solved:      false,
			TimeSpent:   30,
			MovesPlayed: []string{"Qh5"},
			// AttemptType, CandidateMoves, ChosenMove not set (backward compatibility)
		}

		assert.Equal(t, "puzzle-456", eventData.PuzzleID)
		assert.Equal(t, PuzzleTypeLichess, eventData.PuzzleType)
		// AttemptType should be empty string for backward compatibility (zero value)
		assert.Equal(t, AttemptType(""), eventData.AttemptType)
		assert.False(t, eventData.Solved)
		assert.Equal(t, 30, eventData.TimeSpent)
		assert.Len(t, eventData.MovesPlayed, 1)
		assert.Nil(t, eventData.IsDisliked)
		assert.Nil(t, eventData.CandidateMoves)
		assert.Nil(t, eventData.ChosenMove)
	})
}

func TestPuzzleSprintEventData_ArrowDuel(t *testing.T) {
	t.Run("PuzzleSprintEventData_ArrowDuel", func(t *testing.T) {
		eloChange := 25
		duration := 600
		succeededPuzzles := 15
		failedPuzzles := 5
		attemptType := AttemptTypeArrowDuel

		eventData := PuzzleSprintEventData{
			SprintID:         "sprint-123",
			Duration:         &duration,
			SucceededPuzzles: &succeededPuzzles,
			FailedPuzzles:    &failedPuzzles,
			EloType:          "arrowduel",
			EloChange:        &eloChange,
			AttemptType:      attemptType,
		}

		assert.Equal(t, "sprint-123", eventData.SprintID)
		assert.NotNil(t, eventData.Duration)
		assert.Equal(t, 600, *eventData.Duration)
		assert.NotNil(t, eventData.SucceededPuzzles)
		assert.Equal(t, 15, *eventData.SucceededPuzzles)
		assert.NotNil(t, eventData.FailedPuzzles)
		assert.Equal(t, 5, *eventData.FailedPuzzles)
		assert.Equal(t, "arrowduel", eventData.EloType)
		assert.NotNil(t, eventData.EloChange)
		assert.Equal(t, 25, *eventData.EloChange)
		assert.Equal(t, AttemptTypeArrowDuel, eventData.AttemptType)
	})

	t.Run("PuzzleSprintEventData_Regular_BackwardCompatibility", func(t *testing.T) {
		// Test that regular sprint events work without arrow-duel fields
		duration := 300
		succeededPuzzles := 8
		failedPuzzles := 2

		eventData := PuzzleSprintEventData{
			SprintID:         "sprint-456",
			Duration:         &duration,
			SucceededPuzzles: &succeededPuzzles,
			FailedPuzzles:    &failedPuzzles,
			EloType:          "mixed 10/30",
			// AttemptType not set (backward compatibility)
		}

		assert.Equal(t, "sprint-456", eventData.SprintID)
		assert.NotNil(t, eventData.Duration)
		assert.Equal(t, 300, *eventData.Duration)
		assert.NotNil(t, eventData.SucceededPuzzles)
		assert.Equal(t, 8, *eventData.SucceededPuzzles)
		assert.NotNil(t, eventData.FailedPuzzles)
		assert.Equal(t, 2, *eventData.FailedPuzzles)
		assert.Equal(t, "mixed 10/30", eventData.EloType)
		assert.Nil(t, eventData.EloChange)
		// AttemptType should be empty string for backward compatibility (zero value)
		assert.Equal(t, AttemptType(""), eventData.AttemptType)
	})
}
