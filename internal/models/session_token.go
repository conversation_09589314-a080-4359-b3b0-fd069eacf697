package models

import (
	"time"
)

// SessionToken represents a long-term authentication token for a user
type SessionToken struct {
	ID        string    `gorm:"type:varchar(36);primary_key" json:"id"`
	UserID    string    `gorm:"type:varchar(36);not null;index" json:"user_id"`
	Token     string    `gorm:"type:varchar(255);unique;not null;index" json:"-"` // Don't expose in JSON
	UserAgent string    `gorm:"type:varchar(512)" json:"user_agent"`
	ExpiresAt time.Time `gorm:"not null;index" json:"expires_at"`
	CreatedAt time.Time `gorm:"not null;default:current_timestamp" json:"created_at"`
	UpdatedAt time.Time `gorm:"not null;default:current_timestamp" json:"updated_at"`

	// Relationships
	User User `gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE" json:"-"`
}

// IsExpired checks if the session token has expired
func (st *SessionToken) IsExpired() bool {
	return time.Now().After(st.ExpiresAt)
}

// SessionTokenResponse represents the response structure for session tokens
// Note: We don't include the token hash for security reasons (like passwords)
type SessionTokenResponse struct {
	ID        string    `json:"id"`
	UserAgent string    `json:"user_agent"`
	ExpiresAt time.Time `json:"expires_at"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// ToResponse converts a SessionToken to SessionTokenResponse
func (st *SessionToken) ToResponse() SessionTokenResponse {
	return SessionTokenResponse{
		ID:        st.ID,
		UserAgent: st.UserAgent,
		ExpiresAt: st.ExpiresAt,
		CreatedAt: st.CreatedAt,
		UpdatedAt: st.UpdatedAt,
	}
}
