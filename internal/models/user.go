package models

import (
	"time"
)

// ChessProfile represents a user's profile on a specific chess platform.
type ChessProfile struct {
	ID                string        `gorm:"type:varchar(36);primary_key" json:"id"`                                   // UUID primary key for the profile
	UserID            string        `gorm:"type:varchar(36);not null;index:idx_user_platform,unique" json:"-"`        // Foreign key to User
	Platform          ChessPlatform `gorm:"type:varchar(50);not null;index:idx_user_platform,unique" json:"platform"` // e.g., "chess.com", "lichess"
	Username          string        `gorm:"type:varchar(100);not null" json:"username"`
	GamesFetched      int           `gorm:"default:0" json:"games_fetched"`
	LastGameFetchedAt *time.Time    `json:"last_game_fetched_at,omitempty"`
	LastGamePlayedAt  *time.Time    `json:"last_game_played_at,omitempty"` // Timestamp of the latest game played (from games table)
	CreatedAt         time.Time     `gorm:"not null;default:current_timestamp" json:"created_at"`
	UpdatedAt         time.Time     `gorm:"not null;default:current_timestamp" json:"updated_at"`
}

// User represents a registered user of the service
type User struct {
	ID            string         `gorm:"type:varchar(36);primary_key" json:"id"`
	Email         string         `gorm:"type:varchar(255);unique;not null;index" json:"email"`
	PasswordHash  string         `gorm:"type:varchar(255);not null" json:"-"`
	FirebaseUID   *string        `gorm:"type:varchar(255);uniqueIndex" json:"firebase_uid,omitempty"`
	RegisteredAt  time.Time      `gorm:"not null;default:current_timestamp" json:"registered_at"`
	UpdatedAt     time.Time      `gorm:"not null;default:current_timestamp" json:"updated_at"`
	LastSignInAt  *time.Time     `json:"last_sign_in_at,omitempty"`
	ChessProfiles []ChessProfile `gorm:"foreignKey:UserID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;" json:"chess_profiles,omitempty"` // One-to-many relationship
	Games         []Game         `gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE" json:"games,omitempty"`
	Puzzles       []Puzzle       `gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE" json:"puzzles,omitempty"`
}
