package models

import (
	"time"

	"github.com/chessticize/chessticize-server/internal/utils"
)

// Game represents a chess game played on either chess.com or lichess
type Game struct {
	ID            string        `gorm:"type:varchar(36);primary_key" json:"id"`
	UserID        string        `gorm:"type:varchar(36);not null;index;index:idx_user_platform_time" json:"user_id"`
	Platform      ChessPlatform `gorm:"type:varchar(20);not null;index:idx_user_platform_time" json:"platform"`
	ChessUsername string        `gorm:"type:varchar(255);not null" json:"chess_username"`
	UserColor     Color         `gorm:"type:varchar(10);not null" json:"user_color"`
	GameTime      time.Time     `gorm:"not null;index:idx_user_platform_time" json:"game_time"`
	CompressedPGN []byte        `gorm:"type:bytea;not null" json:"-"`
	TimeControl   string        `gorm:"type:varchar(20);not null" json:"time_control"`
	Rated         bool          `gorm:"not null" json:"rated"`
	URL           *string       `gorm:"type:varchar(512)" json:"url,omitempty"`
	WhitePlayer   string        `gorm:"type:varchar(512);not null" json:"white_player"` // JSON serialized PlayerInfo
	BlackPlayer   string        `gorm:"type:varchar(512);not null" json:"black_player"` // JSON serialized PlayerInfo
	Winner        Winner        `gorm:"type:varchar(10);not null" json:"winner"`
	Result        GameResult    `gorm:"type:varchar(20);not null" json:"result"`

	// Timestamps handled by GORM
	CreatedAt time.Time `gorm:"not null;index" json:"created_at"`
	UpdatedAt time.Time `gorm:"not null" json:"updated_at"`

	// Relationships
	User    User     `gorm:"foreignKey:UserID" json:"-"`
	Puzzles []Puzzle `gorm:"foreignKey:GameID;constraint:OnDelete:CASCADE" json:"puzzles,omitempty"`
}

// GetPGN decompresses and returns the PGN data
func (g *Game) GetPGN() (string, error) {
	return utils.DecompressPGN(g.CompressedPGN)
}
