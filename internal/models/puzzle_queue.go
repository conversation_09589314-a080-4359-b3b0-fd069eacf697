package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// PuzzleQueueEntry represents a puzzle in the user's spaced repetition queue
type PuzzleQueueEntry struct {
	ID                 string      `gorm:"type:varchar(36);primarykey" json:"id"`
	UserID             string      `gorm:"type:varchar(36);not null;index:idx_puzzle_queue_user_due,priority:1;uniqueIndex:idx_puzzle_queue_user_puzzle,priority:1;index:idx_puzzle_queue_user_mistake_due,priority:1" json:"user_id"`
	PuzzleID           string      `gorm:"type:varchar(255);not null;uniqueIndex:idx_puzzle_queue_user_puzzle,priority:2" json:"puzzle_id"`
	PuzzleTheme        PuzzleTheme `gorm:"type:varchar(50);not null" json:"puzzle_theme"`
	MistakeBy          string      `gorm:"type:varchar(20);not null;index:idx_puzzle_queue_user_mistake_due,priority:2" json:"mistake_by"` // "opponent" or "own"
	DueAt              time.Time   `gorm:"not null;index:idx_puzzle_queue_user_due,priority:2;index:idx_puzzle_queue_user_mistake_due,priority:3" json:"due_at"`
	AttemptsSinceAdded int         `gorm:"not null;default:0" json:"attempts_since_added"`
	ConsecutiveCorrect int         `gorm:"not null;default:0" json:"consecutive_correct"`
	CreatedAt          time.Time   `gorm:"not null" json:"created_at"`
	UpdatedAt          time.Time   `gorm:"not null" json:"updated_at"`

	// Relationships
	Puzzle Puzzle `gorm:"foreignKey:PuzzleID;constraint:OnDelete:CASCADE" json:"-"`
	User   User   `gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE" json:"-"`
}

// TableName returns the table name for PuzzleQueueEntry
func (PuzzleQueueEntry) TableName() string {
	return "puzzle_queue"
}

// BeforeCreate sets the ID if not already set
func (pq *PuzzleQueueEntry) BeforeCreate(tx *gorm.DB) error {
	if pq.ID == "" {
		pq.ID = uuid.New().String()
	}
	return nil
}

// GetMistakeByFromTheme returns the mistake_by category based on puzzle theme
func GetMistakeByFromTheme(theme PuzzleTheme) string {
	switch theme {
	case OpponentMistakeMissed, OpponentBlunderMissed:
		return "opponent"
	case OwnMistakePunished, OwnMistakeEscaped, OwnBlunderPunished, OwnBlunderEscaped:
		return "own"
	default:
		return ""
	}
}

// IsQueueableTheme returns true if the theme should be added to the puzzle queue
func IsQueueableTheme(theme PuzzleTheme) bool {
	switch theme {
	case OpponentMistakeMissed, OpponentBlunderMissed,
		OwnMistakePunished, OwnMistakeEscaped, OwnBlunderPunished, OwnBlunderEscaped:
		return true
	default:
		return false
	}
}

// PuzzleQueueItem represents a puzzle queue entry with puzzle data for API responses
type PuzzleQueueItem struct {
	QueueID            string    `json:"queue_id"`
	PuzzleID           string    `json:"puzzle_id"`
	PuzzleTheme        string    `json:"puzzle_theme"`
	MistakeBy          string    `json:"mistake_by"`
	DueAt              time.Time `json:"due_at"`
	AttemptsSinceAdded int       `json:"attempts_since_added"`
	ConsecutiveCorrect int       `json:"consecutive_correct"`
	PuzzleData         *Puzzle   `json:"puzzle_data"`
}

// PuzzleQueueStats represents statistics about a user's puzzle queue
type PuzzleQueueStats struct {
	TotalQueued      int64 `json:"total_queued"`
	DueToday         int64 `json:"due_today"`
	OpponentMistakes int64 `json:"opponent_mistakes"`
	OwnMistakes      int64 `json:"own_mistakes"`
}
