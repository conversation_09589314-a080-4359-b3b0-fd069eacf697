package models

import (
	"time"
)

// EloHistory represents a historical record of ELO rating changes
type EloHistory struct {
	ID                    string    `gorm:"type:varchar(36);primary_key" json:"id"`
	UserID                string    `gorm:"type:varchar(36);not null;index" json:"user_id"`
	EloType               string    `gorm:"type:varchar(100);not null;index" json:"elo_type"`
	RatingBefore          int       `gorm:"not null" json:"rating_before"`
	RatingAfter           int       `gorm:"not null" json:"rating_after"`
	RatingChange          int       `gorm:"not null" json:"rating_change"`
	RatingDeviationBefore float64   `gorm:"not null" json:"rating_deviation_before"`
	RatingDeviationAfter  float64   `gorm:"not null" json:"rating_deviation_after"`
	SprintID              string    `gorm:"type:varchar(36);not null;index" json:"sprint_id"`
	CreatedAt             time.Time `gorm:"not null;default:current_timestamp;index" json:"created_at"`

	// Relationships
	User   User   `gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE" json:"-"`
	Sprint Sprint `gorm:"foreignKey:SprintID;constraint:OnDelete:CASCADE" json:"-"`
}

// TableName returns the table name for EloHistory
func (EloHistory) TableName() string {
	return "elo_history"
}
