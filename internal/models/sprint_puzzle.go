package models

import (
	"time"

	"github.com/lib/pq"
)

// SprintPuzzle represents the batch of puzzles selected for a sprint session
// Actual user attempts and results are tracked in UserLichessPuzzleStats
type SprintPuzzle struct {
	ID               string    `gorm:"type:varchar(36);primary_key" json:"id"`
	SprintID         string    `gorm:"type:varchar(36);not null;index" json:"sprint_id"`
	LichessPuzzleID  string    `gorm:"type:varchar(10);not null;index" json:"lichess_puzzle_id"`
	SequenceInSprint int       `gorm:"not null" json:"sequence_in_sprint"`
	CreatedAt        time.Time `gorm:"not null;default:current_timestamp" json:"created_at"`
	UpdatedAt        time.Time `gorm:"not null;default:current_timestamp" json:"updated_at"`

	// Relationships
	Sprint        Sprint        `gorm:"foreignKey:SprintID;constraint:OnDelete:CASCADE" json:"-"`
	LichessPuzzle LichessPuzzle `gorm:"foreignKey:LichessPuzzleID;constraint:OnDelete:RESTRICT" json:"lichess_puzzle,omitempty"`
}

// SprintPuzzleAttempt represents a user's attempt at a puzzle during a sprint
type SprintPuzzleAttempt struct {
	ID               string         `gorm:"type:varchar(36);primary_key" json:"id"`
	SprintID         string         `gorm:"type:varchar(36);not null;index:idx_sprint_user,composite:user_id;uniqueIndex:idx_sprint_user_puzzle,composite:user_id,composite:lichess_puzzle_id" json:"sprint_id"`
	UserID           string         `gorm:"type:varchar(36);not null;index:idx_sprint_user,composite:sprint_id;uniqueIndex:idx_sprint_user_puzzle,composite:sprint_id,composite:lichess_puzzle_id" json:"user_id"`
	LichessPuzzleID  string         `gorm:"type:varchar(10);not null;index;uniqueIndex:idx_sprint_user_puzzle,composite:sprint_id,composite:user_id" json:"lichess_puzzle_id"`
	SequenceInSprint int            `gorm:"not null" json:"sequence_in_sprint"`
	UserMoves        pq.StringArray `gorm:"type:text[]" json:"user_moves"`
	WasCorrect       bool           `gorm:"not null" json:"was_correct"`
	TimeTakenMs      int            `gorm:"not null" json:"time_taken_ms"`
	AttemptedAt      time.Time      `gorm:"not null;index" json:"attempted_at"`

	// Arrow-duel specific fields
	AttemptType    string         `gorm:"type:varchar(20);default:'regular';not null;index:idx_sprint_puzzle_attempts_attempt_type" json:"attempt_type"`
	CandidateMoves pq.StringArray `gorm:"type:text[]" json:"candidate_moves,omitempty"`
	ChosenMove     *string        `gorm:"type:varchar(20)" json:"chosen_move,omitempty"`

	CreatedAt time.Time `gorm:"not null;default:current_timestamp" json:"created_at"`
	UpdatedAt time.Time `gorm:"not null;default:current_timestamp" json:"updated_at"`

	// Relationships
	Sprint        Sprint        `gorm:"foreignKey:SprintID;constraint:OnDelete:CASCADE" json:"-"`
	User          User          `gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE" json:"-"`
	LichessPuzzle LichessPuzzle `gorm:"foreignKey:LichessPuzzleID;constraint:OnDelete:RESTRICT" json:"lichess_puzzle,omitempty"`
}

// TableName returns the table name for SprintPuzzle
func (SprintPuzzle) TableName() string {
	return "sprint_puzzles"
}

// TableName returns the table name for SprintPuzzleAttempt
func (SprintPuzzleAttempt) TableName() string {
	return "sprint_puzzle_attempts"
}
