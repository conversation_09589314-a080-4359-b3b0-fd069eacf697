#!/bin/bash
set -e

# Update package lists
sudo apt-get update

# Install Go 1.24.2
echo "Installing Go 1.24.2..."
cd /tmp
wget https://go.dev/dl/go1.24.2.linux-amd64.tar.gz
sudo rm -rf /usr/local/go
sudo tar -C /usr/local -xzf go1.24.2.linux-amd64.tar.gz

# Add Go to PATH in .profile
echo 'export PATH=$PATH:/usr/local/go/bin' >> $HOME/.profile
echo 'export GOPATH=$HOME/go' >> $HOME/.profile
echo 'export PATH=$PATH:$GOPATH/bin' >> $HOME/.profile

# Source the profile to make Go available immediately
export PATH=$PATH:/usr/local/go/bin
export GOPATH=$HOME/go
export PATH=$PATH:$GOPATH/bin

# Verify Go installation
go version

# Install golangci-lint
echo "Installing golangci-lint..."
curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(go env GOPATH)/bin v2.2.2

# Install make if not present
sudo apt-get install -y make

# Navigate to workspace
cd /mnt/persist/workspace

# Download Go dependencies
echo "Downloading Go dependencies..."
go mod download

# Generate GraphQL code
echo "Generating GraphQL code..."
go run github.com/99designs/gqlgen generate

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "Creating .env file..."
    cp .env.example .env
fi

# Build the application
echo "Building application..."
make build

echo "Setup completed successfully!"