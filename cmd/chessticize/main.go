package main

import (
	"flag"
	"fmt"
	"log"
	"os"

	"github.com/chessticize/chessticize-server/cmd/chessticize/migrate"
	"github.com/chessticize/chessticize-server/cmd/chessticize/server"
	"github.com/chessticize/chessticize-server/cmd/chessticize/token"
)

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: chessticize <command> [options]")
		fmt.Println("\nCommands:")
		fmt.Println("  serve        Start the server")
		fmt.Println("  migrate      Run database migrations")
		fmt.Println("  admin-token  Generate an admin JWT token")
		os.Exit(1)
	}

	switch os.Args[1] {
	case "serve":
		serverCmd := flag.NewFlagSet("serve", flag.ExitOnError)
		if err := serverCmd.Parse(os.Args[2:]); err != nil {
			log.Fatal(err)
		}
		server.Run()
	case "migrate":
		migrateCmd := flag.NewFlagSet("migrate", flag.ExitOnError)
		if err := migrateCmd.Parse(os.Args[2:]); err != nil {
			log.Fatal(err)
		}
		migrate.Run()
	case "admin-token":
		tokenCmd := flag.NewFlagSet("admin-token", flag.ExitOnError)
		userID := tokenCmd.String("user-id", "", "User ID for the token")
		email := tokenCmd.String("email", "", "Email for the token")
		expiry := tokenCmd.String("expiry", "24h", "Token expiry duration (e.g. 24h, 1h, 30m)")
		if err := tokenCmd.Parse(os.Args[2:]); err != nil {
			log.Fatal(err)
		}
		token.Run(*userID, *email, *expiry)
	default:
		fmt.Printf("Unknown command: %s\n", os.Args[1])
		os.Exit(1)
	}
}
