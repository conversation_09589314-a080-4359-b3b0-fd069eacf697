package token

import (
	"fmt"
	"os"
	"time"

	"github.com/chessticize/chessticize-server/internal/config"
	"github.com/chessticize/chessticize-server/internal/middleware"
	"github.com/golang-jwt/jwt/v5"
)

func Run(userID, email, expiry string) {
	// Validate required parameters
	if userID == "" || email == "" {
		fmt.Println("Error: user-id and email are required")
		os.Exit(1)
	}

	// Load JWT config from environment variables
	cfg, err := config.Load()
	if err != nil {
		fmt.Printf("Error loading config: %v\n", err)
		os.Exit(1)
	}

	// Parse expiry duration
	expiryDuration, err := time.ParseDuration(expiry)
	if err != nil {
		fmt.Printf("Error parsing expiry duration: %v\n", err)
		os.Exit(1)
	}

	// Create token with claims
	expirationTime := time.Now().Add(expiryDuration)
	claims := &middleware.Claims{
		UserID:  userID,
		Email:   email,
		IsAdmin: true, // Always true for admin-token command
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
		},
	}

	// Generate token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(cfg.JWT.Secret))
	if err != nil {
		fmt.Printf("Error generating token: %v\n", err)
		os.Exit(1)
	}

	// Print the token
	fmt.Println(tokenString)
}
