package migrate

import (
	"fmt"
	"io/fs"
	"log"
	"os"
	"path/filepath"
	"sort"
	"strings"

	"github.com/chessticize/chessticize-server/internal/config"
	"github.com/chessticize/chessticize-server/internal/db"
	"github.com/chessticize/chessticize-server/internal/models"
)

func Run() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		fmt.Printf("Error loading config: %v\n", err)
		os.Exit(1)
	}

	// Initialize database
	database, err := db.NewConnection(cfg.Database)
	if err != nil {
		fmt.Printf("Error initializing database: %v\n", err)
		os.Exit(1)
	}
	defer func() {
		if err := database.Close(); err != nil {
			log.Printf("Error closing database: %v", err)
		}
	}()

	// Run migrations
	if err := database.AutoMigrate(
		&models.User{},
		&models.Game{},
		&models.Puzzle{},
		&models.LichessPuzzle{},
		&models.ChessProfile{},
		&models.Task{},
		&models.PuzzleQueueEntry{},
		&models.IdempotencyRecord{},
		&models.InvitationCode{},
		&models.SessionToken{},
		&models.Event{},
		&models.UserPuzzleStats{},
		&models.UserLichessPuzzleStats{},
		&models.UserPuzzleArrowDuelStats{},
		&models.UserLichessPuzzleArrowDuelStats{},
		&models.UserDailyStats{},
		&models.UserElo{},
		&models.Sprint{},
		&models.SprintPuzzle{},
		&models.SprintPuzzleAttempt{},
		&models.EloHistory{},
		&models.UserSprintDailyStats{},
		&models.DailyQuestRequirement{},
	); err != nil {
		fmt.Printf("Error running migrations: %v\n", err)
		os.Exit(1)
	}

	// Run manual SQL migrations
	if err := runSQLMigrations(database); err != nil {
		fmt.Printf("Error running SQL migrations: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("Migrations completed successfully")
}

// runSQLMigrations executes SQL migration files from the migrations directory
func runSQLMigrations(database *db.Database) error {
	// Look for SQL files in the migrations directory
	migrationsDir := "migrations"

	// Check if migrations directory exists
	if _, err := os.Stat(migrationsDir); os.IsNotExist(err) {
		fmt.Println("No migrations directory found, skipping SQL migrations")
		return nil
	}

	// Create migration tracking table if it doesn't exist
	if err := createMigrationTable(database); err != nil {
		return fmt.Errorf("failed to create migration table: %w", err)
	}

	// Read all SQL files from the migrations directory
	sqlFiles, err := findSQLFiles(migrationsDir)
	if err != nil {
		return fmt.Errorf("failed to find SQL files: %w", err)
	}

	if len(sqlFiles) == 0 {
		fmt.Println("No SQL migration files found")
		return nil
	}

	// Sort files to ensure consistent execution order
	sort.Strings(sqlFiles)

	// Execute each SQL file if not already executed
	executedCount := 0
	for _, file := range sqlFiles {
		executed, err := isMigrationExecuted(database, file)
		if err != nil {
			return fmt.Errorf("failed to check migration status for %s: %w", file, err)
		}

		if executed {
			fmt.Printf("Skipping already executed migration: %s\n", file)
			continue
		}

		fmt.Printf("Executing SQL migration: %s\n", file)
		if err := executeSQLFile(database, file); err != nil {
			return fmt.Errorf("failed to execute %s: %w", file, err)
		}

		// Mark migration as executed
		if err := markMigrationExecuted(database, file); err != nil {
			return fmt.Errorf("failed to mark migration as executed %s: %w", file, err)
		}

		executedCount++
	}

	if executedCount > 0 {
		fmt.Printf("Successfully executed %d new SQL migration files\n", executedCount)
	} else {
		fmt.Println("All SQL migrations are up to date")
	}
	return nil
}

// findSQLFiles recursively finds all .sql files in the given directory
func findSQLFiles(dir string) ([]string, error) {
	var sqlFiles []string

	err := filepath.WalkDir(dir, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}

		if !d.IsDir() && strings.HasSuffix(strings.ToLower(path), ".sql") {
			sqlFiles = append(sqlFiles, path)
		}

		return nil
	})

	return sqlFiles, err
}

// executeSQLFile reads and executes a SQL file
func executeSQLFile(database *db.Database, filePath string) error {
	// Read the SQL file
	content, err := os.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("failed to read file: %w", err)
	}

	// Execute the SQL content
	if err := database.Exec(string(content)).Error; err != nil {
		return fmt.Errorf("failed to execute SQL: %w", err)
	}

	return nil
}

// createMigrationTable creates the migration tracking table if it doesn't exist
func createMigrationTable(database *db.Database) error {
	sql := `
		CREATE TABLE IF NOT EXISTS schema_migrations (
			id SERIAL PRIMARY KEY,
			filename VARCHAR(255) NOT NULL UNIQUE,
			executed_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
		);
	`
	return database.Exec(sql).Error
}

// isMigrationExecuted checks if a migration has already been executed
func isMigrationExecuted(database *db.Database, filename string) (bool, error) {
	var count int64
	err := database.Raw("SELECT COUNT(*) FROM schema_migrations WHERE filename = ?", filename).Scan(&count).Error
	return count > 0, err
}

// markMigrationExecuted marks a migration as executed
func markMigrationExecuted(database *db.Database, filename string) error {
	sql := "INSERT INTO schema_migrations (filename) VALUES (?) ON CONFLICT (filename) DO NOTHING"
	return database.Exec(sql, filename).Error
}
