#!/bin/bash

# Configuration
# For local testing: 
API_URL="http://localhost:8080"
# For Heroku testing:
# API_URL="https://chessticize-server-9ddca5bcf137.herokuapp.com"
USERNAME="testuser-$(uuidgen)"
PASSWORD="$(uuidgen)"

CHESS_COM_USERNAME="zsmickycat"
LICHESS_USERNAME="zsmickycat"
POLL_INTERVAL=2  # seconds between task status checks
MAX_RETRIES=30   # maximum number of times to check task status

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check that admin token is set
if [ -z "$ADMIN_TOKEN" ]; then
    echo -e "${RED}Admin token is not set${NC}"
    exit 1
fi

# Error handling
set -e
trap 'echo -e "${RED}Error: Script failed${NC}" >&2' ERR

echo -e "${BLUE}Testing Chess Puzzler Mock API${NC}"

# 0. Register
echo -e "\n${BLUE}0. Registering user...${NC}"
REGISTER_RESPONSE=$(curl -s -X POST "$API_URL/api/v1/auth/register" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $ADMIN_TOKEN" \
    -d "{\"username\": \"$USERNAME\", \"password\": \"$PASSWORD\"}")

echo -e "${GREEN}Successfully registered user${NC}"

# 1. Login
echo -e "\n${BLUE}1. Logging in as $USERNAME...${NC}"
LOGIN_RESPONSE=$(curl -s -X POST "$API_URL/api/v1/auth/login" \
    -H "Content-Type: application/json" \
    -d "{\"username\": \"$USERNAME\", \"password\": \"$PASSWORD\"}")

# Extract token using grep and cut
TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"token":"[^"]*' | cut -d'"' -f4)

if [ -z "$TOKEN" ]; then
    echo -e "${RED}Failed to get authentication token${NC}"
    exit 1
fi

echo -e "${GREEN}Successfully logged in${NC}"
echo -e "Token: $TOKEN"

# 2. Add chess accounts
echo -e "\n${BLUE}2. Adding chess platform accounts...${NC}"
ACCOUNTS_RESPONSE=$(curl -s -X PUT "$API_URL/api/users/me/chess-accounts" \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -d "{\"chess_com\": \"$CHESS_COM_USERNAME\", \"lichess\": \"$LICHESS_USERNAME\"}")

echo -e "${GREEN}Successfully added chess accounts${NC}"
echo "Response: $ACCOUNTS_RESPONSE"

# 3. Poll task status
echo -e "\n${BLUE}3. Polling task status...${NC}"
RETRY_COUNT=0
TASK_DONE=false

while [ $RETRY_COUNT -lt $MAX_RETRIES ] && [ "$TASK_DONE" = false ]; do
    TASKS_RESPONSE=$(curl -s "$API_URL/api/users/me/tasks" \
        -H "Authorization: Bearer $TOKEN")
    
    # Check if any task is in 'done' phase
    CURRENT_PHASE=$(echo $TASKS_RESPONSE | grep -o '"currentPhase":"[^"]*' | cut -d'"' -f4)
    PROGRESS=$(echo $TASKS_RESPONSE | grep -o '"progress":[^,}]*' | cut -d':' -f2)
    
    echo -e "Current phase: $CURRENT_PHASE, Progress: $PROGRESS%"
    
    if [ "$CURRENT_PHASE" = "done" ]; then
        TASK_DONE=true
        echo -e "${GREEN}Task completed!${NC}"
    else
        RETRY_COUNT=$((RETRY_COUNT + 1))
        if [ $RETRY_COUNT -lt $MAX_RETRIES ]; then
            echo "Waiting $POLL_INTERVAL seconds before next check..."
            sleep $POLL_INTERVAL
        fi
    fi
done

if [ "$TASK_DONE" = false ]; then
    echo -e "${RED}Timeout waiting for task completion${NC}"
    exit 1
fi

# 4. Fetch games
echo -e "\n${BLUE}4. Fetching analyzed games...${NC}"
GAMES_RESPONSE=$(curl -s "$API_URL/api/users/me/games" \
    -H "Authorization: Bearer $TOKEN")

# Pretty print the games response using python (if available)
if command -v python3 &> /dev/null; then
    echo "Games data:"
    echo $GAMES_RESPONSE | python3 -m json.tool
else
    echo "Games response: $GAMES_RESPONSE"
fi

echo -e "\n${GREEN}API test completed successfully!${NC}"
