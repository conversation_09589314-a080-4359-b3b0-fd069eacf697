services:
  db:
    image: postgres:15
    restart: always
    environment:
      POSTGRES_DB: chess_puzzler_dev
      POSTGRES_USER: puzzler
      POSTGRES_PASSWORD: puzzler_secret  # Change this in production
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./db-init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U puzzler -d chess_puzzler_dev"]
      interval: 5s
      timeout: 5s
      retries: 5

volumes:
  postgres_data: 