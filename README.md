# Chessticize Server

A Go-based server for the Chessticize chess puzzle platform.

## Prerequisites

- Go 1.21 or later
- PostgreSQL 14 or later
- Make (optional, for using Makefile commands)
- golangci-lint (for code linting)

## Project Structure

```
.
├── cmd/
│   └── server/          # Application entrypoints
├── internal/            # Private application code
│   ├── api/            # API routes and handlers
│   ├── config/         # Configuration management
│   ├── db/             # Database connection and migrations
│   ├── middleware/     # HTTP middleware
│   ├── models/         # Database models
│   ├── repository/     # Database operations
│   └── service/        # Business logic
└── pkg/                # Public libraries
    └── utils/          # Shared utilities
```

## Getting Started

1. Install Go:
   ```bash
   # macOS with Homebrew
   brew install go
   ```

2. Install golangci-lint:
   ```bash
   # macOS with Homebrew
   brew install golangci-lint
   ```

3. Clone the repository:
   ```bash
   git clone https://github.com/chessticize/chessticize-server.git
   cd chessticize-server
   ```

4. Install dependencies:
   ```bash
   go mod download
   ```

5. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

6. Run the server:
   ```bash
   go run cmd/server/main.go
   ```

## Development

### Makefile Commands

The project includes several useful Makefile commands for development:

- `make build`: Build the application binary
- `make clean`: Remove build artifacts
- `make test`: Run unit tests (excluding e2e tests)
- `make e2e-test`: Run end-to-end tests
- `make dev-setup`: Set up development environment (starts containers and server)
- `make dev-down`: Stop development environment
- `make run`: Run the server
- `make migrate`: Run database migrations
- `make admin-token`: Generate an admin token
- `make reset-db`: Reset the database (useful for development)
- `make lint`: Run the linter
- `make format`: Format the code
- `make verify`: Run all verifications (format, lint, tests, e2e tests)

### Continuous Integration

The project uses GitHub Actions for continuous integration. The CI workflow runs automatically on pull requests and can be manually triggered on the main branch. It will:

1. Check code formatting
2. Run the linter
3. Build the application
4. Set up the database
5. Run tests
6. Run end-to-end tests

This ensures that all changes meet the project's quality standards before being merged.

To view the CI status of a pull request, click on the "Checks" tab in the pull request page on GitHub.

If the CI fails, you can view the logs to identify the issue. Common failures include:
- Code formatting issues: Run `make format` locally
- Linting issues: Run `make lint` locally
- Build failures: Check the build logs
- Test failures: Run `make test` or `make e2e-test` locally to reproduce

### Running Tests
```bash
go test ./...
```

### Building
```bash
go build -o bin/server cmd/server/main.go
```

### API Documentation

The API documentation is available in the `docs/api-documentation.md` file in the project root. It details all endpoints, methods, paths, headers, query parameters, and request body specifications.

## Deployment

### Heroku Deployment

1. Install the Heroku CLI:
   ```bash
   # macOS with Homebrew
   brew install heroku
   ```

2. Login to Heroku:
   ```bash
   heroku login
   ```

3. Create a new Heroku app:
   ```bash
   heroku create your-app-name
   ```

4. Add the PostgreSQL addon:
   ```bash
   heroku addons:create heroku-postgresql:hobby-dev
   ```

5. Set environment variables:
   ```bash
   heroku config:set JWT_SECRET=your-secret-key
   heroku config:set JWT_EXPIRY_MINUTES=1440
   ```

6. Deploy to Heroku:
   ```bash
   git push heroku main
   ```

The application will automatically:
1. Build the binary
2. Run database migrations
3. Start the server

### Scheduled Jobs

Setup the following jobs in Heroku Scheduler:

```
# Run the reset hanging job on hourly basis
curl https://chessticize-server-9ddca5bcf137.herokuapp.com/api/v1/admin/tasks/reset-hanging\?timeout\=1h -XPOST -H "Authorization: Bearer $CHESSTICIZE_ADMIN_TOKEN"

# Run the cleanup on daily basis
curl -XPOST "https://chessticize-server-9ddca5bcf137.herokuapp.com/api/v1/admin/tasks/cleanup?cutoff=$(date -u -d '15 days ago' +%Y-%m-%dT00:00:00Z)" -H "Authorization: Bearer $CHESSTICIZE_ADMIN_TOKEN"
```

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
