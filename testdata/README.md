# Test Data

This directory contains test data files used by the test suite.

## Files

### `lichess_db_puzzle_samples.csv`

Sample data from the Lichess puzzle database containing 9 puzzles with various ratings and themes.

**Format**: CSV with the following columns:
- `PuzzleId`: Unique puzzle identifier (e.g., "00008")
- `FEN`: Chess position in FEN notation
- `Moves`: Solution moves (space-separated)
- `Rating`: Puzzle difficulty rating
- `RatingDeviation`: Rating uncertainty
- `Popularity`: Popularity score (0-100)
- `NbPlays`: Number of times the puzzle has been played
- `Themes`: Puzzle themes (space-separated, e.g., "crushing hangingPiece")
- `GameUrl`: URL to the original game on Lichess
- `OpeningTags`: Opening classification tags (space-separated)

**Usage**:
```bash
# Import puzzles into the database
go run cmd/chessticize/populate-lichess-puzzles/main.go testdata/lichess_db_puzzle_samples.csv

# Run e2e tests
RUN_E2E=true go test ./e2e -run TestLichessPuzzle -v
```

**Sample Puzzles**:
- `00008`: Rating 1798, themes: crushing, hangingPiece, long, middlegame
- `000VW`: Rating 2847, themes: crushing, endgame, long (highest rated)
- `0009B`: Rating 1080, themes: advantage, middlegame, short (lowest rated)

This data is used for:
- Testing the lichess puzzle import functionality
- Validating CSV parsing and data transformation
- End-to-end testing of the populate script
- Verifying database schema and constraints
