---
description: 
globs: 
alwaysApply: true
---
# Chessticize Code Guidelines

## General Rules

- Follow Google's Go coding guidelines for all Go code.
- Always use fake implementations instead of mocks when available for testing, unless explicitly allowed in a README.md file.
- Respect conventions and guidelines outlined in any README.md files within project folders.
- In tests, use simple structure. Avoid new inventions like TestSuites.
- Try to fit local convention. When adding a component, find the proper folder to add it. And read through the other files in the same folder before start writing the new feature.
- Walk through the folder structure and find related files and read them before taking actions.
- If it's a new component, read through the code of other similar components and follow the convention.

## Repository Pattern
- All database repositories should be implemented in `internal/repository` folder.
- See [README.md](mdc:internal/repository/README.md) for latest information.

## API Pattern
- All APIs should be implemented in `internal/api` folder.
- See [README.md](mdc:internal/api/README.md) for latest information.

## Testing Guidelines
- In API testing and other testing, mocks are discouraged.
- When testing a component, if its dependency has a fake then the fake should be used. Otherwise use the real implementation with its ingestions faked. 
- If the current compoenent under development is complicated to initialize due to a lot of dependencies, consider provide a fake that exposes an initialized fake. For example, if API provides a fake, then everything inside it is real, except the DB is initialized as a fake, too.
- A maintained fake with a different implementation is encouraged if the component is taking external dependencies. The fake should pass the same set of testing as the dev code.

## Model Patterns
- All models are under `internal/models` folder.
- All models has a primary key with string type. The value is always a UUID. The database column type is varchar(36).


