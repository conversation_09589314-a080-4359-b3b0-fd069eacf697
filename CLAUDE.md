# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Chessticize Server is a Go-based chess puzzle platform that provides a REST API and GraphQL interface for managing chess games, puzzles, user statistics, and competitive features like sprint modes and arrow duels. The server integrates with chess platforms like Chess.com and Lichess.

## Essential Commands

### Development Environment
```bash
# Start development environment (PostgreSQL + containers)
make up

# Build the application
make build

# Run the server (after building)
make run

# Stop development environment
make down

# Reset database (useful for fresh start)
make reset-db
```

### Database Operations
```bash
# Run database migrations
make migrate

# Generate admin token (10-year expiry)
make admin-token
```

### Testing and Quality
```bash
# Run unit tests (excludes e2e tests)
make test

# Run short tests (excludes database tests)
make short-test

# Run end-to-end tests (requires RUN_E2E=true)
make e2e-test

# Format code
make format

# Run linter
make lint

# Run all verifications (format, lint, build, reset-db, test, e2e-test)
make verify
```

### GraphQL Code Generation
```bash
# Generate GraphQL code (automatically run during build)
go run github.com/99designs/gqlgen generate
```

## Architecture Overview

The codebase follows a clean architecture pattern with these key layers:

```
cmd/           - Application entry points and CLI commands
├── chessticize/
│   ├── main.go      - Main CLI with serve/migrate/admin-token commands
│   ├── server/      - HTTP server setup
│   ├── migrate/     - Database migration runner
│   └── token/       - Admin token generator

internal/      - Private application code
├── api/             - REST API handlers and routes
├── graphql/         - GraphQL schema, resolvers, and generated code
├── repository/      - Database abstraction layer
│   ├── interfaces.go    - Repository interface definitions (IUserRepository, etc.)
│   ├── fake/           - In-memory test implementations
│   ├── testing/        - Shared test suites for all repository implementations
│   └── common/         - Shared utilities for filtering and statistics
├── service/         - Business logic layer
├── models/          - Data models and database entities
├── middleware/      - HTTP middleware (auth, logging, idempotency)
├── config/          - Configuration management
└── db/              - Database connection setup
```

## Key Design Patterns

### Repository Pattern
- All database operations go through repository interfaces defined in `internal/repository/interfaces.go`
- Interface names follow pattern: `I<Entity>Repository` (e.g., `IUserRepository`, `IGameRepository`)
- Real implementations in `internal/repository/` use PostgreSQL via GORM
- Fake implementations in `internal/repository/fake/` use in-memory SQLite for testing
- Both implementations must pass identical test suites in `internal/repository/testing/`

### API Layer Structure
- REST endpoints in `internal/api/` follow resource-based patterns
- Each resource has dedicated handler files: `{resource}_handlers.go` and `{resource}_handlers_test.go`
- Routes organized by access level:
  - Public routes (health, auth)
  - User routes (authenticated users)
  - Admin routes (admin-only with JWT middleware)

### GraphQL Integration
- Schema defined in `internal/graphql/schema.graphql`
- Generated code in `internal/graphql/generated/`
- Resolvers in `internal/graphql/resolvers/` follow schema structure
- Custom models in `internal/graphql/model/`

### Authentication & Authorization
- JWT-based authentication with role-based access control
- Public, user, and admin endpoint categories
- All authenticated endpoints must be registered in `auth_test.go` for automatic security testing

## Testing Guidelines

### Repository Testing
- Use fake repository implementations instead of mocks
- Both real and fake repositories must pass shared test suites in `internal/repository/testing/`
- Test structure pattern:
  ```go
  func TestEntityRepository(t *testing.T) { /* fake repo tests */ }
  func TestRealEntityRepository(t *testing.T) { /* real repo tests */ }
  func RunEntityRepositoryTests(t *testing.T, provider TestDBProvider) { /* shared test cases */ }
  ```

### API Testing
- Use fake repositories with real API handlers for integration tests
- Test utilities in `internal/api/testutils/`
- Authentication tests automatically cover all registered endpoints
- Follow simple test structure, avoid test suites

### General Testing Principles
- All models use string UUIDs as primary keys (varchar(36) in database)
- Prefer fake implementations over mocks when available
- Read component READMEs before implementing tests
- Use consistent patterns within each folder

## GraphQL Development

### Schema Changes
1. Modify `internal/graphql/schema.graphql`
2. Run `go run github.com/99designs/gqlgen generate` (or `make build`)
3. Implement required resolvers in `internal/graphql/resolvers/`
4. Update model mappings in `gqlgen.yml` if needed

### Model Binding
- GraphQL types map to internal models via `gqlgen.yml` configuration
- Custom scalars: UUID, Time, Int64, Color, ChessPlatform, etc.
- Automatic binding for models in `internal/models` and `internal/graphql/model`

## Database Schema

### Migrations
- GORM auto migration is preferred
- SQL migrations in `migrations/` directory is used when GORM migration is not possible
- SQL migrations should be rerunable. For example, use `CREATE OR REPLACE` and `IF NOT EXISTS` to make statements rerunnable.
- Run via `make migrate` or `./build/chessticize migrate`
- Initialization script in `db-init/01-init.sql`

### Key Entities
- **Users**: Authentication and user management
- **Games**: Chess games from Chess.com/Lichess
- **Puzzles**: Generated from game positions with tactical themes
- **Sprints**: Competitive puzzle-solving sessions
- **Arrow Duels**: Head-to-head puzzle competitions
- **Stats**: User performance analytics and ELO ratings

## Development Workflow

1. **Setup**: Run `make up` to start PostgreSQL and development environment
2. **Database**: Run `make migrate` to apply schema changes
3. **Build**: Run `make build` to compile binary and generate GraphQL code
4. **Test**: Run `make test` for unit tests, `make e2e-test` for integration tests
5. **Quality**: Run `make verify` before committing (includes format, lint, tests)
6. **Admin Tasks**: Use `make admin-token` to generate authentication tokens

## Code Style Requirements

- Follow Google's Go coding guidelines
- Use golangci-lint for code quality enforcement
- Format code with `go fmt` (automated in `make format`)
- All string primary keys must be UUIDs
- Repository interfaces must start with `I` prefix
- Maintain consistency within each package/folder
- Read existing code patterns before adding new components