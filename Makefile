.PHONY: build clean short-test test e2e-test up down reset-db start run stop migrate admin-token lint verify

# Include environment variables from .env file
include .env
export

# Build the binary
build:
	mkdir -p build
	go run github.com/99designs/gqlgen generate
	go build -o build/chessticize cmd/chessticize/main.go

# Clean build artifacts
clean:
	rm -rf build

# Run short tests (excluding e2e tests and real database tests)
short-test:
	go test ./... -short

# Run tests (excluding e2e tests)
test:
	go test $(shell go list ./... | grep -v '/e2e')

# Run e2e tests (requires RUN_E2E=true)
e2e-test: stop build start
	RUN_E2E=true go test ./e2e -v

# Start the development environment
up:
	@echo "Setting up development environment..."
	docker compose up -d
	@sleep 3 # Wait for PostgreSQL to be ready

# Stop the development environment
down:
	@echo "Stopping development environment..."
	# Stop containers
	docker compose down

# Reset the database
reset-db: build
	# Delete the volumes to clear the database
	docker compose down -v
	make up migrate

# Start the server asynchronously
start: stop build
	# Start server in background
	./build/chessticize serve &
	@sleep 3 # Wait for server to start

# Run the server synchronously
run: stop build
	./build/chessticize serve

# Stop the server
stop:
	# Stop server
	pgrep -f "^./build/chessticize serve" | xargs -r kill || true

# Run migrations
migrate: build
	./build/chessticize migrate

# Generate admin token
admin-token: build
	# Generate admin token with 10-year expiry
	./build/chessticize admin-token -user-id="admin" -email="<EMAIL>" -expiry="87600h"

# Run linter
lint:
	golangci-lint run

# Run formatter
format:
	go fmt ./...

# Run all verifications
verify: format build lint reset-db test e2e-test stop
