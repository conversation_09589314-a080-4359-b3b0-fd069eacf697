# Arrow-Duel Sprint Mistakes Implementation Plan

## Overview

This plan outlines the steps needed to fix the arrow-duel sprint mistakes API so that candidate moves and chosen moves are properly stored and returned for client-side mistake review.

## Implementation Steps

### Step 1: Database Migration
**File**: `migrations/004-add-arrow-duel-fields-to-sprint-attempts.sql`

```sql
-- Add arrow-duel fields to sprint_puzzle_attempts table
ALTER TABLE sprint_puzzle_attempts 
ADD COLUMN attempt_type VARCHAR(20) DEFAULT 'regular' NOT NULL,
ADD COLUMN candidate_moves TEXT[],
ADD COLUMN chosen_move VARCHAR(20);

-- Add index for filtering by attempt type
CREATE INDEX idx_sprint_puzzle_attempts_attempt_type ON sprint_puzzle_attempts(attempt_type);

-- Add comments for documentation
COMMENT ON COLUMN sprint_puzzle_attempts.attempt_type IS 'Type of attempt: regular or arrow_duel';
COMMENT ON COLUMN sprint_puzzle_attempts.candidate_moves IS 'Array of candidate moves [blunder_move, correct_move] for arrow-duel attempts';
COMMENT ON COLUMN sprint_puzzle_attempts.chosen_move IS 'Move chosen by player for arrow-duel attempts';
```

### Step 2: Update SprintPuzzleAttempt Model
**File**: `internal/models/sprint_puzzle.go`

**Changes**:
```go
type SprintPuzzleAttempt struct {
    ID               string         `gorm:"type:varchar(36);primary_key" json:"id"`
    SprintID         string         `gorm:"type:varchar(36);not null;index:idx_sprint_user,composite:user_id;uniqueIndex:idx_sprint_user_puzzle,composite:user_id,composite:lichess_puzzle_id" json:"sprint_id"`
    UserID           string         `gorm:"type:varchar(36);not null;index:idx_sprint_user,composite:sprint_id;uniqueIndex:idx_sprint_user_puzzle,composite:sprint_id,composite:lichess_puzzle_id" json:"user_id"`
    LichessPuzzleID  string         `gorm:"type:varchar(10);not null;index;uniqueIndex:idx_sprint_user_puzzle,composite:sprint_id,composite:user_id" json:"lichess_puzzle_id"`
    SequenceInSprint int            `gorm:"not null" json:"sequence_in_sprint"`
    UserMoves        pq.StringArray `gorm:"type:text[]" json:"user_moves"`
    WasCorrect       bool           `gorm:"not null" json:"was_correct"`
    TimeTakenMs      int            `gorm:"not null" json:"time_taken_ms"`
    AttemptedAt      time.Time      `gorm:"not null;index" json:"attempted_at"`
    
    // NEW: Arrow-duel specific fields
    AttemptType      string         `gorm:"type:varchar(20);default:'regular';not null" json:"attempt_type"`
    CandidateMoves   pq.StringArray `gorm:"type:text[]" json:"candidate_moves,omitempty"`
    ChosenMove       *string        `gorm:"type:varchar(20)" json:"chosen_move,omitempty"`
    
    CreatedAt        time.Time      `gorm:"not null;default:current_timestamp" json:"created_at"`
    UpdatedAt        time.Time      `gorm:"not null;default:current_timestamp" json:"updated_at"`

    // Relationships
    Sprint        Sprint        `gorm:"foreignKey:SprintID;constraint:OnDelete:CASCADE" json:"-"`
    User          User          `gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE" json:"-"`
    LichessPuzzle LichessPuzzle `gorm:"foreignKey:LichessPuzzleID;constraint:OnDelete:RESTRICT" json:"lichess_puzzle,omitempty"`
}
```

### Step 3: Update Repository Layer

#### 3a. Update SprintPuzzleWithAttempt struct
**File**: `internal/repository/interfaces.go`

```go
// SprintPuzzleWithAttempt represents a sprint puzzle with attempt information
type SprintPuzzleWithAttempt struct {
    SprintPuzzle  models.SprintPuzzle
    LichessPuzzle models.LichessPuzzle
    AttemptStatus SprintPuzzleAttemptStatus
    UserMoves     []string
    WasCorrect    *bool
    TimeTakenMs   *int
    AttemptedAt   *time.Time
    
    // NEW: Arrow-duel specific fields
    AttemptType    *string
    CandidateMoves []string
    ChosenMove     *string
}
```

#### 3b. Update Repository Query
**File**: `internal/repository/sprint_puzzle_repository.go`

**Update the SELECT clause** in `GetSprintPuzzlesWithAttempts`:
```go
Select(`
    sp.id as sprint_puzzle_id,
    sp.sprint_id,
    sp.lichess_puzzle_id,
    sp.sequence_in_sprint,
    sp.created_at as sprint_puzzle_created_at,
    sp.updated_at as sprint_puzzle_updated_at,
    lp.id as lichess_puzzle_id,
    lp.fen,
    lp.moves,
    lp.rating,
    lp.themes,
    lp.popularity,
    lp.nb_plays,
    lp.created_at as lichess_puzzle_created_at,
    lp.updated_at as lichess_puzzle_updated_at,
    spa.user_moves,
    spa.was_correct,
    spa.time_taken_ms,
    spa.attempted_at,
    spa.attempt_type,        -- NEW
    spa.candidate_moves,     -- NEW
    spa.chosen_move          -- NEW
`)
```

**Update the result mapping**:
```go
// In the result scanning loop, add:
var attemptType *string
var candidateMoves *string
var chosenMove *string

// Add to the Scan call:
&attemptType,
&candidateMoves, 
&chosenMove,

// In the SprintPuzzleWithAttempt creation:
sprintPuzzles[i] = SprintPuzzleWithAttempt{
    // ... existing fields ...
    AttemptType:    attemptType,
    CandidateMoves: parseCandidateMoves(candidateMoves),
    ChosenMove:     chosenMove,
}

// Helper function to parse candidate moves from database array
func parseCandidateMoves(candidateMovesStr *string) []string {
    if candidateMovesStr == nil {
        return nil
    }
    // Parse PostgreSQL array format: {move1,move2}
    str := *candidateMovesStr
    if str == "{}" || str == "" {
        return []string{}
    }
    // Remove braces and split
    str = strings.Trim(str, "{}")
    return strings.Split(str, ",")
}
```

#### 3c. Update Fake Repository
**File**: `internal/repository/fake/sprint_puzzle_repository.go`

Add the new fields to the fake implementation's result mapping.

### Step 4: Update Sprint Puzzle Attempt Creation

#### 4a. Update Service Layer
**File**: `internal/service/sprint_service.go`

In the `SubmitPuzzleResults` method, ensure the arrow-duel fields are properly mapped when creating `SprintPuzzleAttempt` records:

```go
// When creating SprintPuzzleAttempt from PuzzleAttemptResult
attempt := &models.SprintPuzzleAttempt{
    // ... existing field mappings ...
    AttemptType:    result.AttemptType,
    CandidateMoves: result.CandidateMoves,
    ChosenMove:     result.ChosenMove,
}
```

#### 4b. Update Fake Sprint Puzzle Attempt Repository
**File**: `internal/repository/fake/sprint_puzzle_attempt_repository.go`

Add the new fields to the fake model:
```go
type fakeSprintPuzzleAttempt struct {
    // ... existing fields ...
    AttemptType      string
    CandidateMoves   string // Comma-separated for fake DB
    ChosenMove       *string
}
```

### Step 5: Update API Response

#### 5a. Update SprintPuzzleResponse
**File**: `internal/api/sprint_handler.go`

```go
type SprintPuzzleResponse struct {
    PuzzleID         string   `json:"puzzle_id"`
    SequenceInSprint int      `json:"sequence_in_sprint"`
    FEN              string   `json:"fen"`
    SolutionMoves    []string `json:"solution_moves"`
    Rating           int      `json:"rating"`
    Themes           []string `json:"themes"`
    AttemptStatus    string   `json:"attempt_status"`
    UserMoves        []string `json:"user_moves,omitempty"`
    WasCorrect       *bool    `json:"was_correct,omitempty"`
    TimeTakenMs      *int     `json:"time_taken_ms,omitempty"`
    AttemptedAt      *string  `json:"attempted_at,omitempty"`
    
    // NEW: Arrow-duel specific fields
    AttemptType      *string  `json:"attempt_type,omitempty"`
    CandidateMoves   []string `json:"candidate_moves,omitempty"`
    ChosenMove       *string  `json:"chosen_move,omitempty"`
}
```

#### 5b. Update Response Mapping
**File**: `internal/api/sprint_handler.go`

In `GetSprintPuzzles` method:
```go
puzzleResponses[i] = SprintPuzzleResponse{
    // ... existing field mappings ...
    AttemptType:      sp.AttemptType,
    CandidateMoves:   sp.CandidateMoves,
    ChosenMove:       sp.ChosenMove,
}
```

### Step 6: Add Optional API Filtering

#### 6a. Update Filter Structure
**File**: `internal/repository/interfaces.go`

```go
type SprintPuzzleFilter struct {
    Status      *SprintPuzzleAttemptStatus
    SequenceMin *int
    SequenceMax *int
    AttemptType *string  // NEW: Filter by attempt type
}
```

#### 6b. Update API Handler
**File**: `internal/api/sprint_handler.go`

Add attempt type filtering in `GetSprintPuzzles`:
```go
// Attempt type filter
if attemptTypeStr := r.URL.Query().Get("attempt_type"); attemptTypeStr != "" {
    if attemptTypeStr == "regular" || attemptTypeStr == "arrow_duel" {
        filter.AttemptType = &attemptTypeStr
    } else {
        apiError(w, r, http.StatusBadRequest, nil, "Invalid attempt_type filter. Valid values: regular, arrow_duel")
        return
    }
}
```

#### 6c. Update Repository Query
Add filtering logic to the repository query:
```go
if filter.AttemptType != nil {
    query = query.Where("spa.attempt_type = ?", *filter.AttemptType)
}
```

### Step 7: Testing Updates

#### 7a. Update API Tests
**File**: `e2e/sprint_test.go`

Add tests for arrow-duel sprint puzzle retrieval to verify the new fields are returned correctly.

#### 7b. Update Repository Tests
**File**: `internal/repository/testing/sprint_puzzle_repository_test.go`

Add tests to verify the new fields are properly stored and retrieved.

## Implementation Order

1. **Database Migration** (Required first)
2. **Model Updates** (Foundation)
3. **Repository Layer** (Storage/Retrieval)
4. **Service Layer** (Data mapping)
5. **API Response** (Client interface)
6. **Testing** (Validation)
7. **Optional Filtering** (Enhancement)

## Validation Steps

After implementation:

1. **Submit arrow-duel results** - Verify new fields are stored
2. **Retrieve sprint puzzles** - Verify new fields are returned
3. **Test filtering** - Verify attempt_type filtering works
4. **Backward compatibility** - Verify regular attempts still work
5. **Client integration** - Verify client can use the data for review

## Expected API Response After Fix

```json
{
  "puzzles": [
    {
      "puzzle_id": "00008",
      "sequence_in_sprint": 1,
      "fen": "rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 1",
      "solution_moves": ["e7e5", "Nf3"],
      "rating": 1200,
      "themes": ["opening", "centerControl"],
      "attempt_status": "solved",
      "user_moves": ["Qh5"],
      "was_correct": true,
      "time_taken_ms": 12000,
      "attempted_at": "2023-10-01T12:00:00Z",
      "attempt_type": "arrow_duel",
      "candidate_moves": ["Qh4", "Qh5"],
      "chosen_move": "Qh5"
    }
  ],
  "total_count": 1,
  "offset": 0,
  "limit": 50
}
```

This complete response will enable the client to provide comprehensive arrow-duel mistake reviews.