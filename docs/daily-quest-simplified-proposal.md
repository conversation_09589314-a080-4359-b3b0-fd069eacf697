# Simplified Daily Quest System API Proposal

## Overview

This simplified approach extends the existing `UserDailyStats` model to include sprint and arrow-duel tracking, then adds quest completion tracking. The client can check quest requirements against the daily stats to determine completion status.

## Key Benefits

1. **Leverages Existing Infrastructure**: Uses current daily stats and event system
2. **Automatic Tracking**: Sprint and arrow-duel completion tracked via existing events
3. **Client-Side Flexibility**: Clients can check requirements and show progress
4. **Backward Compatible**: Maintains existing stats while adding new fields
5. **Simple Implementation**: No complex quest progress models needed

## Data Model Changes

### Extended UserDailyStats Model

```go
type UserDailyStats struct {
    ID                  string    `gorm:"type:varchar(36);primary_key" json:"id"`
    UserID              string    `gorm:"type:varchar(36);not null;index:idx_user_date,unique" json:"user_id"`
    Date                time.Time `gorm:"type:date;not null;index:idx_user_date,unique" json:"date"`
    
    // Existing puzzle stats
    PuzzleSuccess       int       `gorm:"default:0" json:"puzzle_success"`
    PuzzleTotal         int       `gorm:"default:0" json:"puzzle_total"`
    Streak              int       `gorm:"default:0" json:"streak"`                // Legacy streak
    PuzzleTotalDuration int       `gorm:"default:0" json:"puzzle_total_duration"`
    
    // NEW: Sprint statistics
    SprintSuccess       int       `gorm:"default:0" json:"sprint_success"`         // Successful sprints
    SprintTotal         int       `gorm:"default:0" json:"sprint_total"`           // Total sprint attempts
    SprintTotalDuration int       `gorm:"default:0" json:"sprint_total_duration"`  // Total sprint time
    
    // NEW: Arrow duel statistics  
    ArrowDuelSuccess    int       `gorm:"default:0" json:"arrow_duel_success"`     // Successful arrow duels
    ArrowDuelTotal      int       `gorm:"default:0" json:"arrow_duel_total"`       // Total arrow duel attempts
    ArrowDuelTotalDuration int    `gorm:"default:0" json:"arrow_duel_total_duration"` // Total arrow duel time
    
    // NEW: Daily quest tracking
    QuestCompleted      bool      `gorm:"default:false" json:"quest_completed"`   // All daily quests completed
    QuestStreak         int       `gorm:"default:0" json:"quest_streak"`          // Consecutive days with all quests completed
    
    CreatedAt           time.Time `gorm:"not null;default:current_timestamp" json:"created_at"`
    UpdatedAt           time.Time `gorm:"not null;default:current_timestamp" json:"updated_at"`
}
```

### Quest Definition Model

```go
type DailyQuestRequirement struct {
    ID          string    `gorm:"type:varchar(36);primary_key" json:"id"`
    Type        string    `gorm:"type:varchar(50);not null;unique" json:"type"`        // "sprint", "arrow_duel"
    Name        string    `gorm:"type:varchar(100);not null" json:"name"`
    Description string    `gorm:"type:text" json:"description"`
    Target      int       `gorm:"default:1" json:"target"`                            // Required count (e.g., 1 sprint)
    IsActive    bool      `gorm:"default:true" json:"isActive"`
    CreatedAt   time.Time `gorm:"not null;default:current_timestamp" json:"createdAt"`
    UpdatedAt   time.Time `gorm:"not null;default:current_timestamp" json:"updatedAt"`
}
```

## GraphQL Schema Extensions

### Types
```graphql
type DailyQuestRequirement {
  id: ID!
  type: String!
  name: String!
  description: String!
  target: Int!
  isActive: Boolean!
  createdAt: Time!
  updatedAt: Time!
}

# Extended existing UserDailyStats type
type UserDailyStats {
  id: ID!
  date: Time!
  
  # Existing puzzle stats
  puzzleSuccess: Int!
  puzzleTotal: Int!
  streak: Int!                    # Legacy streak
  puzzleTotalDuration: Int!
  
  # NEW: Sprint stats
  sprintSuccess: Int!
  sprintTotal: Int!
  sprintTotalDuration: Int!
  
  # NEW: Arrow duel stats
  arrowDuelSuccess: Int!
  arrowDuelTotal: Int!
  arrowDuelTotalDuration: Int!
  
  # NEW: Quest tracking
  questCompleted: Boolean!
  questStreak: Int!
  
  createdAt: Time!
  updatedAt: Time!
}
```

### Queries
```graphql
extend type Query {
  # Get daily quest requirements (global for all users)
  dailyQuestRequirements: [DailyQuestRequirement!]!
  
  # Existing queries are extended to include new fields
  # myDailyStats: [UserDailyStats!]! (already exists, now includes quest fields)
}
```

## Event Integration & Database Triggers

### Updated Database Triggers

The existing triggers need to be extended to update the new sprint and arrow-duel fields:

```sql
-- Updated trigger function to handle sprint and arrow duel stats
CREATE OR REPLACE FUNCTION trigger_update_daily_stats()
RETURNS TRIGGER AS $$
BEGIN
    -- Handle puzzle events (existing logic)
    IF NEW.event_type = 'puzzle' AND NEW.event_sub_type = 'end' THEN
        -- Check if this is an arrow duel
        IF NEW.event_data::jsonb ? 'attemptType' AND 
           NEW.event_data::jsonb->>'attemptType' = 'arrow_duel' THEN
            CALL update_arrow_duel_daily_stats(NEW.user_id, NEW.created_at::date, NEW.event_data::jsonb);
        ELSE
            -- Regular puzzle logic (existing)
            CALL update_puzzle_daily_stats(NEW.user_id, NEW.created_at::date, NEW.event_data::jsonb);
        END IF;
    END IF;
    
    -- Handle sprint events (NEW)
    IF NEW.event_type = 'puzzle_sprint' AND NEW.event_sub_type = 'end' THEN
        CALL update_sprint_daily_stats(NEW.user_id, NEW.created_at::date, NEW.event_data::jsonb);
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- New procedure to update sprint daily stats
CREATE OR REPLACE PROCEDURE update_sprint_daily_stats(
    p_user_id VARCHAR(36),
    p_date DATE,
    p_event_data JSONB
)
LANGUAGE plpgsql AS $$
DECLARE
    v_status TEXT;
    v_duration INT;
    v_success_increment INT := 0;
BEGIN
    -- Extract sprint data
    v_status := p_event_data->>'status';
    v_duration := COALESCE((p_event_data->>'duration')::INT, 0);
    
    -- Determine if sprint was successful
    IF v_status = 'completed_success' THEN
        v_success_increment := 1;
    END IF;
    
    -- Insert or update daily stats
    INSERT INTO user_daily_stats (
        id, user_id, date, sprint_success, sprint_total, sprint_total_duration,
        created_at, updated_at
    ) VALUES (
        gen_random_uuid()::text, p_user_id, p_date, v_success_increment, 1, v_duration,
        NOW(), NOW()
    )
    ON CONFLICT (user_id, date) DO UPDATE SET
        sprint_success = user_daily_stats.sprint_success + v_success_increment,
        sprint_total = user_daily_stats.sprint_total + 1,
        sprint_total_duration = user_daily_stats.sprint_total_duration + v_duration,
        updated_at = NOW();
    
    -- Update quest completion status
    CALL update_quest_completion(p_user_id, p_date);
END;
$$;

-- New procedure to update arrow duel daily stats
CREATE OR REPLACE PROCEDURE update_arrow_duel_daily_stats(
    p_user_id VARCHAR(36),
    p_date DATE,
    p_event_data JSONB
)
LANGUAGE plpgsql AS $$
DECLARE
    v_was_correct BOOLEAN;
    v_time_taken INT;
    v_success_increment INT := 0;
BEGIN
    -- Extract arrow duel data
    v_was_correct := COALESCE((p_event_data->>'was_correct')::BOOLEAN, false);
    v_time_taken := COALESCE((p_event_data->>'time_taken_ms')::INT, 0);
    
    -- Determine if arrow duel was successful
    IF v_was_correct THEN
        v_success_increment := 1;
    END IF;
    
    -- Insert or update daily stats
    INSERT INTO user_daily_stats (
        id, user_id, date, arrow_duel_success, arrow_duel_total, arrow_duel_total_duration,
        created_at, updated_at
    ) VALUES (
        gen_random_uuid()::text, p_user_id, p_date, v_success_increment, 1, v_time_taken,
        NOW(), NOW()
    )
    ON CONFLICT (user_id, date) DO UPDATE SET
        arrow_duel_success = user_daily_stats.arrow_duel_success + v_success_increment,
        arrow_duel_total = user_daily_stats.arrow_duel_total + 1,
        arrow_duel_total_duration = user_daily_stats.arrow_duel_total_duration + v_time_taken,
        updated_at = NOW();
    
    -- Update quest completion status
    CALL update_quest_completion(p_user_id, p_date);
END;
$$;

-- New procedure to check and update quest completion
CREATE OR REPLACE PROCEDURE update_quest_completion(
    p_user_id VARCHAR(36),
    p_date DATE
)
LANGUAGE plpgsql AS $$
DECLARE
    v_stats_record RECORD;
    v_quest_completed BOOLEAN := true;
    v_requirement RECORD;
    v_previous_streak INT := 0;
    v_new_streak INT;
BEGIN
    -- Get current daily stats
    SELECT * INTO v_stats_record
    FROM user_daily_stats
    WHERE user_id = p_user_id AND date = p_date;
    
    IF v_stats_record IS NULL THEN
        RETURN; -- No stats record exists yet
    END IF;
    
    -- Check each active quest requirement
    FOR v_requirement IN 
        SELECT type, target FROM daily_quest_requirements WHERE is_active = true
    LOOP
        CASE v_requirement.type
            WHEN 'sprint' THEN
                IF COALESCE(v_stats_record.sprint_total, 0) < v_requirement.target THEN
                    v_quest_completed := false;
                    EXIT; -- No need to check further
                END IF;
            WHEN 'arrow_duel' THEN
                IF COALESCE(v_stats_record.arrow_duel_total, 0) < v_requirement.target THEN
                    v_quest_completed := false;
                    EXIT;
                END IF;
        END CASE;
    END LOOP;
    
    -- Calculate quest streak
    IF v_quest_completed THEN
        -- Get previous day's quest streak
        SELECT COALESCE(quest_streak, 0) INTO v_previous_streak
        FROM user_daily_stats
        WHERE user_id = p_user_id 
          AND date = p_date - INTERVAL '1 day'
          AND quest_completed = true;
        
        v_new_streak := COALESCE(v_previous_streak, 0) + 1;
    ELSE
        v_new_streak := 0;
    END IF;
    
    -- Update quest completion status
    UPDATE user_daily_stats 
    SET 
        quest_completed = v_quest_completed,
        quest_streak = v_new_streak,
        updated_at = NOW()
    WHERE user_id = p_user_id AND date = p_date;
END;
$$;
```

## REST API for Quest Requirements (Admin Only)

### Quest Requirement CRUD Endpoints
```
GET    /api/v1/admin/quest-requirements        # List all quest requirements
GET    /api/v1/admin/quest-requirements/:id    # Get specific requirement
POST   /api/v1/admin/quest-requirements        # Create new requirement
PUT    /api/v1/admin/quest-requirements/:id    # Update requirement
DELETE /api/v1/admin/quest-requirements/:id    # Delete requirement
```

### Quest Requirements in User Data
```
GET    /api/v1/users/me                        # Now includes quest_requirements in response
```

### Request/Response Examples

#### Create Quest Requirement
```http
POST /api/admin/quest-requirements
Authorization: Bearer <admin-token>
Content-Type: application/json

{
  "type": "sprint",
  "name": "Daily Sprint",
  "description": "Complete at least one puzzle sprint",
  "target": 1,
  "isActive": true
}
```

#### Update Quest Requirement
```http
PUT /api/admin/quest-requirements/sprint-quest-id
Authorization: Bearer <admin-token>
Content-Type: application/json

{
  "target": 2,
  "description": "Complete at least two puzzle sprints"
}
```

## API Usage Examples

### 1. Get Today's Quest Status
**Client combines two simple queries:**

```graphql
# Get today's daily stats
query TodayStats {
  myDailyStats(
    startDate: "2024-07-14T00:00:00Z"
    endDate: "2024-07-14T23:59:59Z"
  ) {
    date
    sprintSuccess
    sprintTotal
    arrowDuelSuccess
    arrowDuelTotal
    questCompleted
    questStreak
  }
}

# Get quest requirements
query QuestRequirements {
  dailyQuestRequirements {
    id
    type
    name
    description
    target
    isActive
  }
}
```

**Client-side logic:**
```typescript
function checkQuestCompletion(stats: UserDailyStats, requirements: DailyQuestRequirement[]) {
  return requirements.map(req => ({
    requirement: req,
    currentCount: stats[`${req.type}Total`], // sprintTotal, arrowDuelTotal
    completed: stats[`${req.type}Total`] >= req.target,
    progress: Math.min(stats[`${req.type}Total`] / req.target, 1.0)
  }));
}
```

### 2. Get Quest History
```graphql
query QuestHistory {
  myDailyStats(
    startDate: "2024-07-01T00:00:00Z"
    endDate: "2024-07-14T23:59:59Z"
  ) {
    date
    questCompleted
    questStreak
    sprintTotal
    arrowDuelTotal
  }
}
```

## Repository Interface Extensions

### IUserStatsRepository Updates
```go
type IUserStatsRepository interface {
    // Existing methods remain unchanged - they now return UserDailyStats with new fields
    GetUserDailyStats(ctx context.Context, userID string, date time.Time) (*UserDailyStats, error)
    GetUserDailyStatsRange(ctx context.Context, userID string, startDate, endDate time.Time) ([]*UserDailyStats, error)
    
    // No new methods needed - existing methods now include quest fields
}
```

### NEW: IQuestRequirementRepository
```go
type IQuestRequirementRepository interface {
    CreateQuestRequirement(ctx context.Context, req *DailyQuestRequirement) error
    GetQuestRequirementByID(ctx context.Context, id string) (*DailyQuestRequirement, error)
    GetActiveQuestRequirements(ctx context.Context) ([]*DailyQuestRequirement, error)
    UpdateQuestRequirement(ctx context.Context, req *DailyQuestRequirement) error
    DeleteQuestRequirement(ctx context.Context, id string) error
    ListQuestRequirements(ctx context.Context) ([]*DailyQuestRequirement, error)
}
```

## Data Population Strategy

### SQL Migration for Initial Requirements
The initial quest requirements are populated via SQL migration `004-daily-quest-requirements.sql`:

```sql
INSERT INTO daily_quest_requirements (id, type, name, description, target, is_active)
VALUES 
    ('quest-sprint-daily', 'sprint', 'Daily Sprint', 'Complete at least one puzzle sprint', 1, true),
    ('quest-arrow-duel-daily', 'arrow_duel', 'Daily Arrow Duel', 'Participate in at least one arrow duel', 1, true)
ON CONFLICT (type) DO NOTHING;
```

This migration:
- Runs after GORM AutoMigration creates the table
- Is idempotent (can be run multiple times safely)
- Populates global quest requirements for all users

## Implementation Benefits

1. **Minimal Code Changes**: Extends existing infrastructure rather than creating new systems
2. **Automatic Tracking**: Leverages existing event triggers for real-time updates
3. **Client Flexibility**: Clients can implement custom quest checking logic
4. **Performance**: Single query gets all needed data from UserDailyStats
5. **Backward Compatible**: Existing stats queries continue to work
6. **Extensible**: Easy to add new quest types by adding requirement definitions

## Migration Strategy

1. **Database Migration**: Add new columns to `user_daily_stats` table
2. **Backfill Data**: Optionally backfill recent sprint/arrow-duel stats from events
3. **Update Triggers**: Deploy updated database triggers
4. **Seed Quest Requirements**: Create initial sprint and arrow-duel requirements
5. **Frontend Integration**: Update clients to use new quest APIs

This simplified approach provides all the quest functionality needed while keeping the implementation minimal and leveraging existing systems effectively.