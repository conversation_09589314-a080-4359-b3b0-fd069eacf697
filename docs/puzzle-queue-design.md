# Puzzle Queue Design

## Overview

The puzzle queue system provides a spaced repetition mechanism for users to review their puzzles, similar to the existing task queue system. Users can add puzzles to their queue and retrieve them based on due dates, with automatic rescheduling based on performance.

**Key Features:**
- Only user-generated puzzles (no Lichess puzzles) with mistake/blunder themes are added to the queue
- All mistake/blunder themes except caught ones: `opponent_mistake_missed`, `opponent_blunder_missed`, `own_mistake_punished`, `own_mistake_escaped`, `own_blunder_punished`, and `own_blunder_escaped`
- High-level categorization by `mistake_by`: `opponent` or `own` for easier filtering
- Support for different attempt types: normal solving vs mistake retry solving
- Queue-specific attempt flag to control scheduling behavior
- Theme-based filtering for different frontend rendering approaches

## Database Schema

### PuzzleQueue Model (GORM Auto-Migration)

Following the pattern from `internal/models/task.go`, the puzzle queue will use GORM auto-migration:

```go
// PuzzleQueueEntry represents a puzzle in the user's spaced repetition queue
type PuzzleQueueEntry struct {
    ID                  string    `gorm:"type:varchar(36);primary_key" json:"id"`
    UserID              string    `gorm:"type:varchar(36);not null;index:idx_puzzle_queue_user_due,priority:1;index:idx_puzzle_queue_user_puzzle,priority:1;index:idx_puzzle_queue_user_mistake_due,priority:1" json:"user_id"`
    PuzzleID            string    `gorm:"type:varchar(255);not null;index:idx_puzzle_queue_user_puzzle,priority:2" json:"puzzle_id"`
    PuzzleTheme         PuzzleTheme `gorm:"type:varchar(50);not null" json:"puzzle_theme"`
    MistakeBy           string    `gorm:"type:varchar(20);not null;index:idx_puzzle_queue_user_mistake_due,priority:2" json:"mistake_by"` // "opponent" or "own"
    DueAt               time.Time `gorm:"not null;index:idx_puzzle_queue_user_due,priority:2;index:idx_puzzle_queue_user_mistake_due,priority:3" json:"due_at"`
    AttemptsSinceAdded  int       `gorm:"not null;default:0" json:"attempts_since_added"`
    ConsecutiveCorrect  int       `gorm:"not null;default:0" json:"consecutive_correct"`
    CreatedAt           time.Time `gorm:"not null" json:"created_at"`
    UpdatedAt           time.Time `gorm:"not null" json:"updated_at"`

    // Relationships
    Puzzle Puzzle `gorm:"foreignKey:PuzzleID;constraint:OnDelete:CASCADE" json:"-"`
    User   User   `gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE" json:"-"`
}

// TableName returns the table name for PuzzleQueueEntry
func (PuzzleQueueEntry) TableName() string {
    return "puzzle_queue"
}
```

**GORM Indexes Created:**
- `idx_puzzle_queue_user_due` on `(user_id, due_at)` - for fetching due puzzles
- `idx_puzzle_queue_user_puzzle` on `(user_id, puzzle_id)` - unique constraint and quick removal
- `idx_puzzle_queue_user_mistake_due` on `(user_id, mistake_by, due_at)` - for filtering by mistake type

### Key Design Decisions

1. **User Puzzles Only**: Only user-generated puzzles are queued (no Lichess puzzles)
2. **Mistake/Blunder Themes**: All themes except caught ones are queued: `opponent_mistake_missed`, `opponent_blunder_missed`, `own_mistake_punished`, `own_mistake_escaped`, `own_blunder_punished`, `own_blunder_escaped`
3. **High-Level Categorization**: `mistake_by` field (`opponent` or `own`) for easier filtering
4. **Unique Constraint**: `(user_id, puzzle_id)` prevents duplicate queue entries
5. **User-Centric Indexing**: All indexes include `user_id` as first column for efficient user-based queries
6. **Cascade Delete**: Automatically removes queue entries when users or puzzles are deleted
7. **Attempt Tracking**: Tracks attempts and consecutive correct answers for scheduling logic

## API Endpoints

### 1. Add Puzzles to Queue

**Endpoint**: `POST /api/puzzle-queue/add`

**Request Body**:
```json
{
    "count": 50,
    "mistake_by": "opponent"
}
```

**Parameters**:
- `count` (required): Number of puzzles to add (max 200)
- `mistake_by` (optional): Filter by `"opponent"` or `"own"`, if not specified adds both types

**Response**:
```json
{
    "added_count": 45,
    "skipped_count": 5,
    "message": "Added 45 puzzles to queue, skipped 5 already queued"
}
```

**Logic**:
- Fetch N most recent user puzzles (where N <= 200)
- Filter to include all mistake/blunder themes except caught ones
- Apply `mistake_by` filter if specified (`opponent` or `own`)
- Filter out puzzles already in the queue
- Add new puzzles with `due_at = NOW()` (due today)
- Set `mistake_by` field based on puzzle theme
- Prioritize newer puzzles first

**Theme to MistakeBy Mapping**:
- `opponent_mistake_missed`, `opponent_blunder_missed` → `mistake_by = "opponent"`
- `own_mistake_punished`, `own_mistake_escaped`, `own_blunder_punished`, `own_blunder_escaped` → `mistake_by = "own"`

### 2. Fetch Due Puzzles

**Endpoint**: `GET /api/puzzle-queue/due`

**Query Parameters**:
- `limit` (optional, default: 10, max: 50)
- `mistake_by` (optional: `"opponent"` or `"own"`, both if not specified)

**Response**:
```json
{
    "puzzles": [
        {
            "queue_id": "uuid",
            "puzzle_id": "puzzle-123",
            "puzzle_theme": "opponent_mistake_missed",
            "mistake_by": "opponent",
            "due_at": "2024-01-15T10:00:00Z",
            "attempts_since_added": 2,
            "consecutive_correct": 1,
            "puzzle_data": {
                "id": "puzzle-123",
                "fen": "...",
                "moves": ["e2e4", "e7e5"],
                "theme": "opponent_mistake_missed"
            }
        }
    ],
    "total_due": 25
}
```

**Logic**:
- Query puzzles where `due_at <= NOW()`
- Apply `mistake_by` filter if specified
- Order by `due_at ASC` (oldest due first)
- Join with user puzzle data
- Apply limit

## Puzzle Attempt Types and Frontend Rendering

### Theme-Based Rendering Approaches

#### Opponent Mistake Missed (`opponent_mistake_missed`)
- **Frontend Approach**: Standard puzzle solving
- **Rendering**: Show the puzzle position and let user find the best move
- **Success Criteria**: User finds the correct tactical solution
- **Attempt Type**: `normal_solve`

#### Own Mistake Punished (`own_mistake_punished`)
- **Frontend Approach**: Mistake identification and correction
- **Rendering**:
  1. Show the initial puzzle position
  2. The first move in the puzzle is the user's blunder/mistake
  3. Use Stockfish to calculate the correct move
  4. Present user with choice: the blunder move vs the correct move
- **Success Criteria**: User chooses the correct move (not the blunder)
- **Attempt Type**: `mistake_retry`

### Attempt Type Enhancement

To distinguish between different solving approaches, puzzle attempt requests should include an `attempt_type` field:

```json
{
    "moves": ["e2e4"],
    "solved": true,
    "time_spent": 30,
    "attempt_type": "mistake_retry", // or "normal_solve"
    "is_queue_attempt": true, // Flag to enable queue scheduling
    "is_disliked": false
}
```

**Attempt Types**:
- `normal_solve`: Standard puzzle solving (finding the best move)
- `mistake_retry`: Identifying and avoiding previous mistakes

## Spaced Repetition Logic

When a puzzle attempt is submitted via existing endpoints (`/puzzles/{puzzleID}/attempts`) with `is_queue_attempt: true`, the system will:

1. **Check if puzzle is in queue**: Look up the puzzle in the user's queue
2. **Update attempt counters**: Increment `attempts_since_added`
3. **Apply scheduling logic**:

### Scheduling Rules

```
IF attempt is CORRECT:
    consecutive_correct++
    
    IF consecutive_correct >= 5:
        // Remove from queue - mastered
        DELETE from puzzle_queue
    ELSE:
        // Schedule for future review
        CASE consecutive_correct:
            1: due_at = NOW() + 2 days
            2: due_at = NOW() + 4 days  
            3: due_at = NOW() + 7 days
            4: due_at = NOW() + 15 days
ELSE (attempt is INCORRECT):
    consecutive_correct = 0
    due_at = NOW() + 24 hours  // Try again tomorrow
```

## Repository Interface

### IPuzzleQueueRepository

```go
type IPuzzleQueueRepository interface {
    // Add puzzles to queue
    AddPuzzlesToQueue(ctx context.Context, userID string, puzzles []PuzzleQueueEntry) (int, error)
    
    // Get due puzzles for user
    GetDuePuzzles(ctx context.Context, userID string, mistakeBy *string, limit int) ([]PuzzleQueueItem, error)

    // Update puzzle queue entry after attempt
    UpdateAfterAttempt(ctx context.Context, userID, puzzleID string, wasCorrect bool) error

    // Get recent puzzles not in queue (filtered by allowed themes)
    GetRecentPuzzlesNotInQueue(ctx context.Context, userID string, mistakeBy *string, limit int) ([]string, error)
    
    // Remove puzzle from queue (when mastered)
    RemoveFromQueue(ctx context.Context, userID, puzzleID string) error
    
    // Get queue stats for user
    GetQueueStats(ctx context.Context, userID string) (*PuzzleQueueStats, error)
}
```

## Models

### PuzzleQueueEntry

```go
type PuzzleQueueEntry struct {
    ID                  string      `gorm:"type:varchar(36);primary_key" json:"id"`
    UserID              string      `gorm:"type:varchar(36);not null;index:idx_puzzle_queue_user_due,priority:1;index:idx_puzzle_queue_user_puzzle,priority:1;index:idx_puzzle_queue_user_mistake_due,priority:1" json:"user_id"`
    PuzzleID            string      `gorm:"type:varchar(255);not null;index:idx_puzzle_queue_user_puzzle,priority:2" json:"puzzle_id"`
    PuzzleTheme         PuzzleTheme `gorm:"type:varchar(50);not null" json:"puzzle_theme"`
    MistakeBy           string      `gorm:"type:varchar(20);not null;index:idx_puzzle_queue_user_mistake_due,priority:2" json:"mistake_by"` // "opponent" or "own"
    DueAt               time.Time   `gorm:"not null;index:idx_puzzle_queue_user_due,priority:2;index:idx_puzzle_queue_user_mistake_due,priority:3" json:"due_at"`
    AttemptsSinceAdded  int         `gorm:"not null;default:0" json:"attempts_since_added"`
    ConsecutiveCorrect  int         `gorm:"not null;default:0" json:"consecutive_correct"`
    CreatedAt           time.Time   `gorm:"not null" json:"created_at"`
    UpdatedAt           time.Time   `gorm:"not null" json:"updated_at"`

    // Relationships
    Puzzle Puzzle `gorm:"foreignKey:PuzzleID;constraint:OnDelete:CASCADE" json:"-"`
    User   User   `gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE" json:"-"`
}

// TableName returns the table name for PuzzleQueueEntry
func (PuzzleQueueEntry) TableName() string {
    return "puzzle_queue"
}
```

### PuzzleQueueItem (for API responses)

```go
type PuzzleQueueItem struct {
    QueueID            string      `json:"queue_id"`
    PuzzleID           string      `json:"puzzle_id"`
    PuzzleTheme        string      `json:"puzzle_theme"`
    MistakeBy          string      `json:"mistake_by"`
    DueAt              time.Time   `json:"due_at"`
    AttemptsSinceAdded int         `json:"attempts_since_added"`
    ConsecutiveCorrect int         `json:"consecutive_correct"`
    PuzzleData         *Puzzle     `json:"puzzle_data"` // User puzzle data
}
```

## Integration Points

### 1. Existing Puzzle Attempt Handlers

Modify existing handlers to check for queue entries and update scheduling:

- `PostPuzzleAttempt` in `user_handlers.go` (only user puzzles, no Lichess)

**Enhanced Request Structure**:
```go
type PostPuzzleAttemptRequest struct {
    Moves           []string `json:"moves"`                     // User's moves in the puzzle
    Solved          bool     `json:"solved"`                    // Whether the puzzle was solved
    TimeSpent       int      `json:"time_spent"`                // Time spent in seconds
    AttemptType     string   `json:"attempt_type"`              // "normal_solve" or "mistake_retry"
    IsQueueAttempt  bool     `json:"is_queue_attempt"`          // Flag to enable queue scheduling
    IsDisliked      *bool    `json:"is_disliked,omitempty"`     // Optional: whether user dislikes this puzzle
}
```

### 2. Event System Integration

The puzzle queue updates will be triggered by the existing puzzle attempt events, ensuring consistency with the current event-driven architecture.

### 3. Database Triggers (Optional Enhancement)

Consider adding database triggers to automatically update puzzle queue entries when puzzle events are inserted, similar to the existing stats triggers.

## Implementation Phases

### Phase 1: Core Infrastructure
1. Create database migration for `puzzle_queue` table with theme support
2. Implement `PuzzleQueueEntry` model with theme field
3. Create `PuzzleQueueRepository` with theme filtering
4. Add unit tests for repository with theme constraints

### Phase 2: API Endpoints
1. Implement `POST /api/puzzle-queue/add` endpoint with theme filtering
2. Implement `GET /api/puzzle-queue/due` endpoint with theme parameter
3. Create API handlers and request/response models
4. Add integration tests for endpoints with theme scenarios

### Phase 3: Enhanced Puzzle Attempts
1. Add `attempt_type` field to puzzle attempt requests
2. Modify existing puzzle attempt handlers for queue integration
3. Implement scheduling logic in service layer
4. Add queue update logic to puzzle attempt processing
5. Test end-to-end flow with different attempt types

### Phase 4: Theme-Specific Features
1. Add theme-based filtering in repository queries
2. Implement theme validation in API endpoints
3. Add queue statistics endpoint with theme breakdown
4. Consider database triggers for automatic updates

### Phase 5: Frontend Integration Support
1. Document theme-specific rendering approaches
2. Add API documentation for attempt types
3. Create example requests for different puzzle themes
4. Add performance monitoring for queue operations

## Testing Strategy

### Unit Tests
- Repository operations (CRUD, filtering, scheduling)
- Service layer logic (spaced repetition algorithm)
- Model validation and constraints

### Integration Tests
- API endpoint functionality
- Database constraint enforcement
- Event system integration

### End-to-End Tests
- Complete puzzle queue workflow
- Spaced repetition scheduling accuracy
- Performance with large datasets

## Performance Considerations

1. **Indexing**: Proper indexes on `(user_id, due_at)` for efficient due puzzle queries
2. **Batch Operations**: Support adding multiple puzzles in single transaction
3. **Pagination**: Limit query results to prevent memory issues
4. **Cleanup**: Periodic cleanup of old completed queue entries

## Security Considerations

1. **User Isolation**: All queries filtered by user ID to prevent data leakage
2. **Input Validation**: Validate puzzle IDs and types before queue operations
3. **Rate Limiting**: Prevent abuse of add-to-queue endpoint
4. **Authorization**: Ensure users can only access their own queue data
