# Arrow-Duel Sprint Mistakes API Analysis

## Issue Summary

The current arrow-duel sprint mistakes API (`GET /api/v1/users/me/sprint/{sessionId}/puzzles`) does not return the critical arrow-duel information (candidate moves and chosen move) needed for the client to provide meaningful mistake reviews. This data is being captured during result submission but is not being stored in the `sprint_puzzle_attempts` table and therefore cannot be retrieved for review.

## Current Implementation Analysis

### Data Submission (Working Correctly)
The arrow-duel results submission API (`POST /api/v1/users/me/sprint/{sessionId}/results`) correctly accepts and validates arrow-duel specific data:

```json
{
  "results": [
    {
      "puzzle_id": "00008",
      "sequence_in_sprint": 1,
      "user_moves": ["Qh5"],
      "was_correct": true,
      "time_taken_ms": 12000,
      "attempted_at": "2023-10-01T12:00:00Z",
      "attempt_type": "arrow_duel",
      "candidate_moves": ["Qh4", "Qh5"],  // [blunder_move, correct_move]
      "chosen_move": "Qh5"
    }
  ]
}
```

### Data Storage (Missing Arrow-Duel Fields)
The `SprintPuzzleAttempt` model in `/internal/models/sprint_puzzle.go` only stores basic attempt data:

```go
type SprintPuzzleAttempt struct {
    ID               string         `gorm:"type:varchar(36);primary_key" json:"id"`
    SprintID         string         `gorm:"type:varchar(36);not null;index" json:"sprint_id"`
    UserID           string         `gorm:"type:varchar(36);not null;index" json:"user_id"`
    LichessPuzzleID  string         `gorm:"type:varchar(10);not null;index" json:"lichess_puzzle_id"`
    SequenceInSprint int            `gorm:"not null" json:"sequence_in_sprint"`
    UserMoves        pq.StringArray `gorm:"type:text[]" json:"user_moves"`
    WasCorrect       bool           `gorm:"not null" json:"was_correct"`
    TimeTakenMs      int            `gorm:"not null" json:"time_taken_ms"`
    AttemptedAt      time.Time      `gorm:"not null;index" json:"attempted_at"`
    // ❌ MISSING: AttemptType, CandidateMoves, ChosenMove
}
```

### Data Retrieval (Missing Arrow-Duel Fields)
The sprint puzzles API (`GET /api/v1/users/me/sprint/{sessionId}/puzzles`) returns:

```go
type SprintPuzzleResponse struct {
    PuzzleID         string   `json:"puzzle_id"`
    SequenceInSprint int      `json:"sequence_in_sprint"`
    FEN              string   `json:"fen"`
    SolutionMoves    []string `json:"solution_moves"`
    Rating           int      `json:"rating"`
    Themes           []string `json:"themes"`
    AttemptStatus    string   `json:"attempt_status"`
    UserMoves        []string `json:"user_moves,omitempty"`
    WasCorrect       *bool    `json:"was_correct,omitempty"`
    TimeTakenMs      *int     `json:"time_taken_ms,omitempty"`
    AttemptedAt      *string  `json:"attempted_at,omitempty"`
    // ❌ MISSING: AttemptType, CandidateMoves, ChosenMove
}
```

## Problem Analysis

### Root Cause
The arrow-duel specific fields (`attempt_type`, `candidate_moves`, `chosen_move`) are being:
1. ✅ **Accepted** in the API request validation
2. ✅ **Passed** to the service layer via `PuzzleAttemptResult`
3. ❌ **NOT STORED** in the `sprint_puzzle_attempts` database table
4. ❌ **NOT RETURNED** in the sprint puzzles response

### Missing Database Schema
The `sprint_puzzle_attempts` table needs these additional columns:
- `attempt_type` (VARCHAR) - "regular" or "arrow_duel"
- `candidate_moves` (TEXT[]) - Array of 2 moves [blunder_move, correct_move]
- `chosen_move` (VARCHAR) - The move chosen by the player

### Data Flow Gap
```
API Request → Service Layer → ❌ Database Storage ❌ → API Response
     ✅             ✅                   ❌                  ❌
```

## Required Changes

### 1. Database Schema Update
Add arrow-duel fields to `sprint_puzzle_attempts` table:

```sql
-- New migration needed
ALTER TABLE sprint_puzzle_attempts 
ADD COLUMN attempt_type VARCHAR(20) DEFAULT 'regular',
ADD COLUMN candidate_moves TEXT[],
ADD COLUMN chosen_move VARCHAR(20);

-- Add index for filtering by attempt type
CREATE INDEX idx_sprint_puzzle_attempts_attempt_type ON sprint_puzzle_attempts(attempt_type);
```

### 2. Model Update
Update `SprintPuzzleAttempt` model to include arrow-duel fields:

```go
type SprintPuzzleAttempt struct {
    // ... existing fields ...
    AttemptType    string         `gorm:"type:varchar(20);default:'regular'" json:"attempt_type"`
    CandidateMoves pq.StringArray `gorm:"type:text[]" json:"candidate_moves,omitempty"`
    ChosenMove     *string        `gorm:"type:varchar(20)" json:"chosen_move,omitempty"`
}
```

### 3. Repository Layer Update
Update `SprintPuzzleWithAttempt` struct and repository queries:

```go
type SprintPuzzleWithAttempt struct {
    // ... existing fields ...
    AttemptType    *string
    CandidateMoves []string
    ChosenMove     *string
}
```

### 4. API Response Update
Update `SprintPuzzleResponse` to include arrow-duel fields:

```go
type SprintPuzzleResponse struct {
    // ... existing fields ...
    AttemptType    *string  `json:"attempt_type,omitempty"`
    CandidateMoves []string `json:"candidate_moves,omitempty"`
    ChosenMove     *string  `json:"chosen_move,omitempty"`
}
```

### 5. Service Layer Update
Update the sprint service to properly store and retrieve arrow-duel data.

## Implementation Priority

### High Priority (Required for Client Review)
1. **Database migration** - Add missing columns
2. **Model updates** - Include arrow-duel fields
3. **Repository updates** - Store and retrieve arrow-duel data
4. **API response updates** - Return arrow-duel data for review

### Medium Priority (Enhanced Filtering)
1. **API filtering** - Add ability to filter by attempt_type
2. **Validation updates** - Ensure data consistency

## Benefits After Fix

### For Arrow-Duel Mistake Review
1. **Client can display both moves**: Show the blunder move vs. correct move
2. **User choice visualization**: Highlight which move the user actually chose
3. **Learning enhancement**: Users can understand why their choice was wrong
4. **Tactical analysis**: Compare candidate moves with detailed explanations

### For Regular Sprint Review
1. **Consistent API**: Both regular and arrow-duel attempts use same endpoint
2. **Future extensibility**: Easy to add more attempt types
3. **Analytics potential**: Track arrow-duel vs. regular performance

## Data Availability

The good news is that the arrow-duel data IS being submitted correctly in the results API. The issue is purely in the storage and retrieval layers. Once the database schema is updated and the models are enhanced, all future arrow-duel attempts will have the complete data needed for comprehensive mistake review.

## Migration Considerations

- **Backward compatibility**: Existing sprint_puzzle_attempts will have NULL values for new fields
- **Default values**: `attempt_type` defaults to 'regular' for existing records
- **Data validation**: Ensure arrow-duel attempts have complete candidate_moves and chosen_move data