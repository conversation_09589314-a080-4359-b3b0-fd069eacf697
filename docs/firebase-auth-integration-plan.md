# Firebase Authentication Integration Plan

## Overview

This document outlines the implementation plan for integrating Firebase Authentication with our existing authentication system. The integration will allow users to authenticate using Firebase tokens while maintaining our current token-based API architecture.

## Firebase Documentation References

- **Firebase Authentication Overview**: https://firebase.google.com/docs/auth
- **Verify ID Tokens**: https://firebase.google.com/docs/auth/admin/verify-id-tokens
- **Firebase Authentication Emulator**: https://firebase.google.com/docs/emulator-suite/connect_auth
- **Firebase JWKS Endpoint**: https://www.googleapis.com/service_accounts/v1/jwk/<EMAIL>
- **Firebase Token Claims**: https://firebase.google.com/docs/reference/rest/auth#section-verify-custom-token

## Firebase ID Token Structure

Firebase ID tokens are JWT tokens with the following structure:

### Token Header
```json
{
  "alg": "RS256",
  "kid": "1f525b0f2b144a0c8b5d8e8c7f4a3b2c1d0e9f8a",
  "typ": "JWT"
}
```

### Token Payload (Claims)
```json
{
  "iss": "https://securetoken.google.com/your-project-id",
  "aud": "your-project-id",
  "auth_time": **********,
  "user_id": "firebase-user-uid-string",
  "sub": "firebase-user-uid-string",
  "iat": **********,
  "exp": **********,
  "email": "<EMAIL>",
  "email_verified": true,
  "firebase": {
    "identities": {
      "email": ["<EMAIL>"]
    },
    "sign_in_provider": "password"
  }
}
```

### Key Claims for Our Integration
- **`sub`**: Firebase user ID (our `firebase_uid`)
- **`email`**: User's email address (sync to our `email` field)
- **`email_verified`**: Whether email is verified in Firebase
- **`iss`**: Must match `https://securetoken.google.com/{project_id}`
- **`aud`**: Must match our Firebase project ID

## Goals

1. **Local Testing Support**: Implement a configurable JWKS system that supports both Firebase production JWKS and local emulator testing
2. **Token Exchange API**: Create a new API endpoint that exchanges Firebase ID tokens for our internal JWT tokens
3. **User Linking**: Automatically link Firebase users to existing users based on email addresses
4. **Gradual Migration**: Maintain backward compatibility while deprecating duplicate authentication APIs

## Current Authentication System Analysis

### Current Components

1. **User Model** (`internal/models/user.go`):
   - ID (UUID primary key)
   - Email (unique, indexed)
   - PasswordHash (bcrypt)
   - RegisteredAt, UpdatedAt, LastSignInAt
   - ChessProfiles (one-to-many relationship)

2. **Session Management** (`internal/models/session_token.go`):
   - SessionToken model with hashed tokens
   - Expiry-based session management
   - User-Agent tracking

3. **Authentication APIs** (`internal/api/auth_handlers.go`):
   - `/auth/login` - Email/password and session token login
   - `/auth/register` - User registration
   - `/auth/register-with-invitation` - Invitation-based registration
   - `/auth/session-tokens` - Session token management

4. **JWT Service** (`internal/service/auth_service.go`):
   - JWT token generation with HS256
   - Session token generation and validation
   - Claims structure: UserID, Email, IsAdmin

5. **Middleware** (`internal/middleware/jwt.go`):
   - JWT validation middleware
   - Admin-only middleware
   - Context-based user ID extraction

### Current Configuration (`internal/config/config.go`):
```go
type Config struct {
    Server       ServerConfig
    Database     DatabaseConfig
    JWT          JWTConfig
    SessionToken SessionTokenConfig
}

type JWTConfig struct {
    Secret        string
    ExpiryMinutes int
}
```

## Implementation Plan

### Phase 1: Firebase JWT Verification Infrastructure

#### 1.1 Add Firebase Configuration
**Files to modify**: `internal/config/config.go`

Add Firebase-specific configuration:
```go
type FirebaseConfig struct {
    ProjectID    string
    JWKSEndpoint string  // Configurable for local testing
}

type Config struct {
    // ... existing fields
    Firebase FirebaseConfig
}
```

Environment variables:
- `FIREBASE_PROJECT_ID` - Firebase project ID
- `FIREBASE_JWKS_ENDPOINT` - JWKS endpoint (defaults to Firebase production, configurable for local testing)

#### 1.2 Implement Firebase JWT Verification Service
**New file**: `internal/service/firebase_auth_service.go`

Key components:
- JWKS fetching and caching with configurable endpoint
- Firebase ID token validation using `lestrrat-go/jwx` library
- Token claims extraction (sub, email, email_verified, etc.)
- Support for both production Firebase and local emulator tokens

**Firebase Token Validation Requirements** (per [Firebase docs](https://firebase.google.com/docs/auth/admin/verify-id-tokens)):
- Verify signature using JWKS from Firebase
- Validate `iss` claim matches `https://securetoken.google.com/{project_id}`
- Validate `aud` claim matches Firebase project ID
- Verify token is not expired (`exp` claim)
- Verify token was issued in the past (`iat` claim)
- Verify `auth_time` is in the past

Dependencies to add:
```go
github.com/lestrrat-go/jwx/v2/jwk
github.com/lestrrat-go/jwx/v2/jwt
```

#### 1.3 Update User Model
**Files to modify**: `internal/models/user.go`

Add Firebase user ID field:
```go
type User struct {
    // ... existing fields
    FirebaseUID *string `gorm:"type:varchar(255);uniqueIndex" json:"firebase_uid,omitempty"`
}
```

**GORM Auto-Migration will handle**:
- Add firebase_uid column as nullable VARCHAR(255)
- Create unique index on firebase_uid (via `uniqueIndex` tag)
- Update auto-migration in `cmd/chessticize/migrate/migrate.go`

**Manual SQL Migration needed**:
- Remove unique constraint on email field (GORM can't remove existing constraints)

### Phase 2: Firebase Token Exchange API

#### 2.1 Create Firebase Token Exchange Endpoint
**Files to modify**: `internal/api/auth_handlers.go`

New endpoint: `POST /auth/firebase-exchange`

Request:
```json
{
    "firebase_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

Response:
```json
{
    "token": "our_jwt_token",
    "session_token": "session_token_string",
    "user": {
        "id": "user_uuid",
        "email": "<EMAIL>",
        "firebase_uid": "firebase_user_id"
    }
}
```

#### 2.2 User Linking Logic
**Files to modify**: `internal/repository/user_repository.go`

Add methods:
- `GetByFirebaseUID(ctx context.Context, firebaseUID string) (*User, error)`
- `GetByEmailForFirebaseLinking(ctx context.Context, email string) (*User, error)`
- `LinkFirebaseUID(ctx context.Context, userID, firebaseUID string) error`
- `UpdateEmailFromFirebase(ctx context.Context, userID, email string) error`

Linking algorithm:
1. Check if Firebase UID already exists → return existing user, update email from token
2. If not found, check if email exists → link Firebase UID to existing user, update email
3. If neither exists → create new user with Firebase UID and email from token

Email synchronization:
- Always update user.email field with email from Firebase token on login
- Handle email changes in Firebase by updating local user record

#### 2.3 Event Tracking
**Files to modify**: `internal/service/event_service.go`

Add new event types:
- `FirebaseSignIn` - Track Firebase authentication events
- `UserLinked` - Track when Firebase UID is linked to existing user

### Phase 3: Local Testing Infrastructure

#### 3.1 Firebase Authentication Emulator
**Reference**: [Firebase Authentication Emulator Documentation](https://firebase.google.com/docs/emulator-suite/connect_auth)

The Firebase Authentication emulator provides:
- Local JWKS endpoint: `http://localhost:9099/.well-known/jwks.json`
- Unsigned tokens for local testing (alg: "none")
- Same token structure as production but without signature verification

**Emulator Token Example**:
```json
{
  "iss": "https://securetoken.google.com/demo-project",
  "aud": "demo-project",
  "auth_time": **********,
  "user_id": "local-test-user-id",
  "sub": "local-test-user-id",
  "iat": **********,
  "exp": **********,
  "email": "<EMAIL>",
  "email_verified": true,
  "firebase": {
    "identities": {
      "email": ["<EMAIL>"]
    },
    "sign_in_provider": "password"
  }
}
```

#### 3.2 Test Configuration
**Files to modify**: `e2e/helpers.go`

Add Firebase token generation for tests:
```go
func generateFirebaseToken(t *testing.T, userID, email string) string {
    // Generate mock Firebase token for testing
    // Uses unsigned tokens for emulator compatibility
}
```

Environment setup for tests:
- `FIREBASE_JWKS_ENDPOINT=http://localhost:9099/.well-known/jwks.json` (Firebase emulator)
- `FIREBASE_PROJECT_ID=demo-project` (for emulator testing)

### Phase 4: API Deprecation Strategy

#### 4.1 APIs to Deprecate

**Immediate Deprecation** (Phase 4):
1. **Password-based authentication**:
   - `POST /auth/register` (replaced by Firebase registration)
   - `POST /auth/login` with email/password (replaced by Firebase sign-in)

**Future Deprecation** (Post-migration):
2. **Session token management** (Firebase provides refresh tokens):
   - `GET /auth/session-tokens` - List user's session tokens
   - `DELETE /auth/session-tokens/{tokenID}` - Revoke session tokens
   - `POST /auth/login` with session_token (replaced by Firebase refresh tokens)

#### 4.2 APIs to Maintain

**During Migration Period**:
1. **Session token management** (temporary):
   - `GET /auth/session-tokens` - List user's session tokens
   - `DELETE /auth/session-tokens/{tokenID}` - Revoke session tokens
   - `POST /auth/login` with session_token (for existing users)

**Permanent APIs**:
2. **Admin APIs**:
   - All admin user management APIs
   - Invitation code management (if still needed)

3. **User profile APIs**:
   - All `/users/me/*` endpoints remain unchanged

#### 4.3 Database Schema Changes

**GORM Auto-Migration Handles**:
- Add `firebase_uid` field as nullable VARCHAR(255) with unique index
- Preserve all existing fields and data

**Manual SQL Migration Required**:
- Remove unique constraint on email (GORM can't modify existing constraints)
- Add non-unique index on email for performance

**Fields to deprecate** (but not remove for backward compatibility):
- `users.password_hash` - No longer needed for Firebase users
- `users.email` unique constraint - Firebase handles email uniqueness
- Session token related fields (future deprecation after full migration)

**Email handling**:
- Remove unique constraint on email via SQL migration (Firebase users may change emails)
- Email remains indexed for performance but not unique
- Email field updated from Firebase token on each login (Firebase emails are mutable)
- Primary identification through firebase_uid for Firebase users

## Implementation Timeline

### Week 1: Infrastructure Setup ✅ COMPLETED
- [x] Add Firebase configuration
- [x] Implement Firebase JWT verification service
- [x] Add firebase_uid to User model with `uniqueIndex` GORM tag
- [x] Run GORM auto-migration (handles firebase_uid column and index)
- [x] Create manual SQL migration for email constraint removal

### Week 2: Token Exchange API ✅ COMPLETED
- [x] Implement Firebase token exchange endpoint
- [x] Add user linking logic with email synchronization
- [x] Add event tracking for Firebase authentication
- [x] Write unit tests

### Week 3: Local Testing Support ✅ COMPLETED
- [x] Create local JWKS emulator
- [x] Update test helpers for Firebase tokens
- [x] Write e2e tests for Firebase authentication
- [x] Document local development setup

### Week 4: Integration and Documentation ✅ COMPLETED
- [x] Integration testing with real Firebase
- [x] Performance testing of JWKS caching
- [x] Update API documentation
- [x] Create migration guide for existing users

### Post-Migration (Future Phase): Session Token Deprecation
- [ ] Evaluate Firebase refresh token adoption
- [ ] Plan session token API deprecation
- [ ] Migrate remaining users to Firebase refresh tokens
- [ ] Remove session token infrastructure

## ✅ IMPLEMENTATION COMPLETED

### Summary of Completed Work

**Core Infrastructure:**
- ✅ Firebase authentication service with JWT verification
- ✅ JWKS caching with configurable endpoints
- ✅ Database schema updates with firebase_uid field
- ✅ User repository methods for Firebase operations

**API Implementation:**
- ✅ `/api/v1/auth/firebase-exchange` endpoint
- ✅ User linking logic (existing users + new users)
- ✅ Proper HTTP status codes (201 for new, 200 for existing)
- ✅ Session token generation for Firebase users

**Event Tracking:**
- ✅ Enhanced event tracking with sign-in provider information
- ✅ Updated event data models to use `sign_in_type` field consistently
- ✅ Registration and sign-in events for Firebase authentication

**Testing:**
- ✅ Comprehensive unit tests with Firebase emulator
- ✅ Integration tests for all authentication flows
- ✅ E2E tests demonstrating full functionality
- ✅ Test coverage for edge cases and error scenarios

**Documentation:**
- ✅ Updated implementation plan with progress
- ✅ API usage examples and response formats
- ✅ Configuration documentation
- ✅ Local development setup instructions

## Technical Considerations

### Security
- JWKS caching strategy to prevent excessive requests
- Proper validation of Firebase token claims
- Rate limiting on token exchange endpoint
- Secure handling of Firebase project configuration

### Performance
- Cache JWKS responses with appropriate TTL
- Efficient user lookup by Firebase UID
- Minimize database queries in token exchange flow

### Monitoring
- Track Firebase authentication success/failure rates
- Monitor JWKS endpoint availability
- Alert on user linking failures

### Backward Compatibility
- Maintain existing JWT token format
- Keep existing session token functionality
- Gradual deprecation with proper warnings

## Dependencies

### New Go Modules
```go
github.com/lestrrat-go/jwx/v2 v2.0.21
```

### Environment Variables
```bash
# Production
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_JWKS_ENDPOINT=https://www.googleapis.com/service_accounts/v1/jwk/<EMAIL>

# Local Development (Firebase Emulator)
FIREBASE_PROJECT_ID=demo-project
FIREBASE_JWKS_ENDPOINT=http://localhost:9099/.well-known/jwks.json
```

**Firebase JWKS Endpoints**:
- **Production**: `https://www.googleapis.com/service_accounts/v1/jwk/<EMAIL>`
- **Emulator**: `http://localhost:9099/.well-known/jwks.json`

**Firebase Documentation References**:
- [Firebase Project Configuration](https://firebase.google.com/docs/projects/learn-more)
- [Firebase Emulator Setup](https://firebase.google.com/docs/emulator-suite/install_and_configure)

## Testing Strategy

### Unit Tests
- Firebase JWT verification service
- User linking logic
- Token exchange endpoint logic

### Integration Tests
- End-to-end Firebase authentication flow
- User linking scenarios
- Error handling for invalid tokens

### Local Development
- Firebase emulator integration
- Mock JWKS endpoint for testing
- Test user creation and linking

## Migration Strategy

### For Existing Users
1. **Gradual Migration**: Existing users continue using current authentication
2. **Optional Linking**: Provide UI for users to link Firebase accounts
3. **Email-based Linking**: Automatic linking when Firebase email matches existing user
4. **Session Token Transition**: Maintain session tokens during migration period

### For New Users
1. **Firebase-first**: Encourage Firebase authentication for new registrations
2. **Fallback Support**: Maintain current registration for edge cases during transition
3. **Progressive Enhancement**: Add Firebase authentication to existing flows

### Post-Migration Strategy
1. **Session Token Deprecation**: After full Firebase adoption, deprecate session token APIs
2. **Firebase Refresh Tokens**: Leverage Firebase's built-in refresh token mechanism
3. **Cleanup**: Remove deprecated authentication infrastructure

## Detailed Technical Implementation

### Firebase JWT Verification Implementation

#### JWKS Caching Strategy
```go
type JWKSCache struct {
    keys      jwk.Set
    lastFetch time.Time
    ttl       time.Duration
    mutex     sync.RWMutex
}

func (c *JWKSCache) GetKeys(ctx context.Context, endpoint string) (jwk.Set, error) {
    c.mutex.RLock()
    if time.Since(c.lastFetch) < c.ttl && c.keys != nil {
        defer c.mutex.RUnlock()
        return c.keys, nil
    }
    c.mutex.RUnlock()

    // Fetch new keys with write lock
    c.mutex.Lock()
    defer c.mutex.Unlock()

    // Double-check pattern
    if time.Since(c.lastFetch) < c.ttl && c.keys != nil {
        return c.keys, nil
    }

    // Fetch from endpoint
    keys, err := jwk.Fetch(ctx, endpoint)
    if err != nil {
        return nil, err
    }

    c.keys = keys
    c.lastFetch = time.Now()
    return keys, nil
}
```

#### Firebase Token Validation
**Reference**: [Firebase ID Token Verification](https://firebase.google.com/docs/auth/admin/verify-id-tokens)

```go
type FirebaseTokenClaims struct {
    UserID        string `json:"user_id"`
    Email         string `json:"email"`
    EmailVerified bool   `json:"email_verified"`
    AuthTime      int64  `json:"auth_time"`
    Firebase      struct {
        Identities struct {
            Email []string `json:"email"`
        } `json:"identities"`
        SignInProvider string `json:"sign_in_provider"`
    } `json:"firebase"`
}

func (s *FirebaseAuthService) VerifyIDToken(ctx context.Context, tokenString string) (*FirebaseTokenClaims, error) {
    // Parse token without verification first to get kid
    token, err := jwt.ParseString(tokenString, jwt.WithVerify(false))
    if err != nil {
        return nil, fmt.Errorf("failed to parse token: %w", err)
    }

    // Get JWKS from Firebase endpoint
    keys, err := s.jwksCache.GetKeys(ctx, s.jwksEndpoint)
    if err != nil {
        return nil, fmt.Errorf("failed to fetch JWKS: %w", err)
    }

    // Verify token according to Firebase requirements
    verified, err := jwt.ParseString(tokenString,
        jwt.WithKeySet(keys),
        jwt.WithValidate(true),
        jwt.WithAudience(s.projectID), // Must match Firebase project ID
        jwt.WithIssuer(fmt.Sprintf("https://securetoken.google.com/%s", s.projectID)), // Firebase issuer
    )
    if err != nil {
        return nil, fmt.Errorf("token verification failed: %w", err)
    }

    // Extract Firebase-specific claims
    claims := &FirebaseTokenClaims{}
    if err := json.Unmarshal(verified.PrivateClaims(), claims); err != nil {
        return nil, fmt.Errorf("failed to extract claims: %w", err)
    }

    // Additional Firebase validations
    if claims.AuthTime > time.Now().Unix() {
        return nil, fmt.Errorf("auth_time is in the future")
    }

    return claims, nil
}
```

### Local Testing Setup

#### Firebase Emulator JWKS Endpoint
**Reference**: [Firebase Emulator Suite](https://firebase.google.com/docs/emulator-suite/connect_auth)

For local testing, the Firebase Authentication emulator provides JWKS at:
```
http://localhost:9099/.well-known/jwks.json
```

**Important**: Emulator tokens are unsigned (alg: "none") for testing purposes.

#### Test Token Generation
```go
func generateTestFirebaseToken(projectID, userID, email string) (string, error) {
    // Create claims matching Firebase token structure
    claims := map[string]interface{}{
        "iss":            fmt.Sprintf("https://securetoken.google.com/%s", projectID),
        "aud":            projectID,
        "auth_time":      time.Now().Unix(),
        "user_id":        userID,
        "sub":            userID, // Firebase user ID (same as user_id)
        "iat":            time.Now().Unix(),
        "exp":            time.Now().Add(time.Hour).Unix(),
        "email":          email,
        "email_verified": true,
        "firebase": map[string]interface{}{
            "identities": map[string]interface{}{
                "email": []string{email},
            },
            "sign_in_provider": "password",
        },
    }

    // For emulator, tokens are unsigned (alg: none)
    // Production tokens use RS256 with Firebase's private keys
    token := jwt.NewWithClaims(jwt.SigningMethodNone, jwt.MapClaims(claims))
    return token.SignedString(jwt.UnsafeAllowNoneSignatureType)
}
```

### Database Migration Strategy

#### GORM Auto-Migration Handles
**Files to modify**: `cmd/chessticize/migrate/migrate.go`

GORM will automatically handle:
```go
// Add to AutoMigrate call - no changes needed, just run migration
&models.User{}, // firebase_uid field will be added automatically
```

**What GORM auto-migration provides**:
- Add firebase_uid column as nullable VARCHAR(255)
- Create unique index on firebase_uid (via `uniqueIndex` GORM tag)
- Preserve existing data and constraints

#### Manual SQL Migration Required
**New file**: `migrations/003-firebase-auth-setup.sql`

Only operations GORM can't handle:
```sql
-- Remove unique constraint on email (GORM can't remove existing constraints)
ALTER TABLE users DROP CONSTRAINT IF EXISTS users_email_key;
ALTER TABLE users DROP CONSTRAINT IF EXISTS idx_users_email;

-- Add non-unique index on email for performance (if not already exists)
CREATE INDEX IF NOT EXISTS idx_users_email_non_unique ON users(email);
```

#### Backward Compatibility Considerations
- Keep password_hash field for existing users (GORM preserves existing fields)
- firebase_uid added as nullable field (GORM default)
- Existing JWT tokens continue to work unchanged
- Email field remains but loses uniqueness constraint

### Error Handling and Edge Cases

#### Token Exchange Error Scenarios
1. **Invalid Firebase Token**: Return 401 with specific error message
2. **Expired Token**: Return 401 with token expiry information
3. **Email Mismatch**: When linking, verify email matches
4. **Multiple Users with Same Email**: Handle gracefully with user choice
5. **JWKS Fetch Failure**: Fallback to cached keys, return 503 if unavailable

#### User Linking Edge Cases
1. **Email Not Verified in Firebase**: Require email verification before linking
2. **Existing User with Different Email**: Prevent automatic linking
3. **Firebase User Already Linked**: Return existing user information
4. **Database Constraints**: Handle unique constraint violations gracefully

### Performance Optimizations

#### Database Queries
```go
// Optimized user lookup for Firebase authentication
func (r *UserRepository) GetOrCreateFirebaseUser(ctx context.Context, firebaseUID, email string) (*User, bool, error) {
    // First try to find by Firebase UID
    user, err := r.GetByFirebaseUID(ctx, firebaseUID)
    if err == nil {
        // Update email from Firebase token (emails can change in Firebase)
        if user.Email != email {
            user.Email = email
            if err := r.Update(ctx, user); err != nil {
                return nil, false, fmt.Errorf("failed to update email: %w", err)
            }
        }
        return user, false, nil // existing user
    }
    if !errors.Is(err, gorm.ErrRecordNotFound) {
        return nil, false, err
    }

    // Try to find by email for linking
    if email != "" {
        user, err = r.GetByEmail(ctx, email)
        if err == nil {
            // Link Firebase UID to existing user and update email
            user.FirebaseUID = &firebaseUID
            user.Email = email // Ensure email is current
            if err := r.Update(ctx, user); err != nil {
                return nil, false, err
            }
            return user, false, nil // linked existing user
        }
        if !errors.Is(err, gorm.ErrRecordNotFound) {
            return nil, false, err
        }
    }

    // Create new user
    newUser := &User{
        Email:       email,
        FirebaseUID: &firebaseUID,
    }
    if err := r.Create(ctx, newUser); err != nil {
        return nil, false, err
    }

    return newUser, true, nil // new user created
}
```

#### JWKS Caching
- Cache JWKS responses for 1 hour (Firebase recommendation)
- Use in-memory cache with TTL
- Implement cache warming on startup
- Handle cache misses gracefully

### Security Considerations

#### Token Validation Checklist
- [ ] Verify token signature using JWKS
- [ ] Validate issuer matches Firebase
- [ ] Validate audience matches project ID
- [ ] Check token expiration
- [ ] Verify auth_time is reasonable
- [ ] Validate email_verified claim if required

#### Rate Limiting
```go
// Rate limit Firebase token exchange endpoint
func (h *AuthHandler) FirebaseTokenExchange(w http.ResponseWriter, r *http.Request) {
    // Implement rate limiting per IP
    if !h.rateLimiter.Allow(r.RemoteAddr) {
        http.Error(w, "Rate limit exceeded", http.StatusTooManyRequests)
        return
    }

    // ... rest of implementation
}
```

## Conclusion

This implementation plan provides a comprehensive approach to integrating Firebase Authentication while maintaining backward compatibility and supporting local development. The phased approach allows for gradual migration and thorough testing at each stage.

The key benefits of this approach:
- **Flexibility**: Configurable JWKS endpoints for different environments
- **Security**: Proper JWT verification with industry-standard libraries
- **Compatibility**: Maintains existing API contracts
- **Testability**: Full local development and testing support
- **Scalability**: Efficient caching and user lookup strategies
- **Robustness**: Comprehensive error handling and edge case management
