# Arrow-Duel Feature Design Document

## Overview

Arrow-Duel is a new puzzle mode where players are presented with a chess position and two candidate moves (shown as arrows): one is a blunder/mistake and the other is the correct move. The player must choose which move is correct.

### Key Characteristics
- **Client-side puzzle generation**: Server provides FEN and solution, client uses chess engine to determine the blunder move
- **Two puzzle sources**: Lichess puzzles (with new ELO type) and user puzzles (via puzzle queue)
- **Separate stats tracking**: Arrow-duel attempts don't affect regular puzzle stats
- **Different validation logic**: Success is determined by choosing the correct move over the blunder

## Data Model Changes

### 1. New Attempt Type Enum

```go
// AttemptType represents the type of puzzle attempt
type AttemptType string

const (
    AttemptTypeRegular   AttemptType = "regular"    // Normal puzzle solving (DEFAULT)
    AttemptTypeArrowDuel AttemptType = "arrow_duel" // Arrow-duel mode
)

// GetAttemptTypeOrDefault returns the attempt type or "regular" if empty/nil
func GetAttemptTypeOrDefault(attemptType *AttemptType) AttemptType {
    if attemptType == nil || *attemptType == "" {
        return AttemptTypeRegular
    }
    return *attemptType
}
```

### 2. Enhanced Event Data Structures

```go
// PuzzleEventData - Add attempt type and arrow-duel specific fields
type PuzzleEventData struct {
    PuzzleID        string      `json:"puzzle_id"`
    PuzzleType      PuzzleType  `json:"puzzle_type"`
    AttemptType     AttemptType `json:"attempt_type,omitempty"`     // NEW: defaults to "regular" if not specified
    Solved          bool        `json:"solved"`
    TimeSpent       int         `json:"time_spent"`
    MovesPlayed     []string    `json:"moves_played"`
    IsDisliked      *bool       `json:"is_disliked,omitempty"`

    // Arrow-duel specific fields (only used when AttemptType is "arrow_duel")
    CandidateMoves  []string    `json:"candidate_moves,omitempty"`  // [blunder_move, correct_move]
    ChosenMove      *string     `json:"chosen_move,omitempty"`      // Move chosen by player
}

// PuzzleSprintEventData - Add attempt type
type PuzzleSprintEventData struct {
    SprintID         string      `json:"sprint_id"`
    Duration         int         `json:"duration"`
    SuccededPuzzles  int         `json:"succeded_puzzles"`
    FailedPuzzles    int         `json:"failed_puzzles"`
    EloType          string      `json:"elo_type"`
    EloChange        *int        `json:"elo_change,omitempty"`
    AttemptType      AttemptType `json:"attempt_type,omitempty"`     // NEW: defaults to "regular" if not specified
}
```

### 3. New Stats Tables

```go
// UserPuzzleArrowDuelStats - Arrow-duel stats for user puzzles
type UserPuzzleArrowDuelStats struct {
    ID                 string     `gorm:"type:varchar(36);primary_key" json:"id"`
    UserID             string     `gorm:"type:varchar(36);not null;index:idx_user_puzzle_arrow_duel,unique" json:"user_id"`
    PuzzleID           string     `gorm:"type:varchar(255);not null;index:idx_user_puzzle_arrow_duel,unique" json:"puzzle_id"`
    Attempts           int        `gorm:"default:0" json:"attempts"`
    SuccessCount       int        `gorm:"default:0" json:"success_count"`
    TotalTime          int        `gorm:"default:0" json:"total_time"`
    AverageTime        float64    `gorm:"default:0" json:"average_time"`
    LastAttemptTime    time.Time  `gorm:"not null;default:current_timestamp" json:"last_attempt_time"`
    LastAttemptSuccess bool       `gorm:"default:false" json:"last_attempt_success"`
    IsDisliked         bool       `gorm:"default:false" json:"is_disliked"`
    DislikedAt         *time.Time `gorm:"" json:"disliked_at,omitempty"`
    CreatedAt          time.Time  `gorm:"not null;default:current_timestamp" json:"created_at"`
    UpdatedAt          time.Time  `gorm:"not null;default:current_timestamp" json:"updated_at"`

    // Relationships
    User   User   `gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE" json:"-"`
    Puzzle Puzzle `gorm:"foreignKey:PuzzleID;constraint:OnDelete:CASCADE" json:"-"`
}

// UserLichessPuzzleArrowDuelStats - Arrow-duel stats for Lichess puzzles
type UserLichessPuzzleArrowDuelStats struct {
    ID                 string     `gorm:"type:varchar(36);primary_key" json:"id"`
    UserID             string     `gorm:"type:varchar(36);not null;index:idx_user_lichess_arrow_duel,unique" json:"user_id"`
    LichessPuzzleID    string     `gorm:"type:varchar(10);not null;index:idx_user_lichess_arrow_duel,unique" json:"lichess_puzzle_id"`
    Attempts           int        `gorm:"default:0" json:"attempts"`
    SuccessCount       int        `gorm:"default:0" json:"success_count"`
    TotalTime          int        `gorm:"default:0" json:"total_time"`
    AverageTime        float64    `gorm:"default:0" json:"average_time"`
    LastAttemptTime    time.Time  `gorm:"not null;default:current_timestamp" json:"last_attempt_time"`
    LastAttemptSuccess bool       `gorm:"default:false" json:"last_attempt_success"`
    IsDisliked         bool       `gorm:"default:false" json:"is_disliked"`
    DislikedAt         *time.Time `gorm:"" json:"disliked_at,omitempty"`
    CreatedAt          time.Time  `gorm:"not null;default:current_timestamp" json:"created_at"`
    UpdatedAt          time.Time  `gorm:"not null;default:current_timestamp" json:"updated_at"`

    // Relationships
    User          User          `gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE" json:"-"`
    LichessPuzzle LichessPuzzle `gorm:"foreignKey:LichessPuzzleID;constraint:OnDelete:CASCADE" json:"-"`
}
```

## API Design

### 1. Enhanced Puzzle Attempt APIs

#### User Puzzle Attempts
**POST** `/api/v1/users/me/puzzles/{puzzleID}/attempts`

```json
{
  "attempt_type": "arrow_duel",
  "solved": true,
  "time_spent": 15,
  "moves": ["e2e4"],
  "candidate_moves": ["e2e3", "e2e4"],  // [blunder, correct]
  "chosen_move": "e2e4",
  "is_disliked": false
}
```

#### Lichess Puzzle Attempts  
**POST** `/api/v1/users/me/lichess-puzzles/{puzzleID}/attempts`

```json
{
  "attempt_type": "arrow_duel", 
  "solved": true,
  "time_spent": 12,
  "moves": ["Qh5"],
  "candidate_moves": ["Qh4", "Qh5"],  // [blunder, correct]
  "chosen_move": "Qh5",
  "is_disliked": false
}
```

### 2. Enhanced Sprint APIs

#### Start Sprint (Enhanced)
**POST** `/api/v1/users/me/sprint/start`

```json
{
  "elo_type": "arrowduel 5/30"  // NEW: Arrow-duel ELO type
}
```

#### Submit Sprint Results (Enhanced)
**POST** `/api/v1/users/me/sprint/{sessionId}/results`

```json
{
  "results": [
    {
      "puzzle_id": "00008",
      "sequence_in_sprint": 1,
      "user_moves": ["Qh5"],
      "was_correct": true,
      "time_taken_ms": 12000,
      "attempted_at": "2023-10-01T12:00:00Z",
      "attempt_type": "arrow_duel",        // NEW
      "candidate_moves": ["Qh4", "Qh5"],  // NEW
      "chosen_move": "Qh5"                // NEW
    }
  ]
}
```

### 3. Puzzle Queue APIs (No Changes Required)

#### Current Puzzle Queue Status
The puzzle queue **already supports arrow-duel** perfectly! Here's what's currently queued:

**Currently Queued Themes** (all support arrow-duel):
- `opponent_mistake_missed` → `mistake_by: "opponent"`
- `opponent_blunder_missed` → `mistake_by: "opponent"`
- `own_mistake_punished` → `mistake_by: "own"`
- `own_mistake_escaped` → `mistake_by: "own"`
- `own_blunder_punished` → `mistake_by: "own"`
- `own_blunder_escaped` → `mistake_by: "own"`

**Arrow-Duel Logic**:
- Client uses `mistake_by` field to determine rendering mode
- `mistake_by: "opponent"` → Render as arrow-duel (choose between blunder and correct move)
- `mistake_by: "own"` → Render as arrow-duel (choose between blunder and correct move)
- All queued themes represent mistake scenarios perfect for arrow-duel

#### Get Due Puzzles (Current API - No Changes)
**GET** `/api/v1/users/me/puzzle-queue/due?mistake_by=opponent&limit=10`

```json
{
  "puzzles": [
    {
      "queue_id": "queue-entry-123",
      "puzzle_id": "puzzle-456",
      "puzzle_theme": "opponent_mistake_missed",
      "mistake_by": "opponent",
      "due_at": "2023-10-01T12:00:00Z",
      "attempts_since_added": 2,
      "consecutive_correct": 1,
      "puzzle_data": {
        "id": "puzzle-456",
        "fen": "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
        "moves": ["e2e4", "e7e5"],
        "theme": "opponent_mistake_missed",
        "user_color": "white",
        "puzzle_color": "white"
      }
    }
  ],
  "total_due": 5
}
```

## ELO Type Extensions

### New Arrow-Duel ELO Types

The existing ELO type system will be extended to support arrow-duel variants:

```
Format: "arrowduel {duration}/{per_puzzle_time}"
Examples:
- "arrowduel 5/30"  - 5 minute sprint, 30 seconds per puzzle
- "arrowduel 10/20" - 10 minute sprint, 20 seconds per puzzle  
- "arrowduel 15/60" - 15 minute sprint, 60 seconds per puzzle
```

### ELO Type Service Updates

```go
// Enhanced EloTypeConfig
type EloTypeConfig struct {
    Theme            string      // empty for "mixed", or specific lichess theme
    DurationSeconds  int         // total sprint duration in seconds
    PerPuzzleSeconds int         // time per puzzle in seconds
    TargetPuzzles    int         // calculated target number of puzzles
    MaxMistakes      int         // maximum allowed mistakes
    AttemptType      AttemptType // NEW: regular or arrow_duel
}
```

## Database Schema Changes

### 1. Auto-Migration Models
- `UserPuzzleArrowDuelStats`
- `UserLichessPuzzleArrowDuelStats`

### 2. Enhanced Trigger Functions

The existing trigger system will be updated to handle arrow-duel attempts:

```sql
-- Enhanced trigger_update_daily_stats() function
-- Will check attempt_type and route to appropriate stats tables
-- Arrow-duel attempts update separate stats tables
-- Regular attempts continue to use existing stats tables
```

## Repository Interface Changes

### 1. New Repository Interfaces

```go
// IUserPuzzleArrowDuelStatsRepository
type IUserPuzzleArrowDuelStatsRepository interface {
    GetByUserIDAndPuzzleID(ctx context.Context, userID, puzzleID string) (*models.UserPuzzleArrowDuelStats, error)
    Create(ctx context.Context, stats *models.UserPuzzleArrowDuelStats) error
    Update(ctx context.Context, stats *models.UserPuzzleArrowDuelStats) error
    ListByUserID(ctx context.Context, userID string, filter repository.StatsFilter, offset, limit int) ([]models.UserPuzzleArrowDuelStats, int64, error)
}

// IUserLichessPuzzleArrowDuelStatsRepository  
type IUserLichessPuzzleArrowDuelStatsRepository interface {
    GetByUserIDAndPuzzleID(ctx context.Context, userID, puzzleID string) (*models.UserLichessPuzzleArrowDuelStats, error)
    Create(ctx context.Context, stats *models.UserLichessPuzzleArrowDuelStats) error
    Update(ctx context.Context, stats *models.UserLichessPuzzleArrowDuelStats) error
    ListByUserID(ctx context.Context, userID string, filter repository.StatsFilter, offset, limit int) ([]models.UserLichessPuzzleArrowDuelStats, int64, error)
}
```

### 2. Existing Repositories (No Changes Required)

The existing puzzle queue repository already supports arrow-duel perfectly:

```go
// IPuzzleQueueRepository - Current interface works as-is
type IPuzzleQueueRepository interface {
    GetDuePuzzles(ctx context.Context, userID string, mistakeBy *string, limit int) ([]models.PuzzleQueueItem, error)
    // ... other existing methods ...
}

// All queued puzzles support arrow-duel based on mistake_by field:
// - mistake_by: "opponent" → Client renders as arrow-duel
// - mistake_by: "own" → Client renders as arrow-duel
```

## Service Layer Changes

### 1. Enhanced Event Service

```go
// IEventService - Enhanced methods
type IEventService interface {
    // ... existing methods ...
    CreatePuzzleEvent(ctx context.Context, userID string, data models.PuzzleEventData) error // Enhanced with AttemptType
    CreatePuzzleSprintEndEvent(ctx context.Context, userID string, data models.PuzzleSprintEventData) error // Enhanced with AttemptType
}
```

### 2. Enhanced ELO Type Service

```go
// IEloTypeService - Enhanced validation
type IEloTypeService interface {
    // ... existing methods ...
    GetEloTypeConfig(eloType string) (*EloTypeConfig, error) // Enhanced to handle arrow-duel types
    IsArrowDuelEloType(eloType string) bool // NEW: Check if ELO type is arrow-duel
}
```

## Implementation Notes

### 1. Backward Compatibility
- All existing APIs remain unchanged for regular puzzle attempts
- New fields are optional and backward compatible
- Existing stats tables and triggers continue to work for regular attempts

### 2. Validation Logic
- Arrow-duel attempts validate that `chosen_move` matches one of the `candidate_moves`
- Success is determined by choosing the correct move (second element in `candidate_moves`)
- Regular validation logic remains unchanged for non-arrow-duel attempts

### 3. Stats Separation
- Arrow-duel attempts create separate stats records
- Daily stats may need enhancement to track arrow-duel vs regular attempts separately
- ELO calculations remain the same but use arrow-duel specific ELO types

### 4. Client-Server Interaction
- Server provides FEN and solution moves
- Client generates blunder move using chess engine
- Client presents both moves as arrows to user
- Client reports back both candidate moves and user's choice

This design maintains clean separation between regular and arrow-duel puzzle modes while reusing existing infrastructure where possible.

## Database Migration Strategy

### 1. GORM Auto-Migration
The new stats tables will be created using GORM's auto-migration feature:

```go
// In main.go or migration setup
db.AutoMigrate(
    &models.UserPuzzleArrowDuelStats{},
    &models.UserLichessPuzzleArrowDuelStats{},
)
```

### 2. Enhanced Trigger Functions
Update existing SQL triggers to handle arrow-duel attempts:

```sql
-- Update trigger_update_daily_stats() function
CREATE OR REPLACE FUNCTION trigger_update_daily_stats()
RETURNS TRIGGER AS $$
DECLARE
    puzzle_type_val text;
    attempt_type_val text;
    -- ... existing variables ...
BEGIN
    IF NEW.event_type = 'PUZZLE' THEN
        -- Extract attempt_type from event_data
        attempt_type_val := NEW.event_data->>'attempt_type';
        IF attempt_type_val IS NULL THEN
            attempt_type_val := 'regular'; -- Default for backward compatibility
        END IF;

        -- Route to appropriate stats update based on attempt_type
        IF attempt_type_val = 'arrow_duel' THEN
            -- Update arrow-duel specific stats
            IF puzzle_type_val = 'lichess' THEN
                PERFORM update_user_lichess_puzzle_arrow_duel_stats(...);
            ELSE
                PERFORM update_user_puzzle_arrow_duel_stats(...);
            END IF;
        ELSE
            -- Existing regular stats update logic
            IF puzzle_type_val = 'lichess' THEN
                PERFORM update_user_lichess_puzzle_stats(...);
            ELSE
                PERFORM update_user_puzzle_stats(...);
            END IF;
        END IF;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

## API Request/Response Examples

### 1. Enhanced Sprint Flow

#### Start Arrow-Duel Sprint
```bash
curl -X POST /api/v1/users/me/sprint/start \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"elo_type": "arrowduel 5/30"}'
```

Response:
```json
{
  "session_id": "sprint-123",
  "user_elo": {
    "rating": 1500,
    "rating_deviation": 200.0,
    "is_provisional": false
  },
  "target_puzzles": 10,
  "time_limit_seconds": 300,
  "max_mistakes": 2,
  "attempt_type": "arrow_duel"
}
```

#### Get Next Puzzles (Arrow-Duel)
```bash
curl -X POST /api/v1/users/me/sprint/sprint-123/next-puzzles \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"count": 3}'
```

Response:
```json
{
  "puzzles": [
    {
      "puzzle_id": "00008",
      "fen": "r1bqkb1r/pppp1ppp/2n2n2/4p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 4 4",
      "solution_moves": ["Bxf7+", "Ke7", "Nd5"],
      "rating": 1200,
      "themes": ["fork", "tactics"],
      "sequence_in_sprint": 1,
      "attempt_type": "arrow_duel"
    }
  ]
}
```

### 2. Enhanced Puzzle Queue Flow

#### Get Due Puzzles (Already Supports Arrow-Duel)
```bash
curl -X GET "/api/v1/users/me/puzzle-queue/due?mistake_by=opponent&limit=5" \
  -H "Authorization: Bearer $TOKEN"
```

Response (Current API - No Changes):
```json
{
  "puzzles": [
    {
      "queue_id": "queue-entry-123",
      "puzzle_id": "puzzle-456",
      "puzzle_theme": "opponent_mistake_missed",
      "mistake_by": "opponent",
      "due_at": "2023-10-01T12:00:00Z",
      "attempts_since_added": 2,
      "consecutive_correct": 1,
      "puzzle_data": {
        "id": "puzzle-456",
        "fen": "rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 2",
        "moves": ["d2d4", "exd4", "Qxd4"],
        "theme": "opponent_mistake_missed",
        "puzzle_color": "white",
        "user_color": "white"
      }
    }
  ],
  "total_due": 5
}
```

**Client Logic**: Use `mistake_by` field to determine rendering:
- `mistake_by: "opponent"` → Render as arrow-duel mode
- `mistake_by: "own"` → Render as arrow-duel mode

## Implementation Phases

### Phase 1: Core Data Models and Database
1. Create new AttemptType enum
2. Add new stats table models
3. Run GORM auto-migration
4. Update event data structures
5. Create new repository interfaces and implementations

### Phase 2: Enhanced APIs
1. Update puzzle attempt APIs to accept arrow-duel parameters
2. Enhance sprint APIs for arrow-duel ELO types
3. Update puzzle queue APIs to indicate arrow-duel support
4. Add validation for arrow-duel specific fields

### Phase 3: Service Layer Updates
1. Update ELO type service for arrow-duel validation
2. Enhance event service for arrow-duel events
3. Update puzzle service for arrow-duel attempt processing
4. Update sprint service for arrow-duel sprint handling

### Phase 4: Database Triggers and Stats
1. Update SQL trigger functions
2. Create new stats update functions for arrow-duel
3. Test stats separation between regular and arrow-duel attempts
4. Verify ELO calculations work correctly

### Phase 5: Testing and Documentation
1. **Unit Tests**: Write comprehensive unit tests for new functionality
   - **Preserve existing tests**: Keep all existing unit tests untouched to maintain regression protection
   - **Add new test files**: Create separate test files for arrow-duel functionality
   - **Test backward compatibility**: Ensure existing APIs work with missing attempt_type
2. **E2E Tests**: Create comprehensive e2e tests for arrow-duel flows
   - **Preserve existing e2e tests**: All existing e2e tests must continue to pass
   - **Add new e2e test files**: Create separate e2e test files for arrow-duel scenarios
   - **Test both puzzle types**: Cover both Lichess puzzles (sprints) and user puzzles (queue)
3. **Frontend Integration Documentation**: Create detailed integration guide for React frontend
4. **API Documentation Updates**: Update existing API docs with new optional fields
5. **Performance Testing**: Verify new stats tables don't impact existing queries

## Error Handling

### 1. Validation Errors
- Invalid attempt_type: Return 400 with clear error message
- Missing candidate_moves for arrow-duel: Return 400
- Invalid chosen_move (not in candidate_moves): Return 400
- Invalid arrow-duel ELO type format: Return 400

### 2. Backward Compatibility
- **Missing attempt_type defaults to "regular"** - This ensures all existing API calls continue working
- Existing APIs continue to work without changes
- New fields are optional and ignored for regular attempts
- Event processing checks for attempt_type and defaults to "regular" if not present

## Performance Considerations

### 1. Database Indexes
```sql
-- Indexes for new stats tables
CREATE INDEX idx_user_puzzle_arrow_duel_user_id ON user_puzzle_arrow_duel_stats(user_id);
CREATE INDEX idx_user_puzzle_arrow_duel_last_attempt ON user_puzzle_arrow_duel_stats(user_id, last_attempt_time);
CREATE INDEX idx_user_lichess_arrow_duel_user_id ON user_lichess_puzzle_arrow_duel_stats(user_id);
CREATE INDEX idx_user_lichess_arrow_duel_last_attempt ON user_lichess_puzzle_arrow_duel_stats(user_id, last_attempt_time);
```

### 2. Query Optimization
- Separate stats tables prevent mixing regular and arrow-duel data
- Existing queries remain unaffected by new tables
- Arrow-duel queries are isolated and optimized independently

### 3. Event Processing
- Enhanced triggers handle both attempt types efficiently
- Minimal overhead for regular attempts (single attempt_type check)
- Arrow-duel processing is additive, not replacement

## Key Design Decisions Based on Feedback

### 1. ✅ Backward Compatibility with AttemptType Default
- **AttemptType defaults to "regular"** when not specified in API requests
- All existing API calls continue working without any changes
- Event processing includes fallback logic: `attempt_type_val := NEW.event_data->>'attempt_type'; IF attempt_type_val IS NULL THEN attempt_type_val := 'regular';`

### 2. ✅ Puzzle Queue Already Perfect for Arrow-Duel
- **No API changes needed** - existing puzzle queue APIs work as-is
- **All queued themes support arrow-duel**: The 6 currently queued themes (`opponent_mistake_missed`, `opponent_blunder_missed`, `own_mistake_punished`, `own_mistake_escaped`, `own_blunder_punished`, `own_blunder_escaped`) are perfect for arrow-duel mode
- **Client-side decision**: Client uses `mistake_by` field to determine rendering mode
- **Removed unnecessary field**: No `supports_arrow_duel` field needed

### 3. ✅ Clean Implementation Strategy
- **Minimal changes**: Most complexity is in new stats tables and enhanced event processing
- **Reuse existing infrastructure**: Puzzle queue, ELO system, event system all work with minimal changes
- **Performance optimized**: Separate stats tables prevent any impact on existing queries

This comprehensive design ensures the arrow-duel feature integrates seamlessly with the existing codebase while maintaining performance and backward compatibility.
