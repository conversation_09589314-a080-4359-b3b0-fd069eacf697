# Registration and Invitation Code Guide

This document provides detailed information about the registration process and invitation code system in the Chessticize Server.

## Overview

Chessticize uses an invitation-based registration system to control user access. There are two ways to register:

1. **Invitation Code Registration**: Regular users can register using a valid invitation code.
2. **Admin Registration**: Administrators can directly register new users without an invitation code.

## Invitation Codes

Invitation codes are unique tokens that allow new users to register. They have the following properties:

- Each code can only be used once
- Codes can optionally have an expiration date
- Codes are created by administrators
- Default expiration is set to 1 year (8760 hours)

### Creating Invitation Codes

Invitation codes can be created in two ways:

1. **Via Admin API**: Administrators can create invitation codes using the API.
2. **Via GitHub Action**: Invitation codes can be generated using a GitHub Action workflow.

#### Admin API

**Endpoint**: `POST /api/v1/auth/invitation-codes`

**Headers**:
- `Content-Type: application/json`
- `Authorization: Bearer <admin_token>`

**Request Body**:
```json
{
  "expires_in_hours": 8760  // Optional: defaults to 8760 (1 year)
}
```

**Response** (201 Created):
```json
{
  "id": "123e4567-e89b-12d3-a456-426614174000",
  "code": "abcdefghijklmnopqrstuvwxyz123456789ABCDEFG",
  "created_at": "2023-10-01T12:00:00Z",
  "expires_at": "2024-10-01T12:00:00Z",
  "used": false
}
```

#### GitHub Action

To generate an invitation code using GitHub Actions:

1. Go to the repository on GitHub
2. Navigate to Actions > "Generate Invitation Code"
3. Click "Run workflow"
4. Optionally specify an expiration time in hours (defaults to 8760 hours / 1 year)
5. Click "Run workflow" again

The invitation code will be displayed in the workflow output.

### Listing Invitation Codes

Administrators can list all invitation codes:

**Endpoint**: `GET /api/v1/auth/invitation-codes`

**Headers**:
- `Authorization: Bearer <admin_token>`

**Query Parameters**:
- `offset`: Pagination offset (default: 0)
- `limit`: Pagination limit (1-100, default: 50)
- `valid`: If true, only return valid (unused and not expired) codes

**Response**:
```json
{
  "codes": [
    {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "code": "abcdefghijklmnopqrstuvwxyz123456789ABCDEFG",
      "created_at": "2023-10-01T12:00:00Z",
      "expires_at": "2024-10-01T12:00:00Z",
      "used": false
    },
    {
      "id": "123e4567-e89b-12d3-a456-426614174001",
      "code": "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789abcdefg",
      "created_at": "2023-09-01T12:00:00Z",
      "expires_at": null,
      "used": true
    }
  ],
  "total_count": 2
}
```

## Registration

### Public Registration

**Endpoint**: `POST /api/v1/auth/register`

**Headers**:
- `Content-Type: application/json`

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

**Response** (201 Created):
```json
{
  "id": "123e4567-e89b-12d3-a456-426614174000",
  "email": "<EMAIL>",
  "token": "eyJhbGciOiJIUzI1NiIsInR5...",
  "session_token": "abcdefghijklmnopqrstuvwxyz123456789ABCDEFG"
}
```

**Error Responses**:

- **400 Bad Request**: Invalid request body, missing fields, or password too short/long
- **409 Conflict**: Email already registered

### Register with Invitation Code

**Endpoint**: `POST /api/v1/auth/register-with-invitation`

**Headers**:
- `Content-Type: application/json`

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "invitation_code": "abcdefghijklmnopqrstuvwxyz123456789ABCDEFG"
}
```

**Response** (201 Created):
```json
{
  "id": "123e4567-e89b-12d3-a456-426614174000",
  "email": "<EMAIL>",
  "token": "eyJhbGciOiJIUzI1NiIsInR5...",
  "session_token": "abcdefghijklmnopqrstuvwxyz123456789ABCDEFG"
}
```

**Error Responses**:

- **400 Bad Request**: Invalid request body, missing fields, or password too short/long
- **400 Bad Request**: Invalid invitation code, expired code, or already used code
- **409 Conflict**: Email already registered

### Admin Registration

**Endpoint**: `POST /api/v1/auth/admin/register`

**Headers**:
- `Content-Type: application/json`
- `Authorization: Bearer <admin_token>`

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

**Response** (201 Created):
```json
{
  "id": "123e4567-e89b-12d3-a456-426614174000",
  "email": "<EMAIL>",
  "registered_at": "2023-10-01T12:00:00Z",
  "updated_at": "2023-10-01T12:00:00Z",
  "last_sign_in_at": null
}
```

**Error Responses**:

- **400 Bad Request**: Invalid request body, missing fields, or password too short/long
- **401 Unauthorized**: Missing or invalid token
- **403 Forbidden**: Token is valid but user is not an admin
- **409 Conflict**: Email already registered

## Password Requirements

Passwords must meet the following requirements:

- Minimum length: 6 characters
- Maximum length: 40 characters

## Best Practices

1. **Invitation Code Security**: Treat invitation codes as sensitive information. They should be shared securely with intended recipients.

2. **Expiration Times**: Consider setting appropriate expiration times for invitation codes based on your security requirements.

3. **Admin Tokens**: Admin tokens should be kept secure and only used by authorized personnel.

4. **Password Strength**: Encourage users to use strong passwords that include a mix of uppercase and lowercase letters, numbers, and special characters.

## Example Usage with cURL

### Create Invitation Code (Admin)

```bash
curl -X POST \
  https://chessticize-server-9ddca5bcf137.herokuapp.com/api/v1/auth/invitation-codes \
  -H 'Authorization: Bearer YOUR_ADMIN_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "expires_in_hours": 24
  }'
```

### Public Registration

```bash
curl -X POST \
  https://chessticize-server-9ddca5bcf137.herokuapp.com/api/v1/auth/register \
  -H 'Content-Type: application/json' \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePassword123!"
  }'
```

### Register with Invitation Code

```bash
curl -X POST \
  https://chessticize-server-9ddca5bcf137.herokuapp.com/api/v1/auth/register-with-invitation \
  -H 'Content-Type: application/json' \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePassword123!",
    "invitation_code": "YOUR_INVITATION_CODE"
  }'
```

### Admin Register

```bash
curl -X POST \
  https://chessticize-server-9ddca5bcf137.herokuapp.com/api/v1/auth/admin/register \
  -H 'Authorization: Bearer YOUR_ADMIN_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePassword123!"
  }'
```

### List Invitation Codes (Admin)

```bash
curl -X GET \
  'https://chessticize-server-9ddca5bcf137.herokuapp.com/api/v1/auth/invitation-codes?valid=true&limit=10' \
  -H 'Authorization: Bearer YOUR_ADMIN_TOKEN'
```
