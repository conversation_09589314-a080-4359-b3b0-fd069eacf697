# Puzzle Selection Enhancement for Low ELO Users

## Problem

Users with very low ELO ratings (e.g., below 1000) were experiencing issues where the `next-puzzle` API call would return 0 puzzles during sprint sessions. This happened because the original puzzle selection logic only tried two search strategies:

1. ±100 rating points with themes
2. ±200 rating points with themes

For users with very low ratings, especially when combined with specific theme requirements, there might not be enough puzzles in these narrow ranges.

## Solution

Enhanced the `SelectPuzzlesForUser` method in `internal/service/puzzle_service.go` to implement a progressive fallback strategy with 8 different search approaches:

### Search Strategies (in order of preference)

1. **±100 rating with themes** - Preferred strategy for optimal difficulty matching
2. **±200 rating with themes** - Slightly wider range while maintaining theme preference
3. **±400 rating with themes** - Much wider range with themes
4. **±600 rating with themes** - Very wide range with themes
5. **±100 rating without themes** - Narrow range but removes theme restrictions
6. **±200 rating without themes** - Wider range without themes
7. **±400 rating without themes** - Much wider range without themes
8. **Full range (800-2800) without themes** - Ultimate fallback to ensure puzzles are found

### Key Features

- **Progressive Expansion**: Each strategy progressively widens the search criteria
- **Theme Flexibility**: Removes theme restrictions when necessary to find puzzles
- **Minimum Rating Floor**: Maintains a minimum rating of 800 to avoid extremely easy puzzles
- **Comprehensive Logging**: Detailed debug and warning logs for troubleshooting
- **Early Termination**: Stops searching once enough puzzles are found

### Benefits

1. **Guaranteed Puzzle Availability**: Users will always get puzzles, even with very low ratings
2. **Optimal Difficulty Matching**: Still prioritizes puzzles close to user's rating when possible
3. **Theme Preference**: Maintains theme preferences when puzzles are available
4. **Performance**: Stops searching as soon as enough puzzles are found
5. **Observability**: Comprehensive logging for monitoring and debugging

## Implementation Details

### Code Changes

- Enhanced `SelectPuzzlesForUser` method in `internal/service/puzzle_service.go`
- Added progressive fallback strategy with 8 search approaches
- Added comprehensive logging for debugging and monitoring
- Added helper function `max()` for rating calculations

### Testing

- Created comprehensive integration tests in `internal/service/puzzle_selection_integration_test.go`
- Tests cover all fallback scenarios including:
  - Normal rating users (immediate success)
  - Low rating users (fallback to no themes)
  - New users with no ELO (default rating usage)
  - Extreme cases (ultimate fallback to full range)

### Backward Compatibility

- Fully backward compatible with existing API
- No changes to API endpoints or response formats
- Existing behavior preserved for users with sufficient puzzles in preferred ranges

## Usage

The enhancement is automatically applied to all puzzle selection operations, including:

- Sprint puzzle generation via `POST /api/v1/sprint/{sessionId}/next-puzzles`
- Any other puzzle selection that uses the `SelectPuzzlesForUser` method

No configuration changes or API modifications are required.

## Monitoring

The enhancement includes detailed logging that can be used to monitor:

- Which search strategies are being used
- How often fallback strategies are needed
- Performance characteristics of puzzle selection

Log levels:
- **Debug**: Successful puzzle selection with strategy details
- **Warn**: Failed strategy attempts and ultimate fallback scenarios
- **Error**: Complete failure to find puzzles (should be extremely rare)

## Example Scenarios

### Scenario 1: Low ELO User with Theme Preference
- User ELO: 900, Theme: "fork"
- Strategies 1-4 fail (no fork puzzles in rating ranges)
- Strategy 5 succeeds (±100 rating, no theme restriction)
- Result: User gets puzzles around their rating level

### Scenario 2: Very Low ELO User
- User ELO: 800, Theme: "endgame"  
- Strategies 1-7 fail (insufficient puzzles)
- Strategy 8 succeeds (full range, no themes)
- Result: User gets puzzles from broader difficulty range

### Scenario 3: Normal ELO User
- User ELO: 1500, Theme: "tactics"
- Strategy 1 succeeds immediately
- Result: User gets optimal puzzles (1400-1600 rating, tactics theme)
