package e2e

import (
	"fmt"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestE2EGraphQL is a true end-to-end test that tests the GraphQL API by making HTTP requests to the running server
func TestE2EGraphQL(t *testing.T) {
	// Skip unless explicitly enabled
	if os.Getenv("RUN_E2E") != "true" {
		t.Skip("Skipping E2E test (set RUN_E2E=true to run)")
	}

	// Generate an admin JWT token for all requests
	adminToken := generateAdminToken(t, "test-admin-id", "<EMAIL>")
	require.NotEmpty(t, adminToken, "Admin token should not be empty")

	// Create a new user with admin token
	userEmail := fmt.Sprintf("<EMAIL>", time.Now().Unix())
	userPassword := "TestPassword123!"
	userID := createUser(t, adminToken, userEmail, userPassword)
	require.NotEmpty(t, userID, "User ID should not be empty")

	// Generate a user token for the created user
	userToken := generateUserToken(t, userID, userEmail)
	require.NotEmpty(t, userToken, "User token should not be empty")

	// Create test games with different attributes
	game1 := createGameWithPGN(t, adminToken, userID, "1. e4 e5 2. Nf3 Nc6 3. Bb5 a6")
	game2 := createGameWithPGN(t, adminToken, userID, "1. d4 d5 2. c4 e6 3. Nc3 Nf6")
	game3 := createGameWithPGN(t, adminToken, userID, "1. e4 c5 2. Nf3 d6 3. d4 cxd4")

	// Update game2 to have black as user color
	updateGameUserColor(t, adminToken, game2, "black")

	// Create test puzzles with different attributes
	puzzle1 := createPuzzleWithAttributes(t, adminToken, game1, userID, "white", "black", "opponent_blunder_missed", 5, 0, 100, []string{"opening", "fork"})
	puzzle2 := createPuzzleWithAttributes(t, adminToken, game2, userID, "black", "white", "opponent_mistake_caught", 15, -100, 150, []string{"middlegame", "pin"})
	puzzle3 := createPuzzleWithAttributes(t, adminToken, game3, userID, "white", "black", "opponent_blunder_caught", 25, -200, 300, []string{"endgame", "fork"})

	// Run simplified GraphQL test cases
	t.Run("Basic GraphQL API functionality", func(t *testing.T) {
		// Test 1: Simple query to get all games
		t.Run("Query all games", func(t *testing.T) {
			query := `
			query {
				myGames(pagination: {offset: 0, limit: 10}) {
					edges {
						node {
							id
							platform
							user_color
						}
						cursor
					}
					total_count
				}
			}`

			result := executeGraphQLQuery(t, userToken, query)

			// Verify the response structure
			data, ok := result["data"].(map[string]interface{})
			require.True(t, ok, "Response should contain 'data' field")

			myGames, ok := data["myGames"].(map[string]interface{})
			require.True(t, ok, "Data should contain 'myGames' field")

			totalCount, ok := myGames["total_count"].(float64)
			require.True(t, ok, "myGames should contain 'total_count' field")
			assert.Equal(t, float64(3), totalCount, "Should find all three games")
		})

		// Test 2: Simple query to get all puzzles
		t.Run("Query all puzzles", func(t *testing.T) {
			query := `
			query {
				myPuzzles(pagination: {offset: 0, limit: 10}) {
					edges {
						node {
							id
							theme
							user_color
						}
						cursor
					}
					total_count
				}
			}`

			result := executeGraphQLQuery(t, userToken, query)

			// Verify the response structure
			data, ok := result["data"].(map[string]interface{})
			require.True(t, ok, "Response should contain 'data' field")

			myPuzzles, ok := data["myPuzzles"].(map[string]interface{})
			require.True(t, ok, "Data should contain 'myPuzzles' field")

			totalCount, ok := myPuzzles["total_count"].(float64)
			require.True(t, ok, "myPuzzles should contain 'total_count' field")
			assert.Equal(t, float64(3), totalCount, "Should find all three puzzles")
		})

		// Test 3: Query puzzles with game fields
		t.Run("Query puzzles with game fields", func(t *testing.T) {
			query := `
			query {
				myPuzzles(pagination: {offset: 0, limit: 10}) {
					edges {
						node {
							id
							theme
							game {
								id
								platform
							}
						}
					}
					total_count
				}
			}`

			result := executeGraphQLQuery(t, userToken, query)

			// Verify the response structure
			data, ok := result["data"].(map[string]interface{})
			require.True(t, ok, "Response should contain 'data' field")

			myPuzzles, ok := data["myPuzzles"].(map[string]interface{})
			require.True(t, ok, "Data should contain 'myPuzzles' field")

			edges, ok := myPuzzles["edges"].([]interface{})
			require.True(t, ok, "myPuzzles should contain 'edges' field")
			require.Greater(t, len(edges), 0, "Should have at least one puzzle")

			// Check game data is present
			edge := edges[0].(map[string]interface{})
			node := edge["node"].(map[string]interface{})
			game, ok := node["game"].(map[string]interface{})
			require.True(t, ok, "Puzzle should have game field")
			assert.NotNil(t, game)
		})

		// Test 4: PGN optimization
		t.Run("PGN optimization", func(t *testing.T) {
			// Query games without PGN
			queryWithoutPGN := `
			query {
				myGames(pagination: {offset: 0, limit: 1}) {
					edges {
						node {
							id
							platform
						}
					}
				}
			}`

			// Query games with PGN
			queryWithPGN := `
			query {
				myGames(pagination: {offset: 0, limit: 1}) {
					edges {
						node {
							id
							platform
							pgn
						}
					}
				}
			}`

			// Execute both queries and verify PGN is present in second result
			_ = executeGraphQLQuery(t, userToken, queryWithoutPGN) // We don't need to check the first result
			result2 := executeGraphQLQuery(t, userToken, queryWithPGN)

			// Verify PGN is present in second result
			data2 := result2["data"].(map[string]interface{})
			myGames2 := data2["myGames"].(map[string]interface{})
			edges2 := myGames2["edges"].([]interface{})
			if len(edges2) > 0 {
				node2 := edges2[0].(map[string]interface{})["node"].(map[string]interface{})
				assert.Contains(t, node2, "pgn")
			}
		})

		// Test 5: Basic filtering by enum
		t.Run("Filter puzzles by theme", func(t *testing.T) {
			query := `
			query {
				myPuzzles(filter: {themes: [OPPONENT_BLUNDER_MISSED]}) {
					edges {
						node {
							id
							theme
						}
					}
					total_count
				}
			}`

			result := executeGraphQLQuery(t, userToken, query)

			// Verify the response structure
			data, ok := result["data"].(map[string]interface{})
			require.True(t, ok, "Response should contain 'data' field")

			myPuzzles, ok := data["myPuzzles"].(map[string]interface{})
			require.True(t, ok, "Data should contain 'myPuzzles' field")

			totalCount, ok := myPuzzles["total_count"].(float64)
			require.True(t, ok, "myPuzzles should contain 'total_count' field")
			assert.Equal(t, float64(1), totalCount, "Should find exactly one puzzle")

			edges, ok := myPuzzles["edges"].([]interface{})
			require.True(t, ok, "myPuzzles should contain 'edges' field")
			require.Len(t, edges, 1, "Should have one edge")

			edge := edges[0].(map[string]interface{})
			node := edge["node"].(map[string]interface{})
			assert.Equal(t, puzzle1, node["id"], "Should find puzzle1")
			assert.Equal(t, "opponent_blunder_missed", node["theme"], "Theme should match")
		})

		// Test 4: Get game stats
		t.Run("Get game stats", func(t *testing.T) {
			query := `
			query {
				myGameStats {
					platform_counts {
						platform
						count
					}
					user_color_counts {
						color
						count
					}
					result_counts {
						result
						count
					}
					time_control_counts {
						time_control
						count
					}
					rated_counts {
						rated
						count
					}
					average_opponent_rating
					total_count
				}
			}`

			result := executeGraphQLQuery(t, userToken, query)

			// Verify the response structure
			data, ok := result["data"].(map[string]interface{})
			require.True(t, ok, "Response should contain 'data' field")

			stats, ok := data["myGameStats"].(map[string]interface{})
			require.True(t, ok, "Data should contain 'myGameStats' field")

			// Check total count
			totalCount, ok := stats["total_count"].(float64)
			require.True(t, ok, "myGameStats should contain 'total_count' field")
			assert.Equal(t, float64(3), totalCount, "Should find all three games")
		})

		// Test 5: Filter game stats
		t.Run("Filter game stats", func(t *testing.T) {
			query := `
			query {
				myGameStats(filter: {user_color: WHITE}) {
					user_color_counts {
						color
						count
					}
					total_count
				}
			}`

			result := executeGraphQLQuery(t, userToken, query)

			// Verify the response structure
			data, ok := result["data"].(map[string]interface{})
			require.True(t, ok, "Response should contain 'data' field")

			stats, ok := data["myGameStats"].(map[string]interface{})
			require.True(t, ok, "Data should contain 'myGameStats' field")

			// Check total count
			totalCount, ok := stats["total_count"].(float64)
			require.True(t, ok, "myGameStats should contain 'total_count' field")
			assert.Equal(t, float64(3), totalCount, "Should find all three games")
		})
	})

	// Clean up
	deletePuzzle(t, adminToken, puzzle1)
	deletePuzzle(t, adminToken, puzzle2)
	deletePuzzle(t, adminToken, puzzle3)
	deleteGame(t, adminToken, game1)
	deleteGame(t, adminToken, game2)
	deleteGame(t, adminToken, game3)
}
