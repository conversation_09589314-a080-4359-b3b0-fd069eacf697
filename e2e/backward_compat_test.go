package e2e

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestBackwardCompatibility_MissingAttemptType(t *testing.T) {
	// Generate an admin JWT token for setup
	adminToken := generateAdminToken(t, "test-admin-id", "<EMAIL>")
	require.NotEmpty(t, adminToken, "Admin token should not be empty")

	// Create a test user
	userEmail := fmt.Sprintf("<EMAIL>", time.Now().Unix())
	userPassword := "BackwardCompat123!"
	userID := createUser(t, adminToken, userEmail, userPassword)
	require.NotEmpty(t, userID, "User ID should not be empty")

	// Generate a user token
	userToken := generateUserToken(t, userID, userEmail)
	require.NotEmpty(t, userToken, "User token should not be empty")

	// Create a test game first (needed for puzzle creation)
	gameID := createGameWithPGN(t, adminToken, userID, "1. e4 e5 2. Nf3 Nc6 3. Bb5 a6")

	// Create a test puzzle
	puzzleID := createPuzzleWithAttributes(t, adminToken, gameID, userID, "white", "white", "opponent_mistake_missed", 5, 100, 50, []string{"tactics"})

	t.Run("Regular_puzzle_attempt_without_attempt_type", func(t *testing.T) {
		// Submit a puzzle attempt without attempt_type field (backward compatibility)
		attemptData := map[string]interface{}{
			"solved":     true,
			"time_spent": 30,
			"moves":      []string{"e2e4"},
		}

		attemptJSON, _ := json.Marshal(attemptData)
		req, _ := http.NewRequest("POST", fmt.Sprintf("http://localhost:8080/api/v1/users/me/puzzles/%s/attempts", puzzleID), bytes.NewBuffer(attemptJSON))
		req.Header.Set("Authorization", "Bearer "+userToken)
		req.Header.Set("Content-Type", "application/json")

		client := &http.Client{}
		resp, err := client.Do(req)
		require.NoError(t, err)
		defer func() {
			if err := resp.Body.Close(); err != nil {
				t.Logf("Error closing response body: %v", err)
			}
		}()

		// Should succeed - backward compatibility maintained
		assert.Equal(t, http.StatusCreated, resp.StatusCode)
		t.Log("✅ Regular puzzle attempt without attempt_type succeeded (backward compatibility)")
	})

	t.Run("Regular_puzzle_attempt_with_explicit_regular_type", func(t *testing.T) {
		// Submit a puzzle attempt with explicit regular attempt_type
		attemptData := map[string]interface{}{
			"attempt_type": "regular",
			"solved":       false,
			"time_spent":   45,
			"moves":        []string{"e2e3"},
		}

		attemptJSON, _ := json.Marshal(attemptData)
		req, _ := http.NewRequest("POST", fmt.Sprintf("http://localhost:8080/api/v1/users/me/puzzles/%s/attempts", puzzleID), bytes.NewBuffer(attemptJSON))
		req.Header.Set("Authorization", "Bearer "+userToken)
		req.Header.Set("Content-Type", "application/json")

		client := &http.Client{}
		resp, err := client.Do(req)
		require.NoError(t, err)
		defer func() {
			if err := resp.Body.Close(); err != nil {
				t.Logf("Error closing response body: %v", err)
			}
		}()

		// Should succeed
		assert.Equal(t, http.StatusCreated, resp.StatusCode)
		t.Log("✅ Regular puzzle attempt with explicit regular attempt_type succeeded")
	})

	t.Log("🎉 Backward compatibility tests passed!")
	t.Log("✅ Missing attempt_type defaults to regular")
	t.Log("✅ Explicit regular attempt_type works")
	t.Log("✅ Existing APIs continue to work without changes")
}
