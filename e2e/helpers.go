package e2e

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/stretchr/testify/require"
)

// Helper function to execute a GraphQL query
func executeGraphQLQuery(t *testing.T, token string, query string) map[string]interface{} {
	// Ensure the query starts with "query"
	if !strings.HasPrefix(strings.TrimSpace(query), "query") {
		query = "query { " + strings.TrimSpace(query) + " }"
	}

	// Create request body
	body := map[string]interface{}{
		"query": query,
	}

	// Create headers
	headers := map[string]string{
		"Authorization": "Bearer " + token,
		"Content-Type":  "application/json",
	}

	// Make the request
	resp := makeRequest(t, "POST", "/api/v1/graphql/query", headers, body)
	defer closeResponseBody(t, resp)

	// Print the response for debugging
	respBody := readResponseBody(t, resp)
	t.Logf("GraphQL response: %s", respBody)

	require.Equal(t, http.StatusOK, resp.StatusCode, "GraphQL query failed. Response body: %s", respBody)

	var result map[string]interface{}
	err := json.Unmarshal([]byte(respBody), &result)
	require.NoError(t, err, "Failed to decode GraphQL response")

	// Check for errors in the response
	if errors, hasErrors := result["errors"]; hasErrors {
		t.Fatalf("GraphQL query returned errors: %v", errors)
	}

	return result
}

// Generate a JWT token with admin privileges
func generateAdminToken(t *testing.T, userID, email string) string {
	// Create token with claims
	type Claims struct {
		UserID  string `json:"user_id"`
		Email   string `json:"email"`
		IsAdmin bool   `json:"is_admin"`
		jwt.RegisteredClaims
	}

	expirationTime := time.Now().Add(24 * time.Hour)
	claims := &Claims{
		UserID:  userID,
		Email:   email,
		IsAdmin: true,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte("test-secret-key-for-e2e-testing"))
	require.NoError(t, err, "Failed to create admin token")

	return tokenString
}

// Generate a JWT token with user (non-admin) privileges
func generateUserToken(t *testing.T, userID, email string) string {
	// Create token with claims
	type Claims struct {
		UserID  string `json:"user_id"`
		Email   string `json:"email"`
		IsAdmin bool   `json:"is_admin"`
		jwt.RegisteredClaims
	}

	expirationTime := time.Now().Add(24 * time.Hour)
	claims := &Claims{
		UserID:  userID,
		Email:   email,
		IsAdmin: false, // Key difference: IsAdmin is false
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
		},
	}

	// Use the same secret key as the server expects
	secretKey := os.Getenv("JWT_SECRET_KEY")
	if secretKey == "" {
		secretKey = "test-secret-key-for-e2e-testing" // Default fallback
		t.Log("Warning: JWT_SECRET_KEY environment variable not set, using default E2E key.")
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(secretKey))
	require.NoError(t, err, "Failed to create user token")

	return tokenString
}

// Create a new user using the admin API
func createUser(t *testing.T, token string, email, password string) string {
	payload := map[string]string{
		"email":    email,
		"password": password,
	}

	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	resp := makeRequest(t, "POST", "/api/v1/admin/users", headers, payload)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusCreated, resp.StatusCode, "User creation failed. Response body: %s", readResponseBody(t, resp))

	var result map[string]string
	err := json.NewDecoder(resp.Body).Decode(&result)
	require.NoError(t, err, "Failed to decode response")

	return result["id"]
}

// createGameWithPGN creates a game with a specific PGN string
func createGameWithPGN(t *testing.T, token string, userID string, pgn string) string {
	payload := map[string]interface{}{
		"user_id":        userID,
		"platform":       "chess_com",
		"chess_username": "testuser",
		"user_color":     "white",
		"game_time":      time.Now().Format(time.RFC3339),
		"pgn":            pgn,
		"time_control":   "5+0",
		"rated":          true,
		"white_player":   `{"username": "testuser", "is_ai": false}`,
		"black_player":   `{"username": "opponent", "is_ai": false}`,
		"winner":         "white",
		"result":         "mate",
	}

	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	resp := makeRequest(t, "POST", "/api/v1/admin/games", headers, payload)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusCreated, resp.StatusCode, "Create game with PGN failed. Response body: %s", readResponseBody(t, resp))

	var result map[string]interface{}
	err := json.NewDecoder(resp.Body).Decode(&result)
	require.NoError(t, err, "Failed to decode response")

	return result["id"].(string)
}

// Helper function to create a puzzle with specific attributes
func createPuzzleWithAttributes(t *testing.T, token, gameID, userID, userColor, puzzleColor, theme string, gameMove, prevCP, cp int, tags []string) string {
	payload := map[string]interface{}{
		"game_id":      gameID,
		"user_id":      userID,
		"game_move":    gameMove,
		"fen":          "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
		"moves":        []string{"e2e4", "e7e5"},
		"prev_cp":      prevCP,
		"cp":           cp,
		"theme":        theme,
		"user_color":   userColor,
		"puzzle_color": puzzleColor,
		"zugzwang":     false,
		"tags":         tags,
	}

	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	resp := makeRequest(t, "POST", "/api/v1/admin/puzzles", headers, payload)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusCreated, resp.StatusCode, "Create puzzle failed. Response body: %s", readResponseBody(t, resp))

	var result map[string]interface{}
	err := json.NewDecoder(resp.Body).Decode(&result)
	require.NoError(t, err, "Failed to decode response")

	return result["id"].(string)
}

// Helper function to update a game's user color
func updateGameUserColor(t *testing.T, token, gameID, userColor string) {
	payload := map[string]interface{}{
		"user_color": userColor,
	}

	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	resp := makeRequest(t, "PUT", "/api/v1/admin/games/"+gameID, headers, payload)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusOK, resp.StatusCode, "Update game user color failed. Response body: %s", readResponseBody(t, resp))
}

func deletePuzzle(t *testing.T, token string, puzzleID string) {
	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	resp := makeRequest(t, "DELETE", "/api/v1/admin/puzzles/"+puzzleID, headers, nil)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusOK, resp.StatusCode, "Delete puzzle failed. Response body: %s", readResponseBody(t, resp))
}

func deleteGame(t *testing.T, token string, gameID string) {
	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	resp := makeRequest(t, "DELETE", "/api/v1/admin/games/"+gameID, headers, nil)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusOK, resp.StatusCode, "Delete game failed. Response body: %s", readResponseBody(t, resp))
}

// Helper to make HTTP requests
func makeRequest(t *testing.T, method, path string, headers map[string]string, payload interface{}) *http.Response {
	var body io.Reader
	if payload != nil {
		payloadBytes, err := json.Marshal(payload)
		require.NoError(t, err, "Failed to marshal payload")
		body = bytes.NewBuffer(payloadBytes)
	}

	// Construct base URL dynamically
	baseURL := os.Getenv("API_BASE_URL")
	if baseURL == "" {
		baseURL = "http://localhost:8080" // Default if not set
	}

	req, err := http.NewRequest(method, baseURL+path, body)
	require.NoError(t, err, "Failed to create request")

	for k, v := range headers {
		req.Header.Set(k, v)
	}

	if payload != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	// Allow overriding timeout via env var for slower environments
	timeoutStr := os.Getenv("API_TIMEOUT")
	timeout := 10 * time.Second // Default
	if timeoutStr != "" {
		parsedTimeout, err := time.ParseDuration(timeoutStr)
		if err == nil {
			timeout = parsedTimeout
		} else {
			t.Logf("Warning: Invalid API_TIMEOUT value '%s', using default %s", timeoutStr, timeout)
		}
	}

	client := &http.Client{Timeout: timeout}
	resp, err := client.Do(req)
	require.NoError(t, err, "Request failed")

	return resp
}

// Helper function to read response body
func readResponseBody(t *testing.T, resp *http.Response) string {
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		// Attempt to provide more context if reading fails *after* a non-2xx response
		if resp.StatusCode < 200 || resp.StatusCode >= 300 {
			return fmt.Sprintf("status code %d. Failed to read response body: %v", resp.StatusCode, err)
		}
		t.Fatalf("Failed to read response body: %v", err) // Fail fast if reading fails on potential success
		return ""                                         // Keep compiler happy
	}
	// Reset the response body so it can be read again
	resp.Body = io.NopCloser(bytes.NewBuffer(body))
	return string(body)
}

// Helper function to close response body
func closeResponseBody(t *testing.T, resp *http.Response) {
	if err := resp.Body.Close(); err != nil {
		t.Logf("Error closing response body: %v", err)
	}
}

// getUser gets a user by ID using the admin API
func getUser(t *testing.T, token string, userID string) map[string]interface{} {
	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	resp := makeRequest(t, "GET", "/api/v1/admin/users/"+userID, headers, nil)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusOK, resp.StatusCode, "Get user failed. Response body: %s", readResponseBody(t, resp))

	var result map[string]interface{}
	err := json.NewDecoder(resp.Body).Decode(&result)
	require.NoError(t, err, "Failed to decode response")

	return result
}

// getAdminGame gets a game by ID using the admin API
func getAdminGame(t *testing.T, token string, gameID string) map[string]interface{} {
	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	resp := makeRequest(t, "GET", "/api/v1/admin/games/"+gameID, headers, nil)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusOK, resp.StatusCode, "Get game failed. Response body: %s", readResponseBody(t, resp))

	var result map[string]interface{}
	err := json.NewDecoder(resp.Body).Decode(&result)
	require.NoError(t, err, "Failed to decode response")

	return result
}

// updateAdminGame updates a game's PGN using the admin API
func updateAdminGame(t *testing.T, token string, gameID string, pgn string) map[string]interface{} {
	payload := map[string]interface{}{
		"pgn": pgn,
	}

	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	resp := makeRequest(t, "PUT", "/api/v1/admin/games/"+gameID, headers, payload)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusOK, resp.StatusCode, "Update game failed. Response body: %s", readResponseBody(t, resp))

	var result map[string]interface{}
	err := json.NewDecoder(resp.Body).Decode(&result)
	require.NoError(t, err, "Failed to decode response")

	return result
}

// createMyChessProfile creates a chess profile for the authenticated user
func createMyChessProfile(t *testing.T, token string, platform string, username string) string {
	payload := map[string]string{
		"platform": platform,
		"username": username,
	}

	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	resp := makeRequest(t, "POST", "/api/v1/users/me/chess-profiles", headers, payload)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusCreated, resp.StatusCode, "Create chess profile failed. Response body: %s", readResponseBody(t, resp))

	var result map[string]interface{}
	err := json.NewDecoder(resp.Body).Decode(&result)
	require.NoError(t, err, "Failed to decode response")

	id, ok := result["id"].(string)
	require.True(t, ok, "Expected 'id' to be a string in response")
	return id
}

// deleteMyChessProfile deletes a chess profile for the authenticated user
func deleteMyChessProfile(t *testing.T, token string, profileID string) {
	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	resp := makeRequest(t, "DELETE", "/api/v1/users/me/chess-profiles/"+profileID, headers, nil)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusNoContent, resp.StatusCode, "Delete chess profile failed. Response body: %s", readResponseBody(t, resp))
}

// listUserChessProfiles lists chess profiles for a user using the admin API
func listUserChessProfiles(t *testing.T, token string, userID string) []map[string]interface{} {
	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	resp := makeRequest(t, "GET", "/api/v1/admin/users/"+userID+"/chess-profiles", headers, nil)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusOK, resp.StatusCode, "List user chess profiles failed. Response body: %s", readResponseBody(t, resp))

	var result []map[string]interface{}
	err := json.NewDecoder(resp.Body).Decode(&result)
	require.NoError(t, err, "Failed to decode response")

	return result
}

// refreshUserChessProfile refreshes a chess profile using the admin API
func refreshUserChessProfile(t *testing.T, token string, userID string, profileID string) map[string]interface{} {
	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	resp := makeRequest(t, "PUT", "/api/v1/admin/users/"+userID+"/chess-profiles/"+profileID+"/refresh", headers, nil)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusOK, resp.StatusCode, "Refresh chess profile failed. Response body: %s", readResponseBody(t, resp))

	var result map[string]interface{}
	err := json.NewDecoder(resp.Body).Decode(&result)
	require.NoError(t, err, "Failed to decode response")

	return result
}

// listMyGames lists games for the authenticated user
func listMyGames(t *testing.T, token string, filters map[string]string) map[string]interface{} {
	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	url := "/api/v1/users/me/games"
	if len(filters) > 0 {
		url += "?"
		for k, v := range filters {
			url += k + "=" + v + "&"
		}
		url = url[:len(url)-1] // Remove trailing &
	}

	resp := makeRequest(t, "GET", url, headers, nil)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusOK, resp.StatusCode, "List my games failed. Response body: %s", readResponseBody(t, resp))

	var result map[string]interface{}
	err := json.NewDecoder(resp.Body).Decode(&result)
	require.NoError(t, err, "Failed to decode response")

	return result
}

// listMyPuzzles lists puzzles for the authenticated user
func listMyPuzzles(t *testing.T, token string, filters map[string]string) map[string]interface{} {
	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	url := "/api/v1/users/me/puzzles"
	if len(filters) > 0 {
		url += "?"
		for k, v := range filters {
			url += k + "=" + v + "&"
		}
		url = url[:len(url)-1] // Remove trailing &
	}

	resp := makeRequest(t, "GET", url, headers, nil)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusOK, resp.StatusCode, "List my puzzles failed. Response body: %s", readResponseBody(t, resp))

	var result map[string]interface{}
	err := json.NewDecoder(resp.Body).Decode(&result)
	require.NoError(t, err, "Failed to decode response")

	return result
}

// createAdminTask creates a task using the admin API
func createAdminTask(t *testing.T, token string, userID string) string {
	payload := map[string]interface{}{
		"user_id":   userID,
		"task_type": "FetchChessGames",
		"task_data": map[string]string{
			"user_id":          userID,
			"chess_profile_id": "test-profile-id",
		},
	}

	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	resp := makeRequest(t, "POST", "/api/v1/admin/tasks", headers, payload)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusCreated, resp.StatusCode, "Create task failed. Response body: %s", readResponseBody(t, resp))

	var result map[string]interface{}
	err := json.NewDecoder(resp.Body).Decode(&result)
	require.NoError(t, err, "Failed to decode response")

	id, ok := result["id"].(string)
	require.True(t, ok, "Expected 'id' to be a string in response")
	return id
}

// getAdminTask gets a task by ID using the admin API
func getAdminTask(t *testing.T, token string, taskID string) map[string]interface{} {
	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	resp := makeRequest(t, "GET", "/api/v1/admin/tasks/"+taskID, headers, nil)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusOK, resp.StatusCode, "Get task failed. Response body: %s", readResponseBody(t, resp))

	var result map[string]interface{}
	err := json.NewDecoder(resp.Body).Decode(&result)
	require.NoError(t, err, "Failed to decode response")

	return result
}

// updateAdminTask updates a task's status using the admin API
func updateAdminTask(t *testing.T, token string, taskID string, status string) map[string]interface{} {
	payload := map[string]interface{}{
		"status": status,
	}

	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	resp := makeRequest(t, "PUT", "/api/v1/admin/tasks/"+taskID, headers, payload)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusOK, resp.StatusCode, "Update task failed. Response body: %s", readResponseBody(t, resp))

	var result map[string]interface{}
	err := json.NewDecoder(resp.Body).Decode(&result)
	require.NoError(t, err, "Failed to decode response")

	return result
}

// listAdminTasks lists tasks using the admin API
func listAdminTasks(t *testing.T, token string) map[string]interface{} {
	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	resp := makeRequest(t, "GET", "/api/v1/admin/tasks", headers, nil)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusOK, resp.StatusCode, "List tasks failed. Response body: %s", readResponseBody(t, resp))

	var result map[string]interface{}
	err := json.NewDecoder(resp.Body).Decode(&result)
	require.NoError(t, err, "Failed to decode response")

	return result
}

// resetHangingTasks resets hanging tasks using the admin API
func resetHangingTasks(t *testing.T, token string, timeout time.Duration) int {
	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	url := fmt.Sprintf("/api/v1/admin/tasks/reset-hanging?timeout=%s", timeout.String())
	resp := makeRequest(t, "POST", url, headers, nil)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusOK, resp.StatusCode, "Reset hanging tasks failed. Response body: %s", readResponseBody(t, resp))

	var result map[string]interface{}
	err := json.NewDecoder(resp.Body).Decode(&result)
	require.NoError(t, err, "Failed to decode response")

	count, ok := result["reset_count"].(float64)
	require.True(t, ok, "Expected 'reset_count' to be a number in response")
	return int(count)
}

// cleanupOldTasks cleans up old tasks using the admin API
func cleanupOldTasks(t *testing.T, token string, cutoff time.Time) int {
	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	url := fmt.Sprintf("/api/v1/admin/tasks/cleanup?cutoff=%s", cutoff.Format(time.RFC3339))
	resp := makeRequest(t, "POST", url, headers, nil)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusOK, resp.StatusCode, "Cleanup old tasks failed. Response body: %s", readResponseBody(t, resp))

	var result map[string]interface{}
	err := json.NewDecoder(resp.Body).Decode(&result)
	require.NoError(t, err, "Failed to decode response")

	count, ok := result["deleted_count"].(float64)
	require.True(t, ok, "Expected 'deleted_count' to be a number in response")
	return int(count)
}

// createInvitationCode creates a new invitation code using the admin API
func createInvitationCode(t *testing.T, adminToken string, expiresInHours *int) string {
	payload := map[string]interface{}{}
	if expiresInHours != nil {
		payload["expires_in_hours"] = *expiresInHours
	}

	headers := map[string]string{
		"Authorization": "Bearer " + adminToken,
	}

	resp := makeRequest(t, "POST", "/api/v1/auth/invitation-codes", headers, payload)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusCreated, resp.StatusCode, "Create invitation code failed. Response body: %s", readResponseBody(t, resp))

	var result map[string]interface{}
	err := json.NewDecoder(resp.Body).Decode(&result)
	require.NoError(t, err, "Failed to decode response")

	code, ok := result["code"].(string)
	require.True(t, ok, "Expected 'code' to be a string in response")
	return code
}

// registerPublic registers a new user using public registration and returns the user ID and token
func registerPublic(t *testing.T, email, password string) (string, string) {
	payload := map[string]string{
		"email":    email,
		"password": password,
	}

	resp := makeRequest(t, "POST", "/api/v1/auth/register", nil, payload)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusCreated, resp.StatusCode, "User registration failed. Response body: %s", readResponseBody(t, resp))

	var result map[string]interface{}
	err := json.NewDecoder(resp.Body).Decode(&result)
	require.NoError(t, err, "Failed to decode response")

	id, ok := result["id"].(string)
	require.True(t, ok, "Expected 'id' to be a string in response")

	token, ok := result["token"].(string)
	require.True(t, ok, "Expected 'token' to be a string in response")

	return id, token
}

// registerWithInvitation registers a new user with an invitation code
func registerWithInvitation(t *testing.T, invitationCode, email, password string) (string, string) {
	payload := map[string]string{
		"email":           email,
		"password":        password,
		"invitation_code": invitationCode,
	}

	resp := makeRequest(t, "POST", "/api/v1/auth/register-with-invitation", nil, payload)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusCreated, resp.StatusCode, "User registration with invitation failed. Response body: %s", readResponseBody(t, resp))

	var result map[string]interface{}
	err := json.NewDecoder(resp.Body).Decode(&result)
	require.NoError(t, err, "Failed to decode response")

	id, ok := result["id"].(string)
	require.True(t, ok, "Expected 'id' to be a string in response")

	token, ok := result["token"].(string)
	require.True(t, ok, "Expected 'token' to be a string in response")

	return id, token
}

// getMyProfile gets the authenticated user's profile
func getMyProfile(t *testing.T, token string) map[string]interface{} {
	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	resp := makeRequest(t, "GET", "/api/v1/users/me", headers, nil)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusOK, resp.StatusCode, "Get my profile failed. Response body: %s", readResponseBody(t, resp))

	var result map[string]interface{}
	err := json.NewDecoder(resp.Body).Decode(&result)
	require.NoError(t, err, "Failed to decode response")

	return result
}
