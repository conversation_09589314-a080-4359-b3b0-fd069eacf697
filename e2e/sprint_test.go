package e2e

import (
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/api"
	"github.com/chessticize/chessticize-server/internal/models"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestSprintAPI_E2E(t *testing.T) {
	// Skip unless explicitly enabled
	if os.Getenv("RUN_E2E") != "true" {
		t.Skip("Skipping E2E test (set RUN_E2E=true to run)")
	}

	// Generate an admin JWT token for setup
	adminToken := generateAdminToken(t, "test-admin-id", "<EMAIL>")
	require.NotEmpty(t, adminToken, "Admin token should not be empty")

	// Create a test user and get auth token
	userEmail := fmt.Sprintf("<EMAIL>", time.Now().Unix())
	userPassword := "SprintTestPassword123!"
	userID := createUser(t, adminToken, userEmail, userPassword)
	require.NotEmpty(t, userID, "User ID should not be empty")

	// Generate user token
	userToken := generateUserToken(t, userID, userEmail)
	require.NotEmpty(t, userToken, "User token should not be empty")

	var sessionID string
	var firstPuzzle *api.PuzzleForSprint

	// Test 1: Start a sprint
	t.Run("StartSprint", func(t *testing.T) {
		startReq := api.StartSprintRequest{
			EloType: "mixed 10/30",
		}

		headers := map[string]string{
			"Authorization": "Bearer " + userToken,
		}

		resp := makeRequest(t, "POST", "/api/v1/users/me/sprint/start", headers, startReq)
		defer closeResponseBody(t, resp)

		assert.Equal(t, http.StatusCreated, resp.StatusCode)

		var startResp api.StartSprintResponse
		err := json.NewDecoder(resp.Body).Decode(&startResp)
		require.NoError(t, err)

		assert.NotEmpty(t, startResp.SessionID)
		assert.Equal(t, 600, startResp.UserElo.Rating)   // Default ELO from constants
		assert.Equal(t, 20, startResp.TargetPuzzles)     // Default target puzzles from constants
		assert.Equal(t, 600, startResp.TimeLimitSeconds) // 10 minutes from constants
		assert.Equal(t, 2, startResp.MaxMistakes)        // Default max mistakes from constants

		// Store session ID for subsequent tests
		sessionID = startResp.SessionID
	})

	// Test 2: Get sprint state
	t.Run("GetSprintState", func(t *testing.T) {
		require.NotEmpty(t, sessionID, "Session ID should be set from previous test")

		headers := map[string]string{
			"Authorization": "Bearer " + userToken,
		}

		resp := makeRequest(t, "GET", "/api/v1/users/me/sprint/"+sessionID, headers, nil)
		defer closeResponseBody(t, resp)

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var stateResp api.SprintStateResponse
		err := json.NewDecoder(resp.Body).Decode(&stateResp)
		require.NoError(t, err)

		assert.Equal(t, sessionID, stateResp.SessionID)
		assert.Equal(t, models.SprintStatusActive, stateResp.Status)
		assert.Equal(t, 0, stateResp.PuzzlesSolved)
		assert.Equal(t, 0, stateResp.MistakesMade)
		assert.Equal(t, 2, stateResp.MaxMistakes)    // Default max mistakes from constants
		assert.Equal(t, 20, stateResp.TargetPuzzles) // Default target puzzles from constants
		assert.NotNil(t, stateResp.TimeRemainingSeconds)
		assert.Greater(t, *stateResp.TimeRemainingSeconds, 0)
	})

	// Test 3: Get next puzzles for sprint
	t.Run("GetNextPuzzlesForSprint", func(t *testing.T) {
		require.NotEmpty(t, sessionID, "Session ID should be set from previous test")

		headers := map[string]string{
			"Authorization": "Bearer " + userToken,
		}

		// Request next batch of puzzles (default count of 10)
		nextPuzzlesReq := api.NextPuzzlesRequest{
			Count: 3, // Request 3 puzzles for testing
		}

		resp := makeRequest(t, "POST", "/api/v1/users/me/sprint/"+sessionID+"/next-puzzles", headers, nextPuzzlesReq)
		defer closeResponseBody(t, resp)

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var puzzlesResp api.NextPuzzlesResponse
		err := json.NewDecoder(resp.Body).Decode(&puzzlesResp)
		require.NoError(t, err)

		// We requested 3 puzzles, should get 3 (assuming enough puzzles in database)
		assert.Len(t, puzzlesResp.Puzzles, 3)

		// Verify puzzle structure
		for i, puzzle := range puzzlesResp.Puzzles {
			assert.NotEmpty(t, puzzle.PuzzleID)
			assert.NotEmpty(t, puzzle.FEN)
			assert.NotEmpty(t, puzzle.SolutionMoves)
			assert.Greater(t, puzzle.Rating, 0)
			assert.Equal(t, i+1, puzzle.SequenceInSprint) // Should start from 1 for first batch
		}

		// Store first puzzle for submission test
		firstPuzzle = &puzzlesResp.Puzzles[0]
	})

	// Test 4: Submit puzzle results
	t.Run("SubmitPuzzleResults", func(t *testing.T) {
		require.NotEmpty(t, sessionID, "Session ID should be set from previous test")
		require.NotNil(t, firstPuzzle, "First puzzle should be set from previous test")

		submitReq := api.SubmitResultsRequest{
			Results: []api.PuzzleAttemptRequest{
				{
					PuzzleID:         firstPuzzle.PuzzleID,
					SequenceInSprint: firstPuzzle.SequenceInSprint,
					UserMoves:        firstPuzzle.SolutionMoves, // Submit complete solution
					WasCorrect:       true,
					TimeTakenMs:      5000, // 5 seconds
					AttemptedAt:      time.Now(),
				},
			},
		}

		headers := map[string]string{
			"Authorization": "Bearer " + userToken,
		}

		resp := makeRequest(t, "POST", "/api/v1/users/me/sprint/"+sessionID+"/results", headers, submitReq)
		defer closeResponseBody(t, resp)

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var submitResp api.SubmitResultsResponse
		err := json.NewDecoder(resp.Body).Decode(&submitResp)
		require.NoError(t, err)

		assert.Equal(t, 1, submitResp.ProcessedCount)
		assert.Equal(t, models.SprintStatusActive, submitResp.SessionStatus)
		assert.Equal(t, 0, submitResp.MistakesCount) // Correct answer, no mistakes
	})

	// Test 5: End sprint
	t.Run("EndSprint", func(t *testing.T) {
		require.NotEmpty(t, sessionID, "Session ID should be set from previous test")

		headers := map[string]string{
			"Authorization": "Bearer " + userToken,
		}

		resp := makeRequest(t, "POST", "/api/v1/users/me/sprint/"+sessionID+"/end", headers, nil)
		defer closeResponseBody(t, resp)

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var endResp api.EndSprintResponse
		err := json.NewDecoder(resp.Body).Decode(&endResp)
		require.NoError(t, err)

		assert.Equal(t, sessionID, endResp.SessionID)
		// Sprint status can be success, fail by mistakes, or fail by time
		assert.Contains(t, []models.SprintStatus{
			models.SprintStatusCompletedSuccess,
			models.SprintStatusCompletedFailMistakes,
			models.SprintStatusCompletedFailTime,
		}, endResp.Status)
		assert.Equal(t, 1, endResp.PuzzlesSolved)
		assert.GreaterOrEqual(t, endResp.MistakesMade, 0)    // Mistakes can be 0 or more
		assert.Equal(t, 2, endResp.MaxMistakes)              // Default max mistakes from constants
		assert.Equal(t, 20, endResp.TargetPuzzles)           // Default target puzzles from constants
		assert.GreaterOrEqual(t, endResp.DurationSeconds, 0) // Duration can be 0 for very fast sprints

		// ELO should always change, even for the first sprint
		// The Glicko-2 system treats each sprint as a game against a "system" opponent
		// with the same rating as the user, so there will always be a rating change
		require.NotNil(t, endResp.EloChange, "ELO change should not be nil")
		assert.Equal(t, 600, endResp.EloChange.RatingBefore) // Initial default rating

		// ELO change direction depends on sprint outcome
		if endResp.Status == models.SprintStatusCompletedSuccess {
			assert.Greater(t, endResp.EloChange.RatingAfter, endResp.EloChange.RatingBefore,
				"ELO should increase after a successful sprint")
			assert.Greater(t, endResp.EloChange.RatingChange, 0,
				"ELO change should be positive for a successful sprint")
		} else {
			// Failed sprint should decrease ELO, but not below minimum rating (600)
			assert.GreaterOrEqual(t, endResp.EloChange.RatingAfter, 600,
				"ELO should not go below minimum rating after a failed sprint")
			assert.LessOrEqual(t, endResp.EloChange.RatingChange, 0,
				"ELO change should be non-positive for a failed sprint (0 if at minimum)")
		}

		// Verify the rating change is reasonable (not too extreme)
		// For first sprint with high rating deviation, changes can be larger
		assert.LessOrEqual(t, endResp.EloChange.RatingChange, 100,
			"ELO change should not be too extreme for first sprint")
		assert.GreaterOrEqual(t, endResp.EloChange.RatingChange, -100,
			"ELO change should not be too extreme for first sprint")
	})

	// Test 6: Verify ELO and sprint stats are updated in user profile
	t.Run("VerifyEloAndSprintStatsInUserProfile", func(t *testing.T) {
		headers := map[string]string{
			"Authorization": "Bearer " + userToken,
		}

		resp := makeRequest(t, "GET", "/api/v1/users/me", headers, nil)
		defer closeResponseBody(t, resp)

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var userResp api.UserResponse
		err := json.NewDecoder(resp.Body).Decode(&userResp)
		require.NoError(t, err)

		assert.Equal(t, userID, userResp.ID)
		assert.NotNil(t, userResp.SprintDailyStats)

		// The sprint ended and ELO changed, so there MUST be a sprint daily stats record
		// Since the test only solves 1 puzzle out of 20, it should show as a failure (Success: 0, Total: 1)
		assert.Greater(t, len(userResp.SprintDailyStats), 0, "Sprint daily stats should contain at least one record after completing a sprint")

		// Verify the sprint daily stats record exists and shows the expected failure
		stat := userResp.SprintDailyStats[0]
		assert.Equal(t, 0, stat.SprintSuccess, "Sprint should be recorded as a failure since only 1/20 puzzles solved")
		assert.Equal(t, 1, stat.SprintTotal, "Sprint total should be 1")
		assert.Equal(t, "mixed 10/30", stat.EloType, "ELO type should be mixed 10/30")

		// Log the details for debugging
		t.Logf("Sprint daily stats found: Date=%s, Success=%d, Total=%d, EloType=%s",
			stat.Date.Format("2006-01-02"), stat.SprintSuccess, stat.SprintTotal, stat.EloType)

		// Verify ELO has been updated
		require.NotNil(t, userResp.Elos, "User should have ELO ratings")

		// Find the mixed 10/30 ELO (the one we used for the sprint)
		var mixedElo *models.UserElo
		for _, elo := range userResp.Elos {
			if elo.EloType == "mixed 10/30" {
				mixedElo = &elo
				break
			}
		}

		require.NotNil(t, mixedElo, "User should have a mixed 10/30 ELO rating")
		// ELO may stay at minimum rating (600) if user fails sprint and is already at minimum
		assert.GreaterOrEqual(t, mixedElo.Rating, 600, "ELO should not go below minimum rating")
		assert.Equal(t, 1, mixedElo.GamesPlayed, "Games played should be 1 after first sprint")
		assert.NotNil(t, mixedElo.LastActiveAt, "Last active time should be set")
	})
}

func TestAdminSprintAPI_E2E(t *testing.T) {
	// Skip unless explicitly enabled
	if os.Getenv("RUN_E2E") != "true" {
		t.Skip("Skipping E2E test (set RUN_E2E=true to run)")
	}

	// Generate an admin JWT token
	adminToken := generateAdminToken(t, "test-admin-id", "<EMAIL>")
	require.NotEmpty(t, adminToken, "Admin token should not be empty")

	// Test admin sprint cleanup
	t.Run("CleanupAbandonedSprints", func(t *testing.T) {
		headers := map[string]string{
			"Authorization": "Bearer " + adminToken,
		}

		resp := makeRequest(t, "POST", "/api/v1/admin/sprints/cleanup?grace_minutes=1&limit=100", headers, nil)
		defer closeResponseBody(t, resp)

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var cleanupResp api.CleanupSprintsResponse
		err := json.NewDecoder(resp.Body).Decode(&cleanupResp)
		require.NoError(t, err)

		assert.GreaterOrEqual(t, cleanupResp.AbandonedCount, int64(0))
		assert.NotEmpty(t, cleanupResp.Message)
	})
}

func TestSprintDeduplication_E2E(t *testing.T) {
	// Skip unless explicitly enabled
	if os.Getenv("RUN_E2E") != "true" {
		t.Skip("Skipping E2E test (set RUN_E2E=true to run)")
	}

	// Generate an admin JWT token for setup
	adminToken := generateAdminToken(t, "test-admin-id", "<EMAIL>")
	require.NotEmpty(t, adminToken, "Admin token should not be empty")

	// Create a test user and get auth token
	userEmail := fmt.Sprintf("<EMAIL>", time.Now().Unix())
	userPassword := "SprintTestPassword123!"
	userID := createUser(t, adminToken, userEmail, userPassword)
	require.NotEmpty(t, userID, "User ID should not be empty")

	// Generate user token
	userToken := generateUserToken(t, userID, userEmail)
	require.NotEmpty(t, userToken, "User token should not be empty")

	var sessionID string
	var firstPuzzle *api.PuzzleForSprint

	// Start a sprint
	t.Run("StartSprint", func(t *testing.T) {
		startReq := api.StartSprintRequest{
			EloType: "mixed 10/30",
		}

		headers := map[string]string{
			"Authorization": "Bearer " + userToken,
		}

		resp := makeRequest(t, "POST", "/api/v1/users/me/sprint/start", headers, startReq)
		defer closeResponseBody(t, resp)

		assert.Equal(t, http.StatusCreated, resp.StatusCode)

		var startResp api.StartSprintResponse
		err := json.NewDecoder(resp.Body).Decode(&startResp)
		require.NoError(t, err)

		sessionID = startResp.SessionID
		require.NotEmpty(t, sessionID, "Session ID should not be empty")
	})

	// Get puzzles for the sprint
	t.Run("GetPuzzles", func(t *testing.T) {
		require.NotEmpty(t, sessionID, "Session ID should be set from previous test")

		headers := map[string]string{
			"Authorization": "Bearer " + userToken,
		}

		resp := makeRequest(t, "POST", "/api/v1/users/me/sprint/"+sessionID+"/next-puzzles", headers, map[string]int{"count": 5})
		defer closeResponseBody(t, resp)

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var puzzlesResp api.NextPuzzlesResponse
		err := json.NewDecoder(resp.Body).Decode(&puzzlesResp)
		require.NoError(t, err)

		require.NotEmpty(t, puzzlesResp.Puzzles, "Should have puzzles")
		firstPuzzle = &puzzlesResp.Puzzles[0]
	})

	// Test deduplication: Submit the same puzzle result twice
	t.Run("TestDeduplication", func(t *testing.T) {
		require.NotEmpty(t, sessionID, "Session ID should be set from previous test")
		require.NotNil(t, firstPuzzle, "First puzzle should be set from previous test")

		headers := map[string]string{
			"Authorization": "Bearer " + userToken,
		}

		// First submission - should succeed
		submitReq := api.SubmitResultsRequest{
			Results: []api.PuzzleAttemptRequest{
				{
					PuzzleID:         firstPuzzle.PuzzleID,
					SequenceInSprint: firstPuzzle.SequenceInSprint,
					UserMoves:        firstPuzzle.SolutionMoves,
					WasCorrect:       true,
					TimeTakenMs:      5000,
					AttemptedAt:      time.Now(),
				},
			},
		}

		resp := makeRequest(t, "POST", "/api/v1/users/me/sprint/"+sessionID+"/results", headers, submitReq)
		defer closeResponseBody(t, resp)

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var submitResp api.SubmitResultsResponse
		err := json.NewDecoder(resp.Body).Decode(&submitResp)
		require.NoError(t, err)

		assert.Equal(t, 1, submitResp.ProcessedCount, "First submission should be processed")
		assert.Equal(t, 0, submitResp.MistakesCount, "Should have no mistakes")

		// Second submission - same puzzle, should be deduplicated
		submitReq2 := api.SubmitResultsRequest{
			Results: []api.PuzzleAttemptRequest{
				{
					PuzzleID:         firstPuzzle.PuzzleID,
					SequenceInSprint: firstPuzzle.SequenceInSprint,
					UserMoves:        []string{"e2e4"}, // Different moves
					WasCorrect:       false,            // Different result
					TimeTakenMs:      3000,             // Different time
					AttemptedAt:      time.Now(),
				},
			},
		}

		resp2 := makeRequest(t, "POST", "/api/v1/users/me/sprint/"+sessionID+"/results", headers, submitReq2)
		defer closeResponseBody(t, resp2)

		assert.Equal(t, http.StatusOK, resp2.StatusCode)

		var submitResp2 api.SubmitResultsResponse
		err = json.NewDecoder(resp2.Body).Decode(&submitResp2)
		require.NoError(t, err)

		assert.Equal(t, 0, submitResp2.ProcessedCount, "Second submission should be deduplicated (processed count = 0)")
		assert.Equal(t, 0, submitResp2.MistakesCount, "Mistakes count should remain unchanged")
	})

	// Verify sprint state hasn't changed due to duplicate submission
	t.Run("VerifySprintState", func(t *testing.T) {
		require.NotEmpty(t, sessionID, "Session ID should be set from previous test")

		headers := map[string]string{
			"Authorization": "Bearer " + userToken,
		}

		resp := makeRequest(t, "GET", "/api/v1/users/me/sprint/"+sessionID, headers, nil)
		defer closeResponseBody(t, resp)

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var sprintResp models.Sprint
		err := json.NewDecoder(resp.Body).Decode(&sprintResp)
		require.NoError(t, err)

		assert.Equal(t, 1, sprintResp.PuzzlesSolved, "Should still have only 1 puzzle solved")
		assert.Equal(t, 0, sprintResp.MistakesMade, "Should still have 0 mistakes")
		assert.Equal(t, models.SprintStatusActive, sprintResp.Status, "Sprint should still be active")
	})
}

func TestSprintClientProvidedResults_E2E(t *testing.T) {
	// Skip unless explicitly enabled
	if os.Getenv("RUN_E2E") != "true" {
		t.Skip("Skipping E2E test (set RUN_E2E=true to run)")
	}

	// Generate an admin JWT token for setup
	adminToken := generateAdminToken(t, "test-admin-id", "<EMAIL>")
	require.NotEmpty(t, adminToken, "Admin token should not be empty")

	// Create a test user and get auth token
	userEmail := fmt.Sprintf("<EMAIL>", time.Now().Unix())
	userPassword := "SprintTestPassword123!"
	userID := createUser(t, adminToken, userEmail, userPassword)
	require.NotEmpty(t, userID, "User ID should not be empty")

	// Generate user token
	userToken := generateUserToken(t, userID, userEmail)
	require.NotEmpty(t, userToken, "User token should not be empty")

	var sessionID string

	// Start a sprint
	t.Run("StartSprint", func(t *testing.T) {
		startReq := api.StartSprintRequest{
			EloType: "mixed 10/30",
		}

		headers := map[string]string{
			"Authorization": "Bearer " + userToken,
		}

		resp := makeRequest(t, "POST", "/api/v1/users/me/sprint/start", headers, startReq)
		defer closeResponseBody(t, resp)

		assert.Equal(t, http.StatusCreated, resp.StatusCode)

		var startResp api.StartSprintResponse
		err := json.NewDecoder(resp.Body).Decode(&startResp)
		require.NoError(t, err)

		sessionID = startResp.SessionID
		require.NotEmpty(t, sessionID, "Session ID should not be empty")
	})

	// Test EndSprint with client-provided results
	t.Run("EndSprintWithClientResults", func(t *testing.T) {
		require.NotEmpty(t, sessionID, "Session ID should be set from previous test")

		headers := map[string]string{
			"Authorization": "Bearer " + userToken,
		}

		// Client reports they solved 15 puzzles and made 1 mistake
		// This should override any server-calculated values
		clientPuzzlesSolved := 15
		clientMistakesMade := 1
		endReq := api.EndSprintRequest{
			PuzzlesSolved: &clientPuzzlesSolved,
			MistakesMade:  &clientMistakesMade,
		}

		resp := makeRequest(t, "POST", "/api/v1/users/me/sprint/"+sessionID+"/end", headers, endReq)
		defer closeResponseBody(t, resp)

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var endResp api.EndSprintResponse
		err := json.NewDecoder(resp.Body).Decode(&endResp)
		require.NoError(t, err)

		// Verify the response uses client-provided values
		assert.Equal(t, 15, endResp.PuzzlesSolved, "Should use client-provided puzzles solved count")
		assert.Equal(t, 1, endResp.MistakesMade, "Should use client-provided mistakes count")
		assert.Equal(t, models.SprintStatusCompletedFailTime, endResp.Status, "Should be fail by time (15 < 20 target)")
		assert.NotNil(t, endResp.EloChange, "Should have ELO change")
	})
}

func TestSprintClientProvidedResultsSuccess_E2E(t *testing.T) {
	// Skip unless explicitly enabled
	if os.Getenv("RUN_E2E") != "true" {
		t.Skip("Skipping E2E test (set RUN_E2E=true to run)")
	}

	// Generate an admin JWT token for setup
	adminToken := generateAdminToken(t, "test-admin-id", "<EMAIL>")
	require.NotEmpty(t, adminToken, "Admin token should not be empty")

	// Create a test user and get auth token
	userEmail := fmt.Sprintf("<EMAIL>", time.Now().Unix())
	userPassword := "SprintTestPassword123!"
	userID := createUser(t, adminToken, userEmail, userPassword)
	require.NotEmpty(t, userID, "User ID should not be empty")

	// Generate user token
	userToken := generateUserToken(t, userID, userEmail)
	require.NotEmpty(t, userToken, "User token should not be empty")

	var sessionID string

	// Start a sprint
	t.Run("StartSprint", func(t *testing.T) {
		startReq := api.StartSprintRequest{
			EloType: "mixed 10/30",
		}

		headers := map[string]string{
			"Authorization": "Bearer " + userToken,
		}

		resp := makeRequest(t, "POST", "/api/v1/users/me/sprint/start", headers, startReq)
		defer closeResponseBody(t, resp)

		assert.Equal(t, http.StatusCreated, resp.StatusCode)

		var startResp api.StartSprintResponse
		err := json.NewDecoder(resp.Body).Decode(&startResp)
		require.NoError(t, err)

		sessionID = startResp.SessionID
		require.NotEmpty(t, sessionID, "Session ID should not be empty")
	})

	// Test EndSprint with client-provided successful results
	t.Run("EndSprintWithClientSuccessResults", func(t *testing.T) {
		require.NotEmpty(t, sessionID, "Session ID should be set from previous test")

		headers := map[string]string{
			"Authorization": "Bearer " + userToken,
		}

		// Client reports they solved 20 puzzles (target) and made 1 mistake (within limit)
		clientPuzzlesSolved := 20
		clientMistakesMade := 1
		endReq := api.EndSprintRequest{
			PuzzlesSolved: &clientPuzzlesSolved,
			MistakesMade:  &clientMistakesMade,
		}

		resp := makeRequest(t, "POST", "/api/v1/users/me/sprint/"+sessionID+"/end", headers, endReq)
		defer closeResponseBody(t, resp)

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var endResp api.EndSprintResponse
		err := json.NewDecoder(resp.Body).Decode(&endResp)
		require.NoError(t, err)

		// Verify the response uses client-provided values and shows success
		assert.Equal(t, 20, endResp.PuzzlesSolved, "Should use client-provided puzzles solved count")
		assert.Equal(t, 1, endResp.MistakesMade, "Should use client-provided mistakes count")
		assert.Equal(t, models.SprintStatusCompletedSuccess, endResp.Status, "Should be success (20 >= 20 target, 1 <= 2 max mistakes)")
		assert.NotNil(t, endResp.EloChange, "Should have ELO change")
	})
}
