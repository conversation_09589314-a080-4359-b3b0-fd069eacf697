# Daily Quest E2E Tests

This document describes the comprehensive End-to-End tests for the daily quest system in the Chessticize Server.

## Test Overview

The E2E tests in `daily_quest_test.go` verify that:

1. **Admin Quest Management APIs** work correctly
2. **User Quest Requirements** are returned properly
3. **Quest Progress Tracking** updates stats when activities are completed
4. **Quest Streak Calculation** works correctly

## Test Structure

### Main Test Function: `TestDailyQuestAPI_E2E`

This test sets up the test environment and runs four main test suites:

1. **AdminQuestManagement**: Tests CRUD operations for quest requirements
2. **UserQuestRequirements**: Tests that quest requirements are returned to users
3. **QuestProgressTracking**: Tests that completing activities updates daily stats
4. **QuestStreakCalculation**: Tests that quest completion updates streak counters

## Test Implementation Details

### 1. Admin Quest Management Tests

**`testAdminQuestManagement`** verifies:

- **Create Quest Requirement**: POST to `/api/v1/admin/quest-requirements`
  - Creates a new quest requirement
  - Validates response structure and field values
  - Tests required fields validation

- **List Quest Requirements**: GET to `/api/v1/admin/quest-requirements`
  - Retrieves all quest requirements
  - Verifies default requirements exist (sprint and arrow_duel)
  - Checks response structure

- **Update Quest Requirement**: PUT to `/api/v1/admin/quest-requirements/{id}`
  - Updates existing quest requirement
  - Validates partial updates work correctly
  - Verifies response reflects changes

- **Invalid Quest Creation**: POST with invalid data
  - Tests validation of quest types
  - Ensures proper error responses

### 2. User Quest Requirements Tests

**`testUserQuestRequirements`** verifies:

- **Get User Data with Quest Requirements**: GET to `/api/v1/users/me`
  - Ensures quest requirements are included in user response
  - Validates structure of quest requirements data
  - Checks that only active quest requirements are returned

- **Get User Daily Stats**: GET to `/api/v1/users/me`
  - Verifies daily stats include quest-related fields
  - Checks for sprint and arrow duel tracking fields
  - Validates quest completion and streak fields

### 3. Quest Progress Tracking Tests

**`testQuestProgressTracking`** verifies:

- **Sprint Activity Updates Stats**:
  - Creates and completes a sprint using the Sprint API
  - Verifies that `sprint_total` and `sprint_success` are incremented
  - Checks that database triggers work correctly

- **Arrow Duel Activity Updates Stats**:
  - Creates a puzzle and submits an arrow duel attempt
  - Verifies that `arrow_duel_total` and `arrow_duel_success` are incremented
  - Ensures proper attempt type handling

### 4. Quest Streak Calculation Tests

**`testQuestStreakCalculation`** verifies:

- **Quest Completion Updates Streak**:
  - Completes both sprint and arrow duel activities
  - Verifies that quest completion logic works
  - Checks that quest streak calculations are correct

## Helper Functions

### API Interaction Helpers

- **`getUserStats`**: Retrieves user statistics from `/api/v1/users/me`
- **`createAndCompleteSprintSuccessfully`**: Creates and completes a sprint
- **`createTestPuzzle`**: Creates a test puzzle for arrow duel testing
- **`submitArrowDuelAttempt`**: Submits an arrow duel puzzle attempt
- **`findTodaysStats`**: Finds today's daily stats from the stats array

### Sprint API Workflow

The sprint completion process follows these steps:

1. **Start Sprint**: POST to `/api/v1/users/me/sprint/start`
2. **Get Puzzles**: POST to `/api/v1/users/me/sprint/{id}/next-puzzles`
3. **Submit Results**: POST to `/api/v1/users/me/sprint/{id}/results`
4. **End Sprint**: POST to `/api/v1/users/me/sprint/{id}/end`

### Arrow Duel API Workflow

The arrow duel process:

1. **Create Puzzle**: Uses admin API to create a test puzzle
2. **Submit Attempt**: POST to `/api/v1/users/me/puzzles/{id}/attempts`
   - With `attempt_type: "arrow_duel"`
   - Includes `candidate_moves` and `chosen_move`

## Running the Tests

### Prerequisites

1. **Database Setup**: Ensure PostgreSQL is running and migrations are applied
2. **Environment Variables**: Set `RUN_E2E=true` to enable E2E tests
3. **Server Running**: The server must be running on `localhost:8080`

### Test Commands

```bash
# Run all E2E tests
make e2e-test

# Run only daily quest E2E tests
RUN_E2E=true go test ./e2e -v -run TestDailyQuestAPI_E2E

# Run with detailed output
RUN_E2E=true go test ./e2e -v -run TestDailyQuestAPI_E2E -args -test.v
```

### Environment Setup

```bash
# Start development environment
make up

# Run migrations
make migrate

# Start server
make run
```

## Test Data and Cleanup

### Test Data Creation

The tests create temporary data:
- Test users with unique email addresses
- Test games and puzzles for arrow duel testing
- Test quest requirements for admin API testing

### Cleanup

The tests are designed to:
- Use unique identifiers to avoid conflicts
- Create isolated test data
- Not interfere with existing data

## Expected Behavior

### Successful Test Run

When all tests pass, you should see:
- Quest requirements are created, listed, and updated successfully
- User data includes quest requirements and daily stats with all required fields
- Sprint completion updates sprint-related daily stats
- Arrow duel attempts update arrow duel-related daily stats
- Quest completion logic correctly calculates streaks

### Database Triggers

The tests verify that database triggers work correctly:
- Sprint events trigger `update_sprint_daily_stats`
- Arrow duel events trigger `update_arrow_duel_daily_stats`
- Quest completion is automatically calculated via `update_quest_completion`

## Troubleshooting

### Common Issues

1. **Server Not Running**: Ensure the server is running on `localhost:8080`
2. **Database Not Migrated**: Run `make migrate` to apply latest migrations
3. **Missing Test Data**: The tests create their own data, but database should be initialized
4. **Timing Issues**: Tests include sleep periods for database trigger processing

### Debug Information

The tests include logging for:
- HTTP response status codes and bodies
- Quest completion status and streaks
- Sprint and arrow duel activity counts
- Database trigger processing results

## Integration with CI/CD

The E2E tests are integrated into the project's verification process:
- Run via `make e2e-test` in CI pipelines
- Require `RUN_E2E=true` environment variable
- Depend on database and server setup
- Provide comprehensive coverage of the daily quest system

This comprehensive E2E testing ensures that the daily quest system works correctly across all components, from API endpoints to database triggers to client-facing responses.