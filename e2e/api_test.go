package e2e

import (
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

// TestE2EAPI is a true end-to-end test that tests the API by making HTTP requests to the running server
func TestE2EAPI(t *testing.T) {
	// Skip unless explicitly enabled
	if os.Getenv("RUN_E2E") != "true" {
		t.Skip("Skipping E2E test (set RUN_E2E=true to run)")
	}

	// Generate an admin JWT token for all requests
	adminToken := generateAdminToken(t, "test-admin-id", "<EMAIL>")
	require.NotEmpty(t, adminToken, "Admin token should not be empty")

	// Create a new user with admin token
	userEmail := fmt.Sprintf("<EMAIL>", time.Now().Unix())
	userPassword := "TestPassword123!"
	userID := createUser(t, adminToken, userEmail, userPassword)
	require.NotEmpty(t, userID, "User ID should not be empty")

	// Test public registration
	publicRegisterEmail := fmt.Sprintf("<EMAIL>", time.Now().Unix())
	publicRegisterPassword := "PublicRegisterPass123!"
	publicUserID, publicUserToken := registerPublic(t, publicRegisterEmail, publicRegisterPassword)
	require.NotEmpty(t, publicUserID, "Public user ID should not be empty")
	require.NotEmpty(t, publicUserToken, "Public user token should not be empty")

	// Test invitation-based registration
	invitationCode := createInvitationCode(t, adminToken, nil)
	inviteRegisterEmail := fmt.Sprintf("<EMAIL>", time.Now().Unix())
	inviteRegisterPassword := "InviteRegisterPass123!"
	inviteUserID, inviteUserToken := registerWithInvitation(t, invitationCode, inviteRegisterEmail, inviteRegisterPassword)
	require.NotEmpty(t, inviteUserID, "Invite user ID should not be empty")
	require.NotEmpty(t, inviteUserToken, "Invite user token should not be empty")

	// Test admin registration API
	adminRegisterEmail := fmt.Sprintf("<EMAIL>", time.Now().Unix())
	adminRegisterPassword := "AdminRegisterPass123!"
	registerUser(t, adminToken, adminRegisterEmail, adminRegisterPassword)

	// Test admin features
	// List users
	users := listUsers(t, adminToken)
	require.GreaterOrEqual(t, len(users), 1, "Should have at least one user")

	// Get user by ID
	user := getUser(t, adminToken, userID)
	require.Equal(t, userID, user["id"], "User ID should match")

	// Create a game
	gameID := createGame(t, adminToken, userID)
	require.NotEmpty(t, gameID, "Game ID should not be empty")

	// --- Test GetGame ---
	t.Log("Testing Admin GetGame API...")
	gameResp := getAdminGame(t, adminToken, gameID)
	require.Equal(t, gameID, gameResp["id"].(string), "Retrieved game ID should match")
	require.Contains(t, gameResp, "pgn", "Game response should contain 'pgn'")
	require.NotEmpty(t, gameResp["pgn"].(string), "PGN should not be empty")

	// --- Test UpdateGame ---
	t.Log("Testing Admin UpdateGame API...")
	newPGN := "1. a4 a5 2. h4 h5"
	updated := updateAdminGame(t, adminToken, gameID, newPGN)
	require.Equal(t, newPGN, updated["pgn"].(string), "Updated PGN should match")

	// Create a puzzle
	puzzleID := createPuzzle(t, adminToken, gameID, userID)
	require.NotEmpty(t, puzzleID, "Puzzle ID should not be empty")

	// List puzzles
	puzzles := listPuzzles(t, adminToken, userID)
	require.GreaterOrEqual(t, len(puzzles), 1, "Should have at least one puzzle")

	// List puzzles by user
	userPuzzles := listPuzzlesByUser(t, adminToken, userID)
	require.GreaterOrEqual(t, len(userPuzzles), 1, "Should have at least one puzzle for user")

	// Generate a user token for the created user
	userToken := generateUserToken(t, userID, userEmail)
	require.NotEmpty(t, userToken, "User token should not be empty")

	// Test "me" APIs with user token

	// Get my profile
	myProfile := getMyProfile(t, userToken)
	require.Equal(t, userID, myProfile["id"], "User ID should match in profile response")
	require.Equal(t, userEmail, myProfile["email"], "Email should match in profile response")

	// Initially should have no chess profiles
	profiles := listMyChessProfiles(t, userToken)
	require.Equal(t, 0, len(profiles), "New user should have no chess profiles")

	// Create chess profile
	chessProfilePlatform := "chess.com"
	chessUsername := "zsmickycat" // Use a real chess.com username for E2E testing
	profileID := createMyChessProfile(t, userToken, chessProfilePlatform, chessUsername)
	require.NotEmpty(t, profileID, "Chess profile ID should not be empty")

	// List chess profiles again - should have one now
	profiles = listMyChessProfiles(t, userToken)
	require.Equal(t, 1, len(profiles), "User should have one chess profile")
	require.Equal(t, chessProfilePlatform, profiles[0]["platform"], "Platform should match")
	require.Equal(t, chessUsername, profiles[0]["username"], "Username should match")

	// Test admin chess profile APIs

	// List user chess profiles as admin
	adminProfiles := listUserChessProfiles(t, adminToken, userID)
	require.Equal(t, 1, len(adminProfiles), "Admin should see one user chess profile")
	require.Equal(t, chessProfilePlatform, adminProfiles[0]["platform"], "Platform should match in admin view")

	// Refresh chess profile as admin
	refreshedProfile := refreshUserChessProfile(t, adminToken, userID, profileID)
	require.NotEmpty(t, refreshedProfile, "Refreshed profile should not be empty")
	require.Equal(t, profileID, fmt.Sprintf("%v", refreshedProfile["id"]), "Profile ID should match after refresh")

	// Create a game for the chess profile
	gamePGN := "1. e4 e5 2. Nf3 Nc6 3. Bb5 a6"
	gamePayload := map[string]interface{}{
		"user_id":        userID,
		"platform":       chessProfilePlatform,
		"chess_username": chessUsername,
		"user_color":     "white",
		"game_time":      time.Now().Format(time.RFC3339),
		"pgn":            gamePGN,
		"time_control":   "5+0",
		"rated":          true,
		"white_player":   `{"username": "` + chessUsername + `", "rating": 1500, "is_ai": false}`,
		"black_player":   `{"username": "opponent", "rating": 1600, "is_ai": false}`,
		"winner":         "white",
		"result":         "mate",
	}

	// Create game
	headers := map[string]string{
		"Authorization": "Bearer " + adminToken,
	}
	resp := makeRequest(t, "POST", "/api/v1/admin/games", headers, gamePayload)
	defer closeResponseBody(t, resp)
	require.Equal(t, http.StatusCreated, resp.StatusCode, "Create game failed")

	var gameResp2 map[string]interface{}
	err := json.NewDecoder(resp.Body).Decode(&gameResp2)
	require.NoError(t, err, "Failed to decode response")
	gameID2, ok := gameResp2["id"].(string)
	require.True(t, ok, "Game ID should be a string")
	require.NotEmpty(t, gameID2, "Game ID should not be empty")

	// Create a puzzle for the game
	puzzlePayload := map[string]interface{}{
		"game_id":      gameID2,
		"user_id":      userID,
		"game_move":    10,
		"fen":          "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
		"moves":        []string{"e2e4", "e7e5"},
		"prev_cp":      100,
		"cp":           300,
		"theme":        "opponent_blunder_caught",
		"user_color":   "white",
		"puzzle_color": "white",
		"zugzwang":     false,
		"tags":         []string{"opening", "tactics"},
	}

	resp = makeRequest(t, "POST", "/api/v1/admin/puzzles", headers, puzzlePayload)
	defer closeResponseBody(t, resp)
	require.Equal(t, http.StatusCreated, resp.StatusCode, "Create puzzle failed")

	var puzzleResp2 map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&puzzleResp2)
	require.NoError(t, err, "Failed to decode response")
	puzzleID2, ok := puzzleResp2["id"].(string)
	require.True(t, ok, "Puzzle ID should be a string")
	require.NotEmpty(t, puzzleID2, "Puzzle ID should not be empty")

	// Verify game exists
	userHeaders := map[string]string{
		"Authorization": "Bearer " + userToken,
	}
	gamesResp := makeRequest(t, "GET", "/api/v1/users/me/games", userHeaders, nil)
	defer closeResponseBody(t, gamesResp)
	require.Equal(t, http.StatusOK, gamesResp.StatusCode, "List games failed")

	var gamesResult map[string]interface{}
	err = json.NewDecoder(gamesResp.Body).Decode(&gamesResult)
	require.NoError(t, err, "Failed to decode response")
	gamesArray, ok := gamesResult["games"].([]interface{})
	require.True(t, ok, "Games should be an array")
	require.Equal(t, 2, len(gamesArray), "User should have two games")

	// Verify puzzle exists
	puzzlesResp := makeRequest(t, "GET", "/api/v1/users/me/puzzles", userHeaders, nil)
	defer closeResponseBody(t, puzzlesResp)
	require.Equal(t, http.StatusOK, puzzlesResp.StatusCode, "List puzzles failed")

	var puzzlesResult map[string]interface{}
	err = json.NewDecoder(puzzlesResp.Body).Decode(&puzzlesResult)
	require.NoError(t, err, "Failed to decode response")
	puzzlesArray, ok := puzzlesResult["puzzles"].([]interface{})
	require.True(t, ok, "Puzzles should be an array")
	require.Equal(t, 2, len(puzzlesArray), "User should have two puzzles")

	// Delete chess profile as user
	deleteMyChessProfile(t, userToken, profileID)

	// List profiles - should be empty again
	profiles = listMyChessProfiles(t, userToken)
	require.Equal(t, 0, len(profiles), "User should have no chess profiles after deletion")

	// Verify games and puzzles are also deleted
	gamesResp2 := makeRequest(t, "GET", "/api/v1/users/me/games", userHeaders, nil)
	defer closeResponseBody(t, gamesResp2)
	require.Equal(t, http.StatusOK, gamesResp2.StatusCode, "List games failed")

	var gamesResult2 map[string]interface{}
	err = json.NewDecoder(gamesResp2.Body).Decode(&gamesResult2)
	require.NoError(t, err, "Failed to decode response")
	gamesArray2, ok := gamesResult2["games"].([]interface{})
	require.True(t, ok, "Games should be an array")
	require.Equal(t, 1, len(gamesArray2), "User should have one game after profile deletion")

	puzzlesResp2 := makeRequest(t, "GET", "/api/v1/users/me/puzzles", userHeaders, nil)
	defer closeResponseBody(t, puzzlesResp2)
	require.Equal(t, http.StatusOK, puzzlesResp2.StatusCode, "List puzzles failed")

	var puzzlesResult2 map[string]interface{}
	err = json.NewDecoder(puzzlesResp2.Body).Decode(&puzzlesResult2)
	require.NoError(t, err, "Failed to decode response")
	puzzlesArray2, ok := puzzlesResult2["puzzles"].([]interface{})
	require.True(t, ok, "Puzzles should be an array")
	require.Equal(t, 1, len(puzzlesArray2), "User should have one puzzle after profile deletion")

	// Test "me" games and puzzles APIs
	// Create a game with known PGN for the user
	t.Log("Testing /users/me/games with PGN...")
	samplePGN := "1. e4 e5 2. Nf3 Nc6 3. Bb5 a6"
	gameWithPGN := createGameWithPGN(t, adminToken, userID, samplePGN)
	require.NotEmpty(t, gameWithPGN, "Game ID should not be empty")

	// List my games and verify PGN is included
	myGames := listMyGames(t, userToken, nil) // No filters initially
	games, gamesOk := myGames["games"].([]interface{})
	require.True(t, gamesOk, "List my games should return a games array")
	require.Contains(t, myGames, "total_count", "List my games response should contain total_count")

	// Find the game with PGN in the response
	var foundGame bool
	var returnedPGN string
	for _, g := range games {
		gameMap := g.(map[string]interface{})
		if gameMap["id"].(string) == gameWithPGN {
			foundGame = true
			// Verify PGN field exists
			pgn, pgnOk := gameMap["pgn"].(string)
			require.True(t, pgnOk, "Game response should contain 'pgn' field")
			returnedPGN = pgn
			break
		}
	}
	require.True(t, foundGame, "Game with PGN should be found in the response")
	require.Equal(t, samplePGN, returnedPGN, "PGN in response should match the original PGN")

	// List my puzzles (basic check)
	myPuzzles := listMyPuzzles(t, userToken, nil) // No filters initially
	_, puzzlesOk := myPuzzles["puzzles"].([]interface{})
	require.True(t, puzzlesOk, "List my puzzles should return a puzzles array")
	require.Contains(t, myPuzzles, "total_count", "List my puzzles response should contain total_count")

	// Test admin task APIs & Maintenance

	// Create a basic task for general testing
	taskID := createAdminTask(t, adminToken, userID)
	require.NotEmpty(t, taskID)
	_ = getAdminTask(t, adminToken, taskID)                   // Verify it exists
	_ = updateAdminTask(t, adminToken, taskID, "in_progress") // Update status

	// List tasks (basic check)
	taskList := listAdminTasks(t, adminToken)
	_, tasksOk := taskList["tasks"].([]interface{})
	require.True(t, tasksOk, "List tasks should return a tasks array")

	// --- Test Reset Hanging Tasks ---
	t.Log("Testing Reset Hanging Tasks...")
	// Call the reset API
	// Just ensure the endpoint is callable and returns the expected count field.
	// The actual reset logic is tested in unit tests.
	resetThresholdDuration := 1 * time.Hour // Use a reasonable default threshold
	resetCount := resetHangingTasks(t, adminToken, resetThresholdDuration)
	require.GreaterOrEqual(t, resetCount, 0, "Reset count should be a non-negative integer")

	// --- Test Cleanup Old Tasks ---
	t.Log("Testing Cleanup Old Tasks...")
	// Just ensure the endpoint is callable and returns the expected count field.
	// The actual cleanup logic is tested in unit tests.
	cleanupCutoffTime := time.Now().Add(-7 * 24 * time.Hour) // Use a reasonable default cutoff (e.g., 7 days ago)
	deletedCount := cleanupOldTasks(t, adminToken, cleanupCutoffTime)
	require.GreaterOrEqual(t, deletedCount, 0, "Deleted count should be a non-negative integer")

	// --- Final Cleanup ---
	// Delete puzzle
	deletePuzzle(t, adminToken, puzzleID)

	// Delete game
	deleteGame(t, adminToken, gameID)

	// TODO: Delete the user? Currently no endpoint.
	// TODO: Delete the tasks created? Currently no endpoint.
}

// Helper functions

// registerUser attempts to register a new user via the public endpoint
// It now requires an admin token because the endpoint is admin-protected.
func registerUser(t *testing.T, token, email, password string) {
	payload := map[string]string{
		"email":    email,
		"password": password,
	}

	// Add Authorization header
	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	resp := makeRequest(t, "POST", "/api/v1/auth/admin/register", headers, payload)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusCreated, resp.StatusCode, "User registration failed. Response body: %s", readResponseBody(t, resp))
}

func listUsers(t *testing.T, token string) []map[string]interface{} {
	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	resp := makeRequest(t, "GET", "/api/v1/admin/users", headers, nil)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusOK, resp.StatusCode, "List users failed. Response body: %s", readResponseBody(t, resp))

	var result []map[string]interface{}
	err := json.NewDecoder(resp.Body).Decode(&result)
	require.NoError(t, err, "Failed to decode response")

	return result
}

func createGame(t *testing.T, token string, userID string) string {
	payload := map[string]interface{}{
		"user_id":        userID,
		"platform":       "chess_com",
		"chess_username": "testuser",
		"user_color":     "white",
		"game_time":      time.Now().Format(time.RFC3339),
		"pgn":            "SGVsbG8gV29ybGQ=", // Base64 encoded string
		"time_control":   "5+0",
		"rated":          true,
		"white_player":   `{"username": "testuser", "is_ai": false}`,
		"black_player":   `{"username": "opponent", "is_ai": false}`,
		"winner":         "white",
		"result":         "mate",
	}

	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	resp := makeRequest(t, "POST", "/api/v1/admin/games", headers, payload)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusCreated, resp.StatusCode, "Create game failed. Response body: %s", readResponseBody(t, resp))

	var result map[string]interface{}
	err := json.NewDecoder(resp.Body).Decode(&result)
	require.NoError(t, err, "Failed to decode response")

	return result["id"].(string)
}

func createPuzzle(t *testing.T, token string, gameID string, userID string) string {
	payload := map[string]interface{}{
		"game_id":      gameID,
		"user_id":      userID,
		"game_move":    15,
		"fen":          "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
		"moves":        []string{"e2e4", "e7e5"},
		"prev_cp":      -20,
		"cp":           150,
		"theme":        "opponent_mistake_caught",
		"user_color":   "white",
		"puzzle_color": "black",
		"zugzwang":     false,
		"tags":         []string{"opening", "mistake"},
	}

	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	resp := makeRequest(t, "POST", "/api/v1/admin/puzzles", headers, payload)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusCreated, resp.StatusCode, "Create puzzle failed. Response body: %s", readResponseBody(t, resp))

	var result map[string]interface{}
	err := json.NewDecoder(resp.Body).Decode(&result)
	require.NoError(t, err, "Failed to decode response")

	return result["id"].(string)
}

// listPuzzles expects a paginated response now
func listPuzzles(t *testing.T, token string, userID string) map[string]interface{} {
	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	resp := makeRequest(t, "GET", "/api/v1/admin/puzzles?user_id="+userID, headers, nil)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusOK, resp.StatusCode, "List puzzles failed. Response body: %s", readResponseBody(t, resp))

	var result map[string]interface{}
	err := json.NewDecoder(resp.Body).Decode(&result)
	require.NoError(t, err, "Failed to decode response")

	// Check for pagination fields
	_, puzzlesOk := result["puzzles"]
	_, totalCountOk := result["total_count"]
	require.True(t, puzzlesOk, "Response should contain 'puzzles' field")
	require.True(t, totalCountOk, "Response should contain 'total_count' field")

	return result
}

// listPuzzlesByUser expects a paginated response now
func listPuzzlesByUser(t *testing.T, token string, userID string) map[string]interface{} {
	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	resp := makeRequest(t, "GET", "/api/v1/admin/puzzles?user_id="+userID, headers, nil)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusOK, resp.StatusCode, "List puzzles by user failed. Response body: %s", readResponseBody(t, resp))

	var result map[string]interface{}
	err := json.NewDecoder(resp.Body).Decode(&result)
	require.NoError(t, err, "Failed to decode response")

	// Check for pagination fields
	_, puzzlesOk := result["puzzles"]
	_, totalCountOk := result["total_count"]
	require.True(t, puzzlesOk, "Response should contain 'puzzles' field")
	require.True(t, totalCountOk, "Response should contain 'total_count' field")

	return result
}

// ---- Me API Tests ----

// listMyChessProfiles tests the GET /api/v1/users/me/chess-profiles endpoint
func listMyChessProfiles(t *testing.T, token string) []map[string]interface{} {
	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	resp := makeRequest(t, "GET", "/api/v1/users/me/chess-profiles", headers, nil)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusOK, resp.StatusCode, "List my chess profiles failed. Response body: %s", readResponseBody(t, resp))

	var result []map[string]interface{}
	err := json.NewDecoder(resp.Body).Decode(&result)
	require.NoError(t, err, "Failed to decode response")

	return result
}

// ---- Admin User APIs ----

// ---- Admin Chess Profile APIs ----

// ---- Tasks API Tests ----

// FetchChessGamesData defines the structure for task_data of FetchChessGames tasks.
type FetchChessGamesData struct {
	ChessProfileID string `json:"chess_profile_id"`
}

// Note: Removed createAdminTaskWithDetails as manipulating timestamps via API is unreliable.
// Tests now use time.Sleep to simulate aging and rely on API side-effects (like setting picked_up_at).

// ---- Me API Helpers ----
