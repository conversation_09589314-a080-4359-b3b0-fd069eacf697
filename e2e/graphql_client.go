package e2e

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

// Flag to control whether to run e2e tests
var runE2ETests = os.Getenv("RUN_E2E") == "true"

// GraphQLClient is a simple client for making GraphQL requests
type GraphQLClient struct {
	baseURL string
	token   string
	t       *testing.T
}

// createTestClient creates a new GraphQL client for testing
func createTestClient(t *testing.T) *GraphQLClient {
	// Generate an admin JWT token for all requests
	adminToken := generateAdminToken(t, "test-admin-id", "<EMAIL>")
	require.NotEmpty(t, adminToken, "Admin token should not be empty")

	// Create a new user with admin token
	userEmail := fmt.Sprintf("<EMAIL>", time.Now().Unix(), time.Now().Nanosecond())
	userPassword := "TestPassword123!"
	userID := createUser(t, adminToken, userEmail, userPassword)
	require.NotEmpty(t, userID, "User ID should not be empty")

	// Generate a user token for the created user
	userToken := generateUserToken(t, userID, userEmail)
	require.NotEmpty(t, userToken, "User token should not be empty")

	// Construct base URL dynamically
	baseURL := os.Getenv("API_BASE_URL")
	if baseURL == "" {
		baseURL = "http://localhost:8080" // Default if not set
	}

	return &GraphQLClient{
		baseURL: baseURL,
		token:   userToken,
		t:       t,
	}
}

// Post sends a GraphQL query with variables
func (c *GraphQLClient) Post(query string, result interface{}, variables map[string]interface{}) error {
	// Create request body
	body := map[string]interface{}{
		"query":     query,
		"variables": variables,
	}

	// Create headers
	headers := map[string]string{
		"Authorization": "Bearer " + c.token,
		"Content-Type":  "application/json",
	}

	// Make the request
	resp := c.makeRequest("POST", "/api/v1/graphql/query", headers, body)
	defer closeResponseBody(c.t, resp)

	// Read the response body
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %w", err)
	}

	// Check for HTTP errors
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("GraphQL query failed with status code %d: %s", resp.StatusCode, string(respBody))
	}

	// Parse the response
	var graphqlResponse struct {
		Data   interface{} `json:"data"`
		Errors []struct {
			Message string `json:"message"`
		} `json:"errors"`
	}

	if err := json.Unmarshal(respBody, &graphqlResponse); err != nil {
		return fmt.Errorf("failed to parse GraphQL response: %w", err)
	}

	// Check for GraphQL errors
	if len(graphqlResponse.Errors) > 0 {
		var messages []string
		for _, err := range graphqlResponse.Errors {
			messages = append(messages, err.Message)
		}
		return fmt.Errorf("GraphQL query returned errors: %v", messages)
	}

	// Unmarshal the data into the result
	dataJSON, err := json.Marshal(graphqlResponse.Data)
	if err != nil {
		return fmt.Errorf("failed to marshal GraphQL data: %w", err)
	}

	if err := json.Unmarshal(dataJSON, result); err != nil {
		return fmt.Errorf("failed to unmarshal GraphQL data into result: %w", err)
	}

	return nil
}

// makeRequest makes an HTTP request
func (c *GraphQLClient) makeRequest(method, path string, headers map[string]string, payload interface{}) *http.Response {
	var body io.Reader
	if payload != nil {
		payloadBytes, err := json.Marshal(payload)
		require.NoError(c.t, err, "Failed to marshal payload")
		body = bytes.NewBuffer(payloadBytes)
	}

	req, err := http.NewRequest(method, c.baseURL+path, body)
	require.NoError(c.t, err, "Failed to create request")

	for k, v := range headers {
		req.Header.Set(k, v)
	}

	if payload != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	// Allow overriding timeout via env var for slower environments
	timeoutStr := os.Getenv("API_TIMEOUT")
	timeout := 10 * time.Second // Default
	if timeoutStr != "" {
		parsedTimeout, err := time.ParseDuration(timeoutStr)
		if err == nil {
			timeout = parsedTimeout
		} else {
			c.t.Logf("Warning: Invalid API_TIMEOUT value '%s', using default %s", timeoutStr, timeout)
		}
	}

	client := &http.Client{Timeout: timeout}
	resp, err := client.Do(req)
	require.NoError(c.t, err, "Request failed")

	return resp
}
