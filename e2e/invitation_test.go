package e2e

import (
	"fmt"
	"net/http"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

func TestInvitationCodeRegistration(t *testing.T) {
	if os.Getenv("RUN_E2E") != "true" {
		t.Skip("Skipping E2E test (set RUN_E2E=true to run)")
	}
	// Generate an admin JWT token for creating invitation codes
	adminToken := generateAdminToken(t, "test-admin-id", "<EMAIL>")
	require.NotEmpty(t, adminToken, "Admin token should not be empty")

	// Create an invitation code
	invitationCode := createInvitationCode(t, adminToken, nil)
	require.NotEmpty(t, invitationCode, "Invitation code should not be empty")

	// Register a new user with the invitation code
	userEmail := fmt.Sprintf("<EMAIL>", time.Now().Unix())
	userPassword := "TestPassword123!"
	userID, userToken := registerWithInvitation(t, invitationCode, userEmail, userPassword)
	require.NotEmpty(t, userID, "User ID should not be empty")
	require.NotEmpty(t, userToken, "User token should not be empty")

	// Verify the user can access their profile
	myProfile := getMyProfile(t, userToken)
	require.Equal(t, userID, myProfile["id"], "User ID should match in profile response")
	require.Equal(t, userEmail, myProfile["email"], "Email should match in profile response")

	// Try to use the same invitation code again (should fail)
	duplicateEmail := fmt.Sprintf("<EMAIL>", time.Now().Unix())
	failedRegistration(t, invitationCode, duplicateEmail, userPassword)

	// Create an invitation code with expiration
	expiresInHours := 24
	expirableCode := createInvitationCode(t, adminToken, &expiresInHours)
	require.NotEmpty(t, expirableCode, "Expirable invitation code should not be empty")

	// Register another user with the expirable code
	user2Email := fmt.Sprintf("<EMAIL>", time.Now().Unix())
	user2Password := "TestPassword123!"
	user2ID, user2Token := registerWithInvitation(t, expirableCode, user2Email, user2Password)
	require.NotEmpty(t, user2ID, "User2 ID should not be empty")
	require.NotEmpty(t, user2Token, "User2 token should not be empty")

	// Verify the second user can access their profile
	user2Profile := getMyProfile(t, user2Token)
	require.Equal(t, user2ID, user2Profile["id"], "User2 ID should match in profile response")
	require.Equal(t, user2Email, user2Profile["email"], "User2 email should match in profile response")
}

func TestPublicRegistration(t *testing.T) {
	if os.Getenv("RUN_E2E") != "true" {
		t.Skip("Skipping E2E test (set RUN_E2E=true to run)")
	}

	t.Run("PublicRegistrationFlow", func(t *testing.T) {
		// Register a new user using public registration
		userEmail := fmt.Sprintf("<EMAIL>", time.Now().Unix())
		userPassword := "TestPassword123!"
		userID, userToken := registerPublic(t, userEmail, userPassword)
		require.NotEmpty(t, userID, "User ID should not be empty")
		require.NotEmpty(t, userToken, "User token should not be empty")

		// Verify the user can access their profile
		myProfile := getMyProfile(t, userToken)
		require.Equal(t, userID, myProfile["id"], "User ID should match in profile response")
		require.Equal(t, userEmail, myProfile["email"], "Email should match in profile response")

		// Try to register with the same email again (should fail)
		duplicateReq := map[string]string{
			"email":    userEmail,
			"password": "DifferentPassword123!",
		}

		resp := makeRequest(t, "POST", "/api/v1/auth/register", nil, duplicateReq)
		defer closeResponseBody(t, resp)
		require.Equal(t, http.StatusConflict, resp.StatusCode, "Duplicate email registration should fail")
	})
}

// failedRegistration attempts to register with an invalid invitation code and expects failure
func failedRegistration(t *testing.T, invitationCode, email, password string) {
	payload := map[string]string{
		"email":           email,
		"password":        password,
		"invitation_code": invitationCode,
	}

	resp := makeRequest(t, "POST", "/api/v1/auth/register-with-invitation", nil, payload)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusBadRequest, resp.StatusCode, "Expected registration to fail with invalid invitation code")
}
