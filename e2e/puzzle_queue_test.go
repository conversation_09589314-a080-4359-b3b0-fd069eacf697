package e2e

import (
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

// TestE2EPuzzleQueue tests the complete puzzle queue workflow end-to-end
func TestE2EPuzzleQueue(t *testing.T) {
	// Skip unless explicitly enabled
	if os.Getenv("RUN_E2E") != "true" {
		t.Skip("Skipping E2E test (set RUN_E2E=true to run)")
	}

	// Generate an admin JWT token for setup
	adminToken := generateAdminToken(t, "test-admin-id", "<EMAIL>")
	require.NotEmpty(t, adminToken, "Admin token should not be empty")

	// Create a test user
	userEmail := fmt.Sprintf("<EMAIL>", time.Now().Unix())
	userPassword := "TestPassword123!"
	userID := createUser(t, adminToken, userEmail, userPassword)
	require.NotEmpty(t, userID, "User ID should not be empty")

	// Generate a user token
	userToken := generateUserToken(t, userID, userEmail)
	require.NotEmpty(t, userToken, "User token should not be empty")

	// Create a game for the puzzles
	gameID := createGame(t, adminToken, userID)
	require.NotEmpty(t, gameID, "Game ID should not be empty")

	// Create puzzles with different themes for queue testing
	opponentMistakePuzzleID := createPuzzleWithTheme(t, adminToken, gameID, userID, "opponent_mistake_missed")
	ownMistakePuzzleID := createPuzzleWithTheme(t, adminToken, gameID, userID, "own_mistake_punished")
	opponentBlunderPuzzleID := createPuzzleWithTheme(t, adminToken, gameID, userID, "opponent_blunder_missed")
	_ = createPuzzleWithTheme(t, adminToken, gameID, userID, "opponent_mistake_caught") // Should not be queued

	t.Log("Created puzzles for queue testing")

	// Test 1: Add puzzles to queue
	t.Log("Testing Add Puzzles to Queue...")
	addedCount := addPuzzlesToQueue(t, userToken, 10, nil)
	require.Equal(t, 3, addedCount, "Should add 3 queueable puzzles (excluding caught mistakes)")

	// Test 2: Get queue statistics
	t.Log("Testing Queue Statistics...")
	stats := getQueueStats(t, userToken)
	require.Equal(t, float64(3), stats["total_queued"], "Total queued should be 3")
	require.GreaterOrEqual(t, stats["due_today"].(float64), float64(3), "All puzzles should be due today")
	require.GreaterOrEqual(t, stats["opponent_mistakes"].(float64), float64(2), "Should have 2 opponent mistakes")
	require.GreaterOrEqual(t, stats["own_mistakes"].(float64), float64(1), "Should have 1 own mistake")

	// Test 3: Get due puzzles
	t.Log("Testing Get Due Puzzles...")
	duePuzzles := getDuePuzzles(t, userToken, nil, 10)
	puzzles := duePuzzles["puzzles"].([]interface{})
	require.Len(t, puzzles, 3, "Should have 3 due puzzles")

	// Test 4: Get due puzzles filtered by mistake type
	t.Log("Testing Get Due Puzzles with Opponent Filter...")
	opponentPuzzles := getDuePuzzles(t, userToken, stringPtr("opponent"), 10)
	opponentPuzzlesList := opponentPuzzles["puzzles"].([]interface{})
	require.Len(t, opponentPuzzlesList, 2, "Should have 2 opponent mistake puzzles")

	for _, p := range opponentPuzzlesList {
		puzzle := p.(map[string]interface{})
		require.Equal(t, "opponent", puzzle["mistake_by"], "All puzzles should be opponent mistakes")
	}

	t.Log("Testing Get Due Puzzles with Own Filter...")
	ownPuzzles := getDuePuzzles(t, userToken, stringPtr("own"), 10)
	ownPuzzlesList := ownPuzzles["puzzles"].([]interface{})
	require.Len(t, ownPuzzlesList, 1, "Should have 1 own mistake puzzle")

	for _, p := range ownPuzzlesList {
		puzzle := p.(map[string]interface{})
		require.Equal(t, "own", puzzle["mistake_by"], "All puzzles should be own mistakes")
	}

	// Test 5: Submit puzzle attempt with queue update (correct answer)
	t.Log("Testing Puzzle Attempt with Queue Update (Correct)...")
	submitPuzzleAttemptWithQueue(t, userToken, opponentMistakePuzzleID, true, "normal_solve")

	// Verify queue was updated - puzzle should still be in queue but with updated stats
	statsAfterCorrect := getQueueStats(t, userToken)
	require.Equal(t, float64(3), statsAfterCorrect["total_queued"], "Should still have 3 puzzles in queue")

	// Test 6: Submit multiple correct attempts to test spaced repetition
	t.Log("Testing Spaced Repetition Logic...")

	// Submit 4 more correct attempts (total 5) to test mastery removal
	for i := 0; i < 4; i++ {
		submitPuzzleAttemptWithQueue(t, userToken, opponentMistakePuzzleID, true, "normal_solve")
		time.Sleep(100 * time.Millisecond) // Small delay to ensure different timestamps
	}

	// After 5 correct attempts, puzzle should be removed from queue
	statsAfterMastery := getQueueStats(t, userToken)
	require.Equal(t, float64(2), statsAfterMastery["total_queued"], "Should have 2 puzzles after mastery removal")

	// Test 7: Submit incorrect attempt
	t.Log("Testing Incorrect Attempt...")
	submitPuzzleAttemptWithQueue(t, userToken, ownMistakePuzzleID, false, "mistake_retry")

	// Queue should still have 2 puzzles (incorrect attempt doesn't remove)
	statsAfterIncorrect := getQueueStats(t, userToken)
	require.Equal(t, float64(2), statsAfterIncorrect["total_queued"], "Should still have 2 puzzles after incorrect attempt")

	// Test 8: Test attempt without queue flag (should not affect queue)
	t.Log("Testing Attempt Without Queue Flag...")
	submitPuzzleAttemptWithoutQueue(t, userToken, opponentBlunderPuzzleID, true)

	// Queue should remain unchanged
	statsAfterNonQueue := getQueueStats(t, userToken)
	require.Equal(t, float64(2), statsAfterNonQueue["total_queued"], "Queue should be unchanged by non-queue attempt")

	// Test 9: Add puzzles with specific mistake_by filter
	t.Log("Testing Add Puzzles with Mistake Filter...")

	// Get current queue state before adding more puzzles
	currentStats := getQueueStats(t, userToken)
	currentOpponentCount := int(currentStats["opponent_mistakes"].(float64))

	// Add only opponent mistakes (should add puzzles that aren't already in queue)
	addedOpponentCount := addPuzzlesToQueue(t, userToken, 10, stringPtr("opponent"))

	// Verify opponent mistakes were added (exact count depends on what's already in queue)
	statsOpponentOnly := getQueueStats(t, userToken)
	newOpponentCount := int(statsOpponentOnly["opponent_mistakes"].(float64))
	require.GreaterOrEqual(t, newOpponentCount, currentOpponentCount, "Should have at least the same number of opponent mistakes")
	require.GreaterOrEqual(t, addedOpponentCount, 0, "Should add some opponent mistake puzzles")

	// Test 10: Error cases
	t.Log("Testing Error Cases...")

	// Test invalid count (exceeds 200)
	testInvalidAddRequest(t, userToken, 250)

	// Test invalid mistake_by filter
	testInvalidMistakeByFilter(t, userToken)

	t.Log("Puzzle Queue E2E test completed successfully!")
}

// Helper functions

func createPuzzleWithTheme(t *testing.T, token, gameID, userID, theme string) string {
	payload := map[string]interface{}{
		"game_id":      gameID,
		"user_id":      userID,
		"game_move":    15,
		"fen":          "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
		"moves":        []string{"e2e4", "e7e5"},
		"prev_cp":      -20,
		"cp":           150,
		"theme":        theme,
		"user_color":   "white",
		"puzzle_color": "black",
		"zugzwang":     false,
		"tags":         []string{"tactics", "test"},
	}

	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	resp := makeRequest(t, "POST", "/api/v1/admin/puzzles", headers, payload)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusCreated, resp.StatusCode, "Create puzzle failed")

	var result map[string]interface{}
	err := json.NewDecoder(resp.Body).Decode(&result)
	require.NoError(t, err, "Failed to decode response")

	return result["id"].(string)
}

func addPuzzlesToQueue(t *testing.T, token string, count int, mistakeBy *string) int {
	payload := map[string]interface{}{
		"count": count,
	}
	if mistakeBy != nil {
		payload["mistake_by"] = *mistakeBy
	}

	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	resp := makeRequest(t, "POST", "/api/v1/users/me/puzzle-queue/add", headers, payload)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusOK, resp.StatusCode, "Add puzzles to queue failed")

	var result map[string]interface{}
	err := json.NewDecoder(resp.Body).Decode(&result)
	require.NoError(t, err, "Failed to decode response")

	return int(result["added_count"].(float64))
}

func getQueueStats(t *testing.T, token string) map[string]interface{} {
	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	resp := makeRequest(t, "GET", "/api/v1/users/me/puzzle-queue/stats", headers, nil)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusOK, resp.StatusCode, "Get queue stats failed")

	var result map[string]interface{}
	err := json.NewDecoder(resp.Body).Decode(&result)
	require.NoError(t, err, "Failed to decode response")

	return result
}

func getDuePuzzles(t *testing.T, token string, mistakeBy *string, limit int) map[string]interface{} {
	url := "/api/v1/users/me/puzzle-queue/due"
	if mistakeBy != nil {
		url += "?mistake_by=" + *mistakeBy
	}
	if limit > 0 {
		if mistakeBy != nil {
			url += "&limit=" + fmt.Sprintf("%d", limit)
		} else {
			url += "?limit=" + fmt.Sprintf("%d", limit)
		}
	}

	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	resp := makeRequest(t, "GET", url, headers, nil)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusOK, resp.StatusCode, "Get due puzzles failed")

	var result map[string]interface{}
	err := json.NewDecoder(resp.Body).Decode(&result)
	require.NoError(t, err, "Failed to decode response")

	return result
}

func submitPuzzleAttemptWithQueue(t *testing.T, token, puzzleID string, solved bool, attemptType string) {
	payload := map[string]interface{}{
		"moves":            []string{"e2e4"},
		"solved":           solved,
		"time_spent":       30,
		"attempt_type":     attemptType,
		"is_queue_attempt": true,
	}

	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	resp := makeRequest(t, "POST", "/api/v1/users/me/puzzles/"+puzzleID+"/attempts", headers, payload)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusCreated, resp.StatusCode, "Submit puzzle attempt failed")
}

func submitPuzzleAttemptWithoutQueue(t *testing.T, token, puzzleID string, solved bool) {
	payload := map[string]interface{}{
		"moves":            []string{"e2e4"},
		"solved":           solved,
		"time_spent":       30,
		"is_queue_attempt": false,
	}

	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	resp := makeRequest(t, "POST", "/api/v1/users/me/puzzles/"+puzzleID+"/attempts", headers, payload)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusCreated, resp.StatusCode, "Submit puzzle attempt failed")
}

func testInvalidAddRequest(t *testing.T, token string, invalidCount int) {
	payload := map[string]interface{}{
		"count": invalidCount,
	}

	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	resp := makeRequest(t, "POST", "/api/v1/users/me/puzzle-queue/add", headers, payload)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusBadRequest, resp.StatusCode, "Should reject invalid count")
}

func testInvalidMistakeByFilter(t *testing.T, token string) {
	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	resp := makeRequest(t, "GET", "/api/v1/users/me/puzzle-queue/due?mistake_by=invalid", headers, nil)
	defer closeResponseBody(t, resp)

	require.Equal(t, http.StatusBadRequest, resp.StatusCode, "Should reject invalid mistake_by filter")
}

func stringPtr(s string) *string {
	return &s
}
