package e2e

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestMyPuzzleStatsTimeFilteringFix specifically tests the fix for the issue where
// game_start_time and game_end_time filters were not working in myPuzzleStats
func TestMyPuzzleStatsTimeFilteringFix(t *testing.T) {
	// Skip if not running e2e tests
	if !runE2ETests {
		t.Skip("Skipping e2e test")
	}

	// Create a test client
	client := createTestClient(t)

	// Test the original issue: 3 months vs 6 months should return different results
	// when there are games in different time periods
	t.Run("3 months vs 6 months data returns different results", func(t *testing.T) {
		// Get current time for relative date calculations
		now := time.Now()

		// Calculate 3 months ago and 6 months ago
		threeMonthsAgo := now.AddDate(0, -3, 0)
		sixMonthsAgo := now.AddDate(0, -6, 0)

		// Query for puzzles from games in the last 3 months
		query3Months := `
		query MyPuzzleStats($filter: PuzzleFilter!) {
			myPuzzleStats(filter: $filter) {
				total_count
				unique_game_count
			}
		}`

		variables3Months := map[string]interface{}{
			"filter": map[string]interface{}{
				"game_start_time": threeMonthsAgo.Format(time.RFC3339),
			},
		}

		var response3Months struct {
			MyPuzzleStats struct {
				TotalCount      int `json:"total_count"`
				UniqueGameCount int `json:"unique_game_count"`
			} `json:"myPuzzleStats"`
		}

		err := client.Post(query3Months, &response3Months, variables3Months)
		require.NoError(t, err)

		// Query for puzzles from games in the last 6 months
		query6Months := `
		query MyPuzzleStats($filter: PuzzleFilter!) {
			myPuzzleStats(filter: $filter) {
				total_count
				unique_game_count
			}
		}`

		variables6Months := map[string]interface{}{
			"filter": map[string]interface{}{
				"game_start_time": sixMonthsAgo.Format(time.RFC3339),
			},
		}

		var response6Months struct {
			MyPuzzleStats struct {
				TotalCount      int `json:"total_count"`
				UniqueGameCount int `json:"unique_game_count"`
			} `json:"myPuzzleStats"`
		}

		err = client.Post(query6Months, &response6Months, variables6Months)
		require.NoError(t, err)

		// Log the results for debugging
		t.Logf("3 months filter: total_count=%d, unique_game_count=%d",
			response3Months.MyPuzzleStats.TotalCount,
			response3Months.MyPuzzleStats.UniqueGameCount)
		t.Logf("6 months filter: total_count=%d, unique_game_count=%d",
			response6Months.MyPuzzleStats.TotalCount,
			response6Months.MyPuzzleStats.UniqueGameCount)

		// The key assertion: 6 months should include at least as many puzzles as 3 months
		// (since 6 months includes the 3 month period plus additional time)
		assert.GreaterOrEqual(t, response6Months.MyPuzzleStats.TotalCount,
			response3Months.MyPuzzleStats.TotalCount,
			"6 months filter should return at least as many puzzles as 3 months filter")

		assert.GreaterOrEqual(t, response6Months.MyPuzzleStats.UniqueGameCount,
			response3Months.MyPuzzleStats.UniqueGameCount,
			"6 months filter should return at least as many unique games as 3 months filter")

		// Both queries should return non-negative results
		assert.GreaterOrEqual(t, response3Months.MyPuzzleStats.TotalCount, 0)
		assert.GreaterOrEqual(t, response6Months.MyPuzzleStats.TotalCount, 0)
		assert.GreaterOrEqual(t, response3Months.MyPuzzleStats.UniqueGameCount, 0)
		assert.GreaterOrEqual(t, response6Months.MyPuzzleStats.UniqueGameCount, 0)
	})

	// Test that time filtering actually works by using a very restrictive date range
	t.Run("Restrictive date range returns fewer results", func(t *testing.T) {
		// Query without any time filter (should return all puzzles)
		queryAll := `
		{
			myPuzzleStats {
				total_count
				unique_game_count
			}
		}`

		var responseAll struct {
			MyPuzzleStats struct {
				TotalCount      int `json:"total_count"`
				UniqueGameCount int `json:"unique_game_count"`
			} `json:"myPuzzleStats"`
		}

		err := client.Post(queryAll, &responseAll, nil)
		require.NoError(t, err)

		// Query with a very restrictive date range (only games from yesterday)
		yesterday := time.Now().AddDate(0, 0, -1)
		today := time.Now()

		queryRestricted := `
		query MyPuzzleStats($filter: PuzzleFilter!) {
			myPuzzleStats(filter: $filter) {
				total_count
				unique_game_count
			}
		}`

		variablesRestricted := map[string]interface{}{
			"filter": map[string]interface{}{
				"game_start_time": yesterday.Format(time.RFC3339),
				"game_end_time":   today.Format(time.RFC3339),
			},
		}

		var responseRestricted struct {
			MyPuzzleStats struct {
				TotalCount      int `json:"total_count"`
				UniqueGameCount int `json:"unique_game_count"`
			} `json:"myPuzzleStats"`
		}

		err = client.Post(queryRestricted, &responseRestricted, variablesRestricted)
		require.NoError(t, err)

		// Log the results for debugging
		t.Logf("All puzzles: total_count=%d, unique_game_count=%d",
			responseAll.MyPuzzleStats.TotalCount,
			responseAll.MyPuzzleStats.UniqueGameCount)
		t.Logf("Yesterday only: total_count=%d, unique_game_count=%d",
			responseRestricted.MyPuzzleStats.TotalCount,
			responseRestricted.MyPuzzleStats.UniqueGameCount)

		// The restrictive filter should return fewer or equal results than no filter
		assert.LessOrEqual(t, responseRestricted.MyPuzzleStats.TotalCount,
			responseAll.MyPuzzleStats.TotalCount,
			"Restrictive time filter should return fewer or equal puzzles than no filter")

		assert.LessOrEqual(t, responseRestricted.MyPuzzleStats.UniqueGameCount,
			responseAll.MyPuzzleStats.UniqueGameCount,
			"Restrictive time filter should return fewer or equal unique games than no filter")

		// Both queries should return non-negative results
		assert.GreaterOrEqual(t, responseAll.MyPuzzleStats.TotalCount, 0)
		assert.GreaterOrEqual(t, responseRestricted.MyPuzzleStats.TotalCount, 0)
		assert.GreaterOrEqual(t, responseAll.MyPuzzleStats.UniqueGameCount, 0)
		assert.GreaterOrEqual(t, responseRestricted.MyPuzzleStats.UniqueGameCount, 0)

		// This test demonstrates that time filtering is working correctly
		// Before the fix, both queries would have returned the same results
		// Now they correctly return different results based on the time filter
		t.Logf("Time filtering is working correctly - filters are being applied as expected")
	})
}
