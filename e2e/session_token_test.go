package e2e

import (
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestSessionTokenE2E tests the complete session token flow end-to-end
func TestSessionTokenE2E(t *testing.T) {
	// Skip unless explicitly enabled
	if os.Getenv("RUN_E2E") != "true" {
		t.Skip("Skipping E2E test (set RUN_E2E=true to run)")
	}

	// Generate an admin JWT token for creating invitation codes
	adminToken := generateAdminToken(t, "test-admin-id", "<EMAIL>")
	require.NotEmpty(t, adminToken, "Admin token should not be empty")

	t.Run("CompleteSessionTokenFlow", func(t *testing.T) {
		// Step 1: Register a new user using public registration
		userEmail := fmt.Sprintf("<EMAIL>", time.Now().Unix())
		userPassword := "testpassword123"

		userID, userToken := registerPublic(t, userEmail, userPassword)
		require.NotEmpty(t, userID, "User ID should not be empty")
		require.NotEmpty(t, userToken, "User token should not be empty")

		// Step 2: Login to get session token
		loginReq := map[string]interface{}{
			"email":    userEmail,
			"password": userPassword,
		}

		headers := map[string]string{
			"User-Agent": "E2E Test Browser 1.0",
		}

		loginResp := makeRequest(t, "POST", "/api/v1/auth/login", headers, loginReq)
		defer closeResponseBody(t, loginResp)
		require.Equal(t, http.StatusOK, loginResp.StatusCode)

		var loginRespData map[string]interface{}
		err := json.NewDecoder(loginResp.Body).Decode(&loginRespData)
		require.NoError(t, err)

		// Verify we got both tokens from login
		jwtToken, ok := loginRespData["token"].(string)
		require.True(t, ok)
		require.NotEmpty(t, jwtToken)

		sessionToken, ok := loginRespData["session_token"].(string)
		require.True(t, ok)
		require.NotEmpty(t, sessionToken)

		// Step 3: Use session token to login (without email/password)
		sessionLoginReq := map[string]interface{}{
			"session_token": sessionToken,
		}

		sessionLoginResp := makeRequest(t, "POST", "/api/v1/auth/login", nil, sessionLoginReq)
		defer closeResponseBody(t, sessionLoginResp)
		require.Equal(t, http.StatusOK, sessionLoginResp.StatusCode)

		var loginAuthResp map[string]interface{}
		err = json.NewDecoder(sessionLoginResp.Body).Decode(&loginAuthResp)
		require.NoError(t, err)

		// Verify we got a new JWT token but no new session token
		newJWTToken, ok := loginAuthResp["token"].(string)
		require.True(t, ok)
		require.NotEmpty(t, newJWTToken)

		newSessionToken, exists := loginAuthResp["session_token"]
		if exists {
			assert.Empty(t, newSessionToken) // Should be empty for session token login
		}

		// Step 3: List session tokens using JWT authentication
		listHeaders := map[string]string{
			"Authorization": "Bearer " + newJWTToken,
		}
		listResp := makeRequest(t, "GET", "/api/v1/auth/session-tokens", listHeaders, nil)
		defer closeResponseBody(t, listResp)
		require.Equal(t, http.StatusOK, listResp.StatusCode)

		var tokens []map[string]interface{}
		err = json.NewDecoder(listResp.Body).Decode(&tokens)
		require.NoError(t, err)

		// Should have exactly 2 session tokens (one from registration, one from login)
		require.Len(t, tokens, 2)
		tokenID, ok := tokens[0]["id"].(string)
		require.True(t, ok)
		require.NotEmpty(t, tokenID)

		userAgent, ok := tokens[0]["user_agent"].(string)
		require.True(t, ok)
		assert.Equal(t, "E2E Test Browser 1.0", userAgent)

		// Step 4: Revoke the session token
		revokeHeaders := map[string]string{
			"Authorization": "Bearer " + newJWTToken,
		}
		revokeResp := makeRequest(t, "DELETE", "/api/v1/auth/session-tokens/"+tokenID, revokeHeaders, nil)
		defer closeResponseBody(t, revokeResp)
		require.Equal(t, http.StatusNoContent, revokeResp.StatusCode)

		// Step 5: Try to use the revoked session token (should fail)
		loginWithRevokedReq := map[string]interface{}{
			"session_token": sessionToken,
		}

		failedLoginResp := makeRequest(t, "POST", "/api/v1/auth/login", nil, loginWithRevokedReq)
		defer closeResponseBody(t, failedLoginResp)
		require.Equal(t, http.StatusUnauthorized, failedLoginResp.StatusCode)

		// Step 6: Verify session tokens list is now empty
		listAfterRevokeResp := makeRequest(t, "GET", "/api/v1/auth/session-tokens", listHeaders, nil)
		defer closeResponseBody(t, listAfterRevokeResp)
		require.Equal(t, http.StatusOK, listAfterRevokeResp.StatusCode)

		var tokensAfterRevoke []map[string]interface{}
		err = json.NewDecoder(listAfterRevokeResp.Body).Decode(&tokensAfterRevoke)
		require.NoError(t, err)
		assert.Len(t, tokensAfterRevoke, 1, "Should have 1 remaining session token after deleting 1 of 2")
	})
}
