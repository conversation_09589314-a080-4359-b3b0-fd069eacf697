package e2e

import (
	"testing"
	"time"

	"github.com/chessticize/chessticize-server/internal/graphql/model"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestGroupedPuzzleStats(t *testing.T) {
	// Skip if not running e2e tests
	if !runE2ETests {
		t.Skip("Skipping e2e test")
	}

	// Create a test client
	client := createTestClient(t)

	// Create a time range for testing (last 3 months)
	endTime := time.Now()
	startTime := endTime.AddDate(0, -3, 0)

	// Create a filter with the time range
	filter := model.PuzzleFilter{
		GameStartTime: &startTime,
		GameEndTime:   &endTime,
	}

	// Create pagination parameters
	offset := 0
	limit := 10
	pagination := model.OffsetPaginationInput{
		Offset: &offset,
		Limit:  &limit,
	}

	// Test grouping by day
	t.Run("Group by Day", func(t *testing.T) {
		// Create the query
		query := `
		query MyGroupedPuzzleStats($filter: PuzzleFilter!, $pagination: OffsetPaginationInput!, $group_unit: TimeGrouping!, $group_length: Int) {
			myGroupedPuzzleStats(filter: $filter, pagination: $pagination, group_unit: $group_unit, group_length: $group_length) {
				total_count
				nodes {
					start_time
					end_time
					stats {
						total_count
					}
				}
			}
		}
		`

		// Create the variables
		variables := map[string]interface{}{
			"filter":     filter,
			"pagination": pagination,
			"group_unit": model.TimeGroupingDay,
		}

		// Execute the query
		var response struct {
			MyGroupedPuzzleStats struct {
				TotalCount int `json:"total_count"`
				Nodes      []struct {
					StartTime string `json:"start_time"`
					EndTime   string `json:"end_time"`
					Stats     struct {
						TotalCount int `json:"total_count"`
					} `json:"stats"`
				} `json:"nodes"`
			} `json:"myGroupedPuzzleStats"`
		}

		err := client.Post(query, &response, variables)
		require.NoError(t, err)

		// Verify the response
		assert.GreaterOrEqual(t, response.MyGroupedPuzzleStats.TotalCount, 0, "Total count should be non-negative")

		// If we have nodes, verify their structure
		if len(response.MyGroupedPuzzleStats.Nodes) > 0 {
			node := response.MyGroupedPuzzleStats.Nodes[0]
			assert.NotEmpty(t, node.StartTime, "StartTime should not be empty")
			assert.NotEmpty(t, node.EndTime, "EndTime should not be empty")
			assert.GreaterOrEqual(t, node.Stats.TotalCount, 0, "TotalCount should be non-negative")
		}
	})

	// Test grouping by week with custom length
	t.Run("Group by Week with Custom Length", func(t *testing.T) {
		// Create the query
		query := `
		query MyGroupedPuzzleStats($filter: PuzzleFilter!, $pagination: OffsetPaginationInput!, $group_unit: TimeGrouping!, $group_length: Int) {
			myGroupedPuzzleStats(filter: $filter, pagination: $pagination, group_unit: $group_unit, group_length: $group_length) {
				total_count
				nodes {
					start_time
					end_time
					stats {
						total_count
					}
				}
			}
		}
		`

		// Create the variables
		groupLength := 2
		variables := map[string]interface{}{
			"filter":       filter,
			"pagination":   pagination,
			"group_unit":   model.TimeGroupingWeek,
			"group_length": groupLength,
		}

		// Execute the query
		var response struct {
			MyGroupedPuzzleStats struct {
				TotalCount int `json:"total_count"`
				Nodes      []struct {
					StartTime string `json:"start_time"`
					EndTime   string `json:"end_time"`
					Stats     struct {
						TotalCount int `json:"total_count"`
					} `json:"stats"`
				} `json:"nodes"`
			} `json:"myGroupedPuzzleStats"`
		}

		err := client.Post(query, &response, variables)
		require.NoError(t, err)

		// Verify the response
		assert.GreaterOrEqual(t, response.MyGroupedPuzzleStats.TotalCount, 0, "Total count should be non-negative")
	})
}

func TestGroupedGameStats(t *testing.T) {
	// Skip if not running e2e tests
	if !runE2ETests {
		t.Skip("Skipping e2e test")
	}

	// Create a test client
	client := createTestClient(t)

	// Create a time range for testing (last 3 months)
	endTime := time.Now()
	startTime := endTime.AddDate(0, -3, 0)

	// Create a filter with the time range
	filter := model.GameFilter{
		StartTime: &startTime,
		EndTime:   &endTime,
	}

	// Create pagination parameters
	offset := 0
	limit := 10
	pagination := model.OffsetPaginationInput{
		Offset: &offset,
		Limit:  &limit,
	}

	// Test grouping by day
	t.Run("Group by Day", func(t *testing.T) {
		// Create the query
		query := `
		query MyGroupedGameStats($filter: GameFilter!, $pagination: OffsetPaginationInput!, $group_unit: TimeGrouping!, $group_length: Int) {
			myGroupedGameStats(filter: $filter, pagination: $pagination, group_unit: $group_unit, group_length: $group_length) {
				total_count
				nodes {
					start_time
					end_time
					stats {
						total_count
					}
				}
			}
		}
		`

		// Create the variables
		variables := map[string]interface{}{
			"filter":     filter,
			"pagination": pagination,
			"group_unit": model.TimeGroupingDay,
		}

		// Execute the query
		var response struct {
			MyGroupedGameStats struct {
				TotalCount int `json:"total_count"`
				Nodes      []struct {
					StartTime string `json:"start_time"`
					EndTime   string `json:"end_time"`
					Stats     struct {
						TotalCount int `json:"total_count"`
					} `json:"stats"`
				} `json:"nodes"`
			} `json:"myGroupedGameStats"`
		}

		err := client.Post(query, &response, variables)
		require.NoError(t, err)

		// Verify the response
		assert.GreaterOrEqual(t, response.MyGroupedGameStats.TotalCount, 0, "Total count should be non-negative")

		// If we have nodes, verify their structure
		if len(response.MyGroupedGameStats.Nodes) > 0 {
			node := response.MyGroupedGameStats.Nodes[0]
			assert.NotEmpty(t, node.StartTime, "StartTime should not be empty")
			assert.NotEmpty(t, node.EndTime, "EndTime should not be empty")
			assert.GreaterOrEqual(t, node.Stats.TotalCount, 0, "TotalCount should be non-negative")
		}
	})

	// Test grouping by month with custom length
	t.Run("Group by Month with Custom Length", func(t *testing.T) {
		// Create the query
		query := `
		query MyGroupedGameStats($filter: GameFilter!, $pagination: OffsetPaginationInput!, $group_unit: TimeGrouping!, $group_length: Int) {
			myGroupedGameStats(filter: $filter, pagination: $pagination, group_unit: $group_unit, group_length: $group_length) {
				total_count
				nodes {
					start_time
					end_time
					stats {
						total_count
					}
				}
			}
		}
		`

		// Create the variables
		groupLength := 2
		variables := map[string]interface{}{
			"filter":       filter,
			"pagination":   pagination,
			"group_unit":   model.TimeGroupingMonth,
			"group_length": groupLength,
		}

		// Execute the query
		var response struct {
			MyGroupedGameStats struct {
				TotalCount int `json:"total_count"`
				Nodes      []struct {
					StartTime string `json:"start_time"`
					EndTime   string `json:"end_time"`
					Stats     struct {
						TotalCount int `json:"total_count"`
					} `json:"stats"`
				} `json:"nodes"`
			} `json:"myGroupedGameStats"`
		}

		err := client.Post(query, &response, variables)
		require.NoError(t, err)

		// Verify the response
		assert.GreaterOrEqual(t, response.MyGroupedGameStats.TotalCount, 0, "Total count should be non-negative")
	})
}

func TestPuzzleStatsWithUniqueGameCount(t *testing.T) {
	// Skip if not running e2e tests
	if !runE2ETests {
		t.Skip("Skipping e2e test")
	}

	// Create a test client
	client := createTestClient(t)

	// Test basic puzzle stats with unique game count
	t.Run("Basic Puzzle Stats with Unique Game Count", func(t *testing.T) {
		// Create the query
		query := `
		{
			myPuzzleStats {
				tag_counts {
					tag
					count
				}
				theme_counts {
					theme
					count
				}
				user_color_counts {
					color
					count
				}
				game_move_buckets {
					name
					min_move
					max_move
					count
				}
				move_length_counts {
					length
					count
				}
				total_count
				unique_game_count
			}
		}
		`

		// Execute the query
		var response struct {
			MyPuzzleStats struct {
				TagCounts []struct {
					Tag   string `json:"tag"`
					Count int    `json:"count"`
				} `json:"tag_counts"`
				ThemeCounts []struct {
					Theme string `json:"theme"`
					Count int    `json:"count"`
				} `json:"theme_counts"`
				UserColorCounts []struct {
					Color string `json:"color"`
					Count int    `json:"count"`
				} `json:"user_color_counts"`
				GameMoveBuckets []struct {
					Name    string `json:"name"`
					MinMove int    `json:"min_move"`
					MaxMove int    `json:"max_move"`
					Count   int    `json:"count"`
				} `json:"game_move_buckets"`
				MoveLengthCounts []struct {
					Length int `json:"length"`
					Count  int `json:"count"`
				} `json:"move_length_counts"`
				TotalCount      int `json:"total_count"`
				UniqueGameCount int `json:"unique_game_count"`
			} `json:"myPuzzleStats"`
		}

		err := client.Post(query, &response, nil)
		require.NoError(t, err)

		// Verify the response structure
		assert.GreaterOrEqual(t, response.MyPuzzleStats.TotalCount, 0, "Total count should be non-negative")
		assert.GreaterOrEqual(t, response.MyPuzzleStats.UniqueGameCount, 0, "Unique game count should be non-negative")

		// Unique game count should be less than or equal to total count
		assert.LessOrEqual(t, response.MyPuzzleStats.UniqueGameCount, response.MyPuzzleStats.TotalCount,
			"Unique game count should be less than or equal to total count")

		// If we have puzzles, verify the structure
		if response.MyPuzzleStats.TotalCount > 0 {
			assert.Greater(t, response.MyPuzzleStats.UniqueGameCount, 0, "Should have at least one unique game if there are puzzles")
		}
	})

	// Test filtered puzzle stats with unique game count
	t.Run("Filtered Puzzle Stats with Unique Game Count", func(t *testing.T) {
		// Create the query with filter
		query := `
		query MyPuzzleStats($filter: PuzzleFilter!) {
			myPuzzleStats(filter: $filter) {
				total_count
				unique_game_count
				user_color_counts {
					color
					count
				}
			}
		}
		`

		// Create filter for WHITE user color
		variables := map[string]interface{}{
			"filter": map[string]interface{}{
				"user_color": "WHITE",
			},
		}

		// Execute the query
		var response struct {
			MyPuzzleStats struct {
				TotalCount      int `json:"total_count"`
				UniqueGameCount int `json:"unique_game_count"`
				UserColorCounts []struct {
					Color string `json:"color"`
					Count int    `json:"count"`
				} `json:"user_color_counts"`
			} `json:"myPuzzleStats"`
		}

		err := client.Post(query, &response, variables)
		require.NoError(t, err)

		// Verify the response
		assert.GreaterOrEqual(t, response.MyPuzzleStats.TotalCount, 0, "Total count should be non-negative")
		assert.GreaterOrEqual(t, response.MyPuzzleStats.UniqueGameCount, 0, "Unique game count should be non-negative")

		// Unique game count should be less than or equal to total count
		assert.LessOrEqual(t, response.MyPuzzleStats.UniqueGameCount, response.MyPuzzleStats.TotalCount,
			"Unique game count should be less than or equal to total count")

		// If we have user color counts, they should all be WHITE
		for _, colorCount := range response.MyPuzzleStats.UserColorCounts {
			assert.Equal(t, "white", colorCount.Color, "All user colors should be white when filtered")
		}
	})
}
