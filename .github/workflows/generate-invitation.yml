name: Generate Invitation Code

on:
  workflow_dispatch:
    inputs:
      expires_in_hours:
        description: 'Expiration time in hours (default: 8760 hours = 1 year)'
        required: false
        type: string
        default: '8760'

jobs:
  generate-invitation:
    name: Generate Invitation Code
    runs-on: ubuntu-latest
    environment: chessticize-server

    steps:
      - name: Generate Invitation Code
        id: generate
        env:
          ADMIN_TOKEN: ${{ secrets.CHESSTICIZE_ADMIN_TOKEN }}
          API_URL: ${{ secrets.CHESSTICIZE_API_URL || 'https://chessticize-server-9ddca5bcf137.herokuapp.com' }}
          EXPIRES_IN_HOURS: ${{ inputs.expires_in_hours }}
        run: |
          # Prepare request body
          if [ -n "$EXPIRES_IN_HOURS" ]; then
            REQUEST_BODY="{\"expires_in_hours\": $EXPIRES_IN_HOURS}"
          else
            REQUEST_BODY="{}"
          fi

          # Make API request to create invitation code
          RESPONSE=$(curl -s -X POST "$API_URL/api/v1/auth/invitation-codes" \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer $ADMIN_TOKEN" \
            -d "$REQUEST_BODY")

          # Extract code from response
          CODE=$(echo $RESPONSE | jq -r '.code')
          ID=$(echo $RESPONSE | jq -r '.id')
          CREATED_AT=$(echo $RESPONSE | jq -r '.created_at')
          EXPIRES_AT=$(echo $RESPONSE | jq -r '.expires_at')

          if [ "$CODE" == "null" ]; then
            echo "Error generating invitation code: $RESPONSE"
            exit 1
          fi

          # Set outputs
          echo "code=$CODE" >> $GITHUB_OUTPUT
          echo "id=$ID" >> $GITHUB_OUTPUT

          # Display information
          echo "Invitation Code: $CODE"
          echo "ID: $ID"
          echo "Created At: $CREATED_AT"
          if [ "$EXPIRES_AT" != "null" ]; then
            echo "Expires At: $EXPIRES_AT"
          else
            echo "Expires At: Never"
          fi

          # Create a markdown summary
          echo "## Invitation Code Generated" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Code:** \`$CODE\`" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**ID:** $ID" >> $GITHUB_STEP_SUMMARY
          echo "**Created At:** $CREATED_AT" >> $GITHUB_STEP_SUMMARY
          if [ "$EXPIRES_AT" != "null" ]; then
            echo "**Expires At:** $EXPIRES_AT" >> $GITHUB_STEP_SUMMARY
          else
            echo "**Expires At:** Never" >> $GITHUB_STEP_SUMMARY
          fi
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "This code can be used once to register a new user account." >> $GITHUB_STEP_SUMMARY
