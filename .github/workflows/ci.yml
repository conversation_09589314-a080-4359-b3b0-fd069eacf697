name: CI

on:
  pull_request:
  workflow_dispatch:

jobs:
  verify:
    name: Verify
    runs-on: ubuntu-latest

# We'll use Docker Compose for PostgreSQL instead of GitHub Actions service

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.24.2'
          cache: true

      - name: Create .env file
        run: |
          cat > .env << EOF
          # Server Configuration
          PORT=8080

          # Database Configuration
          DATABASE_URL=postgres://puzzler:puzzler_secret@localhost:5432/chess_puzzler_dev?sslmode=disable

          # JWT Configuration
          JWT_SECRET=test-secret-key-for-e2e-testing
          JWT_EXPIRY_MINUTES=1440
          
          # Firebase Configuration
          FIREBASE_PROJECT_ID=demo-project
          FIREBASE_JWKS_ENDPOINT=http://localhost:9099/.well-known/jwks.json
          EOF

      - name: Format Check
        id: format
        run: |
          echo "Running format check..."
          make format
          # Check if there are any changes in Go files after formatting
          if [ -n "$(git status --porcelain '*.go')" ]; then
            echo "::error::Go code is not properly formatted. Run 'make format' locally and commit the changes."
            git diff '*.go'
            exit 1
          fi

      - name: Install golangci-lint
        uses: golangci/golangci-lint-action@v4
        with:
          version: latest
          args: --timeout=5m

      - name: Lint
        id: lint
        run: |
          echo "Running linter..."
          make lint

      - name: Build
        id: build
        run: |
          echo "Building application..."
          make build

      - name: Setup Database
        id: setup-db
        run: |
          make reset-db

      - name: Run Tests
        id: test
        run: |
          echo "Running tests..."
          make test

      - name: Run E2E Tests
        id: e2e-test
        run: |
          echo "Running E2E tests..."
          make e2e-test

      - name: Cleanup
        if: always()
        run: |
          echo "Cleaning up..."
          make stop
          # Stop and remove Docker containers
          docker compose down -v
